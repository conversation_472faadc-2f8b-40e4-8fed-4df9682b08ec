// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { MeshTools } from "../meshTools/MeshTools";
import { Publish } from "../meshTools/tools/Publish";
import { MoveType, RoomType } from "./bean/EnumBean";
import { AcceptInvite, ExtendLevelInfoResponse, IllegalOperation, InviteKickOut, MoveBlockFail, NoticeLeaveInvite, NoticeMoveBlock, NoticeScoreChg, NoticeSettlement, NoticeStartGame, NoticeUserInviteStatus, ObstacleBlock, UserInfo } from "./bean/GameBean";
import { GlobalBean } from "./bean/GlobalBean";
import LanguageType from "./bean/LanguageType";
import { EventType } from "./common/EventCenter";
import { GameMgr } from "./common/GameMgr";
import GamePageController from "./game/GamePageController";
import HallPageController, { HallOrMatch } from "./hall/HallPageController";
import LevelPageController from "./level/LevelPageController";
import TopUpDialogController from "./hall/TopUpDialogController";
import { ErrorCode } from "./net/ErrorCode";
import { GameServerUrl } from "./net/GameServerUrl";
import { HttpManager } from "./net/HttpManager";
import { AutoMessageBean, AutoMessageId, ReceivedMessageBean } from "./net/MessageBaseBean";
import { MessageId } from "./net/MessageId";
import { WebSocketManager } from "./net/WebSocketManager";
import { WebSocketTool } from "./net/WebSocketTool";
import StartUpPageController from "./start_up/StartUpPageController";
import TipsDialogController from "./TipsDialogController";
import ToastController from "./ToastController";
import { AudioMgr } from "./util/AudioMgr";
import { Config } from "./util/Config";

const { ccclass, property } = cc._decorator;

window.languageName = "en";

export enum PageType {
    START_UP_PAGE,//启动页面
    HALL_PAGE,//大厅页面
    GAME_PAGE,//游戏页面
    LEVEL_PAGE,//关卡页面
}

@ccclass
export default class GlobalManagerController extends cc.Component {

    @property(TipsDialogController)
    tipsDialogController: TipsDialogController = null //这个是错误弹窗 只有一个退出按钮
    @property(TopUpDialogController)
    topUpDialogController: TopUpDialogController = null //充值弹窗
    @property(cc.Node)
    netError: cc.Node = null  //这个是断网的时候展示的转圈的
    @property(ToastController)
    toastController: ToastController = null  //toast 的布局

    @property(cc.Node)
    startUpPage: cc.Node = null  //启动页
    @property(cc.Node)
    hallPage: cc.Node = null  //大厅页
    @property(cc.Node)
    gamePage: cc.Node = null  //游戏页面
    @property(cc.Node)
    levelPage: cc.Node = null  //关卡页面



    currentPage: PageType = PageType.START_UP_PAGE //当前展示的页面，默认展示的是启动页面

    startUpPageController: StartUpPageController = null  //启动页面的总管理器
    hallPageController: HallPageController = null   //大厅页面的总管理器
    gamePageController: GamePageController = null   //游戏页面的总管理器
    levelPageController: LevelPageController = null   //关卡页面的总管理器


    onLoad() {
        cc.resources.preloadDir(Config.hallRes, cc.SpriteFrame);//提前预加载大厅图片资源

        // 获取音频管理器实例
        const audioMgr = AudioMgr.ins;
        // 初始化音频管理器（如果还未初始化）
        audioMgr.init();

        cc.debug.setDisplayStats(false);
        //获取URL拼接渠道参数
        this.getUrlParams();
        GameMgr.H5SDK.AddAPPEvent();

        this.getAppConfig();

        cc.game.on(cc.game.EVENT_SHOW, () => {
            GameMgr.Console.Log("EVENT_SHOW")
            GameMgr.GameData.GameIsInFront = true;
            // 触发重连
            WebSocketTool.GetInstance().atOnceReconnect();

        }, this);

        cc.game.on(cc.game.EVENT_HIDE, () => {
            GameMgr.Console.Log("EVENT_HIDE")
            GameMgr.GameData.GameIsInFront = false;
            // 断开WebSocket连接
            WebSocketTool.GetInstance().disconnect();

        }, this);

        //这里监听程序内消息
        GameMgr.Event.AddEventListener(EventType.AutoMessage, this.onAutoMessage, this);
        //这里监听长链接消息（异常）
        GameMgr.Event.AddEventListener(EventType.ReceiveErrorMessage, this.onErrorMessage, this);
        //这里监听长链接消息（正常）
        GameMgr.Event.AddEventListener(EventType.ReceiveMessage, this.onMessage, this);

        this.setCurrentPage(PageType.START_UP_PAGE)
        this.startUpPageController = this.startUpPage.getComponent(StartUpPageController)
        this.hallPageController = this.hallPage.getComponent(HallPageController)
        this.gamePageController = this.gamePage.getComponent(GamePageController)
        this.levelPageController = this.levelPage.getComponent(LevelPageController)

    }
    protected onEnable(): void {

    }

    protected onDestroy(): void {
        GameMgr.Event.RemoveEventListener(EventType.AutoMessage, this.onAutoMessage, this);
        GameMgr.Event.RemoveEventListener(EventType.ReceiveErrorMessage, this.onErrorMessage, this);
        GameMgr.Event.RemoveEventListener(EventType.ReceiveMessage, this.onMessage, this);
    }

    public getAppConfig(): void {
        GameMgr.H5SDK.GetConfig((config: any) => {
            MeshTools.Publish.appChannel = String(config.appChannel);
            MeshTools.Publish.appId = parseInt(config.appId);
            MeshTools.Publish.gameMode = String(config.gameMode);
            MeshTools.Publish.roomId = String(config.roomId) ?? "";
            MeshTools.Publish.currencyIcon = config?.gameConfig?.currencyIcon ?? "";

            MeshTools.Publish.code = encodeURIComponent(config.code);
            MeshTools.Publish.userId = String(config.userId);
            MeshTools.Publish.language = String(config.language);

            MeshTools.Publish.gsp = config.gsp == undefined ? 101 : parseInt(config.gsp);
            this.getHpptPath();

        });
    }

    getHpptPath() {
         this.setLanguage() //先设置语言
        if (!window.navigator.onLine) {
            this.showTips(window.getLocalizedStr('NetworkError'))
        } else {
            // // 获取游戏服务器地址
            // HttpManager.Instance.ReqServerUrl(() => {
            //     let httpUrl: string = GameServerUrl.Http;
            //     let wsUrl: string = GameServerUrl.Ws;
            //     if (httpUrl != "" || wsUrl != "") {
            //         WebSocketManager.GetInstance().connect();
            //     }
            // });

            GameServerUrl.Ws = "ws://************:2059/acceptor"
            WebSocketManager.GetInstance().connect();
        }   
    }

    public getUrlParams(): void {
        let params: any = GameMgr.Utils.GetUrlParams(window.location.href);//获取当前页面的 url
        if (JSON.stringify(params) != "{}") {
            //@ts-ignore
            if (params.appChannel) {
                //@ts-ignore
                MeshTools.Publish.appChannel = params.appChannel;
                if (params.isDataByUrl) {
                    if (params.isDataByUrl === "true") {
                        MeshTools.Publish.appId = parseInt(params.appId);
                        MeshTools.Publish.gameMode = params.gameMode;
                        MeshTools.Publish.userId = params.userId;
                        MeshTools.Publish.code = params.code;
                        if (params.language) {
                            MeshTools.Publish.language = params.language;
                        }
                        if (params.roomId) {
                            MeshTools.Publish.roomId = params.roomId;
                        }
                        if (params.gsp) {
                            MeshTools.Publish.gsp = parseInt(params.gsp);
                        }
                        MeshTools.Publish.isDataByURL = true;
                    }
                }
            }
        }
    }

    setLanguage() {

        switch (Publish.GetInstance().language) {
            case LanguageType.SimplifiedChinese: //简体中文
                window.languageName = LanguageType.SimplifiedChinese_type
                break;
            case LanguageType.TraditionalChinese: //繁体中文
                window.languageName = LanguageType.TraditionalChinese_type
                break;
            default: //默认是英语
                window.languageName = LanguageType.English_type
                break
        }

        window.refreshAllLocalizedComp()
    }

    showTips(content: string) {

        this.tipsDialogController.showDialog(content, () => {
            GameMgr.H5SDK.CloseWebView()

        })
    }


    //程序内的通知消息
    onAutoMessage(autoMessageBean: AutoMessageBean) {
        switch (autoMessageBean.msgId) {

            case AutoMessageId.JumpHallPage://跳转进大厅页面
                this.setCurrentPage(PageType.HALL_PAGE)
                if (autoMessageBean.data.type === 1) { //1是启动页面跳转的 ，2 是玩家主动离开游戏房间
                    this.hallPageController.LoginSuccess()//因为初始进来的时候是启动页面，大厅页面是隐藏状态，下面发送的消息收不到，所以需要主动调用一次
                } else if (autoMessageBean.data.type === 2) { //2 是玩家主动离开游戏房间，需要更新关卡进度
                    // 单机模式退出关卡后，请求ExtendLevelProgress更新关卡信息
                    WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeExtendLevelProgress, {});
                }
                break;
            case AutoMessageId.ReconnectionFailureMsg://长链接重连失败
                this.netError.active = false
                this.showTips(window.getLocalizedStr('NetworkError'))
                break;
            case AutoMessageId.LinkExceptionMsg://长链接异常
                this.netError.active = true
                break;
            case AutoMessageId.GameRouteNotFoundMsg://游戏线路异常的通知
                this.showTips(autoMessageBean.data.code)
                break;

            case AutoMessageId.SwitchGameSceneMsg://切换游戏场景
                this.setCurrentPage(PageType.GAME_PAGE)
                break;
            case AutoMessageId.WalletUpdateMsg://更新金豆余额的通知
                //发送获取更新用户信息的消息
                WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeUserInfo, {});
                break;
            case AutoMessageId.ServerCodeUpdateMsg://更新 code 的通知
                Publish.GetInstance().code = autoMessageBean.data.code
                break;

        }

    }

    //长链接消息(异常)
    onErrorMessage(messageBean: ReceivedMessageBean) {
        switch (messageBean.code) {
            case ErrorCode.ErrInvalidInviteCode://无效的邀请码
                this.hallPageController.joinError()
                break;
            case ErrorCode.ErrRequestUser://获取用户信息失败
                this.showTips(window.getLocalizedStr('GetUserInfoFailed'))
                // 断开WebSocket连接
                WebSocketTool.GetInstance().disconnect();

                break;
            case ErrorCode.ErrNotFoundRoom://没有找到指定的房间
                if (messageBean.msgId != MessageId.MsgTypeMoveBlock) {
                    this.toastController.showContent(window.getLocalizedStr('RoomDoesNotExist'))
                    //没有找到房间 就直接返回到大厅页面
                    this.setCurrentPage(PageType.HALL_PAGE)
                }

                break;
            case ErrorCode.ErrNotFoundUser:// 没有找到玩家信息
                if (messageBean.msgId === MessageId.MsgTypeEnterRoom) {//只有在这个messageId下 才会踢出到大厅
                    //没有找到玩家信息 就直接返回到大厅页面
                    this.setCurrentPage(PageType.HALL_PAGE)
                }
                break
            case ErrorCode.ErrEnoughUser://房间已满
                this.toastController.showContent(window.getLocalizedStr('RoomIsFull'))
                break;
            case ErrorCode.ErrChangeBalance://扣除金币失败
            case ErrorCode.ErrNotEnoughCoin://金币不足
                this.topUpDialogController.show( () => { })
                break;
            case ErrorCode.ErrPlaying://玩家已经在游戏中了
                //执行一遍 enterroom
                WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeEnterRoom, {})//重连进来的 玩家请求进入房间
                break

        }

    }

    //长链接消息(正常)
    onMessage(messageBean: ReceivedMessageBean) {
        switch (messageBean.msgId) {
            case MessageId.MsgTypeCreateWs://创建ws连接 成功  
                this.netError.active = false
                //登录
                WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeLogin, {});
                break;
            case MessageId.MsgTypeLogin://获取登录数据并存储
                GlobalBean.GetInstance().loginData = messageBean.data;
                if (this.currentPage === PageType.START_UP_PAGE) {
                    //判断当前是否是在启动页面
                    this.startUpPageController.setLogin()
                } else {
                    this.hallPageController.updateGold()
                    this.hallPageController.LoginSuccess()
                    //没有在游戏中，但是还停留在游戏页面
                    if (GlobalBean.GetInstance().loginData.roomId === 0 && this.currentPage === PageType.GAME_PAGE) {
                        this.setCurrentPage(PageType.HALL_PAGE) //返回到大厅
                    }
                }

                break;
            case MessageId.MsgTypePairRequest: //开始匹配
                this.hallPageController.setHallOrMatch(HallOrMatch.MATCH_PARENT)
                this.hallPageController.createMatchView();
                break
            case MessageId.MsgTypeCancelPair: //取消匹配
                this.hallPageController.setHallOrMatch(HallOrMatch.HALL_PARENT)
                break
            case MessageId.MsgTypeGameStart: //游戏开始
                let noticeStartGame: NoticeStartGame = messageBean.data
                GlobalBean.GetInstance().noticeStartGame = noticeStartGame //存储游戏数据

                const index = GlobalBean.GetInstance().noticeStartGame.users.findIndex((item) => item.userId === GlobalBean.GetInstance().loginData.userInfo.userId);//搜索
                //把游戏开始之后最新的金币余额进行赋值
                if (index != -1) {
                    GlobalBean.GetInstance().loginData.userInfo.coin = noticeStartGame.users[index].coin
                }

                // 处理游戏开始数据，获取炸弹数量和地图类型
                this.gamePageController.onGameStart(noticeStartGame);

                if (noticeStartGame.roomType === RoomType.RoomTypeCommon) {// 房间类型 1-普通场 2-私人场
                    this.hallPageController.setGameData()
                } else {
                    this.setCurrentPage(PageType.GAME_PAGE) //开始游戏进入游戏页面
                }

                break

            case MessageId.MsgTypeEnterRoom://重连的游戏数据
                let noticeStartGame2: NoticeStartGame = messageBean.data;

                // 检查isOnlineMode字段来判断游戏模式
                if (noticeStartGame2.isOnlineMode === true) {
                    // 联机模式断线重连，使用EnterRoom的数据恢复
                    GameMgr.Console.Log("联机模式断线重连，使用EnterRoom数据恢复");
                    GlobalBean.GetInstance().noticeStartGame = noticeStartGame2 //存储游戏数据

                    // 处理重连游戏数据，获取炸弹数量和地图类型
                    this.gamePageController.onGameStart(noticeStartGame2);

                    //跳转进游戏页面
                    this.setCurrentPage(PageType.GAME_PAGE)
                } else if (noticeStartGame2.isOnlineMode === false) {
                    // 关卡模式断线重连，需要主动请求ExtendLevelInfo
                    GameMgr.Console.Log("关卡模式断线重连，主动请求ExtendLevelInfo");

                    // 从EnterRoom响应中获取关卡ID（如果有的话）
                    let levelId = noticeStartGame2.roomId; // 假设roomId就是levelId
                    if (levelId && levelId > 0) {
                        // 切换到关卡页面
                        this.setCurrentPage(PageType.LEVEL_PAGE);

                        // 延迟发送ExtendLevelInfo请求，确保页面切换完成
                        this.scheduleOnce(() => {
                            WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeExtendLevelInfo, { levelId: levelId });
                        }, 0.1);
                    } else {
                        // 没有有效的关卡ID，返回大厅
                        GameMgr.Console.Log("关卡模式断线重连失败：没有有效的关卡ID");
                        this.setCurrentPage(PageType.HALL_PAGE);
                    }
                } else {
                    // 没有isOnlineMode字段，按照原来的逻辑处理（兼容旧版本）
                    GameMgr.Console.Log("未检测到isOnlineMode字段，按照原逻辑处理");
                    GlobalBean.GetInstance().noticeStartGame = noticeStartGame2 //存储游戏数据

                    // 处理重连游戏数据，获取炸弹数量和地图类型
                    this.gamePageController.onGameStart(noticeStartGame2);

                    //跳转进游戏页面
                    this.setCurrentPage(PageType.GAME_PAGE)
                }
                break;
            case MessageId.MsgTypeLeaveRoom:// 玩家主动离开房间
                let leaveRoomData: any = messageBean.data;

                // 检查是否是关卡游戏的LeaveRoom响应（包含levelId字段）
                if (leaveRoomData.levelId !== undefined) {
                    // 关卡游戏的LeaveRoom响应，直接返回大厅
                    cc.log("收到关卡游戏退出响应，返回大厅页面");
                    GlobalBean.GetInstance().cleanData(); //清空数据
                    let autoMessageBean: AutoMessageBean = {
                        'msgId': AutoMessageId.JumpHallPage,//进入大厅的消息
                        'data': { 'type': 2 } //2 是玩家主动离开游戏房间
                    }
                    GameMgr.Event.Send(EventType.AutoMessage, autoMessageBean);
                } else if (leaveRoomData.userId === GlobalBean.GetInstance().loginData.userInfo.userId) {
                    // 普通房间游戏的LeaveRoom响应
                    GlobalBean.GetInstance().cleanData() //清空数据
                    let autoMessageBean: AutoMessageBean = {
                        'msgId': AutoMessageId.JumpHallPage,//进入大厅的消息
                        'data': { 'type': 2 } //2 是玩家主动离开游戏房间
                    }
                    GameMgr.Event.Send(EventType.AutoMessage, autoMessageBean);
                }
                break;
            case MessageId.MsgTypeCreateInvite://创建邀请（也就是创建私人游戏房间）
                GlobalBean.GetInstance().inviteInfo = messageBean.data;
                //点击 create 的回调
                this.hallPageController.joinCreateRoom()
                break
            case MessageId.MsgTypeAcceptInvite://接受邀请
                let acceptInvite: AcceptInvite = messageBean.data
                GlobalBean.GetInstance().inviteInfo = acceptInvite.inviteInfo;
                this.hallPageController.setAcceptInvite(acceptInvite)
                break
            case MessageId.MsgTypeLeaveInvite://收到离开房间的信息
                let noticeLeaveInvite: NoticeLeaveInvite = messageBean.data;
                this.hallPageController.leaveRoom(noticeLeaveInvite)
                break
            case MessageId.MsgTypeInviteReady://收到有玩家准备的消息
                let noticeUserInviteStatus: NoticeUserInviteStatus = messageBean.data;
                this.hallPageController.setReadyState(noticeUserInviteStatus)
                break
            case MessageId.MsgTypeNoticeInviteStatus://广播邀请状态
                let noticeUserInviteStatus2: NoticeUserInviteStatus = messageBean.data;
                this.hallPageController.setReadyState(noticeUserInviteStatus2)
                break;
            case MessageId.MsgTypeInviteKickOut://收到玩家被踢出的信息
                let inviteKickOut: InviteKickOut = messageBean.data;

                if (inviteKickOut.userId === GlobalBean.GetInstance().loginData.userInfo.userId) {
                    this.toastController.showContent(window.getLocalizedStr('KickOut'))
                    //被踢的是自己的话 直接返回大厅
                    this.setCurrentPage(PageType.HALL_PAGE)
                } else {
                    //这里拼接一下数据 走离开房间流程，其实是踢出房间
                    let noticeLeaveInvite1 = { 'userId': inviteKickOut.userId, 'isCreator': false }
                    this.hallPageController.leaveRoom(noticeLeaveInvite1)
                }
                break;
            case MessageId.MsgTypeUserInfo://更新用户信息的消息
                let userInfo: UserInfo = messageBean.data;
                GlobalBean.GetInstance().loginData.userInfo = userInfo
                this.hallPageController.updateGold()
                break;


            case MessageId.MsgTypeSettlement: //大结算
                let noticeSettlement: NoticeSettlement = messageBean.data
                this.gamePageController.setCongratsDialog(noticeSettlement)
                break

            case MessageId.MsgTypeNoticeRoundStart: //扫雷回合开始通知
                // 只有在游戏页面时才处理此消息
                if (this.currentPage === PageType.GAME_PAGE) {
                    this.gamePageController.onNoticeRoundStart(messageBean.data);
                }
                break;

            case MessageId.MsgTypeNoticeActionDisplay: //扫雷操作展示通知
                // 只有在游戏页面时才处理此消息
                if (this.currentPage === PageType.GAME_PAGE) {
                    this.gamePageController.onNoticeActionDisplay(messageBean.data);
                }
                break;

            case MessageId.MsgTypeNoticeRoundEnd: //扫雷回合结束通知
                // 只有在游戏页面时才处理此消息
                if (this.currentPage === PageType.GAME_PAGE) {
                    this.gamePageController.onNoticeRoundEnd(messageBean.data);
                }
                break;

            case MessageId.MsgTypeNoticeFirstChoiceBonus: //扫雷首选玩家奖励通知
                // 只有在游戏页面时才处理此消息
                if (this.currentPage === PageType.GAME_PAGE) {
                    this.gamePageController.onNoticeFirstChoiceBonus(messageBean.data);
                }
                break;

            case MessageId.MsgTypeAIStatusChange: //AI托管状态变更通知
                // 只有在游戏页面时才处理此消息
                if (this.currentPage === PageType.GAME_PAGE) {
                    this.gamePageController.onAIStatusChange(messageBean.data);
                }
                break;

            case MessageId.MsgTypeExtendLevelProgress: //关卡进度
                if (this.currentPage === PageType.HALL_PAGE) {
                    // 从后端获取关卡进度并更新
                    var levelProgressData = messageBean.data;
                    if (levelProgressData) {
                        this.hallPageController.setLevelProgress(levelProgressData);
                    }
                }
                break;

            case MessageId.MsgTypeExtendLevelInfo: //关卡信息
                if (this.currentPage === PageType.LEVEL_PAGE) {
                    // 从后端获取关卡信息并更新
                    var levelInfoData: ExtendLevelInfoResponse = messageBean.data;
                    if (levelInfoData) {
                        this.levelPageController.onExtendLevelInfo(levelInfoData);
                    }
                }
                break;

            case MessageId.MsgTypeDebugShowMines: //调试显示地雷位置
                cc.log("=== 收到 DebugShowMines 消息 ===");
                cc.log("当前页面:", this.currentPage);
                cc.log("是否为关卡页面:", this.currentPage === PageType.LEVEL_PAGE);
                cc.log("消息数据:", messageBean.data);
                cc.log("levelPageController 是否存在:", !!this.levelPageController);

                if (this.currentPage === PageType.LEVEL_PAGE) {
                    // 从后端获取地雷位置数据并显示测试预制体
                    var responseData = messageBean.data;
                    cc.log("响应数据:", responseData);

                    // 服务器返回的数据格式是 {mines: Array}，需要提取 mines 字段
                    var minePositions = responseData.mines || responseData;
                    cc.log("提取的地雷位置数据:", minePositions);
                    cc.log("是否为数组:", Array.isArray(minePositions));

                    if (minePositions && Array.isArray(minePositions)) {
                        cc.log("调用 handleDebugShowMines 方法，地雷数量:", minePositions.length);
                        this.levelPageController.handleDebugShowMines(minePositions);
                    } else {
                        cc.warn("地雷位置数据格式不正确:", minePositions);
                    }
                } else {
                    cc.warn("当前不在关卡页面，无法处理 DebugShowMines 消息");
                }
                break;


        }
    }

    //设置展示页面的
    setCurrentPage(pageType: PageType) {
        this.currentPage = pageType
        this.startUpPage.active = false
        this.hallPage.active = false
        this.gamePage.active = false
        this.levelPage.active = false

        switch (pageType) {
            case PageType.START_UP_PAGE:
                this.startUpPage.active = true
                break
            case PageType.HALL_PAGE:
                this.hallPage.active = true
                break
            case PageType.GAME_PAGE:
                this.gamePage.active = true
                break
            case PageType.LEVEL_PAGE:
                this.levelPage.active = true
                break
        }

    }

    // update (dt) {}
}

if (!CC_EDITOR) {
    cc.Sprite.prototype["onLoad"] = function () {
        this.srcBlendFactor = cc.macro.BlendFactor.ONE;
        this.dstBlendFactor = cc.macro.BlendFactor.ONE_MINUS_SRC_ALPHA;

        // 延迟检查 spriteFrame，避免初始化时的警告
        this.scheduleOnce(() => {
            if (this.spriteFrame && this.spriteFrame.getTexture()) {
                this.spriteFrame.getTexture().setPremultiplyAlpha(true);
            }
            // 移除警告，因为很多 Sprite 组件在初始化时确实没有 SpriteFrame
            // 这是正常的，不需要警告
        }, 0.1);
    }

    cc.Label.prototype["onLoad"] = function () {
        this.srcBlendFactor = cc.macro.BlendFactor.ONE;
        this.dstBlendFactor = cc.macro.BlendFactor.ONE_MINUS_SRC_ALPHA;
    }
    cc.macro.ALLOW_IMAGE_BITMAP = false;// 禁用 Bitmap 图片格式
}