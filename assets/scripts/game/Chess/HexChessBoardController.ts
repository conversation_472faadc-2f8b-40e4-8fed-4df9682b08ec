// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { RoomUser, HexCoord, PlayerActionDisplay } from "../../bean/GameBean";
import { GlobalBean } from "../../bean/GlobalBean";
import PlayerGameController from "../../pfb/PlayerGameController ";
import { WebSocketManager } from "../../net/WebSocketManager";
import { MessageId } from "../../net/MessageId";

const {ccclass, property} = cc._decorator;

// 六边形格子数据接口
export interface HexGridData {
    q: number;  // 六边形坐标系q坐标
    r: number;  // 六边形坐标系r坐标
    worldPos: cc.Vec2;  // 格子在世界坐标系中的位置
    hasPlayer: boolean;  // 是否已经放置了玩家预制体
    playerNode?: cc.Node;  // 放置的玩家节点引用
}

@ccclass
export default class HexChessBoardController extends cc.Component {

    @property(cc.Prefab)
    playerGamePrefab: cc.Prefab = null;

    @property(cc.Prefab)
    boomPrefab: cc.Prefab = null;

    @property(cc.Prefab)
    biaojiPrefab: cc.Prefab = null;

    @property(cc.Prefab)
    boom1Prefab: cc.Prefab = null;

    @property(cc.Prefab)
    boom2Prefab: cc.Prefab = null;

    @property(cc.Prefab)
    boom3Prefab: cc.Prefab = null;

    @property(cc.Prefab)
    boom4Prefab: cc.Prefab = null;

    @property(cc.Prefab)
    boom5Prefab: cc.Prefab = null;

    @property(cc.Prefab)
    boom6Prefab: cc.Prefab = null;

    @property(cc.Prefab)
    boom7Prefab: cc.Prefab = null;

    @property(cc.Prefab)
    boom8Prefab: cc.Prefab = null;

    @property(cc.Node)
    boardNode: cc.Node = null;  // 棋盘节点

    // 六边形棋盘配置
    private readonly HEX_SIZE = 44;  // 六边形半径
    private readonly HEX_WIDTH = this.HEX_SIZE * 2;  // 六边形宽度
    private readonly HEX_HEIGHT = this.HEX_SIZE * Math.sqrt(3);  // 六边形高度

    // 格子数据存储 - 使用Map存储六边形坐标
    private hexGridData: Map<string, HexGridData> = new Map();  // 存储六边形格子数据
    private hexGridNodes: Map<string, cc.Node> = new Map();  // 存储六边形格子节点
    private validHexCoords: HexCoord[] = [];  // 有效的六边形坐标列表

    onLoad() {
    
    }

    start() {
        // 延迟一帧后再次尝试启用触摸事件，确保所有节点都已创建完成
        this.scheduleOnce(() => {

            this.setValidHexCoords([]);  // 传入空数组，但会被忽略

            // 测试预制体位置计算
            this.testHexPositionCalculation();

            this.enableTouchForExistingGrids();
        }, 0.1);
    }

    /**
     * 设置有效的六边形坐标列表（忽略服务器数据，使用前端节点坐标）
     * @param _coords 服务器发送的坐标列表（将被忽略）
     */
    public setValidHexCoords(_coords: HexCoord[]) {


        // 忽略服务器传入的坐标，始终从节点名称自动生成
        this.generateCoordsFromNodeNames();

        this.initHexBoard();
    }

    /**
     * 从节点名称自动生成有效坐标列表
     */
    private generateCoordsFromNodeNames() {
        if (!this.boardNode) {
            console.error("❌ 棋盘节点不存在，无法生成坐标列表");
            return;
        }

        const foundCoords: HexCoord[] = [];
        const children = this.boardNode.children;

       

        for (let i = 0; i < children.length; i++) {
            const child = children[i];
            const nodeName = child.name;

            // 跳过游戏元素节点，只处理六边形格子节点
            if (this.isGameElement(child, nodeName)) {
                continue;
            }

            const coords = this.parseHexCoordinateFromName(nodeName);

            if (coords) {
                // 检查是否已经存在相同的坐标
                const exists = foundCoords.some(c => c.q === coords.q && c.r === coords.r);
                if (!exists) {
                    foundCoords.push({ q: coords.q, r: coords.r });

                }
            }
        }

        this.validHexCoords = foundCoords;
       
    }

    // 初始化六边形棋盘
    private initHexBoard() {
        // 清空现有数据
        this.hexGridData.clear();
        this.hexGridNodes.clear();

        // 初始化有效坐标的数据
        for (const coord of this.validHexCoords) {
            const key = this.getHexKey(coord.q, coord.r);
            this.hexGridData.set(key, {
                q: coord.q,
                r: coord.r,
                worldPos: this.getHexWorldPosition(coord.q, coord.r),
                hasPlayer: false
            });
        }

        this.createHexGridNodes();
    }

    // 生成六边形坐标的唯一键
    private getHexKey(q: number, r: number): string {
        return `${q},${r}`;
    }

    // 启用现有格子的触摸事件
    private createHexGridNodes() {
        if (!this.boardNode) {
            console.error("棋盘节点未设置！");
            return;
        }

        // 如果格子已经存在，直接启用触摸事件
        this.enableTouchForExistingGrids();
    }

    // 为现有格子启用触摸事件
    private enableTouchForExistingGrids() {
        // 检查棋盘节点是否存在
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法启用触摸事件！");
            return;
        }

        // 遍历棋盘节点的所有子节点
        let children = this.boardNode.children;

        for (let i = 0; i < children.length; i++) {
            let child = children[i];
            const nodeName = child.name;

            // 跳过游戏元素节点，只处理六边形格子节点
            if (this.isGameElement(child, nodeName)) {
                continue;
            }

            // 尝试从节点名称解析六边形坐标
            let coords = this.parseHexCoordinateFromName(nodeName);
            if (coords) {
                this.setupHexGridTouchEvents(child, coords.q, coords.r);
                const key = this.getHexKey(coords.q, coords.r);
                this.hexGridNodes.set(key, child);
            } else {
                // 如果无法从名称解析，尝试从位置计算
                let pos = child.getPosition();
                let coords = this.getHexCoordinateFromPosition(pos);
                if (coords) {
                    this.setupHexGridTouchEvents(child, coords.q, coords.r);
                    const key = this.getHexKey(coords.q, coords.r);
                    this.hexGridNodes.set(key, child);
                }
            }
        }
    }

    // 从节点名称解析六边形坐标
    private parseHexCoordinateFromName(nodeName: string): {q: number, r: number} | null {
       

        
        const patterns = [
            /^sixblock_(-?\d+)_(-?\d+)$/,   // sixblock_q_r 格式（您使用的格式）
        ];

        for (const pattern of patterns) {
            const match = nodeName.match(pattern);
            if (match) {
                const coords = {q: parseInt(match[1]), r: parseInt(match[2])};
              
                return coords;
            }
        }

        console.warn(`❌ 无法解析节点名称: ${nodeName}`);
        return null;
    }

    // 从位置计算六边形坐标（近似）
    private getHexCoordinateFromPosition(pos: cc.Vec2): {q: number, r: number} | null {
        // 六边形坐标转换（从像素坐标到六边形坐标）
        const x = pos.x;
        const y = pos.y;
        
        // 使用六边形坐标转换公式
        const q = Math.round((Math.sqrt(3)/3 * x - 1/3 * y) / this.HEX_SIZE);
        const r = Math.round((2/3 * y) / this.HEX_SIZE);

        // 检查是否为有效坐标
        if (this.isValidHexCoordinate(q, r)) {
            return {q: q, r: r};
        }
        return null;
    }

    // 计算六边形预制体的生成位置（直接使用您提供的格子中心坐标）
    private getHexWorldPosition(q: number, r: number, isPlayerAvatar: boolean = false): cc.Vec2 {
        // 您提供的精确格子中心坐标
        const exactCoords = new Map<string, cc.Vec2>();
        // 更新后的基准点坐标（与rowData保持一致）
        exactCoords.set("0,0", cc.v2(-300, -258));   // r=0行基准点
        exactCoords.set("1,-1", cc.v2(-258, -184));  // r=-1行基准点
        exactCoords.set("1,-2", cc.v2(-300, -108));  // r=-2行基准点
        exactCoords.set("2,-3", cc.v2(-258, -36));   // r=-3行基准点
        exactCoords.set("2,-4", cc.v2(-300, 37));    // r=-4行基准点
        exactCoords.set("3,-5", cc.v2(-258, 110));   // r=-5行基准点
        exactCoords.set("3,-6", cc.v2(-300, 185));   // r=-6行基准点
        exactCoords.set("4,-7", cc.v2(-258, 260));   // r=-7行基准点

        // 首先检查是否有精确坐标
        const key = `${q},${r}`;
        if (exactCoords.has(key)) {
            const pos = exactCoords.get(key);
            // 如果是单人头像预制体，往左上偏移一点点
            if (isPlayerAvatar) {
                return cc.v2(pos.x , pos.y - 12); // 改为往左偏移10像素
            }
            return pos;
        }

        // 对于其他坐标，使用基于您提供的精确坐标数据进行计算

        // 定义每一行的数据：使用统一步长86，保证美观整齐
        const UNIFORM_STEP_X = 86; // 统一的x方向步长
        const rowData = new Map<number, {baseQ: number, baseX: number, y: number}>();

        // 基于您提供的更新数据，使用统一步长86
        rowData.set(0, { baseQ: 0, baseX: -300, y: -258 });   // r=0行：基准点(0,0) → (-300, -258)
        rowData.set(-1, { baseQ: 1, baseX: -258, y: -184 });  // r=-1行：基准点(1,-1) → (-258, -184)
        rowData.set(-2, { baseQ: 1, baseX: -300, y: -108 });  // r=-2行：基准点(1,-2) → (-300, -108)
        rowData.set(-3, { baseQ: 2, baseX: -258, y: -36 });   // r=-3行：基准点(2,-3) → (-258, -36)
        rowData.set(-4, { baseQ: 2, baseX: -300, y: 37 });    // r=-4行：基准点(2,-4) → (-300, 37)
        rowData.set(-5, { baseQ: 3, baseX: -258, y: 110 });   // r=-5行：基准点(3,-5) → (-258, 110)
        rowData.set(-6, { baseQ: 3, baseX: -300, y: 185 });   // r=-6行：基准点(3,-6) → (-300, 185)
        rowData.set(-7, { baseQ: 4, baseX: -258, y: 260 });   // r=-7行：基准点(4,-7) → (-258, 260)

        // 计算基础位置
        let x: number, y: number;

        // 如果有该行的数据，使用统一步长计算
        if (rowData.has(r)) {
            const data = rowData.get(r);
            x = data.baseX + (q - data.baseQ) * UNIFORM_STEP_X;
            y = data.y;
        } else {
            // 对于其他行，使用通用的六边形轴线坐标系公式（也使用统一步长）
            const baseX = -300;  // 更新为新的基准点
            const baseY = -258;
            const stepXR = -43;
            const stepYR = 74;

            x = baseX + q * UNIFORM_STEP_X + r * stepXR;
            y = baseY - r * stepYR;
        }

        // 如果是单人头像预制体，往左上偏移一点点
        if (isPlayerAvatar) {
            y -= 12; // 往下偏移12像素（相比之前的-20，现在是-12，相当于往上调了8像素）
        }

        return cc.v2(x, y);
    }

    // 为六边形格子节点设置触摸事件
    private setupHexGridTouchEvents(gridNode: cc.Node, q: number, r: number) {
        // 安全检查：确保坐标有效
        if (!this.isValidHexCoordinate(q, r)) {
            console.error(`❌ setupHexGridTouchEvents: 尝试为无效坐标(${q},${r})设置触摸事件`);
            return;
        }

        // 长按相关变量
        let isLongPressing = false;
        let longPressTimer = 0;
        let longPressCallback: Function = null;
        const LONG_PRESS_TIME = 1.0; // 1秒长按时间

        // 触摸开始事件
        gridNode.on(cc.Node.EventType.TOUCH_START, (_event: cc.Event.EventTouch) => {
            isLongPressing = true;
            longPressTimer = 0;

            // 开始长按检测
            longPressCallback = () => {
                if (isLongPressing) {
                    longPressTimer += 0.1;
                    if (longPressTimer >= LONG_PRESS_TIME) {
                        this.onHexGridLongPress(q, r);
                        isLongPressing = false;
                        if (longPressCallback) {
                            this.unschedule(longPressCallback);
                        }
                    }
                }
            };
            this.schedule(longPressCallback, 0.1);
        }, this);

        // 触摸结束事件
        gridNode.on(cc.Node.EventType.TOUCH_END, (event: cc.Event.EventTouch) => {
            // 如果不是长按，则执行点击事件
            if (isLongPressing && longPressTimer < LONG_PRESS_TIME) {
                this.onHexGridClick(q, r, event);
            }

            isLongPressing = false;
            if (longPressCallback) {
                this.unschedule(longPressCallback);
            }
        }, this);

        // 触摸取消事件
        gridNode.on(cc.Node.EventType.TOUCH_CANCEL, (_event: cc.Event.EventTouch) => {
            isLongPressing = false;
            if (longPressCallback) {
                this.unschedule(longPressCallback);
            }
        }, this);

        // 添加Button组件以确保触摸响应
        let button = gridNode.getComponent(cc.Button);
        if (!button) {
            button = gridNode.addComponent(cc.Button);
            button.transition = cc.Button.Transition.SCALE;
            button.zoomScale = 0.95;
        }
    }

    // 六边形格子点击事件 - 发送挖掘操作
    private onHexGridClick(q: number, r: number, _event?: cc.Event.EventTouch) {
       

        // 检查坐标是否有效
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn(`❌ 无效的六边形坐标: (${q}, ${r})`);
            return;
        }

        const key = this.getHexKey(q, r);
        const gridData = this.hexGridData.get(key);

        // 检查该位置是否已经有玩家预制体
        if (gridData && gridData.hasPlayer) {
            console.warn(`⚠️ 格子(${q}, ${r})已有玩家`);
            return;
        }

       

        // 发送挖掘操作事件 (action = 1)
        this.node.emit('hex-chess-board-click', {
            q: q,
            r: r,
            action: 1  // 1 = 挖掘
        });
    }

    // 六边形格子长按事件 - 发送标记操作
    private onHexGridLongPress(q: number, r: number) {
    

        // 检查坐标是否有效
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn(`❌ 无效的六边形坐标: (${q}, ${r})`);
            return;
        }

        const key = this.getHexKey(q, r);
        const gridData = this.hexGridData.get(key);

        // 检查该位置是否已经有玩家预制体
        if (gridData && gridData.hasPlayer) {
            console.warn(`⚠️ 格子(${q}, ${r})已有玩家`);
            return;
        }

       

        // 发送标记操作事件 (action = 2)
        this.node.emit('hex-chess-board-click', {
            q: q,
            r: r,
            action: 2  // 2 = 标记
        });
    }

    // 检查六边形坐标是否有效
    public isValidHexCoordinate(q: number, r: number): boolean {
        const key = this.getHexKey(q, r);
        return this.hexGridData.has(key);
    }

    // 在六边形格子上放置玩家预制体
    public placePlayerOnHexGrid(q: number, r: number, withFlag: boolean = false) {
        // 双重检查：确保坐标有效
        if (!this.isValidHexCoordinate(q, r)) {
            console.error(`❌ placePlayerOnHexGrid: 无效坐标(${q},${r})`);
            return;
        }

        const key = this.getHexKey(q, r);
        const gridData = this.hexGridData.get(key);
        
        // 双重检查：确保格子为空
        if (!gridData || gridData.hasPlayer) {
            console.error(`❌ placePlayerOnHexGrid: 格子(${q},${r})已有玩家，不能重复放置`);
            return;
        }

        if (!this.playerGamePrefab) {
            console.error("❌ 玩家预制体未设置！");
            return;
        }

        if (!this.boardNode) {
            console.error("❌ 棋盘节点未设置！");
            return;
        }

        // 实例化玩家预制体
        let playerNode = cc.instantiate(this.playerGamePrefab);

        // 计算正确的位置（单人头像预制体，y轴+20）
        let correctPosition = this.getHexWorldPosition(q, r, true);
        playerNode.setPosition(correctPosition);

        // 设置单人放置的缩放为0.8
        playerNode.setScale(0.8);

        // 先隐藏节点，等头像加载完成后再显示
        playerNode.active = false;

        // 处理Layout限制问题
        this.addPlayerNodeSafely(playerNode);

        // 设置头像和用户数据（异步加载）
        this.setupPlayerAvatarAsync(playerNode, q, r, withFlag, () => {
            // 头像加载完成的回调，播放生成动画
            this.playAvatarSpawnAnimation(playerNode);
        });

        // 更新格子数据
        gridData.hasPlayer = true;
        gridData.playerNode = playerNode;
    }

    // 安全地添加玩家节点（处理Layout限制）
    private addPlayerNodeSafely(playerNode: cc.Node) {
        // 检查棋盘节点是否存在
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法添加玩家节点！");
            return;
        }

        // 检查棋盘节点是否有Layout组件
        let layout = this.boardNode.getComponent(cc.Layout);
        if (layout) {
            // 临时禁用Layout
            layout.enabled = false;
            // 添加节点
            this.boardNode.addChild(playerNode);
        } else {
            this.boardNode.addChild(playerNode);
        }
    }

    // 异步设置玩家头像（带回调）
    private setupPlayerAvatarAsync(playerNode: cc.Node, q: number, r: number, withFlag: boolean, onComplete: () => void) {
        // 查找PlayerGameController组件（使用类引用）
        let playerController = playerNode.getComponent(PlayerGameController);

        if (playerController) {
            // 检查avatar节点是否存在
            if (playerController.avatar) {
                // 检查avatar节点是否有Sprite组件
                let avatarSprite = playerController.avatar.getComponent(cc.Sprite);
                if (!avatarSprite) {
                    avatarSprite = playerController.avatar.addComponent(cc.Sprite);
                }

                // 确保avatar节点可见
                playerController.avatar.active = true;
                playerController.avatar.opacity = 255;
            } else {
                console.error("❌ PlayerGameController中的avatar节点为null");
                onComplete();
                return;
            }

            // 设置旗子节点的显示状态
            if (playerController.flagNode) {
                playerController.flagNode.active = withFlag;

                // 额外检查旗子节点的可见性
                if (withFlag) {
                    playerController.flagNode.opacity = 255;

                    // 确保旗子节点的父节点也是可见的
                    let parent = playerController.flagNode.parent;
                    while (parent && parent !== playerNode) {
                        parent.active = true;
                        parent = parent.parent;
                    }
                }
            } else {
                console.warn(`⚠️ 找不到旗子节点 (${q},${r})`);
            }

            // 获取当前用户ID
            const currentUserId = GlobalBean.GetInstance().loginData?.userInfo?.userId || `hex_player_${q}_${r}`;

            // 在节点上存储userId，用于后续查找
            playerNode['userId'] = currentUserId;

            // 创建用户数据并设置头像
            let userData = {
                userId: currentUserId,
                nickName: `玩家(${q},${r})`,
                avatar: this.getDefaultAvatarUrl(),
                score: 0,
                pos: 0,
                coin: 0,
                status: 0,
                rank: 0
            } as RoomUser;

            // 使用PlayerGameController的setData方法来设置头像
            try {
                playerController.setData(userData);

                // 延迟设置旗子状态，确保在PlayerGameController初始化之后
                this.scheduleOnce(() => {
                    if (playerController.flagNode) {
                        playerController.flagNode.active = withFlag;
                    }
                    onComplete();
                }, 0.1);
            } catch (error) {
                console.error("设置头像数据失败:", error);
                onComplete();
            }

        } else {
            console.warn("⚠️ 找不到PlayerGameController组件");
            onComplete();
        }
    }

    // 获取默认头像URL
    private getDefaultAvatarUrl(): string {
        // 使用真实的头像URL
        return "https://static.gooplay.com/online/user-avatar/1732786296530322669.jpg";
    }

    /**
     * 播放头像生成动画（由大变小，完全复制四边形棋盘控制器的逻辑）
     * @param playerNode 玩家节点
     */
    private playAvatarSpawnAnimation(playerNode: cc.Node) {
        if (!playerNode) {
            console.warn("播放生成动画失败：节点为空");
            return;
        }

        // 显示节点
        playerNode.active = true;

        // 设置初始缩放为1.5倍（比正常大）
        const originalScale = playerNode.scaleX;
        const startScale = originalScale * 1.5;
        playerNode.setScale(startScale);

        // 使用cc.Tween创建由大变小的缩放动画
        cc.tween(playerNode)
            .to(0.3, { scaleX: originalScale, scaleY: originalScale }, { easing: 'backOut' })
            .start();
    }

    // 清除指定六边形格子的玩家
    public clearHexGridPlayer(q: number, r: number): boolean {
        const key = this.getHexKey(q, r);
        const gridData = this.hexGridData.get(key);

        if (!gridData || !gridData.hasPlayer) {
            return false;
        }

        // 移除玩家节点
        if (gridData.playerNode) {
            gridData.playerNode.removeFromParent();
            gridData.playerNode = null;
        }

        // 更新数据
        gridData.hasPlayer = false;

        return true;
    }

    // 清除所有玩家
    public clearAllPlayers() {
        let clearedCount = 0;

        // 1. 清理存储在hexGridData中的玩家节点（自己的头像）
        this.hexGridData.forEach((gridData) => {
            if (gridData.hasPlayer && gridData.playerNode) {
                gridData.playerNode.removeFromParent();
                gridData.playerNode = null;
                gridData.hasPlayer = false;
                clearedCount++;
            }
        });

        // 2. 清理棋盘上的其他玩家头像节点
        if (this.boardNode) {
            const children = this.boardNode.children.slice(); // 创建副本避免遍历时修改数组
            for (let i = 0; i < children.length; i++) {
                const child = children[i];
                if (child.name === "player_game_pfb") {
                    // 检查是否有PlayerGameController组件
                    const playerController = child.getComponent(PlayerGameController);
                    if (playerController) {
                        child.removeFromParent();
                        clearedCount++;
                    }
                }
            }
        }

    }

    /**
     * 清理所有玩家预制体（新回合开始时调用）
     * 包括自己的头像和其他玩家的头像
     * 为了与四边形棋盘控制器保持一致的接口
     */
    public clearAllPlayerNodes() {
        this.clearAllPlayers();
    }

    // 获取所有已放置玩家的六边形坐标
    public getAllPlayerHexCoordinates(): {q: number, r: number}[] {
        let coordinates: {q: number, r: number}[] = [];

        this.hexGridData.forEach((gridData) => {
            if (gridData.hasPlayer) {
                coordinates.push({q: gridData.q, r: gridData.r});
            }
        });

        return coordinates;
    }

    // 检查六边形格子是否为空
    public isHexGridEmpty(q: number, r: number): boolean {
        if (!this.isValidHexCoordinate(q, r)) {
            return false;
        }

        const key = this.getHexKey(q, r);
        const gridData = this.hexGridData.get(key);
        return gridData ? !gridData.hasPlayer : false;
    }

    /**
     * 重置游戏场景（游戏开始时调用）
     * 清除数字、炸弹、标记预制体，重新显示所有小格子
     */
    public resetGameScene() {
        if (!this.boardNode) {
            console.error("❌ 棋盘节点不存在，无法重置");
            return;
        }

        // 清除所有游戏元素（数字、炸弹、标记等）
        this.clearAllGameElements();

        // 显示所有小格子
        this.showAllHexGrids();

        // 重新初始化棋盘数据
        this.reinitializeHexBoardData();
    }

    /**
     * 清除所有游戏元素（数字、炸弹、标记、玩家头像等），但保留小格子
     */
    private clearAllGameElements() {
        if (!this.boardNode) {
            console.error("❌ 棋盘节点不存在，无法清除游戏元素");
            return;
        }

        const childrenToRemove: cc.Node[] = [];

        

        // 遍历棋盘的所有子节点
        for (let i = 0; i < this.boardNode.children.length; i++) {
            const child = this.boardNode.children[i];
            const nodeName = child.name;

            // 检查是否是需要清除的游戏元素（不包括小格子）
            if (this.isGameElement(child, nodeName)) {
                childrenToRemove.push(child);
          
            }
        }

        // 移除找到的游戏元素
        childrenToRemove.forEach((child) => {
            child.removeFromParent();
        });

       
    }

    /**
     * 判断节点是否是游戏元素（需要清除的），小格子不会被清除
     */
    private isGameElement(node: cc.Node, nodeName: string): boolean {
        // 绝对不清除的节点（六边形小格子）
        if (nodeName.startsWith("sixblock_") || nodeName === "hexblock") {
            return false;
        }

        // 分数控制器不清除
        if (nodeName.includes("Score") || nodeName.includes("score")) {
            return false;
        }

        // UI相关节点不清除
        if (nodeName.includes("UI") || nodeName.includes("ui")) {
            return false;
        }

        // 明确需要清除的游戏预制体
        // 炸弹预制体
        if (nodeName === "Boom") {
            return true;
        }

        // 数字预制体（Boom1, Boom2, Boom3 等，以及 HexBoom1, HexBoom2 等）
        if (nodeName.match(/^(Hex)?Boom\d+$/)) {
            return true;
        }

        // 临时数字节点（NeighborMines_1, NeighborMines_2 等）
        if (nodeName.match(/^NeighborMines_\d+$/)) {
            return true;
        }

        // 测试节点（Test_q_r 格式）
        if (nodeName.match(/^Test_-?\d+_-?\d+$/)) {
            return true;
        }

        // 玩家预制体（通过组件判断）
        if (node.getComponent(PlayerGameController)) {
            return true;
        }

        // 标记预制体
        if (nodeName.includes("Flag") || nodeName.includes("Mark") || nodeName.includes("flag") ||
            nodeName === "Biaoji" || nodeName.includes("Biaoji")) {
            return true;
        }

        // 玩家头像预制体
        if (nodeName.includes("Player") || nodeName.includes("Avatar")) {
            return true;
        }

        // 默认保留未知节点（保守策略）
        return false;
    }

    /**
     * 显示所有六边形小格子（第二把游戏开始时恢复被隐藏的小格子）
     */
    private showAllHexGrids() {
        if (!this.boardNode) {
            console.error("❌ 棋盘节点不存在，无法显示六边形格子");
            return;
        }

        let totalGrids = 0;
        let restoredGrids = 0;
        let alreadyVisibleGrids = 0;

       

        // 遍历棋盘的所有子节点，找到小格子并显示
        for (let i = 0; i < this.boardNode.children.length; i++) {
            const child = this.boardNode.children[i];

            // 如果是六边形小格子节点（sixblock_q_r 格式）
            if (child.name.startsWith("sixblock_") || child.name === "hexblock") {
                totalGrids++;

                // 记录恢复前的状态
                const wasHidden = !child.active || child.opacity < 255 || child.scaleX < 1 || child.scaleY < 1;

                if (wasHidden) {
                    restoredGrids++;
                } else {
                    alreadyVisibleGrids++;
                }

                // 停止所有可能正在进行的动画
                child.stopAllActions();

                // 恢复显示状态
                child.active = true;
                child.opacity = 255;
                child.scaleX = 1;
                child.scaleY = 1;
                child.angle = 0; // 重置旋转角度

                // 恢复原始位置（如果有保存的话）
                if (child['originalPosition']) {
                    child.setPosition(child['originalPosition']);
                }

                // 确保格子可以交互
                const button = child.getComponent(cc.Button);
                if (button) {
                    button.enabled = true;
                }
            }
        }

       

        if (totalGrids === 0) {
            console.warn("⚠️ 没有找到任何六边形格子节点！请检查节点命名是否正确");
            // 列出所有子节点名称以便调试
           
            
        }
    }

    /**
     * 隐藏指定位置的六边形小格子（点击时调用）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param immediate 是否立即隐藏（不播放动画）
     */
    public hideHexGridAt(q: number, r: number, immediate: boolean = false) {
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn(`隐藏格子失败：坐标(${q}, ${r})无效`);
            return;
        }

        // 获取格子节点
        const key = this.getHexKey(q, r);
        const gridNode = this.hexGridNodes.get(key);
        if (gridNode) {
            if (immediate) {
                // 立即隐藏，不播放动画
                gridNode.active = false;
            } else {
                // 播放六边形格子消失动画
                this.playHexGridFallAnimation(gridNode);
            }
        }
    }

    /**
     * 播放六边形格子消失动画
     * 效果：格子持续旋转，给一个随机向上的力，然后旋转着自由落体
     * @param gridNode 格子节点
     */
    private playHexGridFallAnimation(gridNode: cc.Node) {
        if (!gridNode) return;

        // 停止该格子上所有正在进行的动画（包括震动动画）
        gridNode.stopAllActions();

        // 保存格子的原始位置（用于重置时恢复）
        if (!gridNode['originalPosition']) {
            gridNode['originalPosition'] = gridNode.getPosition();
        }

        // 随机选择向上的力的方向：0=向上，1=右上15度，2=左上15度
        const forceDirection = Math.floor(Math.random() * 3);
        let moveX = 0;
        let moveY = 200; // 向上的基础距离（增加高度）

        switch (forceDirection) {
            case 0: // 向上
                moveX = 0;
                break;
            case 1: // 右上20度
                moveX = Math.sin(20 * Math.PI / 180) * moveY;
                break;
            case 2: // 左上20度
                moveX = -Math.sin(20 * Math.PI / 180) * moveY;
                break;
        }

        // 随机旋转速度
        const rotationSpeed = (Math.random() * 1440 + 720) * (Math.random() > 0.5 ? 1 : -1); // 720-2160度/秒，随机方向

        // 动画参数
        const upTime = 0.15; // 向上运动时间
        const fallTime = 0.3; // 下落时间
        const initialPosition = gridNode.getPosition();

        // 创建持续旋转的动画
        const rotationTween = cc.tween(gridNode)
            .repeatForever(
                cc.tween().by(0.1, { angle: rotationSpeed * 0.1 })
            );

        // 创建分阶段的运动动画
        const movementTween = cc.tween(gridNode)
            // 第一阶段：向上抛出
            .to(upTime, {
                x: initialPosition.x + moveX,
                y: initialPosition.y + moveY
            }, { easing: 'quadOut' })
            // 第二阶段：自由落体
            .to(fallTime, {
                x: initialPosition.x + moveX + (Math.random() - 0.5) * 100, // 添加更多随机水平偏移
                y: initialPosition.y - 500 // 下落到屏幕下方更远处
            }, { easing: 'quadIn' })
            .call(() => {
                // 动画结束后隐藏格子
                gridNode.active = false;
                // 停止旋转动画
                gridNode.stopAllActions();
            });

        // 同时开始旋转和移动动画
        rotationTween.start();
        movementTween.start();
    }

    /**
     * 重新初始化六边形棋盘数据
     */
    private reinitializeHexBoardData() {
        // 重置hexGridData中的玩家状态
        this.hexGridData.forEach((gridData) => {
            gridData.hasPlayer = false;
            gridData.playerNode = null;
        });
    }

    /**
     * 获取六边形格子数据
     */
    public getHexGridData(q: number, r: number): HexGridData | null {
        const key = this.getHexKey(q, r);
        return this.hexGridData.get(key) || null;
    }

    /**
     * 批量放置玩家（用于从服务器同步数据）
     */
    public batchPlaceHexPlayers(coordinates: {q: number, r: number}[]) {
        coordinates.forEach(coord => {
            if (this.isValidHexCoordinate(coord.q, coord.r) && this.isHexGridEmpty(coord.q, coord.r)) {
                this.placePlayerOnHexGrid(coord.q, coord.r);
            }
        });
    }

    /**
     * 测试点击功能（调试用）
     */
    public testHexClick(q: number, r: number) {
        this.onHexGridClick(q, r);
    }

    /**
     * 获取棋盘状态信息（调试用）
     */
    public getHexBoardInfo() {
        let info = {
            validHexCoordsCount: this.validHexCoords.length,
            boardNodeChildren: this.boardNode ? this.boardNode.children.length : 0,
            playerCount: this.getAllPlayerHexCoordinates().length,
            hasPlayerGamePrefab: !!this.playerGamePrefab,
            hasBoardNode: !!this.boardNode,
            hexGridDataSize: this.hexGridData.size,
            hexGridNodesSize: this.hexGridNodes.size
        };

        return info;
    }

    /**
     * 获取前端节点的总数量（用于计算炸弹数量）
     */
    public getHexGridCount(): number {
        return this.validHexCoords.length;
    }

    /**
     * 根据前端节点数量计算推荐的炸弹数量
     */
    public getRecommendedMineCount(): number {
        const gridCount = this.getHexGridCount();
        if (gridCount === 0) {
            return 13; // 默认值
        }

        // 约15%的格子是炸弹
        const mineCount = Math.floor(gridCount * 0.15);

        return Math.max(mineCount, 5); // 至少5个炸弹
    }

    /**
     * 测试六边形预制体位置计算是否正确
     */
    public testHexPositionCalculation() {
       

        // 测试更新后的基准点坐标
        const testPoints = [
            { q: 0, r: 0, expected: { x: -300, y: -258 }, desc: "r=0行基准点(0,0)" },
            { q: 1, r: -1, expected: { x: -258, y: -184 }, desc: "r=-1行基准点(1,-1)" },
            { q: 1, r: -2, expected: { x: -300, y: -108 }, desc: "r=-2行基准点(1,-2)" },
            { q: 2, r: -3, expected: { x: -258, y: -36 }, desc: "r=-3行基准点(2,-3)" },
            { q: 2, r: -4, expected: { x: -300, y: 37 }, desc: "r=-4行基准点(2,-4)" },
            { q: 3, r: -5, expected: { x: -258, y: 110 }, desc: "r=-5行基准点(3,-5)" },
            { q: 3, r: -6, expected: { x: -300, y: 185 }, desc: "r=-6行基准点(3,-6)" },
            { q: 4, r: -7, expected: { x: -258, y: 260 }, desc: "r=-7行基准点(4,-7)" }
        ];

        let correctCount = 0;

        testPoints.forEach(point => {
            const calculated = this.getHexWorldPosition(point.q, point.r);
            const errorX = Math.abs(calculated.x - point.expected.x);
            const errorY = Math.abs(calculated.y - point.expected.y);
            const isCorrect = errorX < 2 && errorY < 2; // 允许2像素误差

            if (isCorrect) correctCount++;

            
        });

       
        // 测试一些中间坐标

        const intermediatePoints = [
            // r=0行测试
            { q: 2, r: 0 }, { q: 3, r: 0 }, { q: 4, r: 0 }, { q: 5, r: 0 }, { q: 6, r: 0 },
            // r=-1行测试
            { q: 3, r: -1 }, { q: 4, r: -1 }, { q: 5, r: -1 }, { q: 6, r: -1 },
            // r=-2行测试
            { q: 3, r: -2 }, { q: 4, r: -2 }, { q: 5, r: -2 }, { q: 6, r: -2 }, { q: 7, r: -2 },
            // r=-3行测试
            { q: 4, r: -3 }, { q: 5, r: -3 }, { q: 6, r: -3 }, { q: 7, r: -3 }
        ];

        intermediatePoints.forEach(point => {
            const calculated = this.getHexWorldPosition(point.q, point.r);
           
        });

        // 暴露到全局以便调试
        (window as any).testHexPositions = () => this.testHexPositionCalculation();
    }

    // ==================== NoticeActionDisplay 相关方法 ====================
    // 以下方法与第一张地图（方形地图）的逻辑完全一样，用于处理加分和掀开地图

    /**
     * 在指定六边形位置创建boom预制体
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param isCurrentUser 是否是当前用户点到的雷
     */
    public createHexBoomPrefab(q: number, r: number, isCurrentUser: boolean = true) {
        if (!this.boomPrefab) {
            console.error("boomPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }

        // 实例化boom预制体
        const boomNode = cc.instantiate(this.boomPrefab);
        boomNode.name = "HexBoom";

        // 设置位置（使用六边形坐标计算，不是单人头像所以不偏移）
        const position = this.getHexWorldPosition(q, r, false);
        boomNode.setPosition(position);

        // 添加到棋盘
        this.boardNode.addChild(boomNode);

        // 播放出现动画
        boomNode.setScale(0);
        cc.tween(boomNode)
            .to(0.3, { scaleX: 1.2, scaleY: 1.2 }, { easing: 'backOut' })
            .to(0.1, { scaleX: 1.0, scaleY: 1.0 })
            .start();

        // 只有当前用户点到雷时才播放棋盘震动效果
        // 延迟0.45秒，等格子下落动画完成后再播放震动
        if (isCurrentUser) {
            this.scheduleOnce(() => {
                this.playBoardShakeAnimation();
            }, 0.45);
        }
    }

    /**
     * 在指定六边形位置创建biaoji预制体
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     */
    public createHexBiaojiPrefab(q: number, r: number) {
        if (!this.biaojiPrefab) {
            console.error("biaojiPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }

        // 实例化biaoji预制体
        const biaojiNode = cc.instantiate(this.biaojiPrefab);
        biaojiNode.name = "HexBiaoji";

        // 设置位置（使用六边形坐标计算，不是单人头像所以不偏移）
        const position = this.getHexWorldPosition(q, r, false);
        biaojiNode.setPosition(position);

        // 添加到棋盘
        this.boardNode.addChild(biaojiNode);

        // 播放出现动画
        biaojiNode.setScale(0);
        cc.tween(biaojiNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    }

    /**
     * 更新指定六边形位置的neighborMines显示
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param neighborMines 周围地雷数量
     */
    public updateHexNeighborMinesDisplay(q: number, r: number, neighborMines: number) {
        // 0不需要显示数字
        if (neighborMines === 0) {
            return;
        }

        // 直接使用boom数字预制体
        this.createHexNumberPrefab(q, r, neighborMines);
    }

    /**
     * 创建六边形数字预制体（boom1, boom2, ...）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param number 数字
     */
    private createHexNumberPrefab(q: number, r: number, number: number) {
        // 根据数字选择对应的预制体
        let prefab: cc.Prefab = null;
        switch (number) {
            case 1: prefab = this.boom1Prefab; break;
            case 2: prefab = this.boom2Prefab; break;
            case 3: prefab = this.boom3Prefab; break;
            case 4: prefab = this.boom4Prefab; break;
            case 5: prefab = this.boom5Prefab; break;
            case 6: prefab = this.boom6Prefab; break;
            case 7: prefab = this.boom7Prefab; break;
            case 8: prefab = this.boom8Prefab; break;
            default:
                console.error(`不支持的数字: ${number}`);
                return;
        }

        if (!prefab) {
            console.error(`boom${number}Prefab 预制体未设置，请在编辑器中挂载`);
            return;
        }

        // 实例化数字预制体
        const numberNode = cc.instantiate(prefab);
        numberNode.name = `HexBoom${number}`;

        // 设置位置（使用六边形坐标计算，不是单人头像所以不偏移）
        const position = this.getHexWorldPosition(q, r, false);
        numberNode.setPosition(position);

        // 添加到棋盘
        this.boardNode.addChild(numberNode);

        // 播放出现动画
        numberNode.setScale(0);
        cc.tween(numberNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    }

    /**
     * 播放棋盘震动动画（包括所有小格子）
     */
    private playBoardShakeAnimation() {
        if (!this.boardNode) {
            return;
        }

        // 震动参数 - 增强震动效果
        const shakeIntensity = 30; // 震动强度
        const shakeDuration = 1.0; // 震动持续时间
        const shakeFrequency = 40; // 震动频率

        // 震动棋盘
        this.shakeBoardNode(shakeIntensity, shakeDuration, shakeFrequency);

        // 震动所有六边形格子
        this.shakeAllHexGrids(shakeIntensity * 0.6, shakeDuration, shakeFrequency);
    }

    /**
     * 震动棋盘节点
     */
    private shakeBoardNode(intensity: number, duration: number, frequency: number) {
        // 保存原始位置
        const originalPosition = this.boardNode.position.clone();

        // 创建震动动画，使用递减强度
        let currentIntensity = intensity;
        const intensityDecay = 0.92; // 强度衰减系数

        const createShakeStep = (shakeIntensity: number) => {
            return cc.tween()
                .to(0.025, {
                    x: originalPosition.x + (Math.random() - 0.5) * shakeIntensity * 2,
                    y: originalPosition.y + (Math.random() - 0.5) * shakeIntensity * 2
                });
        };

        // 创建震动序列，强度逐渐衰减
        let shakeTween = cc.tween(this.boardNode);
        const totalSteps = Math.floor(duration * frequency);

        for (let i = 0; i < totalSteps; i++) {
            shakeTween = shakeTween.then(createShakeStep(currentIntensity));
            currentIntensity *= intensityDecay; // 逐渐减弱震动强度
        }

        // 最后恢复到原位置
        shakeTween.to(0.2, {
            x: originalPosition.x,
            y: originalPosition.y
        }, { easing: 'backOut' })
        .start();
    }

    /**
     * 震动所有六边形格子
     */
    private shakeAllHexGrids(intensity: number, duration: number, frequency: number) {
        if (!this.hexGridNodes) return;

        // 遍历所有六边形格子节点
        this.hexGridNodes.forEach((gridNode, key) => {
            if (!gridNode || !gridNode.active) return;

            // 为每个格子创建独立的震动动画
            this.shakeHexGridNode(gridNode, intensity, duration, frequency);
        });
    }

    /**
     * 震动单个六边形格子节点
     */
    private shakeHexGridNode(gridNode: cc.Node, intensity: number, duration: number, frequency: number) {
        // 保存原始位置
        const originalPosition = gridNode.position.clone();

        // 为每个格子添加随机延迟，创造波浪效果
        const randomDelay = Math.random() * 0.1;

        this.scheduleOnce(() => {
            // 创建震动动画，使用递减强度
            let currentIntensity = intensity;
            const intensityDecay = 0.94; // 格子震动衰减稍慢一些

            const createGridShakeStep = (shakeIntensity: number) => {
                return cc.tween()
                    .to(0.02, {
                        x: originalPosition.x + (Math.random() - 0.5) * shakeIntensity * 2,
                        y: originalPosition.y + (Math.random() - 0.5) * shakeIntensity * 2
                    });
            };

            // 创建震动序列
            let shakeTween = cc.tween(gridNode);
            const totalSteps = Math.floor(duration * frequency * 0.8); // 格子震动时间稍短

            for (let i = 0; i < totalSteps; i++) {
                shakeTween = shakeTween.then(createGridShakeStep(currentIntensity));
                currentIntensity *= intensityDecay;
            }

            // 最后恢复到原位置
            shakeTween.to(0.15, {
                x: originalPosition.x,
                y: originalPosition.y
            }, { easing: 'backOut' })
            .start();
        }, randomDelay);
    }

    // ==================== 头像生命周期管理 ====================
    // 以下方法与第一张地图的逻辑完全一样，用于管理头像预制体的生命周期

    /**
     * 让所有六边形头像消失（和第一张地图的hideAvatarsAtPosition逻辑一样）
     * @param onComplete 完成回调
     */
    public hideAllHexAvatars(onComplete: () => void) {
        if (!this.boardNode) {
            console.warn("棋盘节点不存在，无法清理六边形头像");
            onComplete();
            return;
        }

        // 收集所有头像节点（参考第一张地图的逻辑）
        const avatarNodes: cc.Node[] = [];

        // 方法1: 收集存储在hexGridData中的玩家节点（自己的头像）
        this.hexGridData.forEach((gridData) => {
            if (gridData.hasPlayer && gridData.playerNode) {
                avatarNodes.push(gridData.playerNode);
            }
        });

        // 方法2: 收集棋盘上所有的玩家预制体节点（包括其他玩家的头像）
        for (let i = 0; i < this.boardNode.children.length; i++) {
            const child = this.boardNode.children[i];

            // 检查是否是玩家预制体（通过组件判断）
            const playerController = child.getComponent(PlayerGameController);
            if (playerController) {
                // 避免重复添加（可能已经在方法1中添加过）
                if (!avatarNodes.includes(child)) {
                    avatarNodes.push(child);
                }
            }
        }

       

        // 如果没有头像，直接执行回调
        if (avatarNodes.length === 0) {
            this.clearAllMyHexAvatarReferences();
            onComplete();
            return;
        }

        let completedCount = 0;
        const totalCount = avatarNodes.length;

        // 为每个头像播放消失动画（和第一张地图完全一样）
        avatarNodes.forEach((avatarNode) => {
            // 使用cc.Tween播放消失动画
            cc.tween(avatarNode)
                .to(0.3, { opacity: 0, scaleX: 0.5, scaleY: 0.5 }, { easing: 'sineIn' })
                .call(() => {
                    // 动画完成后移除节点
                    avatarNode.removeFromParent();
                    completedCount++;

                    // 所有头像都消失完成后，执行回调
                    if (completedCount >= totalCount) {
                        // 清除所有自己头像的引用
                        this.clearAllMyHexAvatarReferences();
                        onComplete();
                    }
                })
                .start();
        });
    }

    /**
     * 清除所有自己六边形头像的引用（和第一张地图的clearAllMyAvatarReferences逻辑一样）
     */
    private clearAllMyHexAvatarReferences() {
        this.hexGridData.forEach((gridData) => {
            if (gridData.hasPlayer) {
                gridData.hasPlayer = false;
                gridData.playerNode = null;
            }
        });
        
    }

    // ==================== 加分逻辑相关方法 ====================
    // 以下方法与第一张地图的加分逻辑完全一样

    /**
     * 在指定六边形位置的玩家节点上显示分数
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param score 分数
     * @param showPlusOne 是否显示+1（先手奖励）
     */
    public showScoreOnHexPlayerNode(q: number, r: number, score: number, showPlusOne: boolean) {
        // 查找该位置的玩家节点
        const playerNode = this.findHexPlayerNodeAtPosition(q, r);
        if (!playerNode) {
            // 在NoticeActionDisplay流程中，头像会被清理，找不到节点是正常的
            
            return;
        }

        // 获取PlayerGameController组件
        const playerController = playerNode.getComponent(PlayerGameController);
        if (!playerController) {
            console.warn("找不到PlayerGameController组件");
            return;
        }

        // 显示分数动画
        if (showPlusOne) {
            // 先显示+1，再显示本回合得分
            this.showScoreAnimationOnHexNode(playerController, 1, () => {
                this.scheduleOnce(() => {
                    this.showScoreAnimationOnHexNode(playerController, score, null);
                }, 1.0);
            });
        } else {
            // 只显示本回合得分
            this.showScoreAnimationOnHexNode(playerController, score, null);
        }
    }

    /**
     * 查找指定六边形位置的玩家节点
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @returns 玩家节点或null
     */
    private findHexPlayerNodeAtPosition(q: number, r: number): cc.Node | null {
        // 方法1: 从hexGridData中查找（自己的头像）
        const key = this.getHexKey(q, r);
        const gridData = this.hexGridData.get(key);
        if (gridData && gridData.hasPlayer && gridData.playerNode) {
            return gridData.playerNode;
        }

        // 方法2: 在棋盘上查找其他玩家的头像
        if (!this.boardNode) {
            return null;
        }

        // 遍历棋盘上的所有子节点，查找player_game_pfb
        const children = this.boardNode.children;
        for (let i = 0; i < children.length; i++) {
            const child = children[i];
            if (child.name === "player_game_pfb") {
                // 检查位置是否匹配（允许一定的误差）
                const expectedPos = this.getHexWorldPosition(q, r, true);
                const actualPos = child.getPosition();
                const distance = expectedPos.sub(actualPos).mag();
                if (distance < 10) { // 10像素误差范围内
                    return child;
                }
            }
        }

        return null;
    }

    /**
     * 在六边形节点上显示分数动画
     * @param playerController 玩家控制器
     * @param score 分数
     * @param onComplete 完成回调
     */
    private showScoreAnimationOnHexNode(playerController: any, score: number, onComplete: (() => void) | null) {
        // 调用PlayerGameController的showAddScore方法
        if (playerController && typeof playerController.showAddScore === 'function') {
            playerController.showAddScore(score);
        }

        if (onComplete) {
            this.scheduleOnce(onComplete, 1.0);
        }
    }

    /**
     * 显示玩家游戏加减分效果（完全复制四边形棋盘控制器的逻辑）
     * @param userId 用户ID
     * @param score 分数变化（正数为加分，负数为减分）
     */
    public showHexPlayerGameScore(userId: string, score: number) {
        const currentUserId = this.getCurrentHexUserId();
        let foundPlayer = false;

        // 1. 如果是当前用户，查找自己的玩家节点（存储在hexGridData中）
        if (userId === currentUserId) {
            foundPlayer = this.showScoreForCurrentHexUser(score);
        } else {
            // 2. 如果是其他用户，查找对应的玩家头像节点
            foundPlayer = this.showScoreForOtherHexUser(userId, score);
        }

        if (!foundPlayer) {
            console.warn(`未找到用户 ${userId} 的六边形头像节点来显示分数效果`);
        }
    }

    /**
     * 获取当前用户ID（复制四边形棋盘控制器的方法）
     */
    private getCurrentHexUserId(): string {
        return GlobalBean.GetInstance().loginData?.userInfo?.userId || "";
    }

    /**
     * 为当前用户显示分数效果（复制四边形棋盘控制器的逻辑）
     */
    private showScoreForCurrentHexUser(score: number): boolean {
        let foundPlayer = false;

        this.hexGridData.forEach((gridData) => {
            // 如果已经找到了，就不再继续查找
            if (foundPlayer) {
                return;
            }

            if (gridData.hasPlayer && gridData.playerNode) {
                let playerController = gridData.playerNode.getComponent("PlayerGameController") ||
                                     gridData.playerNode.getComponent("PlayerGameController ");

                if (playerController) {
                    
                    this.showScoreOnHexPlayerController(playerController, score);
                    foundPlayer = true;
                }
            }
        });

        if (!foundPlayer) {
            console.warn("❌ 未找到当前用户的六边形头像节点");
        }

        return foundPlayer;
    }

    /**
     * 为其他用户显示分数效果（复制四边形棋盘控制器的逻辑）
     */
    private showScoreForOtherHexUser(userId: string, score: number): boolean {
        if (!this.boardNode) {
            return false;
        }

        // 遍历棋盘上的所有玩家头像节点
        return this.findHexPlayerNodeByUserId(userId, score);
    }

    /**
     * 根据userId查找对应的玩家节点（复制四边形棋盘控制器的逻辑）
     */
    private findHexPlayerNodeByUserId(userId: string, score: number): boolean {
        if (!this.boardNode) {
            console.warn(`棋盘节点不存在，无法查找用户 ${userId} 的头像`);
            return false;
        }

        // 遍历棋盘上的所有玩家头像节点，根据存储的userId精确匹配
        for (let i = 0; i < this.boardNode.children.length; i++) {
            const child = this.boardNode.children[i];

            // 尝试多种方式获取PlayerGameController组件
            let playerController = child.getComponent("PlayerGameController");
            if (!playerController) {
                playerController = child.getComponent("PlayerGameController ");  // 注意末尾有空格
            }
            if (!playerController) {
                // 尝试通过类名获取
                const components = child.getComponents(cc.Component);
                playerController = components.find(comp =>
                    comp.constructor.name === 'PlayerGameController' ||
                    comp.constructor.name === 'PlayerGameController '
                );
            }

            const storedUserId = child['userId'];

            if (storedUserId === userId) {
                if (playerController) {
                    // 找到匹配的用户ID和组件，显示分数效果
                    this.showScoreOnHexPlayerController(playerController, score);
                    return true;
                } else {
                    // 找到匹配的用户ID但没有组件
                    console.warn(`⚠️ 找到用户 ${userId} 的节点但没有PlayerGameController组件`);
                    return false;  // 找到节点但没有组件，返回false
                }
            }
        }

        console.warn(`❌ 未找到用户 ${userId} 的六边形头像节点`);
        return false;
    }

    /**
     * 在PlayerController上显示分数效果（复制四边形棋盘控制器的逻辑）
     */
    private showScoreOnHexPlayerController(playerController: any, score: number) {
        // 临时提升节点层级，避免被其他头像遮挡
        const playerNode = playerController.node;
        const originalSiblingIndex = playerNode.getSiblingIndex();

        // 将节点移到最上层
        playerNode.setSiblingIndex(-1);

        // 同时确保加分/减分节点的层级更高
        this.ensureHexScoreNodeTopLevel(playerController);

        if (score > 0) {
            playerController.showAddScore(score);
        } else if (score < 0) {
            playerController.showSubScore(Math.abs(score));
        }

        // 延迟恢复原始层级（等分数动画播放完成）
        this.scheduleOnce(() => {
            if (playerNode && playerNode.isValid) {
                playerNode.setSiblingIndex(originalSiblingIndex);
            }
        }, 2.5); // 增加到2.5秒，确保动画完全结束
    }

    /**
     * 确保六边形加分/减分节点在最高层级
     */
    private ensureHexScoreNodeTopLevel(playerController: any) {
        // 设置加分节点的最高层级
        if (playerController.addScoreNode) {
            playerController.addScoreNode.zIndex = cc.macro.MAX_ZINDEX - 1;
        }

        // 设置减分节点的最高层级
        if (playerController.subScoreNode) {
            playerController.subScoreNode.zIndex = cc.macro.MAX_ZINDEX - 1;
        }

       
    }

   

    /**
     * 查找指定用户ID的所有六边形头像节点
     * @param userId 用户ID
     * @returns 头像节点数组
     */
    private findAllHexPlayerNodesByUserId(userId: string): cc.Node[] {
        const playerNodes: cc.Node[] = [];

        if (!this.boardNode) {
            return playerNodes;
        }

        // 遍历棋盘上的所有子节点
        const children = this.boardNode.children;
        for (let i = 0; i < children.length; i++) {
            const child = children[i];

            // 检查是否是玩家预制体（通过组件判断）
            const playerController = child.getComponent(PlayerGameController);
            if (playerController) {
                // 检查是否是指定用户的头像（使用存储在节点上的userId）
                const storedUserId = child['userId'];
                if (storedUserId === userId) {
                    playerNodes.push(child);
                }
            }
        }

        // 也检查存储在hexGridData中的玩家节点
        this.hexGridData.forEach((gridData) => {
            if (gridData.hasPlayer && gridData.playerNode) {
                const playerController = gridData.playerNode.getComponent(PlayerGameController);
                const storedUserId = gridData.playerNode['userId'];
                if (playerController && storedUserId === userId) {
                    // 避免重复添加
                    if (!playerNodes.includes(gridData.playerNode)) {
                        playerNodes.push(gridData.playerNode);
                    }
                }
            }
        });

        return playerNodes;
    }

    // ==================== 其他玩家头像生成 ====================
    // 以下方法与第一张地图的逻辑完全一样，用于生成其他玩家的头像

    /**
     * 在指定六边形位置显示其他玩家的操作（完全复制四边形棋盘控制器的逻辑）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param actions 该位置的其他玩家操作列表
     */
    public displayOtherPlayersAtHexPosition(q: number, r: number, actions: PlayerActionDisplay[]) {
        if (!this.isValidHexCoordinate(q, r) || !actions || actions.length === 0) {
            console.warn(`无效参数: (${q}, ${r}), actions: ${actions?.length || 0}`);
            return;
        }

        // 检查该位置是否已经有自己的头像
        const key = this.getHexKey(q, r);
        const gridData = this.hexGridData.get(key);

        if (gridData && gridData.hasPlayer) {
            // 只有当真的有其他玩家时，才调整自己的头像位置
            if (actions.length > 0) {
                // 如果已有自己的头像且有其他玩家，需要使用多人布局策略
                this.addOtherPlayersToExistingHexGrid(q, r, actions);
            }
        } else {
            // 如果没有自己的头像，直接添加其他玩家头像
            this.addOtherPlayersToEmptyHexGrid(q, r, actions);
        }
    }

    /**
     * 在已有自己头像的六边形格子上添加其他玩家头像，并调整自己的头像位置和缩放
     * （完全复制四边形棋盘控制器的逻辑）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param actions 其他玩家操作列表
     */
    private addOtherPlayersToExistingHexGrid(q: number, r: number, actions: PlayerActionDisplay[]) {
        // 总玩家数 = 自己(1) + 其他玩家数量
        const totalPlayers = 1 + actions.length;
        const positions = this.getHexPlayerPositions(totalPlayers);

        // 第一步：调整自己的头像位置和缩放
        const myPosition = positions[0]; // 第一个位置是自己的
        this.adjustMyHexAvatarPosition(q, r, myPosition, actions);

        // 第二步：从第二个位置开始放置其他玩家
        for (let i = 0; i < actions.length; i++) {
            const action = actions[i];
            const position = positions[i + 1]; // 跳过第一个位置（自己的位置）

            // 使用六边形坐标系创建其他玩家头像
            this.createOtherPlayerAtHexPosition(q, r, action, position, totalPlayers);
        }
    }

    /**
     * 在空六边形格子上添加其他玩家头像
     * （完全复制四边形棋盘控制器的逻辑）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param actions 其他玩家操作列表
     */
    private addOtherPlayersToEmptyHexGrid(q: number, r: number, actions: PlayerActionDisplay[]) {
        const totalPlayers = actions.length; // 空格子上只有其他玩家
        const positions = this.getHexPlayerPositions(totalPlayers);

        for (let i = 0; i < actions.length; i++) {
            const action = actions[i];
            const position = positions[i];

            // 使用六边形坐标系创建其他玩家头像
            this.createOtherPlayerAtHexPosition(q, r, action, position, totalPlayers);
        }
    }



    /**
     * 根据玩家数量获取六边形布局位置（完全复制四边形棋盘控制器的逻辑）
     * @param playerCount 玩家数量
     * @returns 位置数组 {x: number, y: number, scale: number}[]
     */
    private getHexPlayerPositions(playerCount: number): {x: number, y: number, scale: number}[] {
        switch (playerCount) {
            case 1:
                // 单个玩家，居中显示，正常大小
                return [{x: 0, y: 0, scale: 1.0}];

            case 2:
                // 两个玩家，左右分布，缩放0.5
                return [
                    {x: -22, y: -8, scale: 0.5}, // 左
                    {x: 22, y: -8, scale: 0.5}   // 右
                ];

            case 3:
                // 三个玩家，上中下分布，缩放0.5
                return [
                    {x: 0, y: 12, scale: 0.5},    // 上
                    {x: -23, y: -27, scale: 0.5}, // 左下
                    {x: 23, y: -27, scale: 0.5}   // 右下
                ];

            case 4:
                // 四个玩家，四角分布，缩放0.5
                return [
                    {x: -22, y: 12, scale: 0.5},  // 左上
                    {x: 22, y: 12, scale: 0.5},   // 右上
                    {x: -22, y: -30, scale: 0.5}, // 左下
                    {x: 22, y: -30, scale: 0.5}   // 右下
                ];

            default:
                // 超过4个玩家，只显示前4个
                console.warn(`玩家数量过多: ${playerCount}，只显示前4个`);
                return this.getHexPlayerPositions(4);
        }
    }

    /**
     * 调整自己的六边形头像位置和缩放（当多人在同一格子时）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param position 新的位置和缩放信息
     * @param actions 其他玩家操作列表
     */
    private adjustMyHexAvatarPosition(q: number, r: number, position: {x: number, y: number, scale: number}, actions: PlayerActionDisplay[]) {
        const key = this.getHexKey(q, r);
        const gridData = this.hexGridData.get(key);

        // 查找自己的头像节点
        if (!gridData || !gridData.hasPlayer || !gridData.playerNode) {
            console.warn(`在六边形位置(${q}, ${r})找不到自己的头像节点`);
            return;
        }

        const myPlayerNode = gridData.playerNode;

        // 计算该格子的总人数（自己 + 其他玩家）
        const totalPlayers = 1 + (actions ? actions.length : 0);

        // 计算基础位置（根据总人数决定是否偏移）
        const basePosition = this.calculateHexBasePositionByPlayerCount(q, r, totalPlayers);

        // 计算新的最终位置
        const newPosition = cc.v2(
            basePosition.x + position.x,
            basePosition.y + position.y
        );

        // 播放平滑移动和缩放动画
        this.playHexAvatarAdjustAnimation(myPlayerNode, newPosition, position.scale);
    }

    /**
     * 根据六边形格子总人数计算基础位置（完全复制四边形棋盘控制器的逻辑）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param totalPlayers 该格子的总人数
     * @returns 基础位置
     */
    private calculateHexBasePositionByPlayerCount(q: number, r: number, totalPlayers: number): cc.Vec2 {
        if (totalPlayers === 1) {
            // 一个格子里只有一个人：使用正常的偏移（单人头像预制体，y轴+20）
            return this.getHexWorldPosition(q, r, true);
        } else {
            // 一个格子里有两个及以上：不偏移（多人头像预制体，不偏移）
            return this.getHexWorldPosition(q, r, false);
        }
    }

    /**
     * 播放六边形头像调整动画（完全复制四边形棋盘控制器的逻辑）
     * @param playerNode 玩家节点
     * @param newPosition 新位置
     * @param newScale 新缩放
     */
    private playHexAvatarAdjustAnimation(playerNode: cc.Node, newPosition: cc.Vec2, newScale: number) {
        if (!playerNode || !playerNode.isValid) {
            return;
        }

        // 停止之前的动画
        playerNode.stopAllActions();

        // 使用cc.tween播放位置和缩放动画
        cc.tween(playerNode)
            .to(0.3, {
                x: newPosition.x,
                y: newPosition.y,
                scaleX: newScale,
                scaleY: newScale
            }, { easing: 'sineOut' })
            .start();
    }

    /**
     * 创建其他玩家在六边形位置的头像
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param action 玩家操作数据
     * @param position 位置和缩放信息
     * @param totalPlayers 总玩家数
     */
    private createOtherPlayerAtHexPosition(q: number, r: number, action: PlayerActionDisplay, position: {x: number, y: number, scale: number}, totalPlayers: number) {
        if (!this.playerGamePrefab || !this.boardNode) {
            console.error("❌ 预制体或棋盘节点未设置！");
            return;
        }

        // 实例化玩家预制体
        let playerNode = cc.instantiate(this.playerGamePrefab);

        // 计算基础位置（根据总人数决定是否偏移）
        const basePosition = this.calculateHexBasePositionByPlayerCount(q, r, totalPlayers);

        // 计算最终位置
        const finalPosition = cc.v2(
            basePosition.x + position.x,
            basePosition.y + position.y
        );

        playerNode.setPosition(finalPosition);

        // 根据总人数设置缩放：单人0.8，多人使用position.scale
        if (totalPlayers === 1) {
            playerNode.setScale(0.8);
        } else {
            playerNode.setScale(position.scale);
        }

        // 先隐藏节点，等头像加载完成后再显示
        playerNode.active = false;

        // 添加到棋盘
        this.addPlayerNodeSafely(playerNode);

        // 设置其他玩家的头像和数据
        this.setupOtherPlayerHexAvatar(playerNode, action, () => {
            // 头像加载完成的回调，播放生成动画
            this.playAvatarSpawnAnimation(playerNode);
        });
    }

    /**
     * 设置其他玩家的六边形头像和数据
     * @param playerNode 玩家节点
     * @param action 玩家操作数据
     * @param onComplete 完成回调
     */
    private setupOtherPlayerHexAvatar(playerNode: cc.Node, action: PlayerActionDisplay, onComplete: () => void) {
        // 查找PlayerGameController组件
        let playerController = playerNode.getComponent(PlayerGameController);

        if (playerController) {
            // 在节点上存储userId，用于后续查找
            playerNode['userId'] = action.userId;

            // 设置旗子节点的显示状态
            const withFlag = (action.action === 2); // action=2表示标记操作，显示旗子
            if (playerController.flagNode) {
                playerController.flagNode.active = withFlag;
            }

            // 获取真实的用户数据（和第一张地图逻辑一样）
            let realUserData = this.getRealUserData(action.userId);
            if (!realUserData) {
                console.warn(`找不到用户 ${action.userId} 的真实数据，使用默认数据`);
                // 使用默认数据作为备选
                realUserData = {
                    userId: action.userId,
                    nickName: `玩家${action.userId}`,
                    avatar: this.getDefaultAvatarUrl(),
                    score: 0,
                    pos: 0,
                    coin: 0,
                    status: 0,
                    rank: 0
                } as RoomUser;
            }

            // 使用PlayerGameController的setData方法来设置头像
            try {
                playerController.setData(realUserData);

                // 延迟设置旗子状态，确保在PlayerGameController初始化之后
                this.scheduleOnce(() => {
                    if (playerController.flagNode) {
                        playerController.flagNode.active = withFlag;
                    }
                    onComplete();
                }, 0.1);
            } catch (error) {
                console.error("设置其他玩家头像数据失败:", error);
                onComplete();
            }
        } else {
            console.warn("⚠️ 找不到PlayerGameController组件");
            onComplete();
        }
    }

    /**
     * 获取其他玩家的头像URL
     * @param userId 用户ID
     * @returns 头像URL
     */
    private getOtherPlayerAvatarUrl(userId: string): string {
        // 这里可以根据userId获取真实的头像URL
        // 暂时使用默认头像
        return this.getDefaultAvatarUrl();
    }





    /**
     * 从GlobalBean中获取真实的用户数据（和第一张地图逻辑完全一样）
     * @param userId 用户ID
     * @returns RoomUser 或 null
     */
    private getRealUserData(userId: string): RoomUser | null {
        try {
            if (!GlobalBean.GetInstance().noticeStartGame || !GlobalBean.GetInstance().noticeStartGame.users) {
                console.warn("没有游戏数据，无法获取用户信息");
                return null;
            }

            const users = GlobalBean.GetInstance().noticeStartGame.users;
            const user = users.find((u: RoomUser) => u.userId === userId);

            if (user) {
                return user;
            } else {
                console.warn(`未找到用户 ${userId} 的数据`);
                return null;
            }
        } catch (error) {
            console.error(`获取用户数据时出错: ${error}`);
            return null;
        }
    }

    /**
     * 恢复联机模式地图状态（断线重连时使用）
     * @param mapData 地图数据
     */
    public restoreOnlineMapState(mapData: any) {
        console.log("HexChessBoardController: 恢复联机模式地图状态", mapData);

        // 检查数据格式
        if (Array.isArray(mapData) && mapData.length > 0) {
            if (Array.isArray(mapData[0])) {
                // 二维数组格式：mapData[x][y] 表示每个格子的状态
                console.log("检测到二维数组格式的mapData，尺寸:", mapData.length, "x", mapData[0].length);
                this.restoreFromGridArray(mapData);
            } else if (mapData[0] && typeof mapData[0] === 'object' && ('q' in mapData[0] || 'x' in mapData[0])) {
                // 联机模式格式：[{q, r, isRevealed, neighborMines, ...}, ...]
                console.log("检测到联机模式格式的mapData，格子数量:", mapData.length);
                this.restoreFromOnlineArray(mapData);
            } else {
                console.warn("未知的数组格式:", mapData[0]);
            }
        } else if (mapData && (mapData.revealedBlocks || mapData.markedBlocks)) {
            // 关卡模式格式：{revealedBlocks: [], markedBlocks: []}
            console.log("检测到关卡模式格式的mapData");
            this.restoreFromBlockLists(mapData);
        } else {
            console.warn("未知的mapData格式:", mapData);
        }
    }

    /**
     * 从二维数组格式恢复地图状态（六边形地图）
     * @param gridArray 二维数组，gridArray[x][y] 表示格子状态
     */
    private restoreFromGridArray(gridArray: any[][]) {
        console.log("从二维数组恢复六边形地图状态，数组内容:", gridArray);

        let restoredCount = 0;

        // 对于六边形地图，我们需要将x,y坐标转换为q,r坐标
        // 这里假设服务端使用的是标准的x,y坐标系
        for (let x = 0; x < gridArray.length; x++) {
            for (let y = 0; y < gridArray[x].length; y++) {
                const cellData = gridArray[x][y];

                if (cellData === null || cellData === undefined || cellData === 0) {
                    continue;
                }

                // 将x,y坐标转换为q,r坐标（这里需要根据实际的坐标映射关系调整）
                // 暂时直接使用x,y作为q,r，后续可能需要调整
                const q = x;
                const r = y;

                if (!this.isValidHexCoordinate(q, r)) {
                    continue;
                }

                console.log(`恢复六边形格子 (${q}, ${r}), 状态值:`, cellData, "类型:", typeof cellData);
                restoredCount++;

                // 获取六边形格子节点，确保它存在
                const key = this.getHexKey(q, r);
                const gridNode = this.hexGridNodes.get(key);
                if (!gridNode) {
                    console.warn(`六边形格子节点不存在: (${q}, ${r})`);
                    continue;
                }

                console.log(`六边形格子 (${q}, ${r}) 当前状态: active=${gridNode.active}, opacity=${gridNode.opacity}`);

                // 立即隐藏格子（不播放动画）
                gridNode.active = false;
                gridNode.opacity = 0;
                gridNode.scaleX = 0;
                gridNode.scaleY = 0;

                console.log(`六边形格子 (${q}, ${r}) 隐藏后状态: active=${gridNode.active}, opacity=${gridNode.opacity}`);

                // 根据cellData的值显示相应内容
                if (typeof cellData === 'number' && cellData > 0 && cellData <= 8) {
                    // 数字：表示周围地雷数
                    console.log(`创建六边形数字预制体 (${q}, ${r}), 数字:`, cellData);
                    this.scheduleOnce(() => {
                        this.createHexNumberPrefab(q, r, cellData);
                    }, 0.1);
                } else if (cellData === -1 || cellData === 'mine' || cellData === 'bomb') {
                    // 地雷
                    console.log(`创建六边形地雷预制体 (${q}, ${r})`);
                    this.scheduleOnce(() => {
                        this.createHexBoomPrefab(q, r);
                    }, 0.1);
                } else if (cellData === 'flag' || cellData === 'marked') {
                    // 标记
                    console.log(`创建六边形标记预制体 (${q}, ${r})`);
                    this.createHexBiaojiPrefab(q, r);
                } else {
                    console.log(`未知的六边形格子状态 (${q}, ${r}):`, cellData);
                }

                // 标记格子已被处理
                const gridData = this.hexGridData.get(key);
                if (gridData) {
                    gridData.hasPlayer = true;
                }
            }
        }

        console.log(`总共恢复了 ${restoredCount} 个六边形格子的状态`);
    }

    /**
     * 从联机模式数组格式恢复地图状态（六边形地图）
     * @param onlineArray 联机模式数组，每个元素包含格子的完整状态信息
     */
    private restoreFromOnlineArray(onlineArray: any[]) {
        console.log("从联机模式数组恢复六边形地图状态，数组内容:", onlineArray);

        let restoredCount = 0;
        let skippedCount = 0;

        onlineArray.forEach((cellInfo: any) => {
            // 获取坐标
            const q = cellInfo.q !== undefined ? cellInfo.q : cellInfo.x;
            const r = cellInfo.r !== undefined ? cellInfo.r : cellInfo.y;

            if (!this.isValidHexCoordinate(q, r)) {
                return;
            }

            // 检查是否已挖掘
            if (!cellInfo.isRevealed) {
                return;
            }

            // 获取六边形格子节点
            const key = this.getHexKey(q, r);
            const gridNode = this.hexGridNodes.get(key);
            if (!gridNode) {
                console.warn(`六边形格子节点不存在: (${q}, ${r})`);
                return;
            }

            // 检查格子是否已经被处理过（已经隐藏）
            if (!gridNode.active || gridNode.opacity === 0) {
                console.log(`跳过已处理的六边形格子: (${q}, ${r}), active=${gridNode.active}, opacity=${gridNode.opacity}`);
                skippedCount++;
                return;
            }

            console.log(`恢复已挖掘六边形方块: (${q}, ${r}), 周围地雷数: ${cellInfo.neighborMines}`);
            console.log(`六边形格子 (${q}, ${r}) 当前状态: active=${gridNode.active}, opacity=${gridNode.opacity}`);
            restoredCount++;

            // 立即隐藏格子（不播放动画）
            gridNode.active = false;
            gridNode.opacity = 0;
            gridNode.scaleX = 0;
            gridNode.scaleY = 0;

            console.log(`六边形格子 (${q}, ${r}) 隐藏后状态: active=${gridNode.active}, opacity=${gridNode.opacity}`);

            // 显示挖掘结果
            if (cellInfo.neighborMines > 0) {
                console.log(`创建六边形数字预制体 (${q}, ${r}), 数字:`, cellInfo.neighborMines);
                this.scheduleOnce(() => {
                    this.createHexNumberPrefab(q, r, cellInfo.neighborMines);
                }, 0.1);
            } else {
                console.log(`六边形格子 (${q}, ${r}) 周围无地雷，不创建数字预制体`);
            }

            // 标记格子已被处理
            const gridData = this.hexGridData.get(key);
            if (gridData) {
                gridData.hasPlayer = true;
            }
        });

        console.log(`联机模式六边形断线重连恢复统计: 新恢复=${restoredCount}个, 跳过已处理=${skippedCount}个, 总数据=${onlineArray.length}个`);
    }

    /**
     * 从对象格式恢复地图状态（六边形地图）
     * @param mapData 包含revealedBlocks和markedBlocks的对象
     */
    private restoreFromBlockLists(mapData: any) {
        console.log("从对象格式恢复六边形地图状态");

        // 恢复已挖掘的方块
        if (mapData.revealedBlocks && Array.isArray(mapData.revealedBlocks)) {
            console.log("恢复已挖掘的方块数量:", mapData.revealedBlocks.length);
            mapData.revealedBlocks.forEach((block: any) => {
                // 六边形地图使用q和r坐标，如果没有则使用x和y
                const q = block.q !== undefined ? block.q : block.x;
                const r = block.r !== undefined ? block.r : block.y;
                const neighborMines = block.neighborMines;

                if (this.isValidHexCoordinate(q, r)) {
                    console.log(`恢复已挖掘六边形方块: (${q}, ${r}), 周围地雷数: ${neighborMines}`);

                    // 立即隐藏格子（不播放动画）
                    this.hideHexGridAt(q, r, true);

                    // 显示挖掘结果
                    if (neighborMines > 0) {
                        // 延迟创建数字预制体，确保格子先隐藏
                        this.scheduleOnce(() => {
                            this.createHexNumberPrefab(q, r, neighborMines);
                        }, 0.1);
                    }

                    // 标记格子已被处理
                    const key = this.getHexKey(q, r);
                    const gridData = this.hexGridData.get(key);
                    if (gridData) {
                        gridData.hasPlayer = true;
                    }
                }
            });
        }

        // 恢复已标记的方块
        if (mapData.markedBlocks && Array.isArray(mapData.markedBlocks)) {
            console.log("恢复已标记的方块数量:", mapData.markedBlocks.length);
            mapData.markedBlocks.forEach((block: any) => {
                // 六边形地图使用q和r坐标，如果没有则使用x和y
                const q = block.q !== undefined ? block.q : block.x;
                const r = block.r !== undefined ? block.r : block.y;

                if (this.isValidHexCoordinate(q, r)) {
                    console.log(`恢复已标记六边形方块: (${q}, ${r})`);
                    // 创建标记预制体
                    this.createHexBiaojiPrefab(q, r);
                }
            });
        }
    }
}
