// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { HexCoord } from "../../bean/GameBean";
import { WebSocketManager } from "../../net/WebSocketManager";
import { MessageId } from "../../net/MessageId";

const {ccclass, property} = cc._decorator;

// 六边形单机模式格子数据接口
export interface HexSingleGridData {
    q: number;  // 六边形坐标系q坐标
    r: number;  // 六边形坐标系r坐标
    worldPos: cc.Vec2;  // 格子在世界坐标系中的位置
    hasPlayer: boolean;  // 是否已经放置了预制体
    playerNode?: cc.Node;  // 放置的节点引用
}

@ccclass
export default class HexSingleChessBoardController extends cc.Component {

    @property(cc.Prefab)
    boomPrefab: cc.Prefab = null;  // boom预制体

    @property(cc.Prefab)
    biaojiPrefab: cc.Prefab = null;  // biaoji预制体

    @property(cc.Prefab)
    boom1Prefab: cc.Prefab = null;  // 数字1预制体

    @property(cc.Prefab)
    boom2Prefab: cc.Prefab = null;  // 数字2预制体

    @property(cc.Prefab)
    boom3Prefab: cc.Prefab = null;  // 数字3预制体

    @property(cc.Prefab)
    boom4Prefab: cc.Prefab = null;  // 数字4预制体

    @property(cc.Prefab)
    boom5Prefab: cc.Prefab = null;  // 数字5预制体

    @property(cc.Prefab)
    boom6Prefab: cc.Prefab = null;  // 数字6预制体

    @property(cc.Prefab)
    boom7Prefab: cc.Prefab = null;  // 数字7预制体

    @property(cc.Prefab)
    boom8Prefab: cc.Prefab = null;  // 数字8预制体

    // 六个六边形棋盘节点
    @property(cc.Node)
    hexBoard1Node: cc.Node = null;  // 六边形棋盘1节点

    @property(cc.Node)
    hexBoard2Node: cc.Node = null;  // 六边形棋盘2节点

    @property(cc.Node)
    hexBoard3Node: cc.Node = null;  // 六边形棋盘3节点

    @property(cc.Node)
    hexBoard4Node: cc.Node = null;  // 六边形棋盘4节点

    @property(cc.Node)
    hexBoard5Node: cc.Node = null;  // 六边形棋盘5节点

    @property(cc.Node)
    hexBoard6Node: cc.Node = null;  // 六边形棋盘6节点

    // 当前使用的棋盘节点
    private currentBoardNode: cc.Node = null;
    private currentBoardType: string = "hexBoard1";  // 默认使用第一个六边形棋盘

    // 六边形棋盘配置
    private readonly HEX_SIZE = 44;  // 六边形半径
    private readonly HEX_WIDTH = this.HEX_SIZE * 2;  // 六边形宽度
    private readonly HEX_HEIGHT = this.HEX_SIZE * Math.sqrt(3);  // 六边形高度

    // 格子数据存储 - 使用Map存储六边形坐标
    private hexGridData: Map<string, HexSingleGridData> = new Map();  // 存储六边形格子数据
    private hexGridNodes: Map<string, cc.Node> = new Map();  // 存储六边形格子节点
    private validHexCoords: HexCoord[] = [];  // 有效的六边形坐标列表

    // 炸弹爆炸标记
    private hasBombExploded: boolean = false;

    // 防重复发送消息
    private lastClickTime: number = 0;
    private lastClickPosition: string = "";
    private readonly CLICK_COOLDOWN = 200; // 200毫秒冷却时间

    onLoad() {
        // 不进行默认初始化，等待外部调用initBoard
    }

    start() {
        // start方法不再自动启用触摸事件，避免与initBoard重复
        // 触摸事件的启用由initBoard方法负责
    }

    /**
     * 根据棋盘类型获取对应的棋盘节点
     * @param boardType 棋盘类型
     */
    private getBoardNodeByType(boardType: string): cc.Node | null {
        switch (boardType) {
            case "hexBoard1":
                return this.hexBoard1Node;
            case "hexBoard2":
                return this.hexBoard2Node;
            case "hexBoard3":
                return this.hexBoard3Node;
            case "hexBoard4":
                return this.hexBoard4Node;
            case "hexBoard5":
                return this.hexBoard5Node;
            case "hexBoard6":
                return this.hexBoard6Node;
            default:
                return null;
        }
    }

    /**
     * 初始化指定类型的六边形棋盘
     * @param boardType 棋盘类型 ("hexBoard1", "hexBoard2", "hexBoard3", "hexBoard4", "hexBoard5", "hexBoard6")
     */
    public initBoard(boardType: string) {
        // 根据棋盘类型获取对应的节点
        this.currentBoardNode = this.getBoardNodeByType(boardType);
        if (!this.currentBoardNode) {
            console.error(`六边形棋盘节点未设置！棋盘类型: ${boardType}`);
            return;
        }

        this.currentBoardType = boardType;

        // 清空现有数据
        this.hexGridData.clear();
        this.hexGridNodes.clear();
        this.validHexCoords = [];

        // 重置炸弹爆炸标记
        this.hasBombExploded = false;

        // 延迟一帧后再次尝试启用触摸事件，确保所有节点都已创建完成
        this.scheduleOnce(() => {
            this.setValidHexCoords([]);  // 传入空数组，但会被忽略
            // 测试预制体位置计算
            this.testHexPositionCalculation();
            this.enableTouchForExistingGrids();
        }, 0.1);

       
    }

    /**
     * 设置有效的六边形坐标列表（忽略服务器数据，使用前端节点坐标）
     * @param _coords 服务器发送的坐标列表（将被忽略）
     */
    public setValidHexCoords(_coords: HexCoord[]) {
        // 忽略服务器传入的坐标，始终从节点名称自动生成
        this.generateCoordsFromNodeNames();
        this.initHexBoard();
    }

    /**
     * 从节点名称自动生成有效坐标列表
     */
    private generateCoordsFromNodeNames() {
        if (!this.currentBoardNode) {
            console.error("❌ 棋盘节点不存在，无法生成坐标列表");
            return;
        }

        const foundCoords: HexCoord[] = [];
        const children = this.currentBoardNode.children;

        for (let i = 0; i < children.length; i++) {
            const child = children[i];
            const nodeName = child.name;

            // 跳过游戏元素节点，只处理六边形格子节点
            if (this.isGameElement(child, nodeName)) {
                continue;
            }

            const coords = this.parseHexCoordinateFromName(nodeName);

            if (coords) {
                // 检查是否已经存在相同的坐标
                const exists = foundCoords.some(c => c.q === coords.q && c.r === coords.r);
                if (!exists) {
                    foundCoords.push({ q: coords.q, r: coords.r });
                }
            }
        }

        this.validHexCoords = foundCoords;
    }

    // 初始化六边形棋盘
    private initHexBoard() {
        // 清空现有数据
        this.hexGridData.clear();
        this.hexGridNodes.clear();

        // 初始化有效坐标的数据
        for (const coord of this.validHexCoords) {
            const key = this.getHexKey(coord.q, coord.r);
            this.hexGridData.set(key, {
                q: coord.q,
                r: coord.r,
                worldPos: this.getHexWorldPosition(coord.q, coord.r),
                hasPlayer: false
            });
        }

        this.createHexGridNodes();
    }

    // 生成六边形坐标的唯一键
    private getHexKey(q: number, r: number): string {
        return `${q},${r}`;
    }

    // 启用现有格子的触摸事件
    private createHexGridNodes() {
        if (!this.currentBoardNode) {
            console.error("棋盘节点未设置！");
            return;
        }

        // 如果格子已经存在，直接启用触摸事件
        this.enableTouchForExistingGrids();
    }

    // 为现有格子启用触摸事件
    private enableTouchForExistingGrids() {
        // 检查棋盘节点是否存在
        if (!this.currentBoardNode) {
            console.error("棋盘节点未设置，无法启用触摸事件！");
            return;
        }


        // 遍历棋盘节点的所有子节点
        let children = this.currentBoardNode.children;
        let validGridCount = 0;
        let skippedCount = 0;

        for (let i = 0; i < children.length; i++) {
            let child = children[i];
            const nodeName = child.name;

           

            // 跳过游戏元素节点，只处理六边形格子节点
            if (this.isGameElement(child, nodeName)) {
               
                skippedCount++;
                continue;
            }

            // 尝试从节点名称解析六边形坐标
            let coords = this.parseHexCoordinateFromName(nodeName);
            if (coords) {
               
                this.setupHexGridTouchEvents(child, coords.q, coords.r);
                const key = this.getHexKey(coords.q, coords.r);
                this.hexGridNodes.set(key, child);
                validGridCount++;
            } else {
               
                // 如果无法从名称解析，尝试从位置计算
                let pos = child.getPosition();
                let coords = this.getHexCoordinateFromPosition(pos);
                if (coords) {
                    
                    this.setupHexGridTouchEvents(child, coords.q, coords.r);
                    const key = this.getHexKey(coords.q, coords.r);
                    this.hexGridNodes.set(key, child);
                    validGridCount++;
                } else {
                   
                }
            }
        }

        

        if (validGridCount === 0) {
            console.warn(`⚠️ 没有找到任何有效的六边形格子节点！`);
            console.warn(`   请检查格子节点命名是否为 sixblock_q_r 格式`);
            console.warn(`   例如: sixblock_0_0, sixblock_1_-1, sixblock_-1_2`);
        }
    }

    // 从节点名称解析六边形坐标
    private parseHexCoordinateFromName(nodeName: string): {q: number, r: number} | null {
        const patterns = [
            /^sixblock_(-?\d+)_(-?\d+)$/,   // sixblock_q_r 格式
        ];

        for (const pattern of patterns) {
            const match = nodeName.match(pattern);
            if (match) {
                const coords = {q: parseInt(match[1]), r: parseInt(match[2])};
                return coords;
            }
        }

        console.warn(`❌ 无法解析节点名称: ${nodeName}`);
        return null;
    }

    // 判断是否为游戏元素节点（需要跳过的节点）
    private isGameElement(child: cc.Node, nodeName: string): boolean {
        // 跳过玩家头像预制体
        if (nodeName === "player_game_pfb" || child.name.includes("Player")) {
            return true;
        }

        // 跳过boom相关预制体
        if (nodeName === "Boom" || nodeName.includes("Boom") || nodeName.includes("boom")) {
            return true;
        }

        // 跳过biaoji相关预制体
        if (nodeName === "Biaoji" || nodeName.includes("Biaoji") || nodeName.includes("biaoji")) {
            return true;
        }

        // 跳过数字预制体
        if (nodeName.match(/^\d+$/) || nodeName.includes("Number")) {
            return true;
        }

        return false;
    }

    // 测试六边形位置计算
    private testHexPositionCalculation() {
      

        // 您提供的实际测量数据
        const testPoints = [
            { q: 0, r: 0, expected: cc.v2(-170, -165), description: "中心位置" },
            { q: 0, r: -1, expected: cc.v2(-220, -81), description: "上方" },
            { q: 1, r: -2, expected: cc.v2(-172, 2), description: "右上" },
            { q: 2, r: -3, expected: cc.v2(-122, 85), description: "右上远" },
            { q: 4, r: -4, expected: cc.v2(23, 171), description: "右上最远" }
        ];

      
        let correctCount = 0;

        testPoints.forEach(point => {
            const calculated = this.getHexWorldPosition(point.q, point.r);
            const errorX = Math.abs(calculated.x - point.expected.x);
            const errorY = Math.abs(calculated.y - point.expected.y);
            const isCorrect = errorX < 1 && errorY < 1; // 允许1像素误差

            if (isCorrect) {
                correctCount++;
                
            } else {
                
            }
        });



        // 如果不是全部正确，显示详细的数据分析
        if (correctCount < testPoints.length) {
           

            // 分析相邻点的差值
            for (let i = 1; i < testPoints.length; i++) {
                const prev = testPoints[i-1];
                const curr = testPoints[i];
                const deltaQ = curr.q - prev.q;
                const deltaR = curr.r - prev.r;
                const deltaX = curr.expected.x - prev.expected.x;
                const deltaY = curr.expected.y - prev.expected.y;

                
            }
        }

        // 测试一些关键的推算坐标
        
        const extraPoints = [
            { q: 1, r: 0, description: "右侧邻居" },
            { q: -1, r: 0, description: "左侧邻居" },
            { q: 0, r: 1, description: "下方邻居" },
            { q: 1, r: -1, description: "右上邻居" },
            { q: 2, r: -2, description: "应该在(1,-2)和(2,-3)之间" }
        ];

        extraPoints.forEach(point => {
            const calculated = this.getHexWorldPosition(point.q, point.r);
            
        });

        // 验证左右间距（q方向）
       
        const pos1 = this.getHexWorldPosition(0, 0);
        const pos2 = this.getHexWorldPosition(1, 0);
        const actualSpacing = pos2.x - pos1.x;  // 不用绝对值，看方向
        

      
    }

    // 从位置计算六边形坐标
    private getHexCoordinateFromPosition(pos: cc.Vec2): {q: number, r: number} | null {
        // 简化的六边形坐标转换，实际项目中可能需要更精确的算法
        const q = Math.round(pos.x / (this.HEX_SIZE * 1.5));
        const r = Math.round((pos.y - pos.x * Math.tan(Math.PI / 6)) / this.HEX_HEIGHT);
        return {q, r};
    }

    // 计算六边形世界坐标位置（参考联机版Level_S001的实现）
    private getHexWorldPosition(q: number, r: number, isPlayerAvatar: boolean = false): cc.Vec2 {
       

        // 根据当前棋盘类型获取配置
        const config = this.getBoardConfig(this.currentBoardType);
        if (!config) {
            console.error(`❌ 未找到棋盘 ${this.currentBoardType} 的配置`);
            return cc.v2(0, 0);
        }

        // 首先检查是否有精确坐标
        const key = `${q},${r}`;
        if (config.exactCoords.has(key)) {
            const pos = config.exactCoords.get(key);
            
            // 如果是玩家头像预制体，y轴向上偏移
            if (isPlayerAvatar) {
                return cc.v2(pos.x, pos.y + 20);
            }
            return pos;
        }

        // 使用联机版的逻辑：每行有基准点，使用统一步长计算
        let x: number, y: number;

        // 如果有该行的数据，使用统一步长计算
        if (config.rowData.has(r)) {
            const data = config.rowData.get(r);
            x = data.baseX + (q - data.baseQ) * config.uniformStepX;
            y = data.y;

           
        } else {
            // 对于其他行，使用通用的六边形轴线坐标系公式
            console.warn(`⚠️ 没有r=${r}行的精确数据，使用通用公式`);
            const stepXR = -config.uniformStepX / 2;
            const stepYR = 74;

            x = config.baseX + q * config.uniformStepX + r * stepXR;
            y = config.baseY - r * stepYR;
        }

       

        // 如果是玩家头像预制体，y轴向上偏移
        if (isPlayerAvatar) {
            y += 20;
        }

        return cc.v2(x, y);
    }

    // 获取棋盘配置（参考联机版Level_S001的实现）
    private getBoardConfig(boardType: string) {
        const configs = new Map();

        // Level_S001 (hexBoard1) - 第5关，您最开始给的数据
        configs.set("hexBoard1", {
            baseX: -170, baseY: -165,
            uniformStepX: 97,
            exactCoords: new Map([
                ["0,0", cc.v2(-170, -165)],
                ["0,-1", cc.v2(-220, -81)],
                ["1,-2", cc.v2(-172, 2)],
                ["2,-3", cc.v2(-122, 85)],
                ["4,-4", cc.v2(23, 171)]
            ]),
            rowData: new Map([
                [0, { baseQ: 0, baseX: -170, y: -165 }],     // r=0行：基准点(0,0) → (-170, -165)
                [-1, { baseQ: 0, baseX: -220, y: -81 }],     // r=-1行：基准点(0,-1) → (-220, -81)
                [-2, { baseQ: 1, baseX: -172, y: 2 }],       // r=-2行：基准点(1,-2) → (-172, 2)
                [-3, { baseQ: 2, baseX: -122, y: 85 }],      // r=-3行：基准点(2,-3) → (-122, 85)
                [-4, { baseQ: 4, baseX: 23, y: 171 }]        // r=-4行：基准点(4,-4) → (23, 171)
            ])
        });

        // Level_S002 (hexBoard2) - 第10关
        configs.set("hexBoard2", {
            baseX: 0, baseY: -293,
            uniformStepX: 98,
            exactCoords: new Map([
                ["0,0", cc.v2(0, -293)],
                ["0,-1", cc.v2(-50, -209)],
                ["0,-2", cc.v2(-100, -125)],
                ["0,-3", cc.v2(-150, -42)],
                ["1,-4", cc.v2(-100, 44)],
                ["2,-5", cc.v2(-50, 127)],
                ["2,-6", cc.v2(-100, 210)],
                ["3,-7", cc.v2(-50, 293)]
            ]),
            rowData: new Map([
                [0, { baseQ: 0, baseX: 0, y: -293 }],        // r=0行：基准点(0,0) → (0, -293)
                [-1, { baseQ: 0, baseX: -50, y: -209 }],     // r=-1行：基准点(0,-1) → (-50, -209)
                [-2, { baseQ: 0, baseX: -100, y: -125 }],    // r=-2行：基准点(0,-2) → (-100, -125)
                [-3, { baseQ: 0, baseX: -150, y: -42 }],     // r=-3行：基准点(0,-3) → (-150, -42)
                [-4, { baseQ: 1, baseX: -100, y: 44 }],      // r=-4行：基准点(1,-4) → (-100, 44)
                [-5, { baseQ: 2, baseX: -50, y: 127 }],      // r=-5行：基准点(2,-5) → (-50, 127)
                [-6, { baseQ: 2, baseX: -100, y: 210 }],     // r=-6行：基准点(2,-6) → (-100, 210)
                [-7, { baseQ: 3, baseX: -50, y: 293 }]       // r=-7行：基准点(3,-7) → (-50, 293)
            ])
        });

        // Level_S003 (hexBoard3) - 第15关
        configs.set("hexBoard3", {
            baseX: -146, baseY: -250,
            uniformStepX: 98,
            exactCoords: new Map([
                ["0,0", cc.v2(-146, -250)],
                ["1,1", cc.v2(0, -336)],
                ["0,-1", cc.v2(-196, -168)],
                ["1,-2", cc.v2(-146, -85)],
                ["2,-3", cc.v2(-99, -1)],      // 更新坐标：(-198, -1) → (-99, -1)
                ["1,-4", cc.v2(-246, 84)],
                ["1,-5", cc.v2(-293, 167)],
                ["2,-6", cc.v2(-246, 251)],
                ["3,-7", cc.v2(-196, 336)]
            ]),
            rowData: new Map([
                [1, { baseQ: 1, baseX: 0, y: -336 }],        // r=1行：基准点(1,1) → (0, -336)
                [0, { baseQ: 0, baseX: -146, y: -250 }],     // r=0行：基准点(0,0) → (-146, -250)
                [-1, { baseQ: 0, baseX: -196, y: -168 }],    // r=-1行：基准点(0,-1) → (-196, -168)
                [-2, { baseQ: 1, baseX: -146, y: -85 }],     // r=-2行：基准点(1,-2) → (-146, -85)
                [-3, { baseQ: 2, baseX: -99, y: -1 }],       // r=-3行：基准点(2,-3) → (-99, -1) 更新
                [-4, { baseQ: 1, baseX: -246, y: 84 }],      // r=-4行：基准点(1,-4) → (-246, 84)
                [-5, { baseQ: 1, baseX: -293, y: 167 }],     // r=-5行：基准点(1,-5) → (-293, 167)
                [-6, { baseQ: 2, baseX: -246, y: 251 }],     // r=-6行：基准点(2,-6) → (-246, 251)
                [-7, { baseQ: 3, baseX: -196, y: 336 }]      // r=-7行：基准点(3,-7) → (-196, 336)
            ])
        });

        // Level_S004 (hexBoard4) - 第20关，同联机版
        configs.set("hexBoard4", {
            baseX: -300, baseY: -258,
            uniformStepX: 86,
            exactCoords: new Map([
                ["0,0", cc.v2(-300, -258)],
                ["1,-1", cc.v2(-258, -184)],
                ["1,-2", cc.v2(-300, -108)],
                ["2,-3", cc.v2(-258, -36)],
                ["2,-4", cc.v2(-300, 37)],
                ["3,-5", cc.v2(-258, 110)],
                ["3,-6", cc.v2(-300, 185)],
                ["4,-7", cc.v2(-258, 260)]
            ]),
            rowData: new Map([
                [0, { baseQ: 0, baseX: -300, y: -258 }],
                [-1, { baseQ: 1, baseX: -258, y: -184 }],
                [-2, { baseQ: 1, baseX: -300, y: -108 }],
                [-3, { baseQ: 2, baseX: -258, y: -36 }],
                [-4, { baseQ: 2, baseX: -300, y: 37 }],
                [-5, { baseQ: 3, baseX: -258, y: 110 }],
                [-6, { baseQ: 3, baseX: -300, y: 185 }],
                [-7, { baseQ: 4, baseX: -258, y: 260 }]
            ])
        });

        // Level_S005 (hexBoard5) - 第25关，预制体scale改为0.8
        configs.set("hexBoard5", {
            baseX: -257, baseY: -293,
            uniformStepX: 85.5,  // 左右间距86
            scale: 0.8,  // 预制体缩放0.8
            exactCoords: new Map([
                ["0,0", cc.v2(-257, -293)],
                ["0,-1", cc.v2(-300, -219)],
                ["1,-2", cc.v2(-257, -146)],
                ["1,-3", cc.v2(-300, -74)],
                ["2,-4", cc.v2(-257, 0)],
                ["2,-5", cc.v2(-300, 74)],
                ["3,-6", cc.v2(-257, 146)],
                ["3,-7", cc.v2(-300, 219)],
                ["4,-8", cc.v2(-257, 293)]
            ]),
            rowData: new Map([
                [0, { baseQ: 0, baseX: -257, y: -293 }],
                [-1, { baseQ: 0, baseX: -300, y: -219 }],
                [-2, { baseQ: 1, baseX: -257, y: -146 }],
                [-3, { baseQ: 1, baseX: -300, y: -74 }],
                [-4, { baseQ: 2, baseX: -257, y: 0 }],
                [-5, { baseQ: 2, baseX: -300, y: 74 }],
                [-6, { baseQ: 3, baseX: -257, y: 146 }],
                [-7, { baseQ: 3, baseX: -300, y: 219 }],
                [-8, { baseQ: 4, baseX: -257, y: 293 }]
            ])
        });

        // Level_S006 (hexBoard6) - 第30关，预制体scale改为0.8
        configs.set("hexBoard6", {
            baseX: -313, baseY: -298,
            uniformStepX: 78,
            scale: 0.8,  // 预制体缩放0.8
            exactCoords: new Map([
                ["0,0", cc.v2(-313, -298)],
                ["1,-1", cc.v2(-274, -233)],
                ["1,-2", cc.v2(-313, -165)],
                ["2,-3", cc.v2(-274, -99)],
                ["2,-4", cc.v2(-313, -34)],
                ["3,-5", cc.v2(-274, 34)],
                ["3,-6", cc.v2(-313, 96)],
                ["4,-7", cc.v2(-274, 165)],
                ["4,-8", cc.v2(-313, 226)],
                ["5,-9", cc.v2(-274, 300)]
            ]),
            rowData: new Map([
                [0, { baseQ: 0, baseX: -313, y: -298 }],
                [-1, { baseQ: 1, baseX: -274, y: -233 }],
                [-2, { baseQ: 1, baseX: -313, y: -165 }],
                [-3, { baseQ: 2, baseX: -274, y: -99 }],
                [-4, { baseQ: 2, baseX: -313, y: -34 }],
                [-5, { baseQ: 3, baseX: -274, y: 34 }],
                [-6, { baseQ: 3, baseX: -313, y: 96 }],
                [-7, { baseQ: 4, baseX: -274, y: 165 }],
                [-8, { baseQ: 4, baseX: -313, y: 226 }],
                [-9, { baseQ: 5, baseX: -274, y: 300 }]
            ])
        });

        return configs.get(boardType);
    }

    // 为六边形格子设置触摸事件
    private setupHexGridTouchEvents(gridNode: cc.Node, q: number, r: number) {
       

        // 确保节点可以接收触摸事件
        if (!gridNode.getComponent(cc.Button)) {
            // 如果没有Button组件，添加一个
            const button = gridNode.addComponent(cc.Button);
            button.transition = cc.Button.Transition.NONE; // 不需要视觉反馈
          
        }

        // 移除现有的触摸事件监听器
        gridNode.off(cc.Node.EventType.TOUCH_END);
        gridNode.off(cc.Node.EventType.TOUCH_START);
        gridNode.off(cc.Node.EventType.TOUCH_CANCEL);

        // 添加点击事件监听器
        gridNode.on(cc.Node.EventType.TOUCH_END, (event: cc.Event.EventTouch) => {
           
            this.onHexGridClick(q, r, event);
        }, this);

        // 添加长按事件监听器
        this.setupLongPressEvent(gridNode, q, r);

        
    }

    // 设置长按事件
    private setupLongPressEvent(gridNode: cc.Node, q: number, r: number) {
        let touchStartTime = 0;
        let longPressTriggered = false;
        const LONG_PRESS_DURATION = 500; // 500毫秒长按

        gridNode.on(cc.Node.EventType.TOUCH_START, () => {
            touchStartTime = Date.now();
            longPressTriggered = false;
            
            // 设置长按定时器
            this.scheduleOnce(() => {
                if (!longPressTriggered && (Date.now() - touchStartTime) >= LONG_PRESS_DURATION) {
                    longPressTriggered = true;
                    this.onHexGridLongPress(q, r);
                }
            }, LONG_PRESS_DURATION / 1000);
        }, this);

        gridNode.on(cc.Node.EventType.TOUCH_END, () => {
            longPressTriggered = true; // 防止长按事件触发
        }, this);

        gridNode.on(cc.Node.EventType.TOUCH_CANCEL, () => {
            longPressTriggered = true; // 防止长按事件触发
        }, this);
    }

    // 六边形格子点击事件 - 发送挖掘操作
    private onHexGridClick(q: number, r: number, _event?: cc.Event.EventTouch) {
       

        // 检查坐标是否有效
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn(`❌ 无效的六边形坐标: (${q}, ${r})`);
            return;
        }

        const key = this.getHexKey(q, r);
        const gridData = this.hexGridData.get(key);

        // 检查该位置是否已经有预制体
        if (gridData && gridData.hasPlayer) {
            console.warn(`⚠️ 格子(${q}, ${r})已有预制体`);
            return;
        }

        // 防重复点击检查
        const currentTime = Date.now();
        const positionKey = `${q},${r}`;
        if (currentTime - this.lastClickTime < this.CLICK_COOLDOWN &&
            this.lastClickPosition === positionKey) {
            console.warn("点击过于频繁，忽略本次点击");
            return;
        }

        this.lastClickTime = currentTime;
        this.lastClickPosition = positionKey;

       

        // 发送LevelClickBlock消息 (action = 1 表示挖掘)
        this.sendLevelClickBlock(q, r, 1);
    }

    // 六边形格子长按事件 - 发送标记操作
    private onHexGridLongPress(q: number, r: number) {
        // 检查坐标是否有效
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn(`❌ 无效的六边形坐标: (${q}, ${r})`);
            return;
        }

        const key = this.getHexKey(q, r);
        const gridData = this.hexGridData.get(key);

        // 检查该位置是否已经有biaoji预制体
        if (this.hasBiaojiAt(q, r)) {
            // 如果已经有biaoji，则删除它（本地立即处理）
            this.removeBiaojiAt(q, r);
            // 发送取消标记消息
            this.sendLevelClickBlock(q, r, 2);
        } else if (!gridData || !gridData.hasPlayer) {
            // 如果没有任何预制体，则生成biaoji（本地立即处理）
            this.createBiaojiPrefab(q, r);
            // 发送标记消息
            this.sendLevelClickBlock(q, r, 2);
        } else {
            // 如果有其他类型的预制体（如数字、boom），则不处理
            console.warn(`格子(${q}, ${r})已有其他预制体，无法标记`);
        }
    }

    // 检查六边形坐标是否有效
    public isValidHexCoordinate(q: number, r: number): boolean {
        const key = this.getHexKey(q, r);
        return this.hexGridData.has(key);
    }

    // 发送LevelClickBlock消息（参考四边形单机控制器）
    private sendLevelClickBlock(q: number, r: number, action: number) {
        const message = {
            q: q,
            r: r,
            action: action  // 1 = 挖掘, 2 = 标记/取消标记
        };

        // 发送WebSocket消息
        WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeLevelClickBlock, message);
    }

    // 检查指定位置是否有biaoji预制体
    private hasBiaojiAt(q: number, r: number): boolean {
        const key = this.getHexKey(q, r);
        const gridData = this.hexGridData.get(key);

        if (!gridData || !gridData.hasPlayer || !gridData.playerNode) {
            return false;
        }

        // 检查节点名称是否为Biaoji
        return gridData.playerNode.name === "HexBiaoji";
    }

    // 移除指定位置的biaoji预制体
    private removeBiaojiAt(q: number, r: number) {
        const key = this.getHexKey(q, r);
        const gridData = this.hexGridData.get(key);

        if (gridData && gridData.hasPlayer && gridData.playerNode &&
            gridData.playerNode.name === "HexBiaoji") {

            // 播放消失动画
            const biaojiNode = gridData.playerNode;
            cc.tween(biaojiNode)
                .to(0.2, { scaleX: 0, scaleY: 0, opacity: 0 })
                .call(() => {
                    biaojiNode.removeFromParent();
                })
                .start();

            // 更新格子数据
            gridData.hasPlayer = false;
            gridData.playerNode = null;
        }
    }

    // 创建biaoji预制体
    private createBiaojiPrefab(q: number, r: number) {
        if (!this.biaojiPrefab) {
            console.error("biaojiPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }

        // 实例化biaoji预制体
        const biaojiNode = cc.instantiate(this.biaojiPrefab);
        biaojiNode.name = "HexBiaoji";

        // 设置位置
        const position = this.getHexWorldPosition(q, r, false);
        biaojiNode.setPosition(position);

        // 添加到棋盘
        this.currentBoardNode.addChild(biaojiNode);

        // 获取当前棋盘的缩放配置
        const config = this.getBoardConfig(this.currentBoardType);
        const targetScale = config?.scale || 1.0;
       

        // 播放出现动画
        biaojiNode.setScale(0);
        cc.tween(biaojiNode)
            .to(0.2, { scaleX: targetScale, scaleY: targetScale }, { easing: 'backOut' })
            .start();

        // 更新格子数据
        const key = this.getHexKey(q, r);
        const gridData = this.hexGridData.get(key);
        if (gridData) {
            gridData.hasPlayer = true;
            gridData.playerNode = biaojiNode;
        }
    }

    // 创建boom预制体
    public createBoomPrefab(q: number, r: number, isCurrentUser: boolean = true) {
        if (!this.boomPrefab) {
            console.error("boomPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }

        // 实例化boom预制体
        const boomNode = cc.instantiate(this.boomPrefab);
        boomNode.name = "HexBoom";

        // 设置位置
        const position = this.getHexWorldPosition(q, r, false);
        boomNode.setPosition(position);

        // 添加到棋盘
        this.currentBoardNode.addChild(boomNode);

        // 获取当前棋盘的缩放配置
        const config = this.getBoardConfig(this.currentBoardType);
        const targetScale = config?.scale || 1.0;
       

        // 播放出现动画
        const bounceScale = targetScale * 1.2; // 弹跳效果，基于目标缩放
        boomNode.setScale(0);
        cc.tween(boomNode)
            .to(0.3, { scaleX: bounceScale, scaleY: bounceScale }, { easing: 'backOut' })
            .to(0.1, { scaleX: targetScale, scaleY: targetScale })
            .start();

        // 只有当前用户点到雷时才播放棋盘震动效果
        if (isCurrentUser) {
            this.playBoardShakeAnimation();
        }

        // 更新格子数据
        const key = this.getHexKey(q, r);
        const gridData = this.hexGridData.get(key);
        if (gridData) {
            gridData.hasPlayer = true;
            gridData.playerNode = boomNode;
        }

        // 设置标记，表示点到了炸弹
        this.hasBombExploded = true;
    }

    // 创建数字预制体
    public createNumberPrefab(q: number, r: number, number: number) {
        // 根据数字选择对应的预制体
        let prefab: cc.Prefab = null;
        switch (number) {
            case 1: prefab = this.boom1Prefab; break;
            case 2: prefab = this.boom2Prefab; break;
            case 3: prefab = this.boom3Prefab; break;
            case 4: prefab = this.boom4Prefab; break;
            case 5: prefab = this.boom5Prefab; break;
            case 6: prefab = this.boom6Prefab; break;
            case 7: prefab = this.boom7Prefab; break;
            case 8: prefab = this.boom8Prefab; break;
            default:
                console.error(`不支持的数字: ${number}`);
                return;
        }

        if (!prefab) {
            console.error(`boom${number}Prefab 预制体未设置，请在编辑器中挂载`);
            return;
        }

        // 实例化数字预制体
        const numberNode = cc.instantiate(prefab);
        numberNode.name = `HexBoom${number}`;

        // 设置位置
        const position = this.getHexWorldPosition(q, r, false);
        numberNode.setPosition(position);

        // 添加到棋盘
        this.currentBoardNode.addChild(numberNode);

        // 获取当前棋盘的缩放配置
        const config = this.getBoardConfig(this.currentBoardType);
        const targetScale = config?.scale || 1.0;
        

        // 播放出现动画
        numberNode.setScale(0);
        cc.tween(numberNode)
            .to(0.2, { scaleX: targetScale, scaleY: targetScale }, { easing: 'backOut' })
            .start();

        // 更新格子数据
        const key = this.getHexKey(q, r);
        const gridData = this.hexGridData.get(key);
        if (gridData) {
            gridData.hasPlayer = true;
            gridData.playerNode = numberNode;
        }
    }

    // 播放棋盘震动动画
    private playBoardShakeAnimation() {
        if (!this.currentBoardNode) {
            return;
        }

        const originalPosition = this.currentBoardNode.getPosition();
        const shakeIntensity = 10;

        cc.tween(this.currentBoardNode)
            .to(0.05, { x: originalPosition.x + shakeIntensity, y: originalPosition.y })
            .to(0.05, { x: originalPosition.x - shakeIntensity, y: originalPosition.y })
            .to(0.05, { x: originalPosition.x, y: originalPosition.y + shakeIntensity })
            .to(0.05, { x: originalPosition.x, y: originalPosition.y - shakeIntensity })
            .to(0.05, { x: originalPosition.x, y: originalPosition.y })
            .start();
    }

    // 隐藏指定位置的六边形小格子（点击时调用）
    public hideHexGridAt(q: number, r: number, immediate: boolean = false) {
      

        if (!this.isValidHexCoordinate(q, r)) {
            console.warn(`❌ 隐藏格子失败：坐标(${q}, ${r})无效`);
            console.warn(`   坐标类型: q=${typeof q}, r=${typeof r}`);
            console.warn(`   有效坐标列表: ${this.validHexCoords.map(c => `(${c.q},${c.r})`).join(', ')}`);
            return;
        }

        // 获取格子节点
        const key = this.getHexKey(q, r);
        const gridNode = this.hexGridNodes.get(key);
        if (gridNode) {
            if (immediate) {
                // 立即隐藏，不播放动画
                gridNode.active = false;
            } else {
                // 播放六边形格子消失动画
                this.playHexGridFallAnimation(gridNode);
            }
        }
    }

    // 播放六边形格子掉落动画
    private playHexGridFallAnimation(gridNode: cc.Node) {
        // 保存原始位置
        const originalPos = gridNode.getPosition();
        gridNode['originalPosition'] = originalPos;

        // 播放掉落动画
        cc.tween(gridNode)
            .parallel(
                cc.tween().to(0.5, { y: originalPos.y - 200 }, { easing: 'sineIn' }),
                cc.tween().to(0.3, { opacity: 0 }),
                cc.tween().to(0.5, { angle: 180 })
            )
            .call(() => {
                gridNode.active = false;
            })
            .start();
    }

    // 显示所有隐藏的格子（游戏结束时调用）
    public showAllHiddenGrids() {
        if (!this.currentBoardNode) {
            console.warn("⚠️ 棋盘节点不存在，无法显示隐藏格子");
            return;
        }

        let totalGrids = 0;
        let restoredGrids = 0;
        let alreadyVisibleGrids = 0;

        // 遍历棋盘的所有子节点
        const children = this.currentBoardNode.children;
        for (let i = 0; i < children.length; i++) {
            const child = children[i];

            // 如果是六边形小格子节点（sixblock_q_r 格式）
            if (child.name.startsWith("sixblock_") || child.name === "hexblock") {
                totalGrids++;

                // 记录恢复前的状态
                const wasHidden = !child.active || child.opacity < 255 || child.scaleX < 1 || child.scaleY < 1;

                if (wasHidden) {
                    restoredGrids++;
                } else {
                    alreadyVisibleGrids++;
                }

                // 停止所有可能正在进行的动画
                child.stopAllActions();

                // 恢复显示状态
                child.active = true;
                child.opacity = 255;
                child.scaleX = 1;
                child.scaleY = 1;
                child.angle = 0; // 重置旋转角度

                // 恢复原始位置（如果有保存的话）
                if (child['originalPosition']) {
                    child.setPosition(child['originalPosition']);
                }

                // 确保格子可以交互
                const button = child.getComponent(cc.Button);
                if (button) {
                    button.enabled = true;
                }
            }
        }

        if (totalGrids === 0) {
            console.warn("⚠️ 没有找到任何六边形格子节点！请检查节点命名是否正确");
        }
    }

    // 清除所有预制体（游戏结束时调用）
    public clearAllPrefabs() {
        if (!this.currentBoardNode) {
            console.warn("⚠️ 棋盘节点不存在，无法清除预制体");
            return;
        }

        let clearedCount = 0;
        const children = this.currentBoardNode.children.slice(); // 创建副本避免遍历时修改数组

        for (let i = 0; i < children.length; i++) {
            const child = children[i];
            const nodeName = child.name;

            // 检查是否是需要清除的游戏预制体
            if (this.isGamePrefab(nodeName)) {
                child.removeFromParent();
                clearedCount++;
            }
        }

        // 重置格子数据中的预制体状态
        this.hexGridData.forEach((gridData) => {
            if (gridData.hasPlayer) {
                gridData.hasPlayer = false;
                gridData.playerNode = null;
            }
        });

        // 重置炸弹爆炸标记
        this.hasBombExploded = false;
    }

    // 判断是否为游戏预制体（需要清除的预制体）
    private isGamePrefab(nodeName: string): boolean {
        // 跳过六边形格子节点
        if (nodeName.startsWith("sixblock_") || nodeName === "hexblock") {
            return false;
        }

        // 炸弹预制体
        if (nodeName === "HexBoom") {
            return true;
        }

        // 数字预制体（HexBoom1, HexBoom2, HexBoom3 等）
        if (nodeName.match(/^HexBoom\d+$/)) {
            return true;
        }

        // 标记预制体
        if (nodeName === "HexBiaoji") {
            return true;
        }

        return false;
    }

    /**
     * 处理点击响应，根据服务器返回的结果更新棋盘
     * @param q 格子q坐标
     * @param r 格子r坐标
     * @param result 点击结果 ("boom" | "safe" | number)
     */
    public handleClickResponse(q: number, r: number, result: any) {
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn(`处理点击响应失败：坐标(${q}, ${r})无效`);
            return;
        }

        // 如果格子上有biaoji预制体，先移除它
        if (this.hasBiaojiAt(q, r)) {
            // 直接移除，不播放动画
            const gridData = this.hexGridData.get(this.getHexKey(q, r));
            if (gridData && gridData.playerNode) {
                gridData.playerNode.removeFromParent();
                gridData.playerNode = null;
            }
        }

        // 标记格子已被处理，防止重复点击
        const key = this.getHexKey(q, r);
        const gridData = this.hexGridData.get(key);
        if (gridData) {
            gridData.hasPlayer = true;
        }

        // 使用连锁动画的方式处理单个格子，保持一致性
        this.playGridDisappearAnimation(q, r, result);
    }

    /**
     * 批量处理连锁反应的格子
     * @param revealedGrids 被揭开的格子列表，支持 {q,r} 或 {x,y} 格式
     */
    public handleChainReaction(revealedGrids: Array<any>) {
        if (!revealedGrids || revealedGrids.length === 0) {
            return;
        }

       

        // 同时播放所有连锁格子的消失动画，不使用延迟
        revealedGrids.forEach((block, index) => {
          

            // 处理坐标映射：服务器可能返回 x,y 格式
            let coordQ: number, coordR: number, neighborMines: any;

            if (block.q !== undefined && block.r !== undefined) {
                // 标准六边形坐标格式
                coordQ = block.q;
                coordR = block.r;
                neighborMines = block.neighborMines !== undefined ? block.neighborMines : block.result;
               
            } else if (block.x !== undefined && block.y !== undefined) {
                // 服务器返回x,y格式，映射为六边形坐标
                coordQ = block.x;  // x 就是 q
                coordR = block.y;  // y 就是 r
                neighborMines = block.neighborMines !== undefined ? block.neighborMines : block.result;
               
            } else {
                console.error(`   ❌ 格子${index + 1}: 无效的坐标数据:`, block);
                console.error(`      可用字段: ${Object.keys(block).join(', ')}`);
                return;
            }

            // 验证坐标是否为有效数字
            if (typeof coordQ !== 'number' || typeof coordR !== 'number' ||
                isNaN(coordQ) || isNaN(coordR)) {
                console.error(`   ❌ 格子${index + 1}: 坐标不是有效数字: q=${coordQ}, r=${coordR}`);
                return;
            }

            // 立即播放动画，不延迟
            
            this.playGridDisappearAnimation(coordQ, coordR, neighborMines);
        });
    }

    // 播放格子消失动画并更新显示
    private playGridDisappearAnimation(q: number, r: number, result: any) {
        // 先隐藏格子
        this.hideHexGridAt(q, r, false);

        // 延迟显示结果，让格子消失动画先播放
        this.scheduleOnce(() => {
            this.updateNeighborMinesDisplay(q, r, result);
        }, 0.3);
    }

    /**
     * 更新指定位置的neighborMines显示（使用boom数字预制体）
     * @param q 格子q坐标
     * @param r 格子r坐标
     * @param neighborMines 周围地雷数量或结果类型
     */
    public updateNeighborMinesDisplay(q: number, r: number, neighborMines: any) {
        if (neighborMines === "boom" || neighborMines === "mine") {
            // 踩到地雷，生成boom预制体并触发震动
            this.createBoomPrefab(q, r, true); // true表示是当前用户踩到的雷，需要震动
            // 设置标记，表示点到了炸弹
            this.hasBombExploded = true;
        } else if (typeof neighborMines === "number" && neighborMines > 0) {
            // 显示数字
            this.createNumberPrefab(q, r, neighborMines);
        } else {
            // 如果是0、"safe"或其他，则不显示任何预制体
        }
    }

    /**
     * 处理ExtendLevelInfo消息（游戏开始时调用）
     */
    public onExtendLevelInfo() {
        this.showAllHiddenGrids();
        this.clearAllPrefabs();
    }

    /**
     * 处理ExtendLevelInfo断线重连（恢复游戏状态）
     * @param levelInfo 关卡信息响应数据
     */
    public onExtendLevelInfoReconnect(levelInfo: any) {
        console.log("HexSingleChessBoardController: 处理断线重连，恢复游戏状态");

        // 清理当前状态
        this.clearAllPrefabs();
        this.showAllHiddenGrids();

        // 如果有地图状态信息，恢复棋盘状态
        if (levelInfo.mineMap) {
            this.restoreBoardState(levelInfo.mineMap);
        }
    }

    /**
     * 恢复棋盘状态（断线重连时使用）
     * @param mineMap 地图状态信息
     */
    private restoreBoardState(mineMap: any) {
        console.log("HexSingleChessBoardController: 恢复棋盘状态", mineMap);

        // 恢复已挖掘的方块
        if (mineMap.revealedBlocks && Array.isArray(mineMap.revealedBlocks)) {
            console.log("恢复已挖掘的方块数量:", mineMap.revealedBlocks.length);
            mineMap.revealedBlocks.forEach((block: any) => {
                // 六边形地图使用q和r坐标，如果没有则使用x和y
                const q = block.q !== undefined ? block.q : block.x;
                const r = block.r !== undefined ? block.r : block.y;
                const neighborMines = block.neighborMines;

                if (this.isValidHexCoordinate(q, r)) {
                    console.log(`恢复已挖掘六边形方块: (${q}, ${r}), 周围地雷数: ${neighborMines}`);

                    // 立即隐藏格子（不播放动画）
                    this.hideHexGridAt(q, r, true);

                    // 延迟显示挖掘结果，确保格子先隐藏
                    this.scheduleOnce(() => {
                        // 显示挖掘结果
                        if (neighborMines > 0) {
                            this.createNumberPrefab(q, r, neighborMines);
                        }
                        // 如果neighborMines为0，则不显示任何预制体（空白区域）
                    }, 0.1);

                    // 标记格子已被处理
                    const key = `${q},${r}`;
                    const gridData = this.hexGridData.get(key);
                    if (gridData) {
                        gridData.hasPlayer = true;
                    }
                }
            });
        }

        // 恢复已标记的方块
        if (mineMap.markedBlocks && Array.isArray(mineMap.markedBlocks)) {
            console.log("恢复已标记的方块数量:", mineMap.markedBlocks.length);
            mineMap.markedBlocks.forEach((block: any) => {
                // 六边形地图使用q和r坐标，如果没有则使用x和y
                const q = block.q !== undefined ? block.q : block.x;
                const r = block.r !== undefined ? block.r : block.y;

                if (this.isValidHexCoordinate(q, r)) {
                    console.log(`恢复已标记六边形方块: (${q}, ${r})`);
                    // 创建标记预制体
                    this.createBiaojiPrefab(q, r);
                }
            });
        }
    }

    /**
     * 处理LevelGameEnd消息（游戏结束时调用）
     * 注意：不清理任何数据，保持玩家的游玩痕迹
     */
    public onLevelGameEnd() {
        // 不显示隐藏的格子，保持当前状态
        // 不清理预制体，不重置格子状态，保持游戏结果显示
        // 让玩家可以看到自己的标记（biaoji）、挖掘结果（数字、boom）等
    }

    /**
     * 重新初始化棋盘数据（仅在开始新游戏时调用）
     */
    private reinitializeBoardData() {
        // 重置hexGridData中的预制体状态
        this.hexGridData.forEach((gridData) => {
            gridData.hasPlayer = false;
            gridData.playerNode = null;
        });

        // 重置炸弹爆炸标记
        this.hasBombExploded = false;
    }

    /**
     * 禁用所有格子的触摸事件（游戏结束时调用）
     */
    public disableAllGridTouch() {
       

        this.hexGridNodes.forEach((gridNode, key) => {
            if (gridNode && cc.isValid(gridNode)) {
                // 移除所有触摸事件监听器
                gridNode.off(cc.Node.EventType.TOUCH_END);
                gridNode.off(cc.Node.EventType.TOUCH_START);
                gridNode.off(cc.Node.EventType.TOUCH_CANCEL);

                // 禁用Button组件（如果有的话）
                const button = gridNode.getComponent(cc.Button);
                if (button) {
                    button.enabled = false;
                }
            }
        });

       
    }

    /**
     * 检查是否点到了炸弹
     * @returns 是否点到了炸弹
     */
    public hasBombExplodedInThisGame(): boolean {
        return this.hasBombExploded;
    }

    /**
     * 重置棋盘状态（清理所有预制体和格子状态）
     */
    public resetBoard() {
      

        // 显示所有隐藏的格子
        this.showAllHiddenGrids();

        // 清除所有预制体
        this.clearAllPrefabs();

        // 重新初始化棋盘数据
        this.reinitializeBoardData();

       
    }

    /**
     * 启用所有格子的触摸事件
     */
    public enableAllGridTouch() {
       
        this.enableTouchForExistingGrids();
    }

    /**
     * 创建自定义预制体（用于调试等特殊用途）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param prefab 预制体
     * @param nodeName 节点名称
     */
    public createCustomPrefab(q: number, r: number, prefab: cc.Prefab, nodeName: string): cc.Node | null {
        if (!prefab) {
            console.error(`自定义预制体未设置: ${nodeName}`);
            return null;
        }

        if (!this.isValidHexCoordinate(q, r)) {
            console.warn(`无效的六边形坐标: (${q}, ${r})`);
            return null;
        }

        // 实例化预制体
        const customNode = cc.instantiate(prefab);
        customNode.name = nodeName;

        // 设置位置
        const position = this.getHexWorldPosition(q, r, false);
        customNode.setPosition(position);

        // 添加到棋盘
        this.currentBoardNode.addChild(customNode);

       
        return customNode;
    }

    /**
     * 重置炸弹爆炸状态（开始新游戏时调用）
     */
    public resetBombExplodedStatus() {
        this.hasBombExploded = false;
       
    }

    /**
     * 获取当前棋盘类型
     */
    public getCurrentBoardType(): string {
        return this.currentBoardType;
    }

    /**
     * 获取当前棋盘配置（六边形版本返回简化信息）
     */
    public getCurrentBoardConfig(): any {
        return {
            boardType: this.currentBoardType,
            gridCount: this.getHexGridCount(),
            hasBombExploded: this.hasBombExploded
        };
    }

    /**
     * 获取棋盘状态信息（调试用）
     */
    public getHexBoardInfo() {
        let info = {
            validHexCoordsCount: this.validHexCoords.length,
            boardNodeChildren: this.currentBoardNode ? this.currentBoardNode.children.length : 0,
            hasPlayerGamePrefab: false, // 单机模式不使用玩家头像预制体
            hasBoardNode: !!this.currentBoardNode,
            currentBoardType: this.currentBoardType,
            hexGridDataSize: this.hexGridData.size,
            hexGridNodesSize: this.hexGridNodes.size,
            hasBombExploded: this.hasBombExploded
        };

        return info;
    }

    /**
     * 获取前端节点的总数量（用于计算炸弹数量）
     */
    public getHexGridCount(): number {
        return this.validHexCoords.length;
    }

    /**
     * 开始新游戏时的重置方法
     */
    public resetForNewGame() {
        // 显示所有隐藏的格子
        this.showAllHiddenGrids();

        // 清除所有预制体
        this.clearAllPrefabs();

        // 重新初始化棋盘数据
        this.reinitializeBoardData();
    }
}
