{"version": 3, "sources": ["assets/scripts/GlobalManagerController.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;;AAEtF,oDAAmD;AACnD,sDAAqD;AACrD,4CAAqD;AAErD,gDAA+C;AAC/C,oDAA+C;AAC/C,oDAAiD;AACjD,4CAA2C;AAC3C,gEAA2D;AAC3D,gEAA4E;AAC5E,mEAA8D;AAC9D,sEAAiE;AACjE,6CAA4C;AAC5C,qDAAoD;AAEpD,yDAA4F;AAC5F,6CAA4C;AAC5C,2DAA0D;AAC1D,qDAAoD;AACpD,0EAAqE;AACrE,+DAA0D;AAC1D,qDAAgD;AAChD,4CAA2C;AAC3C,wCAAuC;AAEjC,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAE5C,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC;AAE3B,IAAY,QAKX;AALD,WAAY,QAAQ;IAChB,yDAAa,CAAA;IACb,iDAAS,CAAA;IACT,iDAAS,CAAA;IACT,mDAAU,CAAA;AACd,CAAC,EALW,QAAQ,GAAR,gBAAQ,KAAR,gBAAQ,QAKnB;AAGD;IAAqD,2CAAY;IAAjE;QAAA,qEAkhBC;QA/gBG,0BAAoB,GAAyB,IAAI,CAAA,CAAC,kBAAkB;QAEpE,2BAAqB,GAA0B,IAAI,CAAA,CAAC,MAAM;QAE1D,cAAQ,GAAY,IAAI,CAAA,CAAE,gBAAgB;QAE1C,qBAAe,GAAoB,IAAI,CAAA,CAAE,WAAW;QAGpD,iBAAW,GAAY,IAAI,CAAA,CAAE,KAAK;QAElC,cAAQ,GAAY,IAAI,CAAA,CAAE,KAAK;QAE/B,cAAQ,GAAY,IAAI,CAAA,CAAE,MAAM;QAEhC,eAAS,GAAY,IAAI,CAAA,CAAE,MAAM;QAIjC,iBAAW,GAAa,QAAQ,CAAC,aAAa,CAAA,CAAC,oBAAoB;QAEnE,2BAAqB,GAA0B,IAAI,CAAA,CAAE,WAAW;QAChE,wBAAkB,GAAuB,IAAI,CAAA,CAAG,WAAW;QAC3D,wBAAkB,GAAuB,IAAI,CAAA,CAAG,WAAW;QAC3D,yBAAmB,GAAwB,IAAI,CAAA,CAAG,WAAW;;QAsf7D,iBAAiB;IACrB,CAAC;IApfG,wCAAM,GAAN;QACI,EAAE,CAAC,SAAS,CAAC,UAAU,CAAC,eAAM,CAAC,OAAO,EAAE,EAAE,CAAC,WAAW,CAAC,CAAC,CAAA,aAAa;QAErE,YAAY;QACZ,IAAM,QAAQ,GAAG,mBAAQ,CAAC,GAAG,CAAC;QAC9B,oBAAoB;QACpB,QAAQ,CAAC,IAAI,EAAE,CAAC;QAEhB,EAAE,CAAC,KAAK,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QAChC,aAAa;QACb,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,iBAAO,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;QAE5B,IAAI,CAAC,YAAY,EAAE,CAAC;QAEpB,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,EAAE;YAC3B,iBAAO,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAA;YACjC,iBAAO,CAAC,QAAQ,CAAC,aAAa,GAAG,IAAI,CAAC;YACtC,OAAO;YACP,6BAAa,CAAC,WAAW,EAAE,CAAC,eAAe,EAAE,CAAC;QAElD,CAAC,EAAE,IAAI,CAAC,CAAC;QAET,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,EAAE;YAC3B,iBAAO,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAA;YACjC,iBAAO,CAAC,QAAQ,CAAC,aAAa,GAAG,KAAK,CAAC;YACvC,gBAAgB;YAChB,6BAAa,CAAC,WAAW,EAAE,CAAC,UAAU,EAAE,CAAC;QAE7C,CAAC,EAAE,IAAI,CAAC,CAAC;QAET,WAAW;QACX,iBAAO,CAAC,KAAK,CAAC,gBAAgB,CAAC,uBAAS,CAAC,WAAW,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;QAChF,eAAe;QACf,iBAAO,CAAC,KAAK,CAAC,gBAAgB,CAAC,uBAAS,CAAC,mBAAmB,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;QACzF,eAAe;QACf,iBAAO,CAAC,KAAK,CAAC,gBAAgB,CAAC,uBAAS,CAAC,cAAc,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QAE/E,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAA;QAC3C,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,+BAAqB,CAAC,CAAA;QACjF,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,4BAAkB,CAAC,CAAA;QACxE,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,4BAAkB,CAAC,CAAA;QACxE,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,6BAAmB,CAAC,CAAA;IAE/E,CAAC;IACS,0CAAQ,GAAlB;IAEA,CAAC;IAES,2CAAS,GAAnB;QACI,iBAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,uBAAS,CAAC,WAAW,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;QACnF,iBAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,uBAAS,CAAC,mBAAmB,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;QAC5F,iBAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,uBAAS,CAAC,cAAc,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;IACtF,CAAC;IAEM,8CAAY,GAAnB;QAAA,iBAgBC;QAfG,iBAAO,CAAC,KAAK,CAAC,SAAS,CAAC,UAAC,MAAW;;YAChC,qBAAS,CAAC,OAAO,CAAC,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YACzD,qBAAS,CAAC,OAAO,CAAC,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACjD,qBAAS,CAAC,OAAO,CAAC,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YACrD,qBAAS,CAAC,OAAO,CAAC,MAAM,SAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,mCAAI,EAAE,CAAC;YACvD,qBAAS,CAAC,OAAO,CAAC,YAAY,eAAG,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,UAAU,0CAAE,YAAY,mCAAI,EAAE,CAAC;YAExE,qBAAS,CAAC,OAAO,CAAC,IAAI,GAAG,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACzD,qBAAS,CAAC,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACjD,qBAAS,CAAC,OAAO,CAAC,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAErD,qBAAS,CAAC,OAAO,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,IAAI,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAC7E,KAAI,CAAC,WAAW,EAAE,CAAC;QAEvB,CAAC,CAAC,CAAC;IACP,CAAC;IAED,6CAAW,GAAX;QACK,IAAI,CAAC,WAAW,EAAE,CAAA,CAAC,OAAO;QAC3B,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE;YAC1B,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC,CAAA;SACxD;aAAM;YACH,eAAe;YACf,4CAA4C;YAC5C,gDAAgD;YAChD,4CAA4C;YAC5C,0CAA0C;YAC1C,oDAAoD;YACpD,QAAQ;YACR,MAAM;YAEN,6BAAa,CAAC,EAAE,GAAG,iCAAiC,CAAA;YACpD,mCAAgB,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,CAAC;SAC5C;IACL,CAAC;IAEM,8CAAY,GAAnB;QACI,IAAI,MAAM,GAAQ,iBAAO,CAAC,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAA,aAAa;QAChF,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE;YAChC,YAAY;YACZ,IAAI,MAAM,CAAC,UAAU,EAAE;gBACnB,YAAY;gBACZ,qBAAS,CAAC,OAAO,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;gBACjD,IAAI,MAAM,CAAC,WAAW,EAAE;oBACpB,IAAI,MAAM,CAAC,WAAW,KAAK,MAAM,EAAE;wBAC/B,qBAAS,CAAC,OAAO,CAAC,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;wBACjD,qBAAS,CAAC,OAAO,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;wBAC7C,qBAAS,CAAC,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;wBACzC,qBAAS,CAAC,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;wBACrC,IAAI,MAAM,CAAC,QAAQ,EAAE;4BACjB,qBAAS,CAAC,OAAO,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;yBAChD;wBACD,IAAI,MAAM,CAAC,MAAM,EAAE;4BACf,qBAAS,CAAC,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;yBAC5C;wBACD,IAAI,MAAM,CAAC,GAAG,EAAE;4BACZ,qBAAS,CAAC,OAAO,CAAC,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;yBAChD;wBACD,qBAAS,CAAC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC;qBACxC;iBACJ;aACJ;SACJ;IACL,CAAC;IAED,6CAAW,GAAX;QAEI,QAAQ,iBAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,EAAE;YACpC,KAAK,sBAAY,CAAC,iBAAiB,EAAE,MAAM;gBACvC,MAAM,CAAC,YAAY,GAAG,sBAAY,CAAC,sBAAsB,CAAA;gBACzD,MAAM;YACV,KAAK,sBAAY,CAAC,kBAAkB,EAAE,MAAM;gBACxC,MAAM,CAAC,YAAY,GAAG,sBAAY,CAAC,uBAAuB,CAAA;gBAC1D,MAAM;YACV,SAAS,OAAO;gBACZ,MAAM,CAAC,YAAY,GAAG,sBAAY,CAAC,YAAY,CAAA;gBAC/C,MAAK;SACZ;QAED,MAAM,CAAC,uBAAuB,EAAE,CAAA;IACpC,CAAC;IAED,0CAAQ,GAAR,UAAS,OAAe;QAEpB,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,OAAO,EAAE;YAC1C,iBAAO,CAAC,KAAK,CAAC,YAAY,EAAE,CAAA;QAEhC,CAAC,CAAC,CAAA;IACN,CAAC;IAGD,UAAU;IACV,+CAAa,GAAb,UAAc,eAAgC;QAC1C,QAAQ,eAAe,CAAC,KAAK,EAAE;YAE3B,KAAK,+BAAa,CAAC,YAAY,EAAC,SAAS;gBACrC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA;gBACvC,IAAI,eAAe,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,EAAE,EAAE,0BAA0B;oBAC7D,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAA,CAAA,gDAAgD;iBACzF;qBAAM,IAAI,eAAe,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,EAAE,EAAE,wBAAwB;oBAClE,wCAAwC;oBACxC,mCAAgB,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,qBAAS,CAAC,0BAA0B,EAAE,EAAE,CAAC,CAAC;iBACpF;gBACD,MAAM;YACV,KAAK,+BAAa,CAAC,sBAAsB,EAAC,SAAS;gBAC/C,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAA;gBAC5B,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC,CAAA;gBACrD,MAAM;YACV,KAAK,+BAAa,CAAC,gBAAgB,EAAC,OAAO;gBACvC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAA;gBAC3B,MAAM;YACV,KAAK,+BAAa,CAAC,oBAAoB,EAAC,WAAW;gBAC/C,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gBACxC,MAAM;YAEV,KAAK,+BAAa,CAAC,kBAAkB,EAAC,QAAQ;gBAC1C,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA;gBACvC,MAAM;YACV,KAAK,+BAAa,CAAC,eAAe,EAAC,WAAW;gBAC1C,eAAe;gBACf,mCAAgB,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,qBAAS,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC;gBACtE,MAAM;YACV,KAAK,+BAAa,CAAC,mBAAmB,EAAC,aAAa;gBAChD,iBAAO,CAAC,WAAW,EAAE,CAAC,IAAI,GAAG,eAAe,CAAC,IAAI,CAAC,IAAI,CAAA;gBACtD,MAAM;SAEb;IAEL,CAAC;IAED,WAAW;IACX,gDAAc,GAAd,UAAe,WAAgC;QAC3C,QAAQ,WAAW,CAAC,IAAI,EAAE;YACtB,KAAK,qBAAS,CAAC,oBAAoB,EAAC,QAAQ;gBACxC,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,CAAA;gBACnC,MAAM;YACV,KAAK,qBAAS,CAAC,cAAc,EAAC,UAAU;gBACpC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC,CAAA;gBAC1D,gBAAgB;gBAChB,6BAAa,CAAC,WAAW,EAAE,CAAC,UAAU,EAAE,CAAC;gBAEzC,MAAM;YACV,KAAK,qBAAS,CAAC,eAAe,EAAC,WAAW;gBACtC,IAAI,WAAW,CAAC,KAAK,IAAI,qBAAS,CAAC,gBAAgB,EAAE;oBACjD,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,MAAM,CAAC,eAAe,CAAC,kBAAkB,CAAC,CAAC,CAAA;oBAC5E,mBAAmB;oBACnB,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA;iBAC1C;gBAED,MAAM;YACV,KAAK,qBAAS,CAAC,eAAe,EAAC,WAAW;gBACtC,IAAI,WAAW,CAAC,KAAK,KAAK,qBAAS,CAAC,gBAAgB,EAAE,EAAC,yBAAyB;oBAC5E,qBAAqB;oBACrB,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA;iBAC1C;gBACD,MAAK;YACT,KAAK,qBAAS,CAAC,aAAa,EAAC,MAAM;gBAC/B,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,MAAM,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,CAAA;gBACtE,MAAM;YACV,KAAK,qBAAS,CAAC,gBAAgB,CAAC,CAAA,QAAQ;YACxC,KAAK,qBAAS,CAAC,gBAAgB,EAAC,MAAM;gBAClC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAE,cAAQ,CAAC,CAAC,CAAA;gBAC3C,MAAM;YACV,KAAK,qBAAS,CAAC,UAAU,EAAC,WAAW;gBACjC,gBAAgB;gBAChB,mCAAgB,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,qBAAS,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAA,CAAA,gBAAgB;gBACtF,MAAK;SAEZ;IAEL,CAAC;IAED,WAAW;IACX,2CAAS,GAAT,UAAU,WAAgC;QACtC,QAAQ,WAAW,CAAC,KAAK,EAAE;YACvB,KAAK,qBAAS,CAAC,eAAe,EAAC,aAAa;gBACxC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAA;gBAC5B,IAAI;gBACJ,mCAAgB,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,qBAAS,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;gBACnE,MAAM;YACV,KAAK,qBAAS,CAAC,YAAY,EAAC,WAAW;gBACnC,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,GAAG,WAAW,CAAC,IAAI,CAAC;gBACtD,IAAI,IAAI,CAAC,WAAW,KAAK,QAAQ,CAAC,aAAa,EAAE;oBAC7C,cAAc;oBACd,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,CAAA;iBACxC;qBAAM;oBACH,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE,CAAA;oBACpC,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAA;oBACtC,mBAAmB;oBACnB,IAAI,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,WAAW,KAAK,QAAQ,CAAC,SAAS,EAAE;wBAC5F,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA,CAAC,OAAO;qBAClD;iBACJ;gBAED,MAAM;YACV,KAAK,qBAAS,CAAC,kBAAkB,EAAE,MAAM;gBACrC,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,gCAAW,CAAC,YAAY,CAAC,CAAA;gBAChE,IAAI,CAAC,kBAAkB,CAAC,eAAe,EAAE,CAAC;gBAC1C,MAAK;YACT,KAAK,qBAAS,CAAC,iBAAiB,EAAE,MAAM;gBACpC,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,gCAAW,CAAC,WAAW,CAAC,CAAA;gBAC/D,MAAK;YACT,KAAK,qBAAS,CAAC,gBAAgB,EAAE,MAAM;gBACnC,IAAI,eAAe,GAAoB,WAAW,CAAC,IAAI,CAAA;gBACvD,uBAAU,CAAC,WAAW,EAAE,CAAC,eAAe,GAAG,eAAe,CAAA,CAAC,QAAQ;gBAEnE,IAAM,KAAK,GAAG,uBAAU,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC,KAAK,CAAC,SAAS,CAAC,UAAC,IAAI,IAAK,OAAA,IAAI,CAAC,MAAM,KAAK,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAlE,CAAkE,CAAC,CAAC,CAAA,IAAI;gBACzJ,oBAAoB;gBACpB,IAAI,KAAK,IAAI,CAAC,CAAC,EAAE;oBACb,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,GAAG,eAAe,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAA;iBACvF;gBAED,uBAAuB;gBACvB,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;gBAErD,IAAI,eAAe,CAAC,QAAQ,KAAK,mBAAQ,CAAC,cAAc,EAAE,EAAC,mBAAmB;oBAC1E,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,CAAA;iBACxC;qBAAM;oBACH,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA,CAAC,YAAY;iBACvD;gBAED,MAAK;YAET,KAAK,qBAAS,CAAC,gBAAgB,EAAC,SAAS;gBACrC,IAAI,gBAAgB,GAAoB,WAAW,CAAC,IAAI,CAAC;gBAEzD,0BAA0B;gBAC1B,IAAI,gBAAgB,CAAC,YAAY,KAAK,IAAI,EAAE;oBACxC,4BAA4B;oBAC5B,iBAAO,CAAC,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;oBAChD,uBAAU,CAAC,WAAW,EAAE,CAAC,eAAe,GAAG,gBAAgB,CAAA,CAAC,QAAQ;oBAEpE,uBAAuB;oBACvB,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC;oBAEtD,SAAS;oBACT,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA;iBAC1C;qBAAM,IAAI,gBAAgB,CAAC,YAAY,KAAK,KAAK,EAAE;oBAChD,iCAAiC;oBACjC,iBAAO,CAAC,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;oBAEpD,UAAU;oBACV,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;oBAEzC,2CAA2C;oBAC3C,IAAI,CAAC,YAAY,CAAC;wBACd,iBAAO,CAAC,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;wBACrD,mCAAgB,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,qBAAS,CAAC,sBAAsB,EAAE,EAAE,CAAC,CAAC;oBACjF,CAAC,EAAE,GAAG,CAAC,CAAC;iBACX;qBAAM;oBACH,oCAAoC;oBACpC,iBAAO,CAAC,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;oBAClD,uBAAU,CAAC,WAAW,EAAE,CAAC,eAAe,GAAG,gBAAgB,CAAA,CAAC,QAAQ;oBAEpE,uBAAuB;oBACvB,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC;oBAEtD,SAAS;oBACT,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA;iBAC1C;gBACD,MAAM;YACV,KAAK,qBAAS,CAAC,gBAAgB,EAAC,WAAW;gBACvC,IAAI,aAAa,GAAQ,WAAW,CAAC,IAAI,CAAC;gBAE1C,qCAAqC;gBACrC,IAAI,aAAa,CAAC,OAAO,KAAK,SAAS,EAAE;oBACrC,0BAA0B;oBAC1B,EAAE,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;oBAC5B,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,EAAE,CAAC,CAAC,MAAM;oBAC5C,IAAI,eAAe,GAAoB;wBACnC,OAAO,EAAE,+BAAa,CAAC,YAAY;wBACnC,MAAM,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,eAAe;qBACxC,CAAA;oBACD,iBAAO,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAS,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;iBAC9D;qBAAM,IAAI,aAAa,CAAC,MAAM,KAAK,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE;oBACpF,qBAAqB;oBACrB,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,EAAE,CAAA,CAAC,MAAM;oBAC3C,IAAI,eAAe,GAAoB;wBACnC,OAAO,EAAE,+BAAa,CAAC,YAAY;wBACnC,MAAM,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,eAAe;qBACxC,CAAA;oBACD,iBAAO,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAS,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;iBAC9D;gBACD,MAAM;YACV,KAAK,qBAAS,CAAC,mBAAmB,EAAC,mBAAmB;gBAClD,uBAAU,CAAC,WAAW,EAAE,CAAC,UAAU,GAAG,WAAW,CAAC,IAAI,CAAC;gBACvD,eAAe;gBACf,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,CAAA;gBACxC,MAAK;YACT,KAAK,qBAAS,CAAC,mBAAmB,EAAC,MAAM;gBACrC,IAAI,YAAY,GAAiB,WAAW,CAAC,IAAI,CAAA;gBACjD,uBAAU,CAAC,WAAW,EAAE,CAAC,UAAU,GAAG,YAAY,CAAC,UAAU,CAAC;gBAC9D,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,YAAY,CAAC,CAAA;gBACrD,MAAK;YACT,KAAK,qBAAS,CAAC,kBAAkB,EAAC,WAAW;gBACzC,IAAI,iBAAiB,GAAsB,WAAW,CAAC,IAAI,CAAC;gBAC5D,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAA;gBACpD,MAAK;YACT,KAAK,qBAAS,CAAC,kBAAkB,EAAC,YAAY;gBAC1C,IAAI,sBAAsB,GAA2B,WAAW,CAAC,IAAI,CAAC;gBACtE,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAA;gBAC7D,MAAK;YACT,KAAK,qBAAS,CAAC,yBAAyB,EAAC,QAAQ;gBAC7C,IAAI,uBAAuB,GAA2B,WAAW,CAAC,IAAI,CAAC;gBACvE,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,uBAAuB,CAAC,CAAA;gBAC9D,MAAM;YACV,KAAK,qBAAS,CAAC,oBAAoB,EAAC,YAAY;gBAC5C,IAAI,aAAa,GAAkB,WAAW,CAAC,IAAI,CAAC;gBAEpD,IAAI,aAAa,CAAC,MAAM,KAAK,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE;oBAC7E,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,MAAM,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,CAAA;oBACnE,iBAAiB;oBACjB,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA;iBAC1C;qBAAM;oBACH,0BAA0B;oBAC1B,IAAI,kBAAkB,GAAG,EAAE,QAAQ,EAAE,aAAa,CAAC,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,CAAA;oBAC/E,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAA;iBACxD;gBACD,MAAM;YACV,KAAK,qBAAS,CAAC,eAAe,EAAC,WAAW;gBACtC,IAAI,QAAQ,GAAa,WAAW,CAAC,IAAI,CAAC;gBAC1C,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,QAAQ,GAAG,QAAQ,CAAA;gBACtD,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE,CAAA;gBACpC,MAAM;YAGV,KAAK,qBAAS,CAAC,iBAAiB,EAAE,KAAK;gBACnC,IAAI,gBAAgB,GAAqB,WAAW,CAAC,IAAI,CAAA;gBACzD,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAA;gBAC3D,MAAK;YAET,KAAK,qBAAS,CAAC,uBAAuB,EAAE,UAAU;gBAC9C,iBAAiB;gBACjB,IAAI,IAAI,CAAC,WAAW,KAAK,QAAQ,CAAC,SAAS,EAAE;oBACzC,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;iBAChE;gBACD,MAAM;YAEV,KAAK,qBAAS,CAAC,0BAA0B,EAAE,UAAU;gBACjD,iBAAiB;gBACjB,IAAI,IAAI,CAAC,WAAW,KAAK,QAAQ,CAAC,SAAS,EAAE;oBACzC,IAAI,CAAC,kBAAkB,CAAC,qBAAqB,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;iBACnE;gBACD,MAAM;YAEV,KAAK,qBAAS,CAAC,qBAAqB,EAAE,UAAU;gBAC5C,iBAAiB;gBACjB,IAAI,IAAI,CAAC,WAAW,KAAK,QAAQ,CAAC,SAAS,EAAE;oBACzC,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;iBAC9D;gBACD,MAAM;YAEV,KAAK,qBAAS,CAAC,6BAA6B,EAAE,YAAY;gBACtD,iBAAiB;gBACjB,IAAI,IAAI,CAAC,WAAW,KAAK,QAAQ,CAAC,SAAS,EAAE;oBACzC,IAAI,CAAC,kBAAkB,CAAC,wBAAwB,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;iBACtE;gBACD,MAAM;YAEV,KAAK,qBAAS,CAAC,qBAAqB,EAAE,YAAY;gBAC9C,iBAAiB;gBACjB,IAAI,IAAI,CAAC,WAAW,KAAK,QAAQ,CAAC,SAAS,EAAE;oBACzC,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;iBAC9D;gBACD,MAAM;YAEV,KAAK,qBAAS,CAAC,0BAA0B,EAAE,MAAM;gBAC7C,IAAI,IAAI,CAAC,WAAW,KAAK,QAAQ,CAAC,SAAS,EAAE;oBACzC,eAAe;oBACf,IAAI,iBAAiB,GAAG,WAAW,CAAC,IAAI,CAAC;oBACzC,IAAI,iBAAiB,EAAE;wBACnB,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,CAAC;qBAC/D;iBACJ;gBACD,MAAM;YAEV,KAAK,qBAAS,CAAC,sBAAsB,EAAE,MAAM;gBACzC,IAAI,IAAI,CAAC,WAAW,KAAK,QAAQ,CAAC,UAAU,EAAE;oBAC1C,eAAe;oBACf,IAAI,aAAa,GAA4B,WAAW,CAAC,IAAI,CAAC;oBAC9D,IAAI,aAAa,EAAE;wBACf,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;qBAC7D;iBACJ;gBACD,MAAM;YAEV,KAAK,qBAAS,CAAC,qBAAqB,EAAE,UAAU;gBAC5C,EAAE,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;gBACvC,EAAE,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;gBAClC,EAAE,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,KAAK,QAAQ,CAAC,UAAU,CAAC,CAAC;gBAC7D,EAAE,CAAC,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC;gBAClC,EAAE,CAAC,GAAG,CAAC,2BAA2B,EAAE,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;gBAEhE,IAAI,IAAI,CAAC,WAAW,KAAK,QAAQ,CAAC,UAAU,EAAE;oBAC1C,sBAAsB;oBACtB,IAAI,YAAY,GAAG,WAAW,CAAC,IAAI,CAAC;oBACpC,EAAE,CAAC,GAAG,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;oBAE9B,2CAA2C;oBAC3C,IAAI,aAAa,GAAG,YAAY,CAAC,KAAK,IAAI,YAAY,CAAC;oBACvD,EAAE,CAAC,GAAG,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC;oBACpC,EAAE,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;oBAE/C,IAAI,aAAa,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;wBAC/C,EAAE,CAAC,GAAG,CAAC,kCAAkC,EAAE,aAAa,CAAC,MAAM,CAAC,CAAC;wBACjE,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;qBAChE;yBAAM;wBACH,EAAE,CAAC,IAAI,CAAC,cAAc,EAAE,aAAa,CAAC,CAAC;qBAC1C;iBACJ;qBAAM;oBACH,EAAE,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;iBAC9C;gBACD,MAAM;SAGb;IACL,CAAC;IAED,SAAS;IACT,gDAAc,GAAd,UAAe,QAAkB;QAC7B,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAA;QAC3B,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,KAAK,CAAA;QAC/B,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAA;QAC5B,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAA;QAC5B,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,KAAK,CAAA;QAE7B,QAAQ,QAAQ,EAAE;YACd,KAAK,QAAQ,CAAC,aAAa;gBACvB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAA;gBAC9B,MAAK;YACT,KAAK,QAAQ,CAAC,SAAS;gBACnB,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAA;gBAC3B,MAAK;YACT,KAAK,QAAQ,CAAC,SAAS;gBACnB,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAA;gBAC3B,MAAK;YACT,KAAK,QAAQ,CAAC,UAAU;gBACpB,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,IAAI,CAAA;gBAC5B,MAAK;SACZ;IAEL,CAAC;IA5gBD;QADC,QAAQ,CAAC,8BAAoB,CAAC;yEACkB;IAEjD;QADC,QAAQ,CAAC,+BAAqB,CAAC;0EACmB;IAEnD;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;6DACM;IAExB;QADC,QAAQ,CAAC,yBAAe,CAAC;oEACa;IAGvC;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;gEACS;IAE3B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;6DACM;IAExB;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;6DACM;IAExB;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACO;IAlBR,uBAAuB;QAD3C,OAAO;OACa,uBAAuB,CAkhB3C;IAAD,8BAAC;CAlhBD,AAkhBC,CAlhBoD,EAAE,CAAC,SAAS,GAkhBhE;kBAlhBoB,uBAAuB;AAohB5C,IAAI,CAAC,SAAS,EAAE;IACZ,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG;QAAA,iBAY/B;QAXG,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC;QAC/C,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,mBAAmB,CAAC;QAE/D,6BAA6B;QAC7B,IAAI,CAAC,YAAY,CAAC;YACd,IAAI,KAAI,CAAC,WAAW,IAAI,KAAI,CAAC,WAAW,CAAC,UAAU,EAAE,EAAE;gBACnD,KAAI,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;aAC3D;YACD,2CAA2C;YAC3C,cAAc;QAClB,CAAC,EAAE,GAAG,CAAC,CAAC;IACZ,CAAC,CAAA;IAED,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG;QAC3B,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC;QAC/C,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,mBAAmB,CAAC;IACnE,CAAC,CAAA;IACD,EAAE,CAAC,KAAK,CAAC,kBAAkB,GAAG,KAAK,CAAC,CAAA,iBAAiB;CACxD", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\nimport { MeshTools } from \"../meshTools/MeshTools\";\nimport { Publish } from \"../meshTools/tools/Publish\";\nimport { MoveType, RoomType } from \"./bean/EnumBean\";\nimport { AcceptInvite, ExtendLevelInfoResponse, IllegalOperation, InviteKickOut, MoveBlockFail, NoticeLeaveInvite, NoticeMoveBlock, NoticeScoreChg, NoticeSettlement, NoticeStartGame, NoticeUserInviteStatus, ObstacleBlock, UserInfo } from \"./bean/GameBean\";\nimport { GlobalBean } from \"./bean/GlobalBean\";\nimport LanguageType from \"./bean/LanguageType\";\nimport { EventType } from \"./common/EventCenter\";\nimport { GameMgr } from \"./common/GameMgr\";\nimport GamePageController from \"./game/GamePageController\";\nimport HallPageController, { HallOrMatch } from \"./hall/HallPageController\";\nimport LevelPageController from \"./level/LevelPageController\";\nimport TopUpDialogController from \"./hall/TopUpDialogController\";\nimport { ErrorCode } from \"./net/ErrorCode\";\nimport { GameServerUrl } from \"./net/GameServerUrl\";\nimport { HttpManager } from \"./net/HttpManager\";\nimport { AutoMessageBean, AutoMessageId, ReceivedMessageBean } from \"./net/MessageBaseBean\";\nimport { MessageId } from \"./net/MessageId\";\nimport { WebSocketManager } from \"./net/WebSocketManager\";\nimport { WebSocketTool } from \"./net/WebSocketTool\";\nimport StartUpPageController from \"./start_up/StartUpPageController\";\nimport TipsDialogController from \"./TipsDialogController\";\nimport ToastController from \"./ToastController\";\nimport { AudioMgr } from \"./util/AudioMgr\";\nimport { Config } from \"./util/Config\";\n\nconst { ccclass, property } = cc._decorator;\n\nwindow.languageName = \"en\";\n\nexport enum PageType {\n    START_UP_PAGE,//启动页面\n    HALL_PAGE,//大厅页面\n    GAME_PAGE,//游戏页面\n    LEVEL_PAGE,//关卡页面\n}\n\n@ccclass\nexport default class GlobalManagerController extends cc.Component {\n\n    @property(TipsDialogController)\n    tipsDialogController: TipsDialogController = null //这个是错误弹窗 只有一个退出按钮\n    @property(TopUpDialogController)\n    topUpDialogController: TopUpDialogController = null //充值弹窗\n    @property(cc.Node)\n    netError: cc.Node = null  //这个是断网的时候展示的转圈的\n    @property(ToastController)\n    toastController: ToastController = null  //toast 的布局\n\n    @property(cc.Node)\n    startUpPage: cc.Node = null  //启动页\n    @property(cc.Node)\n    hallPage: cc.Node = null  //大厅页\n    @property(cc.Node)\n    gamePage: cc.Node = null  //游戏页面\n    @property(cc.Node)\n    levelPage: cc.Node = null  //关卡页面\n\n\n\n    currentPage: PageType = PageType.START_UP_PAGE //当前展示的页面，默认展示的是启动页面\n\n    startUpPageController: StartUpPageController = null  //启动页面的总管理器\n    hallPageController: HallPageController = null   //大厅页面的总管理器\n    gamePageController: GamePageController = null   //游戏页面的总管理器\n    levelPageController: LevelPageController = null   //关卡页面的总管理器\n\n\n    onLoad() {\n        cc.resources.preloadDir(Config.hallRes, cc.SpriteFrame);//提前预加载大厅图片资源\n\n        // 获取音频管理器实例\n        const audioMgr = AudioMgr.ins;\n        // 初始化音频管理器（如果还未初始化）\n        audioMgr.init();\n\n        cc.debug.setDisplayStats(false);\n        //获取URL拼接渠道参数\n        this.getUrlParams();\n        GameMgr.H5SDK.AddAPPEvent();\n\n        this.getAppConfig();\n\n        cc.game.on(cc.game.EVENT_SHOW, () => {\n            GameMgr.Console.Log(\"EVENT_SHOW\")\n            GameMgr.GameData.GameIsInFront = true;\n            // 触发重连\n            WebSocketTool.GetInstance().atOnceReconnect();\n\n        }, this);\n\n        cc.game.on(cc.game.EVENT_HIDE, () => {\n            GameMgr.Console.Log(\"EVENT_HIDE\")\n            GameMgr.GameData.GameIsInFront = false;\n            // 断开WebSocket连接\n            WebSocketTool.GetInstance().disconnect();\n\n        }, this);\n\n        //这里监听程序内消息\n        GameMgr.Event.AddEventListener(EventType.AutoMessage, this.onAutoMessage, this);\n        //这里监听长链接消息（异常）\n        GameMgr.Event.AddEventListener(EventType.ReceiveErrorMessage, this.onErrorMessage, this);\n        //这里监听长链接消息（正常）\n        GameMgr.Event.AddEventListener(EventType.ReceiveMessage, this.onMessage, this);\n\n        this.setCurrentPage(PageType.START_UP_PAGE)\n        this.startUpPageController = this.startUpPage.getComponent(StartUpPageController)\n        this.hallPageController = this.hallPage.getComponent(HallPageController)\n        this.gamePageController = this.gamePage.getComponent(GamePageController)\n        this.levelPageController = this.levelPage.getComponent(LevelPageController)\n\n    }\n    protected onEnable(): void {\n\n    }\n\n    protected onDestroy(): void {\n        GameMgr.Event.RemoveEventListener(EventType.AutoMessage, this.onAutoMessage, this);\n        GameMgr.Event.RemoveEventListener(EventType.ReceiveErrorMessage, this.onErrorMessage, this);\n        GameMgr.Event.RemoveEventListener(EventType.ReceiveMessage, this.onMessage, this);\n    }\n\n    public getAppConfig(): void {\n        GameMgr.H5SDK.GetConfig((config: any) => {\n            MeshTools.Publish.appChannel = String(config.appChannel);\n            MeshTools.Publish.appId = parseInt(config.appId);\n            MeshTools.Publish.gameMode = String(config.gameMode);\n            MeshTools.Publish.roomId = String(config.roomId) ?? \"\";\n            MeshTools.Publish.currencyIcon = config?.gameConfig?.currencyIcon ?? \"\";\n\n            MeshTools.Publish.code = encodeURIComponent(config.code);\n            MeshTools.Publish.userId = String(config.userId);\n            MeshTools.Publish.language = String(config.language);\n\n            MeshTools.Publish.gsp = config.gsp == undefined ? 101 : parseInt(config.gsp);\n            this.getHpptPath();\n\n        });\n    }\n\n    getHpptPath() {\n         this.setLanguage() //先设置语言\n        if (!window.navigator.onLine) {\n            this.showTips(window.getLocalizedStr('NetworkError'))\n        } else {\n            // // 获取游戏服务器地址\n            // HttpManager.Instance.ReqServerUrl(() => {\n            //     let httpUrl: string = GameServerUrl.Http;\n            //     let wsUrl: string = GameServerUrl.Ws;\n            //     if (httpUrl != \"\" || wsUrl != \"\") {\n            //         WebSocketManager.GetInstance().connect();\n            //     }\n            // });\n\n            GameServerUrl.Ws = \"ws://************:2059/acceptor\"\n            WebSocketManager.GetInstance().connect();\n        }   \n    }\n\n    public getUrlParams(): void {\n        let params: any = GameMgr.Utils.GetUrlParams(window.location.href);//获取当前页面的 url\n        if (JSON.stringify(params) != \"{}\") {\n            //@ts-ignore\n            if (params.appChannel) {\n                //@ts-ignore\n                MeshTools.Publish.appChannel = params.appChannel;\n                if (params.isDataByUrl) {\n                    if (params.isDataByUrl === \"true\") {\n                        MeshTools.Publish.appId = parseInt(params.appId);\n                        MeshTools.Publish.gameMode = params.gameMode;\n                        MeshTools.Publish.userId = params.userId;\n                        MeshTools.Publish.code = params.code;\n                        if (params.language) {\n                            MeshTools.Publish.language = params.language;\n                        }\n                        if (params.roomId) {\n                            MeshTools.Publish.roomId = params.roomId;\n                        }\n                        if (params.gsp) {\n                            MeshTools.Publish.gsp = parseInt(params.gsp);\n                        }\n                        MeshTools.Publish.isDataByURL = true;\n                    }\n                }\n            }\n        }\n    }\n\n    setLanguage() {\n\n        switch (Publish.GetInstance().language) {\n            case LanguageType.SimplifiedChinese: //简体中文\n                window.languageName = LanguageType.SimplifiedChinese_type\n                break;\n            case LanguageType.TraditionalChinese: //繁体中文\n                window.languageName = LanguageType.TraditionalChinese_type\n                break;\n            default: //默认是英语\n                window.languageName = LanguageType.English_type\n                break\n        }\n\n        window.refreshAllLocalizedComp()\n    }\n\n    showTips(content: string) {\n\n        this.tipsDialogController.showDialog(content, () => {\n            GameMgr.H5SDK.CloseWebView()\n\n        })\n    }\n\n\n    //程序内的通知消息\n    onAutoMessage(autoMessageBean: AutoMessageBean) {\n        switch (autoMessageBean.msgId) {\n\n            case AutoMessageId.JumpHallPage://跳转进大厅页面\n                this.setCurrentPage(PageType.HALL_PAGE)\n                if (autoMessageBean.data.type === 1) { //1是启动页面跳转的 ，2 是玩家主动离开游戏房间\n                    this.hallPageController.LoginSuccess()//因为初始进来的时候是启动页面，大厅页面是隐藏状态，下面发送的消息收不到，所以需要主动调用一次\n                } else if (autoMessageBean.data.type === 2) { //2 是玩家主动离开游戏房间，需要更新关卡进度\n                    // 单机模式退出关卡后，请求ExtendLevelProgress更新关卡信息\n                    WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeExtendLevelProgress, {});\n                }\n                break;\n            case AutoMessageId.ReconnectionFailureMsg://长链接重连失败\n                this.netError.active = false\n                this.showTips(window.getLocalizedStr('NetworkError'))\n                break;\n            case AutoMessageId.LinkExceptionMsg://长链接异常\n                this.netError.active = true\n                break;\n            case AutoMessageId.GameRouteNotFoundMsg://游戏线路异常的通知\n                this.showTips(autoMessageBean.data.code)\n                break;\n\n            case AutoMessageId.SwitchGameSceneMsg://切换游戏场景\n                this.setCurrentPage(PageType.GAME_PAGE)\n                break;\n            case AutoMessageId.WalletUpdateMsg://更新金豆余额的通知\n                //发送获取更新用户信息的消息\n                WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeUserInfo, {});\n                break;\n            case AutoMessageId.ServerCodeUpdateMsg://更新 code 的通知\n                Publish.GetInstance().code = autoMessageBean.data.code\n                break;\n\n        }\n\n    }\n\n    //长链接消息(异常)\n    onErrorMessage(messageBean: ReceivedMessageBean) {\n        switch (messageBean.code) {\n            case ErrorCode.ErrInvalidInviteCode://无效的邀请码\n                this.hallPageController.joinError()\n                break;\n            case ErrorCode.ErrRequestUser://获取用户信息失败\n                this.showTips(window.getLocalizedStr('GetUserInfoFailed'))\n                // 断开WebSocket连接\n                WebSocketTool.GetInstance().disconnect();\n\n                break;\n            case ErrorCode.ErrNotFoundRoom://没有找到指定的房间\n                if (messageBean.msgId != MessageId.MsgTypeMoveBlock) {\n                    this.toastController.showContent(window.getLocalizedStr('RoomDoesNotExist'))\n                    //没有找到房间 就直接返回到大厅页面\n                    this.setCurrentPage(PageType.HALL_PAGE)\n                }\n\n                break;\n            case ErrorCode.ErrNotFoundUser:// 没有找到玩家信息\n                if (messageBean.msgId === MessageId.MsgTypeEnterRoom) {//只有在这个messageId下 才会踢出到大厅\n                    //没有找到玩家信息 就直接返回到大厅页面\n                    this.setCurrentPage(PageType.HALL_PAGE)\n                }\n                break\n            case ErrorCode.ErrEnoughUser://房间已满\n                this.toastController.showContent(window.getLocalizedStr('RoomIsFull'))\n                break;\n            case ErrorCode.ErrChangeBalance://扣除金币失败\n            case ErrorCode.ErrNotEnoughCoin://金币不足\n                this.topUpDialogController.show( () => { })\n                break;\n            case ErrorCode.ErrPlaying://玩家已经在游戏中了\n                //执行一遍 enterroom\n                WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeEnterRoom, {})//重连进来的 玩家请求进入房间\n                break\n\n        }\n\n    }\n\n    //长链接消息(正常)\n    onMessage(messageBean: ReceivedMessageBean) {\n        switch (messageBean.msgId) {\n            case MessageId.MsgTypeCreateWs://创建ws连接 成功  \n                this.netError.active = false\n                //登录\n                WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeLogin, {});\n                break;\n            case MessageId.MsgTypeLogin://获取登录数据并存储\n                GlobalBean.GetInstance().loginData = messageBean.data;\n                if (this.currentPage === PageType.START_UP_PAGE) {\n                    //判断当前是否是在启动页面\n                    this.startUpPageController.setLogin()\n                } else {\n                    this.hallPageController.updateGold()\n                    this.hallPageController.LoginSuccess()\n                    //没有在游戏中，但是还停留在游戏页面\n                    if (GlobalBean.GetInstance().loginData.roomId === 0 && this.currentPage === PageType.GAME_PAGE) {\n                        this.setCurrentPage(PageType.HALL_PAGE) //返回到大厅\n                    }\n                }\n\n                break;\n            case MessageId.MsgTypePairRequest: //开始匹配\n                this.hallPageController.setHallOrMatch(HallOrMatch.MATCH_PARENT)\n                this.hallPageController.createMatchView();\n                break\n            case MessageId.MsgTypeCancelPair: //取消匹配\n                this.hallPageController.setHallOrMatch(HallOrMatch.HALL_PARENT)\n                break\n            case MessageId.MsgTypeGameStart: //游戏开始\n                let noticeStartGame: NoticeStartGame = messageBean.data\n                GlobalBean.GetInstance().noticeStartGame = noticeStartGame //存储游戏数据\n\n                const index = GlobalBean.GetInstance().noticeStartGame.users.findIndex((item) => item.userId === GlobalBean.GetInstance().loginData.userInfo.userId);//搜索\n                //把游戏开始之后最新的金币余额进行赋值\n                if (index != -1) {\n                    GlobalBean.GetInstance().loginData.userInfo.coin = noticeStartGame.users[index].coin\n                }\n\n                // 处理游戏开始数据，获取炸弹数量和地图类型\n                this.gamePageController.onGameStart(noticeStartGame);\n\n                if (noticeStartGame.roomType === RoomType.RoomTypeCommon) {// 房间类型 1-普通场 2-私人场\n                    this.hallPageController.setGameData()\n                } else {\n                    this.setCurrentPage(PageType.GAME_PAGE) //开始游戏进入游戏页面\n                }\n\n                break\n\n            case MessageId.MsgTypeEnterRoom://重连的游戏数据\n                let noticeStartGame2: NoticeStartGame = messageBean.data;\n\n                // 检查isOnlineMode字段来判断游戏模式\n                if (noticeStartGame2.isOnlineMode === true) {\n                    // 联机模式断线重连，使用EnterRoom的数据恢复\n                    GameMgr.Console.Log(\"联机模式断线重连，使用EnterRoom数据恢复\");\n                    GlobalBean.GetInstance().noticeStartGame = noticeStartGame2 //存储游戏数据\n\n                    // 处理重连游戏数据，获取炸弹数量和地图类型\n                    this.gamePageController.onGameStart(noticeStartGame2);\n\n                    //跳转进游戏页面\n                    this.setCurrentPage(PageType.GAME_PAGE)\n                } else if (noticeStartGame2.isOnlineMode === false) {\n                    // 关卡模式断线重连，需要主动请求ExtendLevelInfo\n                    GameMgr.Console.Log(\"关卡模式断线重连，主动请求ExtendLevelInfo\");\n\n                    // 切换到关卡页面\n                    this.setCurrentPage(PageType.LEVEL_PAGE);\n\n                    // 延迟发送ExtendLevelInfo请求（不带参数，服务端会自动检测断线重连）\n                    this.scheduleOnce(() => {\n                        GameMgr.Console.Log(\"发送ExtendLevelInfo请求进行关卡模式断线重连\");\n                        WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeExtendLevelInfo, {});\n                    }, 0.1);\n                } else {\n                    // 没有isOnlineMode字段，按照原来的逻辑处理（兼容旧版本）\n                    GameMgr.Console.Log(\"未检测到isOnlineMode字段，按照原逻辑处理\");\n                    GlobalBean.GetInstance().noticeStartGame = noticeStartGame2 //存储游戏数据\n\n                    // 处理重连游戏数据，获取炸弹数量和地图类型\n                    this.gamePageController.onGameStart(noticeStartGame2);\n\n                    //跳转进游戏页面\n                    this.setCurrentPage(PageType.GAME_PAGE)\n                }\n                break;\n            case MessageId.MsgTypeLeaveRoom:// 玩家主动离开房间\n                let leaveRoomData: any = messageBean.data;\n\n                // 检查是否是关卡游戏的LeaveRoom响应（包含levelId字段）\n                if (leaveRoomData.levelId !== undefined) {\n                    // 关卡游戏的LeaveRoom响应，直接返回大厅\n                    cc.log(\"收到关卡游戏退出响应，返回大厅页面\");\n                    GlobalBean.GetInstance().cleanData(); //清空数据\n                    let autoMessageBean: AutoMessageBean = {\n                        'msgId': AutoMessageId.JumpHallPage,//进入大厅的消息\n                        'data': { 'type': 2 } //2 是玩家主动离开游戏房间\n                    }\n                    GameMgr.Event.Send(EventType.AutoMessage, autoMessageBean);\n                } else if (leaveRoomData.userId === GlobalBean.GetInstance().loginData.userInfo.userId) {\n                    // 普通房间游戏的LeaveRoom响应\n                    GlobalBean.GetInstance().cleanData() //清空数据\n                    let autoMessageBean: AutoMessageBean = {\n                        'msgId': AutoMessageId.JumpHallPage,//进入大厅的消息\n                        'data': { 'type': 2 } //2 是玩家主动离开游戏房间\n                    }\n                    GameMgr.Event.Send(EventType.AutoMessage, autoMessageBean);\n                }\n                break;\n            case MessageId.MsgTypeCreateInvite://创建邀请（也就是创建私人游戏房间）\n                GlobalBean.GetInstance().inviteInfo = messageBean.data;\n                //点击 create 的回调\n                this.hallPageController.joinCreateRoom()\n                break\n            case MessageId.MsgTypeAcceptInvite://接受邀请\n                let acceptInvite: AcceptInvite = messageBean.data\n                GlobalBean.GetInstance().inviteInfo = acceptInvite.inviteInfo;\n                this.hallPageController.setAcceptInvite(acceptInvite)\n                break\n            case MessageId.MsgTypeLeaveInvite://收到离开房间的信息\n                let noticeLeaveInvite: NoticeLeaveInvite = messageBean.data;\n                this.hallPageController.leaveRoom(noticeLeaveInvite)\n                break\n            case MessageId.MsgTypeInviteReady://收到有玩家准备的消息\n                let noticeUserInviteStatus: NoticeUserInviteStatus = messageBean.data;\n                this.hallPageController.setReadyState(noticeUserInviteStatus)\n                break\n            case MessageId.MsgTypeNoticeInviteStatus://广播邀请状态\n                let noticeUserInviteStatus2: NoticeUserInviteStatus = messageBean.data;\n                this.hallPageController.setReadyState(noticeUserInviteStatus2)\n                break;\n            case MessageId.MsgTypeInviteKickOut://收到玩家被踢出的信息\n                let inviteKickOut: InviteKickOut = messageBean.data;\n\n                if (inviteKickOut.userId === GlobalBean.GetInstance().loginData.userInfo.userId) {\n                    this.toastController.showContent(window.getLocalizedStr('KickOut'))\n                    //被踢的是自己的话 直接返回大厅\n                    this.setCurrentPage(PageType.HALL_PAGE)\n                } else {\n                    //这里拼接一下数据 走离开房间流程，其实是踢出房间\n                    let noticeLeaveInvite1 = { 'userId': inviteKickOut.userId, 'isCreator': false }\n                    this.hallPageController.leaveRoom(noticeLeaveInvite1)\n                }\n                break;\n            case MessageId.MsgTypeUserInfo://更新用户信息的消息\n                let userInfo: UserInfo = messageBean.data;\n                GlobalBean.GetInstance().loginData.userInfo = userInfo\n                this.hallPageController.updateGold()\n                break;\n\n\n            case MessageId.MsgTypeSettlement: //大结算\n                let noticeSettlement: NoticeSettlement = messageBean.data\n                this.gamePageController.setCongratsDialog(noticeSettlement)\n                break\n\n            case MessageId.MsgTypeNoticeRoundStart: //扫雷回合开始通知\n                // 只有在游戏页面时才处理此消息\n                if (this.currentPage === PageType.GAME_PAGE) {\n                    this.gamePageController.onNoticeRoundStart(messageBean.data);\n                }\n                break;\n\n            case MessageId.MsgTypeNoticeActionDisplay: //扫雷操作展示通知\n                // 只有在游戏页面时才处理此消息\n                if (this.currentPage === PageType.GAME_PAGE) {\n                    this.gamePageController.onNoticeActionDisplay(messageBean.data);\n                }\n                break;\n\n            case MessageId.MsgTypeNoticeRoundEnd: //扫雷回合结束通知\n                // 只有在游戏页面时才处理此消息\n                if (this.currentPage === PageType.GAME_PAGE) {\n                    this.gamePageController.onNoticeRoundEnd(messageBean.data);\n                }\n                break;\n\n            case MessageId.MsgTypeNoticeFirstChoiceBonus: //扫雷首选玩家奖励通知\n                // 只有在游戏页面时才处理此消息\n                if (this.currentPage === PageType.GAME_PAGE) {\n                    this.gamePageController.onNoticeFirstChoiceBonus(messageBean.data);\n                }\n                break;\n\n            case MessageId.MsgTypeAIStatusChange: //AI托管状态变更通知\n                // 只有在游戏页面时才处理此消息\n                if (this.currentPage === PageType.GAME_PAGE) {\n                    this.gamePageController.onAIStatusChange(messageBean.data);\n                }\n                break;\n\n            case MessageId.MsgTypeExtendLevelProgress: //关卡进度\n                if (this.currentPage === PageType.HALL_PAGE) {\n                    // 从后端获取关卡进度并更新\n                    var levelProgressData = messageBean.data;\n                    if (levelProgressData) {\n                        this.hallPageController.setLevelProgress(levelProgressData);\n                    }\n                }\n                break;\n\n            case MessageId.MsgTypeExtendLevelInfo: //关卡信息\n                if (this.currentPage === PageType.LEVEL_PAGE) {\n                    // 从后端获取关卡信息并更新\n                    var levelInfoData: ExtendLevelInfoResponse = messageBean.data;\n                    if (levelInfoData) {\n                        this.levelPageController.onExtendLevelInfo(levelInfoData);\n                    }\n                }\n                break;\n\n            case MessageId.MsgTypeDebugShowMines: //调试显示地雷位置\n                cc.log(\"=== 收到 DebugShowMines 消息 ===\");\n                cc.log(\"当前页面:\", this.currentPage);\n                cc.log(\"是否为关卡页面:\", this.currentPage === PageType.LEVEL_PAGE);\n                cc.log(\"消息数据:\", messageBean.data);\n                cc.log(\"levelPageController 是否存在:\", !!this.levelPageController);\n\n                if (this.currentPage === PageType.LEVEL_PAGE) {\n                    // 从后端获取地雷位置数据并显示测试预制体\n                    var responseData = messageBean.data;\n                    cc.log(\"响应数据:\", responseData);\n\n                    // 服务器返回的数据格式是 {mines: Array}，需要提取 mines 字段\n                    var minePositions = responseData.mines || responseData;\n                    cc.log(\"提取的地雷位置数据:\", minePositions);\n                    cc.log(\"是否为数组:\", Array.isArray(minePositions));\n\n                    if (minePositions && Array.isArray(minePositions)) {\n                        cc.log(\"调用 handleDebugShowMines 方法，地雷数量:\", minePositions.length);\n                        this.levelPageController.handleDebugShowMines(minePositions);\n                    } else {\n                        cc.warn(\"地雷位置数据格式不正确:\", minePositions);\n                    }\n                } else {\n                    cc.warn(\"当前不在关卡页面，无法处理 DebugShowMines 消息\");\n                }\n                break;\n\n\n        }\n    }\n\n    //设置展示页面的\n    setCurrentPage(pageType: PageType) {\n        this.currentPage = pageType\n        this.startUpPage.active = false\n        this.hallPage.active = false\n        this.gamePage.active = false\n        this.levelPage.active = false\n\n        switch (pageType) {\n            case PageType.START_UP_PAGE:\n                this.startUpPage.active = true\n                break\n            case PageType.HALL_PAGE:\n                this.hallPage.active = true\n                break\n            case PageType.GAME_PAGE:\n                this.gamePage.active = true\n                break\n            case PageType.LEVEL_PAGE:\n                this.levelPage.active = true\n                break\n        }\n\n    }\n\n    // update (dt) {}\n}\n\nif (!CC_EDITOR) {\n    cc.Sprite.prototype[\"onLoad\"] = function () {\n        this.srcBlendFactor = cc.macro.BlendFactor.ONE;\n        this.dstBlendFactor = cc.macro.BlendFactor.ONE_MINUS_SRC_ALPHA;\n\n        // 延迟检查 spriteFrame，避免初始化时的警告\n        this.scheduleOnce(() => {\n            if (this.spriteFrame && this.spriteFrame.getTexture()) {\n                this.spriteFrame.getTexture().setPremultiplyAlpha(true);\n            }\n            // 移除警告，因为很多 Sprite 组件在初始化时确实没有 SpriteFrame\n            // 这是正常的，不需要警告\n        }, 0.1);\n    }\n\n    cc.Label.prototype[\"onLoad\"] = function () {\n        this.srcBlendFactor = cc.macro.BlendFactor.ONE;\n        this.dstBlendFactor = cc.macro.BlendFactor.ONE_MINUS_SRC_ALPHA;\n    }\n    cc.macro.ALLOW_IMAGE_BITMAP = false;// 禁用 Bitmap 图片格式\n}"]}