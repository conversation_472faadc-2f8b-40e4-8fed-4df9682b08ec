"use strict";
cc._RF.push(module, '04af7LUaqhJFrIS/DTERlBy', 'SingleChessBoardController');
// scripts/game/Chess/SingleChessBoardController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var WebSocketManager_1 = require("../../net/WebSocketManager");
var MessageId_1 = require("../../net/MessageId");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
// 五种棋盘配置
var BOARD_CONFIGS = {
    "8x8": {
        width: 752,
        height: 752,
        rows: 8,
        cols: 8,
        gridWidth: 88,
        gridHeight: 88
    },
    "8x9": {
        width: 752,
        height: 845,
        rows: 9,
        cols: 8,
        gridWidth: 88,
        gridHeight: 88
    },
    "9x9": {
        width: 752,
        height: 747,
        rows: 9,
        cols: 9,
        gridWidth: 76,
        gridHeight: 76
    },
    "9x10": {
        width: 752,
        height: 830,
        rows: 10,
        cols: 9,
        gridWidth: 78,
        gridHeight: 78
    },
    "10x10": {
        width: 752,
        height: 745,
        rows: 10,
        cols: 10,
        gridWidth: 69,
        gridHeight: 69
    }
};
var SingleChessBoardController = /** @class */ (function (_super) {
    __extends(SingleChessBoardController, _super);
    function SingleChessBoardController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boomPrefab = null; // boom预制体
        _this.biaojiPrefab = null; // biaoji预制体
        _this.boom1Prefab = null; // 数字1预制体
        _this.boom2Prefab = null; // 数字2预制体
        _this.boom3Prefab = null; // 数字3预制体
        _this.boom4Prefab = null; // 数字4预制体
        _this.boom5Prefab = null; // 数字5预制体
        _this.boom6Prefab = null; // 数字6预制体
        _this.boom7Prefab = null; // 数字7预制体
        _this.boom8Prefab = null; // 数字8预制体
        // 五个棋盘节点
        _this.qipan8x8Node = null; // 8x8棋盘节点
        _this.qipan8x9Node = null; // 8x9棋盘节点
        _this.qipan9x9Node = null; // 9x9棋盘节点
        _this.qipan9x10Node = null; // 9x10棋盘节点
        _this.qipan10x10Node = null; // 10x10棋盘节点
        // 当前使用的棋盘节点
        _this.currentBoardNode = null;
        // 当前棋盘配置
        _this.currentBoardConfig = null;
        _this.currentBoardType = "8x8"; // 默认8x8棋盘
        // 炸弹爆炸标记
        _this.hasBombExploded = false;
        // 格子数据存储
        _this.gridData = []; // 二维数组存储格子数据
        _this.gridNodes = []; // 二维数组存储格子节点
        // 防重复发送消息
        _this.lastClickTime = 0;
        _this.lastClickPosition = "";
        _this.CLICK_COOLDOWN = 200; // 200毫秒冷却时间
        return _this;
    }
    SingleChessBoardController.prototype.onLoad = function () {
        // 不进行默认初始化，等待外部调用initBoard
    };
    SingleChessBoardController.prototype.start = function () {
        // start方法不再自动启用触摸事件，避免与initBoard重复
        // 触摸事件的启用由initBoard方法负责
    };
    /**
     * 根据棋盘类型获取对应的棋盘节点
     * @param boardType 棋盘类型
     */
    SingleChessBoardController.prototype.getBoardNodeByType = function (boardType) {
        switch (boardType) {
            case "8x8":
                return this.qipan8x8Node;
            case "8x9":
                return this.qipan8x9Node;
            case "9x9":
                return this.qipan9x9Node;
            case "9x10":
                return this.qipan9x10Node;
            case "10x10":
                return this.qipan10x10Node;
            default:
                return null;
        }
    };
    /**
     * 初始化指定类型的棋盘
     * @param boardType 棋盘类型 ("8x8", "8x9", "9x9", "9x10", "10x10")
     */
    SingleChessBoardController.prototype.initBoard = function (boardType) {
        if (!BOARD_CONFIGS[boardType]) {
            console.error("\u4E0D\u652F\u6301\u7684\u68CB\u76D8\u7C7B\u578B: " + boardType);
            return;
        }
        // 根据棋盘类型获取对应的节点
        this.currentBoardNode = this.getBoardNodeByType(boardType);
        if (!this.currentBoardNode) {
            console.error("\u68CB\u76D8\u8282\u70B9\u672A\u8BBE\u7F6E\uFF01\u68CB\u76D8\u7C7B\u578B: " + boardType);
            return;
        }
        this.currentBoardType = boardType;
        this.currentBoardConfig = BOARD_CONFIGS[boardType];
        // 清空现有数据
        this.gridData = [];
        this.gridNodes = [];
        // 初始化数据数组
        for (var x = 0; x < this.currentBoardConfig.cols; x++) {
            this.gridData[x] = [];
            this.gridNodes[x] = [];
            for (var y = 0; y < this.currentBoardConfig.rows; y++) {
                this.gridData[x][y] = {
                    x: x,
                    y: y,
                    worldPos: this.getGridWorldPosition(x, y),
                    hasPlayer: false
                };
            }
        }
        this.createGridNodes();
    };
    // 启用现有格子的触摸事件
    SingleChessBoardController.prototype.createGridNodes = function () {
        if (!this.currentBoardNode) {
            console.error("棋盘节点未设置！");
            return;
        }
        // 如果格子已经存在，直接启用触摸事件
        this.enableTouchForExistingGrids();
    };
    // 为现有格子启用触摸事件
    SingleChessBoardController.prototype.enableTouchForExistingGrids = function () {
        // 检查棋盘节点是否存在
        if (!this.currentBoardNode) {
            console.error("棋盘节点未设置，无法启用触摸事件！");
            return;
        }
        // 遍历棋盘节点的所有子节点
        var children = this.currentBoardNode.children;
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            // 尝试从节点名称解析坐标
            var coords = this.parseGridCoordinateFromName(child.name);
            if (coords) {
                this.setupGridTouchEvents(child, coords.x, coords.y);
                this.gridNodes[coords.x] = this.gridNodes[coords.x] || [];
                this.gridNodes[coords.x][coords.y] = child;
            }
            else {
                // 如果无法从名称解析，尝试从位置计算
                var pos = child.getPosition();
                var coords_1 = this.getGridCoordinateFromPosition(pos);
                if (coords_1) {
                    this.setupGridTouchEvents(child, coords_1.x, coords_1.y);
                    this.gridNodes[coords_1.x] = this.gridNodes[coords_1.x] || [];
                    this.gridNodes[coords_1.x][coords_1.y] = child;
                }
            }
        }
    };
    // 从节点名称解析格子坐标
    SingleChessBoardController.prototype.parseGridCoordinateFromName = function (nodeName) {
        // 尝试匹配 Grid_x_y 格式
        var match = nodeName.match(/Grid_(\d+)_(\d+)/);
        if (match) {
            return { x: parseInt(match[1]), y: parseInt(match[2]) };
        }
        return null;
    };
    // 从位置计算格子坐标（需要考虑不同棋盘类型的边距）
    SingleChessBoardController.prototype.getGridCoordinateFromPosition = function (pos) {
        if (!this.currentBoardConfig)
            return null;
        // 根据不同棋盘类型使用不同的计算方式
        switch (this.currentBoardType) {
            case "8x9":
                return this.getGridCoordinateFromPositionFor8x9(pos);
            case "9x9":
                return this.getGridCoordinateFromPositionFor9x9(pos);
            case "9x10":
                return this.getGridCoordinateFromPositionFor9x10(pos);
            case "10x10":
                return this.getGridCoordinateFromPositionFor10x10(pos);
            default:
                // 默认计算方式（适用于其他棋盘类型）
                var x = Math.floor((pos.x + this.currentBoardConfig.width / 2) / this.currentBoardConfig.gridWidth);
                var y = Math.floor((pos.y + this.currentBoardConfig.height / 2) / this.currentBoardConfig.gridHeight);
                if (this.isValidCoordinate(x, y)) {
                    return { x: x, y: y };
                }
                return null;
        }
    };
    // 8x9棋盘的位置到坐标转换（基于精确坐标，左下角为(0,0)）
    SingleChessBoardController.prototype.getGridCoordinateFromPositionFor8x9 = function (pos) {
        // 使用与预制体位置计算相同的精确参数
        var startX = -321; // 左下角X坐标
        var startY = -364; // 左下角Y坐标
        var stepX = 91.14; // X方向精确步长
        var stepY = 91.125; // Y方向精确步长
        // 从位置反推坐标
        var x = Math.round((pos.x - startX) / stepX);
        var y = Math.round((pos.y - startY) / stepY);
        if (this.isValidCoordinate(x, y)) {
            return { x: x, y: y };
        }
        return null;
    };
    // 9x9棋盘的位置到坐标转换（基于精确坐标，左下角为(0,0)）
    SingleChessBoardController.prototype.getGridCoordinateFromPositionFor9x9 = function (pos) {
        // 使用与预制体位置计算相同的精确参数
        var startX = -322; // 左下角X坐标
        var startY = -320; // 左下角Y坐标
        var stepX = 80.25; // X方向精确步长
        var stepY = 80.375; // Y方向精确步长
        // 从位置反推坐标
        var x = Math.round((pos.x - startX) / stepX);
        var y = Math.round((pos.y - startY) / stepY);
        if (this.isValidCoordinate(x, y)) {
            return { x: x, y: y };
        }
        return null;
    };
    // 9x10棋盘的位置到坐标转换（基于精确坐标，左下角为(0,0)）
    SingleChessBoardController.prototype.getGridCoordinateFromPositionFor9x10 = function (pos) {
        // 使用与预制体位置计算相同的精确参数
        var startX = -320; // 左下角X坐标
        var startY = -361; // 左下角Y坐标
        var stepX = 80; // X方向精确步长
        var stepY = 80.33; // Y方向精确步长
        // 从位置反推坐标
        var x = Math.round((pos.x - startX) / stepX);
        var y = Math.round((pos.y - startY) / stepY);
        if (this.isValidCoordinate(x, y)) {
            return { x: x, y: y };
        }
        return null;
    };
    // 10x10棋盘的位置到坐标转换（基于精确坐标，左下角为(0,0)）
    SingleChessBoardController.prototype.getGridCoordinateFromPositionFor10x10 = function (pos) {
        // 使用与预制体位置计算相同的精确参数
        var startX = -328; // 左下角X坐标
        var startY = -322; // 左下角Y坐标
        var stepX = 72.56; // X方向精确步长
        var stepY = 72; // Y方向精确步长
        // 从位置反推坐标
        var x = Math.round((pos.x - startX) / stepX);
        var y = Math.round((pos.y - startY) / stepY);
        if (this.isValidCoordinate(x, y)) {
            return { x: x, y: y };
        }
        else {
            return null;
        }
    };
    // 为格子设置触摸事件
    SingleChessBoardController.prototype.setupGridTouchEvents = function (gridNode, x, y) {
        var _this = this;
        // 先清除已有的触摸事件，防止重复绑定
        gridNode.off(cc.Node.EventType.TOUCH_START);
        gridNode.off(cc.Node.EventType.TOUCH_END);
        gridNode.off(cc.Node.EventType.TOUCH_CANCEL);
        // 长按相关变量
        var isLongPressing = false;
        var longPressTimer = 0;
        var longPressCallback = null;
        var hasTriggeredLongPress = false; // 标记是否已触发长按
        var LONG_PRESS_TIME = 1.0; // 1秒长按时间
        // 触摸开始事件
        gridNode.on(cc.Node.EventType.TOUCH_START, function (_event) {
            isLongPressing = true;
            longPressTimer = 0;
            hasTriggeredLongPress = false; // 重置长按标记
            // 开始长按检测
            longPressCallback = function () {
                if (isLongPressing) {
                    longPressTimer += 0.1;
                    if (longPressTimer >= LONG_PRESS_TIME && !hasTriggeredLongPress) {
                        hasTriggeredLongPress = true; // 标记已触发长按
                        isLongPressing = false; // 立即停止长按状态，防止触摸结束时执行点击
                        // 执行长按事件
                        _this.onGridLongPress(x, y);
                        // 停止长按检测
                        if (longPressCallback) {
                            _this.unschedule(longPressCallback);
                            longPressCallback = null;
                        }
                    }
                }
            };
            _this.schedule(longPressCallback, 0.1);
        }, this);
        // 触摸结束事件
        gridNode.on(cc.Node.EventType.TOUCH_END, function (event) {
            // 停止长按检测
            if (longPressCallback) {
                _this.unschedule(longPressCallback);
                longPressCallback = null;
            }
            // 严格检查：只有在所有条件都满足的情况下才执行点击事件
            var shouldExecuteClick = isLongPressing &&
                longPressTimer < LONG_PRESS_TIME &&
                !hasTriggeredLongPress;
            if (shouldExecuteClick) {
                _this.onGridClick(x, y, event);
            }
            else {
            }
            // 清理长按检测
            isLongPressing = false;
            hasTriggeredLongPress = false;
        }, this);
        // 触摸取消事件
        gridNode.on(cc.Node.EventType.TOUCH_CANCEL, function (_event) {
            // 清理长按检测
            isLongPressing = false;
            hasTriggeredLongPress = false;
            if (longPressCallback) {
                _this.unschedule(longPressCallback);
                longPressCallback = null;
            }
        }, this);
    };
    // 计算格子的世界坐标位置（左下角为(0,0)）
    SingleChessBoardController.prototype.getGridWorldPosition = function (x, y) {
        if (!this.currentBoardConfig)
            return cc.v2(0, 0);
        // 计算格子中心点位置
        // 左下角为(0,0)，所以y坐标需要从下往上计算
        var posX = (x * this.currentBoardConfig.gridWidth) + (this.currentBoardConfig.gridWidth / 2) - (this.currentBoardConfig.width / 2);
        var posY = (y * this.currentBoardConfig.gridHeight) + (this.currentBoardConfig.gridHeight / 2) - (this.currentBoardConfig.height / 2);
        return cc.v2(posX, posY);
    };
    // 格子点击事件 - 发送挖掘操作
    SingleChessBoardController.prototype.onGridClick = function (x, y, _event) {
        // 检查坐标是否有效
        if (!this.isValidCoordinate(x, y)) {
            return;
        }
        // 检查该位置是否已经有任何预制体（包括biaoji）
        if (this.gridData[x][y].hasPlayer) {
            return;
        }
        // 发送LevelClickBlock消息
        this.sendLevelClickBlock(x, y, 1);
    };
    // 格子长按事件 - 标记/取消标记操作（参考联机版逻辑）
    SingleChessBoardController.prototype.onGridLongPress = function (x, y) {
        // 检查坐标是否有效
        if (!this.isValidCoordinate(x, y)) {
            return;
        }
        // 检查该位置是否已经有biaoji预制体
        if (this.hasBiaojiAt(x, y)) {
            // 如果已经有biaoji，则删除它（本地立即处理）
            this.removeBiaojiAt(x, y);
            // 发送取消标记消息（但不等待响应，因为已经本地处理了）
            this.sendLevelClickBlock(x, y, 2);
        }
        else if (!this.gridData[x][y].hasPlayer) {
            // 如果没有任何预制体，则生成biaoji（本地立即处理）
            this.createBiaojiPrefab(x, y);
            // 发送标记消息（但不等待响应，因为已经本地处理了）
            this.sendLevelClickBlock(x, y, 2);
        }
        else {
            // 如果有其他类型的预制体（如数字、boom），则不处理
        }
    };
    // 发送LevelClickBlock消息
    SingleChessBoardController.prototype.sendLevelClickBlock = function (x, y, action) {
        // 防重复发送检查
        var currentTime = Date.now();
        var positionKey = x + "," + y + "," + action;
        if (currentTime - this.lastClickTime < this.CLICK_COOLDOWN && this.lastClickPosition === positionKey) {
            return;
        }
        this.lastClickTime = currentTime;
        this.lastClickPosition = positionKey;
        var clickData = {
            x: x,
            y: y,
            action: action // 1=挖掘方块，2=标记/取消标记地雷
        };
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeLevelClickBlock, clickData);
    };
    // 检查坐标是否有效
    SingleChessBoardController.prototype.isValidCoordinate = function (x, y) {
        if (!this.currentBoardConfig)
            return false;
        return x >= 0 && x < this.currentBoardConfig.cols && y >= 0 && y < this.currentBoardConfig.rows;
    };
    /**
     * 在指定位置创建biaoji预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     */
    SingleChessBoardController.prototype.createBiaojiPrefab = function (x, y) {
        if (!this.biaojiPrefab) {
            console.error("biaojiPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }
        // 实例化biaoji预制体
        var biaojiNode = cc.instantiate(this.biaojiPrefab);
        biaojiNode.name = "Biaoji";
        // 设置位置
        var position = this.calculatePrefabPosition(x, y);
        biaojiNode.setPosition(position);
        // 添加到棋盘
        this.currentBoardNode.addChild(biaojiNode);
        // 播放出现动画，10x10棋盘使用0.8缩放
        var targetScale = this.currentBoardType === "10x10" ? 0.8 : 1.0;
        biaojiNode.setScale(0);
        cc.tween(biaojiNode)
            .to(0.2, { scaleX: targetScale, scaleY: targetScale }, { easing: 'backOut' })
            .start();
        // 更新格子数据
        this.gridData[x][y].hasPlayer = true;
        this.gridData[x][y].playerNode = biaojiNode;
    };
    /**
     * 在指定位置创建boom预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param isCurrentUser 是否是当前用户点到的雷（可选，默认为true以保持向后兼容）
     */
    SingleChessBoardController.prototype.createBoomPrefab = function (x, y, isCurrentUser) {
        if (isCurrentUser === void 0) { isCurrentUser = true; }
        if (!this.boomPrefab) {
            console.error("SingleChessBoardController: boomPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }
        // 实例化boom预制体
        var boomNode = cc.instantiate(this.boomPrefab);
        boomNode.name = "Boom";
        // 设置位置
        var position = this.calculatePrefabPosition(x, y);
        boomNode.setPosition(position);
        // 添加到棋盘
        this.currentBoardNode.addChild(boomNode);
        // 播放出现动画，10x10棋盘使用0.8缩放
        var targetScale = this.currentBoardType === "10x10" ? 0.8 : 1.0;
        var bounceScale = targetScale * 1.2; // 弹跳效果比目标缩放大20%
        boomNode.setScale(0);
        cc.tween(boomNode)
            .to(0.3, { scaleX: bounceScale, scaleY: bounceScale }, { easing: 'backOut' })
            .to(0.1, { scaleX: targetScale, scaleY: targetScale })
            .start();
        // 只有当前用户点到雷时才播放棋盘震动效果
        if (isCurrentUser) {
            this.playBoardShakeAnimation();
        }
        // 更新格子数据
        this.gridData[x][y].hasPlayer = true;
        this.gridData[x][y].playerNode = boomNode;
    };
    /**
     * 在指定位置创建数字预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param number 数字(1-8)
     */
    SingleChessBoardController.prototype.createNumberPrefab = function (x, y, number) {
        if (number < 1 || number > 8) {
            console.error("\u65E0\u6548\u7684\u6570\u5B57: " + number);
            return;
        }
        // 获取对应的数字预制体
        var prefab = this.getNumberPrefab(number);
        if (!prefab) {
            console.error("\u6570\u5B57" + number + "\u9884\u5236\u4F53\u672A\u8BBE\u7F6E");
            return;
        }
        // 实例化数字预制体
        var numberNode = cc.instantiate(prefab);
        numberNode.name = "Boom" + number;
        // 设置位置
        var position = this.calculatePrefabPosition(x, y);
        numberNode.setPosition(position);
        // 添加到棋盘
        this.currentBoardNode.addChild(numberNode);
        // 播放出现动画，10x10棋盘使用0.8缩放
        var targetScale = this.currentBoardType === "10x10" ? 0.8 : 1.0;
        numberNode.setScale(0);
        cc.tween(numberNode)
            .to(0.2, { scaleX: targetScale, scaleY: targetScale }, { easing: 'backOut' })
            .start();
    };
    /**
     * 在指定位置创建自定义预制体（用于测试等功能）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param prefab 要创建的预制体
     * @param name 节点名称
     */
    SingleChessBoardController.prototype.createCustomPrefab = function (x, y, prefab, name) {
        if (name === void 0) { name = "CustomPrefab"; }
        if (!prefab) {
            console.error("预制体未设置");
            return null;
        }
        if (!this.currentBoardNode) {
            console.error("currentBoardNode 未设置，无法添加预制体");
            return null;
        }
        try {
            // 实例化预制体
            var customNode = cc.instantiate(prefab);
            customNode.name = name;
            // 设置位置
            var position = this.calculatePrefabPosition(x, y);
            customNode.setPosition(position);
            // 添加到棋盘
            this.currentBoardNode.addChild(customNode);
            // 播放出现动画，10x10棋盘使用0.8缩放
            var targetScale = this.currentBoardType === "10x10" ? 0.8 : 1.0;
            customNode.setScale(0);
            cc.tween(customNode)
                .to(0.3, { scaleX: targetScale * 1.2, scaleY: targetScale * 1.2 }, { easing: 'backOut' })
                .to(0.1, { scaleX: targetScale, scaleY: targetScale })
                .start();
            return customNode;
        }
        catch (error) {
            cc.error("❌ 创建自定义预制体时发生错误:", error);
            return null;
        }
    };
    // 获取数字预制体
    SingleChessBoardController.prototype.getNumberPrefab = function (number) {
        switch (number) {
            case 1: return this.boom1Prefab;
            case 2: return this.boom2Prefab;
            case 3: return this.boom3Prefab;
            case 4: return this.boom4Prefab;
            case 5: return this.boom5Prefab;
            case 6: return this.boom6Prefab;
            case 7: return this.boom7Prefab;
            case 8: return this.boom8Prefab;
            default: return null;
        }
    };
    /**
     * 计算预制体的精确位置（参考联机版ChessBoardController）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @returns 预制体应该放置的精确位置
     */
    SingleChessBoardController.prototype.calculatePrefabPosition = function (x, y) {
        if (!this.currentBoardConfig) {
            return cc.v2(0, 0);
        }
        // 根据不同棋盘类型使用不同的计算方式
        switch (this.currentBoardType) {
            case "8x8":
                return this.calculatePrefabPositionFor8x8(x, y);
            case "8x9":
                return this.calculatePrefabPositionFor8x9(x, y);
            case "9x9":
                return this.calculatePrefabPositionFor9x9(x, y);
            case "9x10":
                return this.calculatePrefabPositionFor9x10(x, y);
            case "10x10":
                return this.calculatePrefabPositionFor10x10(x, y);
            default:
                return this.getGridWorldPosition(x, y);
        }
    };
    // 8x8棋盘的预制体位置计算（参考联机版）
    SingleChessBoardController.prototype.calculatePrefabPositionFor8x8 = function (x, y) {
        // 根据联机版的坐标规律计算：
        // (0,0) → (-314, -310)
        // (1,0) → (-224, -310)  // x增加90
        // (0,1) → (-314, -222)  // y增加88
        // (7,7) → (310, 312)
        var startX = -314; // 起始X坐标
        var startY = -310; // 起始Y坐标
        var stepX = 90; // X方向步长
        var stepY = 88; // Y方向步长
        var finalX = startX + (x * stepX);
        var finalY = startY + (y * stepY);
        return cc.v2(finalX, finalY);
    };
    // 8x9棋盘的预制体位置计算（基于精确坐标，左下角为(0,0)）
    SingleChessBoardController.prototype.calculatePrefabPositionFor8x9 = function (x, y) {
        // 根据提供的精确坐标：
        // 左下角(0,0)：(-321, -364)
        // 右下角(7,0)：(317, -364)
        // 左上角(0,8)：(-321, 365)
        // 右上角(7,8)：(317, 365)
        // 计算步长：
        // X方向步长：(317 - (-321)) / 7 = 638 / 7 ≈ 91.14
        // Y方向步长：(365 - (-364)) / 8 = 729 / 8 ≈ 91.125
        var startX = -321; // 左下角X坐标
        var startY = -364; // 左下角Y坐标
        var stepX = 91.14; // X方向精确步长
        var stepY = 91.125; // Y方向精确步长
        var finalX = startX + (x * stepX);
        var finalY = startY + (y * stepY);
        return cc.v2(finalX, finalY);
    };
    // 9x9棋盘的预制体位置计算（基于精确坐标，左下角为(0,0)）
    SingleChessBoardController.prototype.calculatePrefabPositionFor9x9 = function (x, y) {
        // 根据提供的精确坐标：
        // 左下角(0,0)：(-322, -320)
        // 右下角(8,0)：(320, -320)
        // 左上角(0,8)：(-322, 365)
        // 右上角(8,8)：(320, 323)
        // 计算步长：
        // X方向步长：(320 - (-322)) / 8 = 642 / 8 = 80.25
        // Y方向步长：(323 - (-320)) / 8 = 643 / 8 = 80.375
        var startX = -322; // 左下角X坐标
        var startY = -320; // 左下角Y坐标
        var stepX = 80.25; // X方向精确步长
        var stepY = 80.375; // Y方向精确步长
        var finalX = startX + (x * stepX);
        var finalY = startY + (y * stepY);
        return cc.v2(finalX, finalY);
    };
    // 9x10棋盘的预制体位置计算（基于精确坐标，左下角为(0,0)）
    SingleChessBoardController.prototype.calculatePrefabPositionFor9x10 = function (x, y) {
        // 根据提供的精确坐标：
        // 左下角(0,0)：(-320, -361)
        // 右下角(8,0)：(320, -361)
        // 左上角(0,9)：(-320, 362)
        // 右上角(8,9)：(320, 362)
        // 计算步长：
        // X方向步长：(320 - (-320)) / 8 = 640 / 8 = 80
        // Y方向步长：(362 - (-361)) / 9 = 723 / 9 = 80.33
        var startX = -320; // 左下角X坐标
        var startY = -361; // 左下角Y坐标
        var stepX = 80; // X方向精确步长
        var stepY = 80.33; // Y方向精确步长
        var finalX = startX + (x * stepX);
        var finalY = startY + (y * stepY);
        return cc.v2(finalX, finalY);
    };
    // 10x10棋盘的预制体位置计算（基于精确坐标，左下角为(0,0)）
    SingleChessBoardController.prototype.calculatePrefabPositionFor10x10 = function (x, y) {
        // 根据提供的精确坐标：
        // 左下角(0,0)：(-328, -322)
        // 右下角(9,0)：(325, -322)
        // 左上角(0,9)：(-328, 326)
        // 右上角(9,9)：(325, 326)
        // 计算步长：
        // X方向步长：(325 - (-328)) / 9 = 653 / 9 = 72.56
        // Y方向步长：(326 - (-322)) / 9 = 648 / 9 = 72
        var startX = -328; // 左下角X坐标
        var startY = -322; // 左下角Y坐标
        var stepX = 72.56; // X方向精确步长
        var stepY = 72; // Y方向精确步长
        var finalX = startX + (x * stepX);
        var finalY = startY + (y * stepY);
        return cc.v2(finalX, finalY);
    };
    /**
     * 播放棋盘震动动画（包括所有小格子）
     */
    SingleChessBoardController.prototype.playBoardShakeAnimation = function () {
        if (!this.currentBoardNode)
            return;
        // 震动参数 - 增强震动效果
        var shakeIntensity = 30; // 震动强度
        var shakeDuration = 1.0; // 震动持续时间
        var shakeFrequency = 40; // 震动频率
        // 震动棋盘
        this.shakeBoardNode(shakeIntensity, shakeDuration, shakeFrequency);
        // 震动所有小格子
        this.shakeAllGrids(shakeIntensity * 0.6, shakeDuration, shakeFrequency);
    };
    /**
     * 震动棋盘节点
     */
    SingleChessBoardController.prototype.shakeBoardNode = function (intensity, duration, frequency) {
        // 保存原始位置
        var originalPosition = this.currentBoardNode.position.clone();
        // 创建震动动画，使用递减强度
        var currentIntensity = intensity;
        var intensityDecay = 0.92; // 强度衰减系数
        var createShakeStep = function (shakeIntensity) {
            return cc.tween()
                .to(0.025, {
                x: originalPosition.x + (Math.random() - 0.5) * shakeIntensity * 2,
                y: originalPosition.y + (Math.random() - 0.5) * shakeIntensity * 2
            });
        };
        // 创建震动序列，强度逐渐衰减
        var shakeTween = cc.tween(this.currentBoardNode);
        var totalSteps = Math.floor(duration * frequency);
        for (var i = 0; i < totalSteps; i++) {
            shakeTween = shakeTween.then(createShakeStep(currentIntensity));
            currentIntensity *= intensityDecay; // 逐渐减弱震动强度
        }
        // 最后恢复到原位置
        shakeTween.to(0.2, {
            x: originalPosition.x,
            y: originalPosition.y
        }, { easing: 'backOut' })
            .start();
    };
    /**
     * 震动所有小格子
     */
    SingleChessBoardController.prototype.shakeAllGrids = function (intensity, duration, frequency) {
        if (!this.gridNodes)
            return;
        // 遍历所有格子节点
        for (var x = 0; x < this.gridNodes.length; x++) {
            if (!this.gridNodes[x])
                continue;
            for (var y = 0; y < this.gridNodes[x].length; y++) {
                var gridNode = this.gridNodes[x][y];
                if (!gridNode || !gridNode.active)
                    continue;
                // 为每个格子创建独立的震动动画
                this.shakeGridNode(gridNode, intensity, duration, frequency);
            }
        }
    };
    /**
     * 震动单个格子节点
     */
    SingleChessBoardController.prototype.shakeGridNode = function (gridNode, intensity, duration, frequency) {
        // 保存原始位置
        var originalPosition = gridNode.position.clone();
        // 为每个格子添加随机延迟，创造波浪效果
        var randomDelay = Math.random() * 0.1;
        this.scheduleOnce(function () {
            // 创建震动动画，使用递减强度
            var currentIntensity = intensity;
            var intensityDecay = 0.94; // 格子震动衰减稍慢一些
            var createGridShakeStep = function (shakeIntensity) {
                return cc.tween()
                    .to(0.02, {
                    x: originalPosition.x + (Math.random() - 0.5) * shakeIntensity * 2,
                    y: originalPosition.y + (Math.random() - 0.5) * shakeIntensity * 2
                });
            };
            // 创建震动序列
            var shakeTween = cc.tween(gridNode);
            var totalSteps = Math.floor(duration * frequency * 0.8); // 格子震动时间稍短
            for (var i = 0; i < totalSteps; i++) {
                shakeTween = shakeTween.then(createGridShakeStep(currentIntensity));
                currentIntensity *= intensityDecay;
            }
            // 最后恢复到原位置
            shakeTween.to(0.15, {
                x: originalPosition.x,
                y: originalPosition.y
            }, { easing: 'backOut' })
                .start();
        }, randomDelay);
    };
    /**
     * 显示所有隐藏的格子（游戏结束时调用）
     */
    SingleChessBoardController.prototype.showAllHiddenGrids = function () {
        if (!this.currentBoardNode) {
            console.error("棋盘节点未设置，无法显示隐藏格子！");
            return;
        }
        // 遍历棋盘的所有子节点，找到小格子并显示
        for (var i = 0; i < this.currentBoardNode.children.length; i++) {
            var child = this.currentBoardNode.children[i];
            // 如果是小格子节点
            if (child.name.startsWith("Grid_") || child.name === "block") {
                // 停止所有可能正在进行的动画
                child.stopAllActions();
                // 恢复显示状态
                child.active = true;
                child.opacity = 255;
                child.scaleX = 1;
                child.scaleY = 1;
                child.angle = 0; // 重置旋转角度
                // 恢复原始位置（如果有保存的话）
                if (child['originalPosition']) {
                    child.setPosition(child['originalPosition']);
                }
                // 确保格子可以交互
                var button = child.getComponent(cc.Button);
                if (button) {
                    button.enabled = true;
                }
            }
        }
    };
    /**
     * 清除所有预制体（游戏结束时调用）
     */
    SingleChessBoardController.prototype.clearAllPrefabs = function () {
        if (!this.currentBoardNode) {
            console.error("棋盘节点未设置，无法清除预制体！");
            return;
        }
        var childrenToRemove = [];
        // 遍历棋盘的所有子节点
        for (var i = 0; i < this.currentBoardNode.children.length; i++) {
            var child = this.currentBoardNode.children[i];
            // 检查是否是预制体（通过名称判断）
            if (child.name === "Biaoji" || child.name === "Boom" || child.name.startsWith("Boom")) {
                childrenToRemove.push(child);
            }
        }
        // 移除找到的预制体
        childrenToRemove.forEach(function (child) {
            child.removeFromParent();
        });
        // 重置格子数据
        this.reinitializeBoardData();
    };
    /**
     * 重新初始化棋盘数据（仅在开始新游戏时调用）
     */
    SingleChessBoardController.prototype.reinitializeBoardData = function () {
        if (!this.currentBoardConfig)
            return;
        // 重置gridData中的预制体状态
        for (var x = 0; x < this.currentBoardConfig.cols; x++) {
            for (var y = 0; y < this.currentBoardConfig.rows; y++) {
                if (this.gridData[x] && this.gridData[x][y]) {
                    this.gridData[x][y].hasPlayer = false;
                    this.gridData[x][y].playerNode = null;
                }
            }
        }
    };
    /**
     * 处理ExtendLevelInfo消息（游戏结束时调用）
     */
    SingleChessBoardController.prototype.onExtendLevelInfo = function () {
        this.showAllHiddenGrids();
        this.clearAllPrefabs();
    };
    /**
     * 处理ExtendLevelInfo断线重连（恢复游戏状态）
     * @param levelInfo 关卡信息响应数据
     */
    SingleChessBoardController.prototype.onExtendLevelInfoReconnect = function (levelInfo) {
        console.log("SingleChessBoardController: 处理断线重连，恢复游戏状态");
        // 清理当前状态
        this.clearAllPrefabs();
        this.showAllHiddenGrids();
        // 如果有地图状态信息，恢复棋盘状态
        if (levelInfo.mineMap) {
            this.restoreBoardState(levelInfo.mineMap);
        }
    };
    /**
     * 恢复棋盘状态（断线重连时使用）
     * @param mineMap 地图状态信息
     */
    SingleChessBoardController.prototype.restoreBoardState = function (mineMap) {
        var _this = this;
        console.log("SingleChessBoardController: 恢复棋盘状态", mineMap);
        // 恢复已挖掘的方块
        if (mineMap.revealedBlocks && Array.isArray(mineMap.revealedBlocks)) {
            mineMap.revealedBlocks.forEach(function (block) {
                var x = block.x;
                var y = block.y;
                var neighborMines = block.neighborMines;
                if (_this.isValidCoordinate(x, y)) {
                    // 隐藏格子
                    _this.hideGridAt(x, y);
                    // 显示挖掘结果
                    if (neighborMines > 0) {
                        _this.createNumberPrefab(x, y, neighborMines);
                    }
                    // 标记格子已被处理
                    _this.gridData[x][y].hasPlayer = true;
                }
            });
        }
        // 恢复已标记的方块
        if (mineMap.markedBlocks && Array.isArray(mineMap.markedBlocks)) {
            mineMap.markedBlocks.forEach(function (block) {
                var x = block.x;
                var y = block.y;
                if (_this.isValidCoordinate(x, y)) {
                    // 创建标记预制体
                    _this.createBiaojiPrefab(x, y);
                }
            });
        }
    };
    /**
     * 处理LevelGameEnd消息（游戏结束时调用）
     * 注意：不清理任何数据，保持玩家的游玩痕迹
     */
    SingleChessBoardController.prototype.onLevelGameEnd = function () {
        // 不显示隐藏的格子，保持当前状态
        // 不清理预制体，不重置格子状态，保持游戏结果显示
        // 让玩家可以看到自己的标记（biaoji）、挖掘结果（数字、boom）等
    };
    /**
     * 检查是否点到了炸弹
     * @returns 是否点到了炸弹
     */
    SingleChessBoardController.prototype.hasBombExplodedInThisGame = function () {
        return this.hasBombExploded;
    };
    /**
     * 重置炸弹爆炸状态（开始新游戏时调用）
     */
    SingleChessBoardController.prototype.resetBombExplodedStatus = function () {
        this.hasBombExploded = false;
    };
    /**
     * 隐藏指定位置的格子（点击时调用）
     */
    SingleChessBoardController.prototype.hideGridAt = function (x, y) {
        if (!this.isValidCoordinate(x, y)) {
            console.warn("\u9690\u85CF\u683C\u5B50\u5931\u8D25\uFF1A\u5750\u6807(" + x + ", " + y + ")\u65E0\u6548");
            return;
        }
        // 获取格子节点
        var gridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (gridNode) {
            // 使用动画隐藏格子
            cc.tween(gridNode)
                .to(0.3, { opacity: 0, scaleX: 0, scaleY: 0 }, { easing: 'sineIn' })
                .call(function () {
                gridNode.active = false;
            })
                .start();
        }
    };
    /**
     * 获取当前棋盘类型
     */
    SingleChessBoardController.prototype.getCurrentBoardType = function () {
        return this.currentBoardType;
    };
    /**
     * 获取当前棋盘配置
     */
    SingleChessBoardController.prototype.getCurrentBoardConfig = function () {
        return this.currentBoardConfig;
    };
    /**
     * 处理点击响应，根据服务器返回的结果更新棋盘 - 参考联机版的地图更新逻辑
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param result 点击结果 ("boom" | "safe" | number)
     */
    SingleChessBoardController.prototype.handleClickResponse = function (x, y, result) {
        if (!this.isValidCoordinate(x, y)) {
            console.warn("\u5904\u7406\u70B9\u51FB\u54CD\u5E94\u5931\u8D25\uFF1A\u5750\u6807(" + x + ", " + y + ")\u65E0\u6548");
            return;
        }
        // 如果格子上有biaoji预制体，先移除它
        if (this.hasBiaojiAt(x, y)) {
            // 直接移除，不播放动画
            var gridData = this.gridData[x][y];
            if (gridData.playerNode) {
                gridData.playerNode.removeFromParent();
                gridData.playerNode = null;
            }
        }
        // 标记格子已被处理，防止重复点击
        this.gridData[x][y].hasPlayer = true;
        // 使用连锁动画的方式处理单个格子，保持一致性
        this.playGridDisappearAnimation(x, y, result);
    };
    /**
     * 处理连锁展开结果
     * @param floodFillResults 连锁展开数据数组
     */
    SingleChessBoardController.prototype.handleFloodFillResults = function (floodFillResults) {
        var _this = this;
        // 同时播放所有格子的消失动画，不使用延迟
        floodFillResults.forEach(function (gridResult) {
            var x = gridResult.x, y = gridResult.y, neighborMines = gridResult.neighborMines;
            if (!_this.isValidCoordinate(x, y)) {
                console.warn("\u8FDE\u9501\u5C55\u5F00\u8DF3\u8FC7\u65E0\u6548\u5750\u6807: (" + x + ", " + y + ")");
                return;
            }
            // 立即播放动画，不延迟
            _this.playGridDisappearAnimation(x, y, neighborMines);
        });
    };
    /**
     * 批量处理连锁反应的格子（参考联机版的processFloodFillResult）
     * @param revealedGrids 被揭开的格子列表 {x: number, y: number, neighborMines: number}[]
     */
    SingleChessBoardController.prototype.handleChainReaction = function (revealedGrids) {
        var _this = this;
        if (!revealedGrids || revealedGrids.length === 0) {
            return;
        }
        // 同时播放所有连锁格子的消失动画，不使用延迟
        revealedGrids.forEach(function (block) {
            // 立即播放动画，不延迟
            _this.playGridDisappearAnimation(block.x, block.y, block.neighborMines);
        });
    };
    /**
     * 播放格子消失动画（连锁效果）- 参考联机版ChessBoardController
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param neighborMines 周围地雷数量或结果类型（可以是数字、"mine"、"boom"等）
     */
    SingleChessBoardController.prototype.playGridDisappearAnimation = function (x, y, neighborMines) {
        var _this = this;
        // 如果格子上有biaoji预制体，先移除它（连锁展开时）
        if (this.hasBiaojiAt(x, y)) {
            var gridData = this.gridData[x][y];
            if (gridData.playerNode) {
                gridData.playerNode.removeFromParent();
                gridData.playerNode = null;
            }
        }
        // 标记格子已被处理（对于连锁格子）
        if (this.isValidCoordinate(x, y)) {
            this.gridData[x][y].hasPlayer = true;
        }
        // 先删除格子
        this.removeGridAt(x, y);
        // 延迟0.3秒后显示数字（等格子消失动画完成）
        // 使用带标识的延迟任务，方便重置时清理
        var delayCallback = function () {
            _this.updateNeighborMinesDisplay(x, y, neighborMines);
        };
        this.scheduleOnce(delayCallback, 0.3);
    };
    /**
     * 隐藏指定位置的格子（不销毁，以便重置时可以重新显示）- 参考联机版
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param immediate 是否立即隐藏（不播放动画）
     */
    SingleChessBoardController.prototype.removeGridAt = function (x, y, immediate) {
        if (immediate === void 0) { immediate = false; }
        if (!this.isValidCoordinate(x, y)) {
            console.warn("\u9690\u85CF\u683C\u5B50\u5931\u8D25\uFF1A\u5750\u6807(" + x + ", " + y + ")\u65E0\u6548");
            return;
        }
        // 获取格子节点
        var gridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (gridNode) {
            if (immediate) {
                // 立即隐藏，不播放动画
                gridNode.active = false;
            }
            else {
                // 播放四边形格子消失动画
                this.playGridFallAnimation(gridNode);
            }
        }
    };
    /**
     * 播放四边形格子消失动画
     * 效果：格子持续旋转，给一个随机向上的力，然后旋转着自由落体
     * @param gridNode 格子节点
     */
    SingleChessBoardController.prototype.playGridFallAnimation = function (gridNode) {
        if (!gridNode)
            return;
        // 停止该格子上所有正在进行的动画（包括震动动画）
        gridNode.stopAllActions();
        // 保存格子的原始位置（用于重置时恢复）
        if (!gridNode['originalPosition']) {
            gridNode['originalPosition'] = gridNode.getPosition();
        }
        // 保存原始的zIndex，用于动画结束后恢复
        var originalZIndex = gridNode.zIndex;
        // 设置更高的层级，确保下落的格子在数字预制体和其他元素之上
        gridNode.zIndex = 1000;
        // 随机选择向上的力的方向：0=向上，1=右上15度，2=左上15度
        var forceDirection = Math.floor(Math.random() * 3);
        var moveX = 0;
        var moveY = 200; // 向上的基础距离（增加高度）
        switch (forceDirection) {
            case 0: // 向上
                moveX = 0;
                break;
            case 1: // 右上15度
                moveX = Math.sin(20 * Math.PI / 180) * moveY;
                break;
            case 2: // 左上15度
                moveX = -Math.sin(20 * Math.PI / 180) * moveY;
                break;
        }
        // 随机旋转速度
        var rotationSpeed = (Math.random() * 1440 + 720) * (Math.random() > 0.5 ? 1 : -1); // 360-1080度/秒，随机方向
        // 动画参数
        var upTime = 0.15; // 向上运动时间
        var fallTime = 0.3; // 下落时间
        var initialPosition = gridNode.getPosition();
        // 创建持续旋转的动画
        var rotationTween = cc.tween(gridNode)
            .repeatForever(cc.tween().by(0.1, { angle: rotationSpeed * 0.1 }));
        // 创建分阶段的运动动画
        var movementTween = cc.tween(gridNode)
            // 第一阶段：向上抛出
            .to(upTime, {
            x: initialPosition.x + moveX,
            y: initialPosition.y + moveY
        }, { easing: 'quadOut' })
            // 第二阶段：自由落体
            .to(fallTime, {
            x: initialPosition.x + moveX + (Math.random() - 0.5) * 100,
            y: initialPosition.y - 500 // 下落到屏幕下方更远处
        }, { easing: 'quadIn' })
            .call(function () {
            // 动画结束后隐藏格子
            gridNode.active = false;
            // 停止旋转动画
            gridNode.stopAllActions();
            // 恢复原始的zIndex（虽然格子已经隐藏，但保持一致性）
            gridNode.zIndex = originalZIndex;
        });
        // 同时开始旋转和移动动画
        rotationTween.start();
        movementTween.start();
    };
    /**
     * 更新指定位置的neighborMines显示（使用boom数字预制体）- 参考联机版
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param neighborMines 周围地雷数量或结果类型
     */
    SingleChessBoardController.prototype.updateNeighborMinesDisplay = function (x, y, neighborMines) {
        if (neighborMines === "boom" || neighborMines === "mine") {
            // 踩到地雷，生成boom预制体并触发震动
            this.createBoomPrefab(x, y, true); // true表示是当前用户踩到的雷，需要震动
            // 设置标记，表示点到了炸弹
            this.hasBombExploded = true;
        }
        else if (typeof neighborMines === "number" && neighborMines > 0) {
            // 显示数字
            this.createNumberPrefab(x, y, neighborMines);
        }
        else {
            // 如果是0、"safe"或其他，则不显示任何预制体
        }
    };
    /**
     * 移除指定位置的biaoji预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     */
    SingleChessBoardController.prototype.removeBiaojiAt = function (x, y) {
        if (!this.isValidCoordinate(x, y)) {
            return;
        }
        var gridData = this.gridData[x][y];
        if (gridData.hasPlayer && gridData.playerNode && gridData.playerNode.name === "Biaoji") {
            // 播放消失动画
            cc.tween(gridData.playerNode)
                .to(0.2, { scaleX: 0, scaleY: 0 }, { easing: 'sineIn' })
                .call(function () {
                gridData.playerNode.removeFromParent();
                gridData.hasPlayer = false;
                gridData.playerNode = null;
            })
                .start();
        }
    };
    /**
     * 检查指定位置是否有biaoji预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @returns 是否有biaoji预制体
     */
    SingleChessBoardController.prototype.hasBiaojiAt = function (x, y) {
        if (!this.isValidCoordinate(x, y)) {
            return false;
        }
        var gridData = this.gridData[x][y];
        return gridData.hasPlayer && gridData.playerNode && gridData.playerNode.name === "Biaoji";
    };
    /**
     * 获取所有biaoji的位置
     * @returns biaoji位置数组
     */
    SingleChessBoardController.prototype.getAllBiaojiPositions = function () {
        var positions = [];
        if (!this.currentBoardConfig)
            return positions;
        for (var x = 0; x < this.currentBoardConfig.cols; x++) {
            for (var y = 0; y < this.currentBoardConfig.rows; y++) {
                if (this.hasBiaojiAt(x, y)) {
                    positions.push({ x: x, y: y });
                }
            }
        }
        return positions;
    };
    /**
     * 重置棋盘到初始状态
     */
    SingleChessBoardController.prototype.resetBoard = function () {
        var _this = this;
        // 清理所有延迟任务（重要：防止上一局的连锁动画影响新游戏）
        this.unscheduleAllCallbacks();
        // 重置炸弹爆炸状态
        this.resetBombExplodedStatus();
        // 清除所有预制体
        this.clearAllPrefabs();
        // 重新初始化棋盘数据
        this.reinitializeBoardData();
        // 显示所有格子
        this.showAllHiddenGrids();
        // 重新启用触摸事件
        this.scheduleOnce(function () {
            _this.enableTouchForExistingGrids();
        }, 0.1);
    };
    /**
     * 禁用所有格子的触摸事件（游戏结束时调用）
     */
    SingleChessBoardController.prototype.disableAllGridTouch = function () {
        if (!this.currentBoardConfig)
            return;
        for (var x = 0; x < this.currentBoardConfig.cols; x++) {
            for (var y = 0; y < this.currentBoardConfig.rows; y++) {
                var gridNode = this.gridNodes[x] && this.gridNodes[x][y];
                if (gridNode) {
                    gridNode.off(cc.Node.EventType.TOUCH_START);
                    gridNode.off(cc.Node.EventType.TOUCH_END);
                    gridNode.off(cc.Node.EventType.TOUCH_CANCEL);
                }
            }
        }
    };
    /**
     * 启用所有格子的触摸事件
     */
    SingleChessBoardController.prototype.enableAllGridTouch = function () {
        this.enableTouchForExistingGrids();
    };
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boomPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "biaojiPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boom1Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boom2Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boom3Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boom4Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boom5Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boom6Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boom7Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boom8Prefab", void 0);
    __decorate([
        property(cc.Node)
    ], SingleChessBoardController.prototype, "qipan8x8Node", void 0);
    __decorate([
        property(cc.Node)
    ], SingleChessBoardController.prototype, "qipan8x9Node", void 0);
    __decorate([
        property(cc.Node)
    ], SingleChessBoardController.prototype, "qipan9x9Node", void 0);
    __decorate([
        property(cc.Node)
    ], SingleChessBoardController.prototype, "qipan9x10Node", void 0);
    __decorate([
        property(cc.Node)
    ], SingleChessBoardController.prototype, "qipan10x10Node", void 0);
    SingleChessBoardController = __decorate([
        ccclass
    ], SingleChessBoardController);
    return SingleChessBoardController;
}(cc.Component));
exports.default = SingleChessBoardController;

cc._RF.pop();