"use strict";
cc._RF.push(module, '147d16sE21EH4WnxrTp2v7g', 'HexSingleChessBoardController');
// scripts/game/Chess/HexSingleChessBoardController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var WebSocketManager_1 = require("../../net/WebSocketManager");
var MessageId_1 = require("../../net/MessageId");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var HexSingleChessBoardController = /** @class */ (function (_super) {
    __extends(HexSingleChessBoardController, _super);
    function HexSingleChessBoardController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boomPrefab = null; // boom预制体
        _this.biaojiPrefab = null; // biaoji预制体
        _this.boom1Prefab = null; // 数字1预制体
        _this.boom2Prefab = null; // 数字2预制体
        _this.boom3Prefab = null; // 数字3预制体
        _this.boom4Prefab = null; // 数字4预制体
        _this.boom5Prefab = null; // 数字5预制体
        _this.boom6Prefab = null; // 数字6预制体
        _this.boom7Prefab = null; // 数字7预制体
        _this.boom8Prefab = null; // 数字8预制体
        // 六个六边形棋盘节点
        _this.hexBoard1Node = null; // 六边形棋盘1节点
        _this.hexBoard2Node = null; // 六边形棋盘2节点
        _this.hexBoard3Node = null; // 六边形棋盘3节点
        _this.hexBoard4Node = null; // 六边形棋盘4节点
        _this.hexBoard5Node = null; // 六边形棋盘5节点
        _this.hexBoard6Node = null; // 六边形棋盘6节点
        // 当前使用的棋盘节点
        _this.currentBoardNode = null;
        _this.currentBoardType = "hexBoard1"; // 默认使用第一个六边形棋盘
        // 六边形棋盘配置
        _this.HEX_SIZE = 44; // 六边形半径
        _this.HEX_WIDTH = _this.HEX_SIZE * 2; // 六边形宽度
        _this.HEX_HEIGHT = _this.HEX_SIZE * Math.sqrt(3); // 六边形高度
        // 格子数据存储 - 使用Map存储六边形坐标
        _this.hexGridData = new Map(); // 存储六边形格子数据
        _this.hexGridNodes = new Map(); // 存储六边形格子节点
        _this.validHexCoords = []; // 有效的六边形坐标列表
        // 炸弹爆炸标记
        _this.hasBombExploded = false;
        // 防重复发送消息
        _this.lastClickTime = 0;
        _this.lastClickPosition = "";
        _this.CLICK_COOLDOWN = 200; // 200毫秒冷却时间
        return _this;
    }
    HexSingleChessBoardController.prototype.onLoad = function () {
        // 不进行默认初始化，等待外部调用initBoard
    };
    HexSingleChessBoardController.prototype.start = function () {
        // start方法不再自动启用触摸事件，避免与initBoard重复
        // 触摸事件的启用由initBoard方法负责
    };
    /**
     * 根据棋盘类型获取对应的棋盘节点
     * @param boardType 棋盘类型
     */
    HexSingleChessBoardController.prototype.getBoardNodeByType = function (boardType) {
        switch (boardType) {
            case "hexBoard1":
                return this.hexBoard1Node;
            case "hexBoard2":
                return this.hexBoard2Node;
            case "hexBoard3":
                return this.hexBoard3Node;
            case "hexBoard4":
                return this.hexBoard4Node;
            case "hexBoard5":
                return this.hexBoard5Node;
            case "hexBoard6":
                return this.hexBoard6Node;
            default:
                return null;
        }
    };
    /**
     * 初始化指定类型的六边形棋盘
     * @param boardType 棋盘类型 ("hexBoard1", "hexBoard2", "hexBoard3", "hexBoard4", "hexBoard5", "hexBoard6")
     */
    HexSingleChessBoardController.prototype.initBoard = function (boardType) {
        var _this = this;
        // 根据棋盘类型获取对应的节点
        this.currentBoardNode = this.getBoardNodeByType(boardType);
        if (!this.currentBoardNode) {
            console.error("\u516D\u8FB9\u5F62\u68CB\u76D8\u8282\u70B9\u672A\u8BBE\u7F6E\uFF01\u68CB\u76D8\u7C7B\u578B: " + boardType);
            return;
        }
        this.currentBoardType = boardType;
        // 清空现有数据
        this.hexGridData.clear();
        this.hexGridNodes.clear();
        this.validHexCoords = [];
        // 重置炸弹爆炸标记
        this.hasBombExploded = false;
        // 延迟一帧后再次尝试启用触摸事件，确保所有节点都已创建完成
        this.scheduleOnce(function () {
            _this.setValidHexCoords([]); // 传入空数组，但会被忽略
            // 测试预制体位置计算
            _this.testHexPositionCalculation();
            _this.enableTouchForExistingGrids();
        }, 0.1);
        console.log("\u516D\u8FB9\u5F62\u68CB\u76D8\u521D\u59CB\u5316\u5B8C\u6210: " + boardType);
    };
    /**
     * 设置有效的六边形坐标列表（忽略服务器数据，使用前端节点坐标）
     * @param _coords 服务器发送的坐标列表（将被忽略）
     */
    HexSingleChessBoardController.prototype.setValidHexCoords = function (_coords) {
        // 忽略服务器传入的坐标，始终从节点名称自动生成
        this.generateCoordsFromNodeNames();
        this.initHexBoard();
    };
    /**
     * 从节点名称自动生成有效坐标列表
     */
    HexSingleChessBoardController.prototype.generateCoordsFromNodeNames = function () {
        if (!this.currentBoardNode) {
            console.error("❌ 棋盘节点不存在，无法生成坐标列表");
            return;
        }
        var foundCoords = [];
        var children = this.currentBoardNode.children;
        var _loop_1 = function (i) {
            var child = children[i];
            var nodeName = child.name;
            // 跳过游戏元素节点，只处理六边形格子节点
            if (this_1.isGameElement(child, nodeName)) {
                return "continue";
            }
            var coords = this_1.parseHexCoordinateFromName(nodeName);
            if (coords) {
                // 检查是否已经存在相同的坐标
                var exists = foundCoords.some(function (c) { return c.q === coords.q && c.r === coords.r; });
                if (!exists) {
                    foundCoords.push({ q: coords.q, r: coords.r });
                }
            }
        };
        var this_1 = this;
        for (var i = 0; i < children.length; i++) {
            _loop_1(i);
        }
        this.validHexCoords = foundCoords;
    };
    // 初始化六边形棋盘
    HexSingleChessBoardController.prototype.initHexBoard = function () {
        // 清空现有数据
        this.hexGridData.clear();
        this.hexGridNodes.clear();
        // 初始化有效坐标的数据
        for (var _i = 0, _a = this.validHexCoords; _i < _a.length; _i++) {
            var coord = _a[_i];
            var key = this.getHexKey(coord.q, coord.r);
            this.hexGridData.set(key, {
                q: coord.q,
                r: coord.r,
                worldPos: this.getHexWorldPosition(coord.q, coord.r),
                hasPlayer: false
            });
        }
        this.createHexGridNodes();
    };
    // 生成六边形坐标的唯一键
    HexSingleChessBoardController.prototype.getHexKey = function (q, r) {
        return q + "," + r;
    };
    // 启用现有格子的触摸事件
    HexSingleChessBoardController.prototype.createHexGridNodes = function () {
        if (!this.currentBoardNode) {
            console.error("棋盘节点未设置！");
            return;
        }
        // 如果格子已经存在，直接启用触摸事件
        this.enableTouchForExistingGrids();
    };
    // 为现有格子启用触摸事件
    HexSingleChessBoardController.prototype.enableTouchForExistingGrids = function () {
        // 检查棋盘节点是否存在
        if (!this.currentBoardNode) {
            console.error("棋盘节点未设置，无法启用触摸事件！");
            return;
        }
        console.log("\uD83D\uDD0D \u5F00\u59CB\u4E3A\u68CB\u76D8 " + this.currentBoardType + " \u542F\u7528\u89E6\u6478\u4E8B\u4EF6");
        console.log("\uD83D\uDCCB \u68CB\u76D8\u8282\u70B9\u5B50\u8282\u70B9\u6570\u91CF: " + this.currentBoardNode.children.length);
        // 遍历棋盘节点的所有子节点
        var children = this.currentBoardNode.children;
        var validGridCount = 0;
        var skippedCount = 0;
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            var nodeName = child.name;
            console.log("\uD83D\uDD0D \u68C0\u67E5\u5B50\u8282\u70B9 [" + i + "]: " + nodeName);
            // 跳过游戏元素节点，只处理六边形格子节点
            if (this.isGameElement(child, nodeName)) {
                console.log("\u23ED\uFE0F \u8DF3\u8FC7\u6E38\u620F\u5143\u7D20\u8282\u70B9: " + nodeName);
                skippedCount++;
                continue;
            }
            // 尝试从节点名称解析六边形坐标
            var coords = this.parseHexCoordinateFromName(nodeName);
            if (coords) {
                console.log("\u2705 \u6210\u529F\u89E3\u6790\u5750\u6807 " + nodeName + " -> (" + coords.q + ", " + coords.r + ")");
                this.setupHexGridTouchEvents(child, coords.q, coords.r);
                var key = this.getHexKey(coords.q, coords.r);
                this.hexGridNodes.set(key, child);
                validGridCount++;
            }
            else {
                console.log("\u274C \u65E0\u6CD5\u4ECE\u540D\u79F0\u89E3\u6790\u5750\u6807: " + nodeName + "\uFF0C\u5C1D\u8BD5\u4ECE\u4F4D\u7F6E\u8BA1\u7B97");
                // 如果无法从名称解析，尝试从位置计算
                var pos = child.getPosition();
                var coords_1 = this.getHexCoordinateFromPosition(pos);
                if (coords_1) {
                    console.log("\u2705 \u4ECE\u4F4D\u7F6E\u8BA1\u7B97\u5F97\u5230\u5750\u6807 " + nodeName + " -> (" + coords_1.q + ", " + coords_1.r + ")");
                    this.setupHexGridTouchEvents(child, coords_1.q, coords_1.r);
                    var key = this.getHexKey(coords_1.q, coords_1.r);
                    this.hexGridNodes.set(key, child);
                    validGridCount++;
                }
                else {
                    console.log("\u274C \u65E0\u6CD5\u4ECE\u4F4D\u7F6E\u8BA1\u7B97\u5750\u6807: " + nodeName);
                }
            }
        }
        console.log("\uD83D\uDCCA \u89E6\u6478\u4E8B\u4EF6\u542F\u7528\u5B8C\u6210:");
        console.log("   - \u6709\u6548\u683C\u5B50\u6570\u91CF: " + validGridCount);
        console.log("   - \u8DF3\u8FC7\u8282\u70B9\u6570\u91CF: " + skippedCount);
        console.log("   - \u603B\u5B50\u8282\u70B9\u6570\u91CF: " + children.length);
        if (validGridCount === 0) {
            console.warn("\u26A0\uFE0F \u6CA1\u6709\u627E\u5230\u4EFB\u4F55\u6709\u6548\u7684\u516D\u8FB9\u5F62\u683C\u5B50\u8282\u70B9\uFF01");
            console.warn("   \u8BF7\u68C0\u67E5\u683C\u5B50\u8282\u70B9\u547D\u540D\u662F\u5426\u4E3A sixblock_q_r \u683C\u5F0F");
            console.warn("   \u4F8B\u5982: sixblock_0_0, sixblock_1_-1, sixblock_-1_2");
        }
    };
    // 从节点名称解析六边形坐标
    HexSingleChessBoardController.prototype.parseHexCoordinateFromName = function (nodeName) {
        var patterns = [
            /^sixblock_(-?\d+)_(-?\d+)$/,
        ];
        for (var _i = 0, patterns_1 = patterns; _i < patterns_1.length; _i++) {
            var pattern = patterns_1[_i];
            var match = nodeName.match(pattern);
            if (match) {
                var coords = { q: parseInt(match[1]), r: parseInt(match[2]) };
                return coords;
            }
        }
        console.warn("\u274C \u65E0\u6CD5\u89E3\u6790\u8282\u70B9\u540D\u79F0: " + nodeName);
        return null;
    };
    // 判断是否为游戏元素节点（需要跳过的节点）
    HexSingleChessBoardController.prototype.isGameElement = function (child, nodeName) {
        // 跳过玩家头像预制体
        if (nodeName === "player_game_pfb" || child.name.includes("Player")) {
            return true;
        }
        // 跳过boom相关预制体
        if (nodeName === "Boom" || nodeName.includes("Boom") || nodeName.includes("boom")) {
            return true;
        }
        // 跳过biaoji相关预制体
        if (nodeName === "Biaoji" || nodeName.includes("Biaoji") || nodeName.includes("biaoji")) {
            return true;
        }
        // 跳过数字预制体
        if (nodeName.match(/^\d+$/) || nodeName.includes("Number")) {
            return true;
        }
        return false;
    };
    // 测试六边形位置计算
    HexSingleChessBoardController.prototype.testHexPositionCalculation = function () {
        var _this = this;
        console.log("🧪 重新测试六边形位置计算算法（基于您的精确数据）...");
        // 您提供的实际测量数据
        var testPoints = [
            { q: 0, r: 0, expected: cc.v2(-170, -165), description: "中心位置" },
            { q: 0, r: -1, expected: cc.v2(-220, -81), description: "上方" },
            { q: 1, r: -2, expected: cc.v2(-172, 2), description: "右上" },
            { q: 2, r: -3, expected: cc.v2(-122, 85), description: "右上远" },
            { q: 4, r: -4, expected: cc.v2(23, 171), description: "右上最远" }
        ];
        console.log("📍 验证您提供的精确坐标:");
        var correctCount = 0;
        testPoints.forEach(function (point) {
            var calculated = _this.getHexWorldPosition(point.q, point.r);
            var errorX = Math.abs(calculated.x - point.expected.x);
            var errorY = Math.abs(calculated.y - point.expected.y);
            var isCorrect = errorX < 1 && errorY < 1; // 允许1像素误差
            if (isCorrect) {
                correctCount++;
                console.log("\u2705 (" + point.q + ", " + point.r + ") " + point.description + ": \u8BA1\u7B97=(" + calculated.x.toFixed(1) + ", " + calculated.y.toFixed(1) + ") | \u671F\u671B=(" + point.expected.x + ", " + point.expected.y + ")");
            }
            else {
                console.log("\u274C (" + point.q + ", " + point.r + ") " + point.description + ": \u8BA1\u7B97=(" + calculated.x.toFixed(1) + ", " + calculated.y.toFixed(1) + ") | \u671F\u671B=(" + point.expected.x + ", " + point.expected.y + ") | \u8BEF\u5DEE=(" + errorX.toFixed(1) + ", " + errorY.toFixed(1) + ")");
            }
        });
        console.log("\uD83D\uDCCA \u7CBE\u786E\u5750\u6807\u9A8C\u8BC1\u7ED3\u679C: " + correctCount + "/" + testPoints.length + " \u6B63\u786E");
        // 如果不是全部正确，显示详细的数据分析
        if (correctCount < testPoints.length) {
            console.log("\n🔍 详细数据分析:");
            console.log("您的数据规律分析:");
            // 分析相邻点的差值
            for (var i = 1; i < testPoints.length; i++) {
                var prev = testPoints[i - 1];
                var curr = testPoints[i];
                var deltaQ = curr.q - prev.q;
                var deltaR = curr.r - prev.r;
                var deltaX = curr.expected.x - prev.expected.x;
                var deltaY = curr.expected.y - prev.expected.y;
                console.log("\u4ECE(" + prev.q + "," + prev.r + ")\u5230(" + curr.q + "," + curr.r + "): \u0394q=" + deltaQ + ", \u0394r=" + deltaR + ", \u0394x=" + deltaX + ", \u0394y=" + deltaY);
            }
        }
        // 测试一些关键的推算坐标
        console.log("\n📍 测试关键推算坐标:");
        var extraPoints = [
            { q: 1, r: 0, description: "右侧邻居" },
            { q: -1, r: 0, description: "左侧邻居" },
            { q: 0, r: 1, description: "下方邻居" },
            { q: 1, r: -1, description: "右上邻居" },
            { q: 2, r: -2, description: "应该在(1,-2)和(2,-3)之间" }
        ];
        extraPoints.forEach(function (point) {
            var calculated = _this.getHexWorldPosition(point.q, point.r);
            console.log("\uD83D\uDCD0 (" + point.q + ", " + point.r + ") " + point.description + ": (" + calculated.x.toFixed(1) + ", " + calculated.y.toFixed(1) + ")");
        });
        // 验证左右间距（q方向）
        console.log("\n📏 验证左右间距:");
        var pos1 = this.getHexWorldPosition(0, 0);
        var pos2 = this.getHexWorldPosition(1, 0);
        var actualSpacing = pos2.x - pos1.x; // 不用绝对值，看方向
        console.log("\u4ECE(0,0)\u5230(1,0)\u7684x\u95F4\u8DDD: " + actualSpacing.toFixed(1) + " (\u671F\u671B: 97)");
        if (Math.abs(actualSpacing - 97) < 2) {
            console.log("✅ 左右间距正确");
        }
        else {
            console.log("\u274C \u5DE6\u53F3\u95F4\u8DDD\u4E0D\u6B63\u786E\uFF0C\u5DEE\u503C: " + (actualSpacing - 97).toFixed(1));
        }
    };
    // 从位置计算六边形坐标
    HexSingleChessBoardController.prototype.getHexCoordinateFromPosition = function (pos) {
        // 简化的六边形坐标转换，实际项目中可能需要更精确的算法
        var q = Math.round(pos.x / (this.HEX_SIZE * 1.5));
        var r = Math.round((pos.y - pos.x * Math.tan(Math.PI / 6)) / this.HEX_HEIGHT);
        return { q: q, r: r };
    };
    // 计算六边形世界坐标位置（参考联机版Level_S001的实现）
    HexSingleChessBoardController.prototype.getHexWorldPosition = function (q, r, isPlayerAvatar) {
        if (isPlayerAvatar === void 0) { isPlayerAvatar = false; }
        console.log("\uD83D\uDD0D \u8BA1\u7B97\u5750\u6807: (" + q + ", " + r + ")\uFF0C\u5F53\u524D\u68CB\u76D8: " + this.currentBoardType);
        // 根据当前棋盘类型获取配置
        var config = this.getBoardConfig(this.currentBoardType);
        if (!config) {
            console.error("\u274C \u672A\u627E\u5230\u68CB\u76D8 " + this.currentBoardType + " \u7684\u914D\u7F6E");
            return cc.v2(0, 0);
        }
        // 首先检查是否有精确坐标
        var key = q + "," + r;
        if (config.exactCoords.has(key)) {
            var pos = config.exactCoords.get(key);
            console.log("\u2705 \u4F7F\u7528\u7CBE\u786E\u5750\u6807: (" + q + ", " + r + ") \u2192 (" + pos.x + ", " + pos.y + ")");
            // 如果是玩家头像预制体，y轴向上偏移
            if (isPlayerAvatar) {
                return cc.v2(pos.x, pos.y + 20);
            }
            return pos;
        }
        // 使用联机版的逻辑：每行有基准点，使用统一步长计算
        var x, y;
        // 如果有该行的数据，使用统一步长计算
        if (config.rowData.has(r)) {
            var data = config.rowData.get(r);
            x = data.baseX + (q - data.baseQ) * config.uniformStepX;
            y = data.y;
            console.log("\uD83D\uDCCD \u4F7F\u7528\u884C\u6570\u636E: r=" + r + "\u884C\uFF0C\u57FA\u51C6\u70B9(" + data.baseQ + ", " + data.baseX + ", " + data.y + ")");
            console.log("   \u8BA1\u7B97: x = " + data.baseX + " + (" + q + " - " + data.baseQ + ") * " + config.uniformStepX + " = " + x);
        }
        else {
            // 对于其他行，使用通用的六边形轴线坐标系公式
            console.warn("\u26A0\uFE0F \u6CA1\u6709r=" + r + "\u884C\u7684\u7CBE\u786E\u6570\u636E\uFF0C\u4F7F\u7528\u901A\u7528\u516C\u5F0F");
            var stepXR = -config.uniformStepX / 2;
            var stepYR = 74;
            x = config.baseX + q * config.uniformStepX + r * stepXR;
            y = config.baseY - r * stepYR;
        }
        console.log("\uD83D\uDCD0 \u6700\u7EC8\u5750\u6807: (" + q + ", " + r + ") \u2192 (" + x.toFixed(1) + ", " + y.toFixed(1) + ")");
        // 如果是玩家头像预制体，y轴向上偏移
        if (isPlayerAvatar) {
            y += 20;
        }
        return cc.v2(x, y);
    };
    // 获取棋盘配置（参考联机版Level_S001的实现）
    HexSingleChessBoardController.prototype.getBoardConfig = function (boardType) {
        var configs = new Map();
        // Level_S001 (hexBoard1) - 第5关，您最开始给的数据
        configs.set("hexBoard1", {
            baseX: -170, baseY: -165,
            uniformStepX: 97,
            exactCoords: new Map([
                ["0,0", cc.v2(-170, -165)],
                ["0,-1", cc.v2(-220, -81)],
                ["1,-2", cc.v2(-172, 2)],
                ["2,-3", cc.v2(-122, 85)],
                ["4,-4", cc.v2(23, 171)]
            ]),
            rowData: new Map([
                [0, { baseQ: 0, baseX: -170, y: -165 }],
                [-1, { baseQ: 0, baseX: -220, y: -81 }],
                [-2, { baseQ: 1, baseX: -172, y: 2 }],
                [-3, { baseQ: 2, baseX: -122, y: 85 }],
                [-4, { baseQ: 4, baseX: 23, y: 171 }] // r=-4行：基准点(4,-4) → (23, 171)
            ])
        });
        // Level_S002 (hexBoard2) - 第10关
        configs.set("hexBoard2", {
            baseX: 0, baseY: -293,
            uniformStepX: 98,
            exactCoords: new Map([
                ["0,0", cc.v2(0, -293)],
                ["0,-1", cc.v2(-50, -209)],
                ["0,-2", cc.v2(-100, -125)],
                ["0,-3", cc.v2(-150, -42)],
                ["1,-4", cc.v2(-100, 44)],
                ["2,-5", cc.v2(-50, 127)],
                ["2,-6", cc.v2(-100, 210)],
                ["3,-7", cc.v2(-50, 293)]
            ]),
            rowData: new Map([
                [0, { baseQ: 0, baseX: 0, y: -293 }],
                [-1, { baseQ: 0, baseX: -50, y: -209 }],
                [-2, { baseQ: 0, baseX: -100, y: -125 }],
                [-3, { baseQ: 0, baseX: -150, y: -42 }],
                [-4, { baseQ: 1, baseX: -100, y: 44 }],
                [-5, { baseQ: 2, baseX: -50, y: 127 }],
                [-6, { baseQ: 2, baseX: -100, y: 210 }],
                [-7, { baseQ: 3, baseX: -50, y: 293 }] // r=-7行：基准点(3,-7) → (-50, 293)
            ])
        });
        // Level_S003 (hexBoard3) - 第15关
        configs.set("hexBoard3", {
            baseX: -146, baseY: -250,
            uniformStepX: 98,
            exactCoords: new Map([
                ["0,0", cc.v2(-146, -250)],
                ["1,1", cc.v2(0, -336)],
                ["0,-1", cc.v2(-196, -168)],
                ["1,-2", cc.v2(-146, -85)],
                ["2,-3", cc.v2(-99, -1)],
                ["1,-4", cc.v2(-246, 84)],
                ["1,-5", cc.v2(-293, 167)],
                ["2,-6", cc.v2(-246, 251)],
                ["3,-7", cc.v2(-196, 336)]
            ]),
            rowData: new Map([
                [1, { baseQ: 1, baseX: 0, y: -336 }],
                [0, { baseQ: 0, baseX: -146, y: -250 }],
                [-1, { baseQ: 0, baseX: -196, y: -168 }],
                [-2, { baseQ: 1, baseX: -146, y: -85 }],
                [-3, { baseQ: 2, baseX: -99, y: -1 }],
                [-4, { baseQ: 1, baseX: -246, y: 84 }],
                [-5, { baseQ: 1, baseX: -293, y: 167 }],
                [-6, { baseQ: 2, baseX: -246, y: 251 }],
                [-7, { baseQ: 3, baseX: -196, y: 336 }] // r=-7行：基准点(3,-7) → (-196, 336)
            ])
        });
        // Level_S004 (hexBoard4) - 第20关，同联机版
        configs.set("hexBoard4", {
            baseX: -300, baseY: -258,
            uniformStepX: 86,
            exactCoords: new Map([
                ["0,0", cc.v2(-300, -258)],
                ["1,-1", cc.v2(-258, -184)],
                ["1,-2", cc.v2(-300, -108)],
                ["2,-3", cc.v2(-258, -36)],
                ["2,-4", cc.v2(-300, 37)],
                ["3,-5", cc.v2(-258, 110)],
                ["3,-6", cc.v2(-300, 185)],
                ["4,-7", cc.v2(-258, 260)]
            ]),
            rowData: new Map([
                [0, { baseQ: 0, baseX: -300, y: -258 }],
                [-1, { baseQ: 1, baseX: -258, y: -184 }],
                [-2, { baseQ: 1, baseX: -300, y: -108 }],
                [-3, { baseQ: 2, baseX: -258, y: -36 }],
                [-4, { baseQ: 2, baseX: -300, y: 37 }],
                [-5, { baseQ: 3, baseX: -258, y: 110 }],
                [-6, { baseQ: 3, baseX: -300, y: 185 }],
                [-7, { baseQ: 4, baseX: -258, y: 260 }]
            ])
        });
        // Level_S005 (hexBoard5) - 第25关，预制体scale改为0.8
        configs.set("hexBoard5", {
            baseX: -257, baseY: -293,
            uniformStepX: 85.5,
            scale: 0.8,
            exactCoords: new Map([
                ["0,0", cc.v2(-257, -293)],
                ["0,-1", cc.v2(-300, -219)],
                ["1,-2", cc.v2(-257, -146)],
                ["1,-3", cc.v2(-300, -74)],
                ["2,-4", cc.v2(-257, 0)],
                ["2,-5", cc.v2(-300, 74)],
                ["3,-6", cc.v2(-257, 146)],
                ["3,-7", cc.v2(-300, 219)],
                ["4,-8", cc.v2(-257, 293)]
            ]),
            rowData: new Map([
                [0, { baseQ: 0, baseX: -257, y: -293 }],
                [-1, { baseQ: 0, baseX: -300, y: -219 }],
                [-2, { baseQ: 1, baseX: -257, y: -146 }],
                [-3, { baseQ: 1, baseX: -300, y: -74 }],
                [-4, { baseQ: 2, baseX: -257, y: 0 }],
                [-5, { baseQ: 2, baseX: -300, y: 74 }],
                [-6, { baseQ: 3, baseX: -257, y: 146 }],
                [-7, { baseQ: 3, baseX: -300, y: 219 }],
                [-8, { baseQ: 4, baseX: -257, y: 293 }]
            ])
        });
        // Level_S006 (hexBoard6) - 第30关，预制体scale改为0.8
        configs.set("hexBoard6", {
            baseX: -313, baseY: -298,
            uniformStepX: 78,
            scale: 0.8,
            exactCoords: new Map([
                ["0,0", cc.v2(-313, -298)],
                ["1,-1", cc.v2(-274, -233)],
                ["1,-2", cc.v2(-313, -165)],
                ["2,-3", cc.v2(-274, -99)],
                ["2,-4", cc.v2(-313, -34)],
                ["3,-5", cc.v2(-274, 34)],
                ["3,-6", cc.v2(-313, 96)],
                ["4,-7", cc.v2(-274, 165)],
                ["4,-8", cc.v2(-313, 226)],
                ["5,-9", cc.v2(-274, 300)]
            ]),
            rowData: new Map([
                [0, { baseQ: 0, baseX: -313, y: -298 }],
                [-1, { baseQ: 1, baseX: -274, y: -233 }],
                [-2, { baseQ: 1, baseX: -313, y: -165 }],
                [-3, { baseQ: 2, baseX: -274, y: -99 }],
                [-4, { baseQ: 2, baseX: -313, y: -34 }],
                [-5, { baseQ: 3, baseX: -274, y: 34 }],
                [-6, { baseQ: 3, baseX: -313, y: 96 }],
                [-7, { baseQ: 4, baseX: -274, y: 165 }],
                [-8, { baseQ: 4, baseX: -313, y: 226 }],
                [-9, { baseQ: 5, baseX: -274, y: 300 }]
            ])
        });
        return configs.get(boardType);
    };
    // 为六边形格子设置触摸事件
    HexSingleChessBoardController.prototype.setupHexGridTouchEvents = function (gridNode, q, r) {
        var _this = this;
        console.log("\uD83C\uDFAF \u4E3A\u683C\u5B50 (" + q + ", " + r + ") \u8BBE\u7F6E\u89E6\u6478\u4E8B\u4EF6\uFF0C\u8282\u70B9\u540D\u79F0: " + gridNode.name);
        // 确保节点可以接收触摸事件
        if (!gridNode.getComponent(cc.Button)) {
            // 如果没有Button组件，添加一个
            var button = gridNode.addComponent(cc.Button);
            button.transition = cc.Button.Transition.NONE; // 不需要视觉反馈
            console.log("\u2795 \u4E3A\u8282\u70B9 " + gridNode.name + " \u6DFB\u52A0\u4E86Button\u7EC4\u4EF6");
        }
        // 移除现有的触摸事件监听器
        gridNode.off(cc.Node.EventType.TOUCH_END);
        gridNode.off(cc.Node.EventType.TOUCH_START);
        gridNode.off(cc.Node.EventType.TOUCH_CANCEL);
        // 添加点击事件监听器
        gridNode.on(cc.Node.EventType.TOUCH_END, function (event) {
            console.log("\uD83D\uDC46 \u683C\u5B50 (" + q + ", " + r + ") \u88AB\u70B9\u51FB");
            _this.onHexGridClick(q, r, event);
        }, this);
        // 添加长按事件监听器
        this.setupLongPressEvent(gridNode, q, r);
        console.log("\u2705 \u683C\u5B50 (" + q + ", " + r + ") \u89E6\u6478\u4E8B\u4EF6\u8BBE\u7F6E\u5B8C\u6210");
    };
    // 设置长按事件
    HexSingleChessBoardController.prototype.setupLongPressEvent = function (gridNode, q, r) {
        var _this = this;
        var touchStartTime = 0;
        var longPressTriggered = false;
        var LONG_PRESS_DURATION = 500; // 500毫秒长按
        gridNode.on(cc.Node.EventType.TOUCH_START, function () {
            touchStartTime = Date.now();
            longPressTriggered = false;
            // 设置长按定时器
            _this.scheduleOnce(function () {
                if (!longPressTriggered && (Date.now() - touchStartTime) >= LONG_PRESS_DURATION) {
                    longPressTriggered = true;
                    _this.onHexGridLongPress(q, r);
                }
            }, LONG_PRESS_DURATION / 1000);
        }, this);
        gridNode.on(cc.Node.EventType.TOUCH_END, function () {
            longPressTriggered = true; // 防止长按事件触发
        }, this);
        gridNode.on(cc.Node.EventType.TOUCH_CANCEL, function () {
            longPressTriggered = true; // 防止长按事件触发
        }, this);
    };
    // 六边形格子点击事件 - 发送挖掘操作
    HexSingleChessBoardController.prototype.onHexGridClick = function (q, r, _event) {
        console.log("\uD83C\uDFAF \u516D\u8FB9\u5F62\u683C\u5B50\u70B9\u51FB\u4E8B\u4EF6\u89E6\u53D1: (" + q + ", " + r + ")");
        // 检查坐标是否有效
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn("\u274C \u65E0\u6548\u7684\u516D\u8FB9\u5F62\u5750\u6807: (" + q + ", " + r + ")");
            return;
        }
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        // 检查该位置是否已经有预制体
        if (gridData && gridData.hasPlayer) {
            console.warn("\u26A0\uFE0F \u683C\u5B50(" + q + ", " + r + ")\u5DF2\u6709\u9884\u5236\u4F53");
            return;
        }
        // 防重复点击检查
        var currentTime = Date.now();
        var positionKey = q + "," + r;
        if (currentTime - this.lastClickTime < this.CLICK_COOLDOWN &&
            this.lastClickPosition === positionKey) {
            console.warn("点击过于频繁，忽略本次点击");
            return;
        }
        this.lastClickTime = currentTime;
        this.lastClickPosition = positionKey;
        console.log("\uD83D\uDCE4 \u53D1\u9001LevelClickBlock\u6D88\u606F: (" + q + ", " + r + "), action=1");
        // 发送LevelClickBlock消息 (action = 1 表示挖掘)
        this.sendLevelClickBlock(q, r, 1);
    };
    // 六边形格子长按事件 - 发送标记操作
    HexSingleChessBoardController.prototype.onHexGridLongPress = function (q, r) {
        // 检查坐标是否有效
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn("\u274C \u65E0\u6548\u7684\u516D\u8FB9\u5F62\u5750\u6807: (" + q + ", " + r + ")");
            return;
        }
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        // 检查该位置是否已经有biaoji预制体
        if (this.hasBiaojiAt(q, r)) {
            // 如果已经有biaoji，则删除它（本地立即处理）
            this.removeBiaojiAt(q, r);
            // 发送取消标记消息
            this.sendLevelClickBlock(q, r, 2);
        }
        else if (!gridData || !gridData.hasPlayer) {
            // 如果没有任何预制体，则生成biaoji（本地立即处理）
            this.createBiaojiPrefab(q, r);
            // 发送标记消息
            this.sendLevelClickBlock(q, r, 2);
        }
        else {
            // 如果有其他类型的预制体（如数字、boom），则不处理
            console.warn("\u683C\u5B50(" + q + ", " + r + ")\u5DF2\u6709\u5176\u4ED6\u9884\u5236\u4F53\uFF0C\u65E0\u6CD5\u6807\u8BB0");
        }
    };
    // 检查六边形坐标是否有效
    HexSingleChessBoardController.prototype.isValidHexCoordinate = function (q, r) {
        var key = this.getHexKey(q, r);
        return this.hexGridData.has(key);
    };
    // 发送LevelClickBlock消息（参考四边形单机控制器）
    HexSingleChessBoardController.prototype.sendLevelClickBlock = function (q, r, action) {
        var message = {
            q: q,
            r: r,
            action: action // 1 = 挖掘, 2 = 标记/取消标记
        };
        // 发送WebSocket消息
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeLevelClickBlock, message);
    };
    // 检查指定位置是否有biaoji预制体
    HexSingleChessBoardController.prototype.hasBiaojiAt = function (q, r) {
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (!gridData || !gridData.hasPlayer || !gridData.playerNode) {
            return false;
        }
        // 检查节点名称是否为Biaoji
        return gridData.playerNode.name === "HexBiaoji";
    };
    // 移除指定位置的biaoji预制体
    HexSingleChessBoardController.prototype.removeBiaojiAt = function (q, r) {
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (gridData && gridData.hasPlayer && gridData.playerNode &&
            gridData.playerNode.name === "HexBiaoji") {
            // 播放消失动画
            var biaojiNode_1 = gridData.playerNode;
            cc.tween(biaojiNode_1)
                .to(0.2, { scaleX: 0, scaleY: 0, opacity: 0 })
                .call(function () {
                biaojiNode_1.removeFromParent();
            })
                .start();
            // 更新格子数据
            gridData.hasPlayer = false;
            gridData.playerNode = null;
        }
    };
    // 创建biaoji预制体
    HexSingleChessBoardController.prototype.createBiaojiPrefab = function (q, r) {
        if (!this.biaojiPrefab) {
            console.error("biaojiPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }
        // 实例化biaoji预制体
        var biaojiNode = cc.instantiate(this.biaojiPrefab);
        biaojiNode.name = "HexBiaoji";
        // 设置位置
        var position = this.getHexWorldPosition(q, r, false);
        biaojiNode.setPosition(position);
        // 添加到棋盘
        this.currentBoardNode.addChild(biaojiNode);
        // 获取当前棋盘的缩放配置
        var config = this.getBoardConfig(this.currentBoardType);
        var targetScale = (config === null || config === void 0 ? void 0 : config.scale) || 1.0;
        console.log("\uD83C\uDFAF \u521B\u5EFABiaoji\u9884\u5236\u4F53: \u68CB\u76D8=" + this.currentBoardType + ", \u76EE\u6807\u7F29\u653E=" + targetScale);
        // 播放出现动画
        biaojiNode.setScale(0);
        cc.tween(biaojiNode)
            .to(0.2, { scaleX: targetScale, scaleY: targetScale }, { easing: 'backOut' })
            .start();
        // 更新格子数据
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (gridData) {
            gridData.hasPlayer = true;
            gridData.playerNode = biaojiNode;
        }
    };
    // 创建boom预制体
    HexSingleChessBoardController.prototype.createBoomPrefab = function (q, r, isCurrentUser) {
        if (isCurrentUser === void 0) { isCurrentUser = true; }
        if (!this.boomPrefab) {
            console.error("boomPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }
        // 实例化boom预制体
        var boomNode = cc.instantiate(this.boomPrefab);
        boomNode.name = "HexBoom";
        // 设置位置
        var position = this.getHexWorldPosition(q, r, false);
        boomNode.setPosition(position);
        // 添加到棋盘
        this.currentBoardNode.addChild(boomNode);
        // 获取当前棋盘的缩放配置
        var config = this.getBoardConfig(this.currentBoardType);
        var targetScale = (config === null || config === void 0 ? void 0 : config.scale) || 1.0;
        console.log("\uD83D\uDCA5 \u521B\u5EFABoom\u9884\u5236\u4F53: \u68CB\u76D8=" + this.currentBoardType + ", \u76EE\u6807\u7F29\u653E=" + targetScale);
        // 播放出现动画
        var bounceScale = targetScale * 1.2; // 弹跳效果，基于目标缩放
        boomNode.setScale(0);
        cc.tween(boomNode)
            .to(0.3, { scaleX: bounceScale, scaleY: bounceScale }, { easing: 'backOut' })
            .to(0.1, { scaleX: targetScale, scaleY: targetScale })
            .start();
        // 只有当前用户点到雷时才播放棋盘震动效果
        if (isCurrentUser) {
            this.playBoardShakeAnimation();
        }
        // 更新格子数据
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (gridData) {
            gridData.hasPlayer = true;
            gridData.playerNode = boomNode;
        }
        // 设置标记，表示点到了炸弹
        this.hasBombExploded = true;
    };
    // 创建数字预制体
    HexSingleChessBoardController.prototype.createNumberPrefab = function (q, r, number) {
        // 根据数字选择对应的预制体
        var prefab = null;
        switch (number) {
            case 1:
                prefab = this.boom1Prefab;
                break;
            case 2:
                prefab = this.boom2Prefab;
                break;
            case 3:
                prefab = this.boom3Prefab;
                break;
            case 4:
                prefab = this.boom4Prefab;
                break;
            case 5:
                prefab = this.boom5Prefab;
                break;
            case 6:
                prefab = this.boom6Prefab;
                break;
            case 7:
                prefab = this.boom7Prefab;
                break;
            case 8:
                prefab = this.boom8Prefab;
                break;
            default:
                console.error("\u4E0D\u652F\u6301\u7684\u6570\u5B57: " + number);
                return;
        }
        if (!prefab) {
            console.error("boom" + number + "Prefab \u9884\u5236\u4F53\u672A\u8BBE\u7F6E\uFF0C\u8BF7\u5728\u7F16\u8F91\u5668\u4E2D\u6302\u8F7D");
            return;
        }
        // 实例化数字预制体
        var numberNode = cc.instantiate(prefab);
        numberNode.name = "HexBoom" + number;
        // 设置位置
        var position = this.getHexWorldPosition(q, r, false);
        numberNode.setPosition(position);
        // 添加到棋盘
        this.currentBoardNode.addChild(numberNode);
        // 获取当前棋盘的缩放配置
        var config = this.getBoardConfig(this.currentBoardType);
        var targetScale = (config === null || config === void 0 ? void 0 : config.scale) || 1.0;
        console.log("\uD83D\uDD22 \u521B\u5EFA\u6570\u5B57" + number + "\u9884\u5236\u4F53: \u68CB\u76D8=" + this.currentBoardType + ", \u76EE\u6807\u7F29\u653E=" + targetScale);
        // 播放出现动画
        numberNode.setScale(0);
        cc.tween(numberNode)
            .to(0.2, { scaleX: targetScale, scaleY: targetScale }, { easing: 'backOut' })
            .start();
        // 更新格子数据
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (gridData) {
            gridData.hasPlayer = true;
            gridData.playerNode = numberNode;
        }
    };
    // 播放棋盘震动动画
    HexSingleChessBoardController.prototype.playBoardShakeAnimation = function () {
        if (!this.currentBoardNode) {
            return;
        }
        var originalPosition = this.currentBoardNode.getPosition();
        var shakeIntensity = 10;
        cc.tween(this.currentBoardNode)
            .to(0.05, { x: originalPosition.x + shakeIntensity, y: originalPosition.y })
            .to(0.05, { x: originalPosition.x - shakeIntensity, y: originalPosition.y })
            .to(0.05, { x: originalPosition.x, y: originalPosition.y + shakeIntensity })
            .to(0.05, { x: originalPosition.x, y: originalPosition.y - shakeIntensity })
            .to(0.05, { x: originalPosition.x, y: originalPosition.y })
            .start();
    };
    // 隐藏指定位置的六边形小格子（点击时调用）
    HexSingleChessBoardController.prototype.hideHexGridAt = function (q, r, immediate) {
        if (immediate === void 0) { immediate = false; }
        console.log("\uD83D\uDD0D \u5C1D\u8BD5\u9690\u85CF\u683C\u5B50: q=" + q + " (" + typeof q + "), r=" + r + " (" + typeof r + "), immediate=" + immediate);
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn("\u274C \u9690\u85CF\u683C\u5B50\u5931\u8D25\uFF1A\u5750\u6807(" + q + ", " + r + ")\u65E0\u6548");
            console.warn("   \u5750\u6807\u7C7B\u578B: q=" + typeof q + ", r=" + typeof r);
            console.warn("   \u6709\u6548\u5750\u6807\u5217\u8868: " + this.validHexCoords.map(function (c) { return "(" + c.q + "," + c.r + ")"; }).join(', '));
            return;
        }
        // 获取格子节点
        var key = this.getHexKey(q, r);
        var gridNode = this.hexGridNodes.get(key);
        if (gridNode) {
            if (immediate) {
                // 立即隐藏，不播放动画
                gridNode.active = false;
            }
            else {
                // 播放六边形格子消失动画
                this.playHexGridFallAnimation(gridNode);
            }
        }
    };
    // 播放六边形格子掉落动画
    HexSingleChessBoardController.prototype.playHexGridFallAnimation = function (gridNode) {
        // 保存原始位置
        var originalPos = gridNode.getPosition();
        gridNode['originalPosition'] = originalPos;
        // 播放掉落动画
        cc.tween(gridNode)
            .parallel(cc.tween().to(0.5, { y: originalPos.y - 200 }, { easing: 'sineIn' }), cc.tween().to(0.3, { opacity: 0 }), cc.tween().to(0.5, { angle: 180 }))
            .call(function () {
            gridNode.active = false;
        })
            .start();
    };
    // 显示所有隐藏的格子（游戏结束时调用）
    HexSingleChessBoardController.prototype.showAllHiddenGrids = function () {
        if (!this.currentBoardNode) {
            console.warn("⚠️ 棋盘节点不存在，无法显示隐藏格子");
            return;
        }
        var totalGrids = 0;
        var restoredGrids = 0;
        var alreadyVisibleGrids = 0;
        // 遍历棋盘的所有子节点
        var children = this.currentBoardNode.children;
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            // 如果是六边形小格子节点（sixblock_q_r 格式）
            if (child.name.startsWith("sixblock_") || child.name === "hexblock") {
                totalGrids++;
                // 记录恢复前的状态
                var wasHidden = !child.active || child.opacity < 255 || child.scaleX < 1 || child.scaleY < 1;
                if (wasHidden) {
                    restoredGrids++;
                }
                else {
                    alreadyVisibleGrids++;
                }
                // 停止所有可能正在进行的动画
                child.stopAllActions();
                // 恢复显示状态
                child.active = true;
                child.opacity = 255;
                child.scaleX = 1;
                child.scaleY = 1;
                child.angle = 0; // 重置旋转角度
                // 恢复原始位置（如果有保存的话）
                if (child['originalPosition']) {
                    child.setPosition(child['originalPosition']);
                }
                // 确保格子可以交互
                var button = child.getComponent(cc.Button);
                if (button) {
                    button.enabled = true;
                }
            }
        }
        if (totalGrids === 0) {
            console.warn("⚠️ 没有找到任何六边形格子节点！请检查节点命名是否正确");
        }
    };
    // 清除所有预制体（游戏结束时调用）
    HexSingleChessBoardController.prototype.clearAllPrefabs = function () {
        if (!this.currentBoardNode) {
            console.warn("⚠️ 棋盘节点不存在，无法清除预制体");
            return;
        }
        var clearedCount = 0;
        var children = this.currentBoardNode.children.slice(); // 创建副本避免遍历时修改数组
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            var nodeName = child.name;
            // 检查是否是需要清除的游戏预制体
            if (this.isGamePrefab(nodeName)) {
                child.removeFromParent();
                clearedCount++;
            }
        }
        // 重置格子数据中的预制体状态
        this.hexGridData.forEach(function (gridData) {
            if (gridData.hasPlayer) {
                gridData.hasPlayer = false;
                gridData.playerNode = null;
            }
        });
        // 重置炸弹爆炸标记
        this.hasBombExploded = false;
    };
    // 判断是否为游戏预制体（需要清除的预制体）
    HexSingleChessBoardController.prototype.isGamePrefab = function (nodeName) {
        // 跳过六边形格子节点
        if (nodeName.startsWith("sixblock_") || nodeName === "hexblock") {
            return false;
        }
        // 炸弹预制体
        if (nodeName === "HexBoom") {
            return true;
        }
        // 数字预制体（HexBoom1, HexBoom2, HexBoom3 等）
        if (nodeName.match(/^HexBoom\d+$/)) {
            return true;
        }
        // 标记预制体
        if (nodeName === "HexBiaoji") {
            return true;
        }
        return false;
    };
    /**
     * 处理点击响应，根据服务器返回的结果更新棋盘
     * @param q 格子q坐标
     * @param r 格子r坐标
     * @param result 点击结果 ("boom" | "safe" | number)
     */
    HexSingleChessBoardController.prototype.handleClickResponse = function (q, r, result) {
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn("\u5904\u7406\u70B9\u51FB\u54CD\u5E94\u5931\u8D25\uFF1A\u5750\u6807(" + q + ", " + r + ")\u65E0\u6548");
            return;
        }
        // 如果格子上有biaoji预制体，先移除它
        if (this.hasBiaojiAt(q, r)) {
            // 直接移除，不播放动画
            var gridData_1 = this.hexGridData.get(this.getHexKey(q, r));
            if (gridData_1 && gridData_1.playerNode) {
                gridData_1.playerNode.removeFromParent();
                gridData_1.playerNode = null;
            }
        }
        // 标记格子已被处理，防止重复点击
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (gridData) {
            gridData.hasPlayer = true;
        }
        // 使用连锁动画的方式处理单个格子，保持一致性
        this.playGridDisappearAnimation(q, r, result);
    };
    /**
     * 批量处理连锁反应的格子
     * @param revealedGrids 被揭开的格子列表，支持 {q,r} 或 {x,y} 格式
     */
    HexSingleChessBoardController.prototype.handleChainReaction = function (revealedGrids) {
        var _this = this;
        if (!revealedGrids || revealedGrids.length === 0) {
            return;
        }
        console.log("🔗 处理连锁反应数据:", revealedGrids);
        // 同时播放所有连锁格子的消失动画，不使用延迟
        revealedGrids.forEach(function (block, index) {
            console.log("   \uD83D\uDD0D \u683C\u5B50" + (index + 1) + "\u539F\u59CB\u6570\u636E:", JSON.stringify(block));
            // 处理坐标映射：服务器可能返回 x,y 格式
            var coordQ, coordR, neighborMines;
            if (block.q !== undefined && block.r !== undefined) {
                // 标准六边形坐标格式
                coordQ = block.q;
                coordR = block.r;
                neighborMines = block.neighborMines !== undefined ? block.neighborMines : block.result;
                console.log("   \u2705 \u683C\u5B50" + (index + 1) + ": \u4F7F\u7528\u6807\u51C6\u5750\u6807 (" + coordQ + ", " + coordR + "), \u5730\u96F7\u6570: " + neighborMines);
            }
            else if (block.x !== undefined && block.y !== undefined) {
                // 服务器返回x,y格式，映射为六边形坐标
                coordQ = block.x; // x 就是 q
                coordR = block.y; // y 就是 r
                neighborMines = block.neighborMines !== undefined ? block.neighborMines : block.result;
                console.log("   \u2705 \u683C\u5B50" + (index + 1) + ": \u6620\u5C04\u5750\u6807 (x=" + block.x + ", y=" + block.y + ") -> (q=" + coordQ + ", r=" + coordR + "), \u5730\u96F7\u6570: " + neighborMines);
            }
            else {
                console.error("   \u274C \u683C\u5B50" + (index + 1) + ": \u65E0\u6548\u7684\u5750\u6807\u6570\u636E:", block);
                console.error("      \u53EF\u7528\u5B57\u6BB5: " + Object.keys(block).join(', '));
                return;
            }
            // 验证坐标是否为有效数字
            if (typeof coordQ !== 'number' || typeof coordR !== 'number' ||
                isNaN(coordQ) || isNaN(coordR)) {
                console.error("   \u274C \u683C\u5B50" + (index + 1) + ": \u5750\u6807\u4E0D\u662F\u6709\u6548\u6570\u5B57: q=" + coordQ + ", r=" + coordR);
                return;
            }
            // 立即播放动画，不延迟
            console.log("   \uD83C\uDFAC \u64AD\u653E\u683C\u5B50\u52A8\u753B: (" + coordQ + ", " + coordR + "), \u7ED3\u679C: " + neighborMines);
            _this.playGridDisappearAnimation(coordQ, coordR, neighborMines);
        });
    };
    // 播放格子消失动画并更新显示
    HexSingleChessBoardController.prototype.playGridDisappearAnimation = function (q, r, result) {
        var _this = this;
        // 先隐藏格子
        this.hideHexGridAt(q, r, false);
        // 延迟显示结果，让格子消失动画先播放
        this.scheduleOnce(function () {
            _this.updateNeighborMinesDisplay(q, r, result);
        }, 0.3);
    };
    /**
     * 更新指定位置的neighborMines显示（使用boom数字预制体）
     * @param q 格子q坐标
     * @param r 格子r坐标
     * @param neighborMines 周围地雷数量或结果类型
     */
    HexSingleChessBoardController.prototype.updateNeighborMinesDisplay = function (q, r, neighborMines) {
        if (neighborMines === "boom" || neighborMines === "mine") {
            // 踩到地雷，生成boom预制体并触发震动
            this.createBoomPrefab(q, r, true); // true表示是当前用户踩到的雷，需要震动
            // 设置标记，表示点到了炸弹
            this.hasBombExploded = true;
        }
        else if (typeof neighborMines === "number" && neighborMines > 0) {
            // 显示数字
            this.createNumberPrefab(q, r, neighborMines);
        }
        else {
            // 如果是0、"safe"或其他，则不显示任何预制体
        }
    };
    /**
     * 处理ExtendLevelInfo消息（游戏开始时调用）
     */
    HexSingleChessBoardController.prototype.onExtendLevelInfo = function () {
        this.showAllHiddenGrids();
        this.clearAllPrefabs();
    };
    /**
     * 处理LevelGameEnd消息（游戏结束时调用）
     * 注意：不清理任何数据，保持玩家的游玩痕迹
     */
    HexSingleChessBoardController.prototype.onLevelGameEnd = function () {
        // 不显示隐藏的格子，保持当前状态
        // 不清理预制体，不重置格子状态，保持游戏结果显示
        // 让玩家可以看到自己的标记（biaoji）、挖掘结果（数字、boom）等
    };
    /**
     * 重新初始化棋盘数据（仅在开始新游戏时调用）
     */
    HexSingleChessBoardController.prototype.reinitializeBoardData = function () {
        // 重置hexGridData中的预制体状态
        this.hexGridData.forEach(function (gridData) {
            gridData.hasPlayer = false;
            gridData.playerNode = null;
        });
        // 重置炸弹爆炸标记
        this.hasBombExploded = false;
    };
    /**
     * 禁用所有格子的触摸事件（游戏结束时调用）
     */
    HexSingleChessBoardController.prototype.disableAllGridTouch = function () {
        console.log("🚫 禁用所有六边形格子的触摸事件");
        this.hexGridNodes.forEach(function (gridNode, key) {
            if (gridNode && cc.isValid(gridNode)) {
                // 移除所有触摸事件监听器
                gridNode.off(cc.Node.EventType.TOUCH_END);
                gridNode.off(cc.Node.EventType.TOUCH_START);
                gridNode.off(cc.Node.EventType.TOUCH_CANCEL);
                // 禁用Button组件（如果有的话）
                var button = gridNode.getComponent(cc.Button);
                if (button) {
                    button.enabled = false;
                }
            }
        });
        console.log("\u2705 \u5DF2\u7981\u7528 " + this.hexGridNodes.size + " \u4E2A\u516D\u8FB9\u5F62\u683C\u5B50\u7684\u89E6\u6478\u4E8B\u4EF6");
    };
    /**
     * 检查是否点到了炸弹
     * @returns 是否点到了炸弹
     */
    HexSingleChessBoardController.prototype.hasBombExplodedInThisGame = function () {
        return this.hasBombExploded;
    };
    /**
     * 重置棋盘状态（清理所有预制体和格子状态）
     */
    HexSingleChessBoardController.prototype.resetBoard = function () {
        console.log("🔄 重置六边形棋盘状态");
        // 显示所有隐藏的格子
        this.showAllHiddenGrids();
        // 清除所有预制体
        this.clearAllPrefabs();
        // 重新初始化棋盘数据
        this.reinitializeBoardData();
        console.log("✅ 六边形棋盘重置完成");
    };
    /**
     * 启用所有格子的触摸事件
     */
    HexSingleChessBoardController.prototype.enableAllGridTouch = function () {
        console.log("✅ 启用所有六边形格子的触摸事件");
        this.enableTouchForExistingGrids();
    };
    /**
     * 创建自定义预制体（用于调试等特殊用途）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param prefab 预制体
     * @param nodeName 节点名称
     */
    HexSingleChessBoardController.prototype.createCustomPrefab = function (q, r, prefab, nodeName) {
        if (!prefab) {
            console.error("\u81EA\u5B9A\u4E49\u9884\u5236\u4F53\u672A\u8BBE\u7F6E: " + nodeName);
            return null;
        }
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn("\u65E0\u6548\u7684\u516D\u8FB9\u5F62\u5750\u6807: (" + q + ", " + r + ")");
            return null;
        }
        // 实例化预制体
        var customNode = cc.instantiate(prefab);
        customNode.name = nodeName;
        // 设置位置
        var position = this.getHexWorldPosition(q, r, false);
        customNode.setPosition(position);
        // 添加到棋盘
        this.currentBoardNode.addChild(customNode);
        console.log("\u2705 \u521B\u5EFA\u81EA\u5B9A\u4E49\u9884\u5236\u4F53: " + nodeName + " at (" + q + ", " + r + ")");
        return customNode;
    };
    /**
     * 重置炸弹爆炸状态（开始新游戏时调用）
     */
    HexSingleChessBoardController.prototype.resetBombExplodedStatus = function () {
        this.hasBombExploded = false;
        console.log("🔄 重置炸弹爆炸状态");
    };
    /**
     * 获取当前棋盘类型
     */
    HexSingleChessBoardController.prototype.getCurrentBoardType = function () {
        return this.currentBoardType;
    };
    /**
     * 获取当前棋盘配置（六边形版本返回简化信息）
     */
    HexSingleChessBoardController.prototype.getCurrentBoardConfig = function () {
        return {
            boardType: this.currentBoardType,
            gridCount: this.getHexGridCount(),
            hasBombExploded: this.hasBombExploded
        };
    };
    /**
     * 获取棋盘状态信息（调试用）
     */
    HexSingleChessBoardController.prototype.getHexBoardInfo = function () {
        var info = {
            validHexCoordsCount: this.validHexCoords.length,
            boardNodeChildren: this.currentBoardNode ? this.currentBoardNode.children.length : 0,
            hasPlayerGamePrefab: false,
            hasBoardNode: !!this.currentBoardNode,
            currentBoardType: this.currentBoardType,
            hexGridDataSize: this.hexGridData.size,
            hexGridNodesSize: this.hexGridNodes.size,
            hasBombExploded: this.hasBombExploded
        };
        return info;
    };
    /**
     * 获取前端节点的总数量（用于计算炸弹数量）
     */
    HexSingleChessBoardController.prototype.getHexGridCount = function () {
        return this.validHexCoords.length;
    };
    /**
     * 开始新游戏时的重置方法
     */
    HexSingleChessBoardController.prototype.resetForNewGame = function () {
        // 显示所有隐藏的格子
        this.showAllHiddenGrids();
        // 清除所有预制体
        this.clearAllPrefabs();
        // 重新初始化棋盘数据
        this.reinitializeBoardData();
    };
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boomPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "biaojiPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boom1Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boom2Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boom3Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boom4Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boom5Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boom6Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boom7Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boom8Prefab", void 0);
    __decorate([
        property(cc.Node)
    ], HexSingleChessBoardController.prototype, "hexBoard1Node", void 0);
    __decorate([
        property(cc.Node)
    ], HexSingleChessBoardController.prototype, "hexBoard2Node", void 0);
    __decorate([
        property(cc.Node)
    ], HexSingleChessBoardController.prototype, "hexBoard3Node", void 0);
    __decorate([
        property(cc.Node)
    ], HexSingleChessBoardController.prototype, "hexBoard4Node", void 0);
    __decorate([
        property(cc.Node)
    ], HexSingleChessBoardController.prototype, "hexBoard5Node", void 0);
    __decorate([
        property(cc.Node)
    ], HexSingleChessBoardController.prototype, "hexBoard6Node", void 0);
    HexSingleChessBoardController = __decorate([
        ccclass
    ], HexSingleChessBoardController);
    return HexSingleChessBoardController;
}(cc.Component));
exports.default = HexSingleChessBoardController;

cc._RF.pop();