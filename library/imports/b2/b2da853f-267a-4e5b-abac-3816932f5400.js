"use strict";
cc._RF.push(module, 'b2da8U/JnpOW6usOBaTL1QA', 'ChessBoardController');
// scripts/game/Chess/ChessBoardController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __spreadArrays = (this && this.__spreadArrays) || function () {
    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;
    for (var r = Array(s), k = 0, i = 0; i < il; i++)
        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)
            r[k] = a[j];
    return r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalBean_1 = require("../../bean/GlobalBean");
var PlayerGameController_1 = require("../../pfb/PlayerGameController ");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var ChessBoardController = /** @class */ (function (_super) {
    __extends(ChessBoardController, _super);
    function ChessBoardController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.playerGamePrefab = null;
        _this.boomPrefab = null;
        _this.biaojiPrefab = null;
        _this.boom1Prefab = null;
        _this.boom2Prefab = null;
        _this.boom3Prefab = null;
        _this.boom4Prefab = null;
        _this.boom5Prefab = null;
        _this.boom6Prefab = null;
        _this.boom7Prefab = null;
        _this.boom8Prefab = null; // player_game_pfb 预制体
        _this.boardNode = null; // 棋盘节点
        // 棋盘配置
        _this.BOARD_SIZE = 8; // 8x8棋盘
        _this.BOARD_WIDTH = 750; // 棋盘总宽度
        _this.BOARD_HEIGHT = 750; // 棋盘总高度
        _this.GRID_SIZE = 88; // 每个格子的大小 88x88
        // 格子数据存储
        _this.gridData = []; // 二维数组存储格子数据
        _this.gridNodes = []; // 二维数组存储格子节点
        // 添加到坐标历史记录
        _this.coordinateHistory = [];
        // 自定义偏移量（如果需要调整位置）
        _this.customOffsetX = 0;
        _this.customOffsetY = -16; // 恢复原来的值，保持点击生成位置正确
        return _this;
        // update (dt) {}
    }
    ChessBoardController.prototype.onLoad = function () {
        this.initBoard();
    };
    ChessBoardController.prototype.start = function () {
        var _this = this;
        // 延迟一帧后再次尝试启用触摸事件，确保所有节点都已创建完成
        this.scheduleOnce(function () {
            _this.enableTouchForExistingGrids();
        }, 0.1);
    };
    // 初始化棋盘
    ChessBoardController.prototype.initBoard = function () {
        // 初始化数据数组
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            this.gridData[x] = [];
            this.gridNodes[x] = [];
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                this.gridData[x][y] = {
                    x: x,
                    y: y,
                    worldPos: this.getGridWorldPosition(x, y),
                    hasPlayer: false
                };
            }
        }
        this.createGridNodes();
    };
    // 启用现有格子的触摸事件
    ChessBoardController.prototype.createGridNodes = function () {
        if (!this.boardNode) {
            console.error("棋盘节点未设置！");
            return;
        }
        // 如果格子已经存在，直接启用触摸事件
        this.enableTouchForExistingGrids();
    };
    // 为现有格子启用触摸事件
    ChessBoardController.prototype.enableTouchForExistingGrids = function () {
        // 检查棋盘节点是否存在
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法启用触摸事件！");
            return;
        }
        // 遍历棋盘节点的所有子节点
        var children = this.boardNode.children;
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            // 尝试从节点名称解析坐标
            var coords = this.parseGridCoordinateFromName(child.name);
            if (coords) {
                this.setupGridTouchEvents(child, coords.x, coords.y);
                this.gridNodes[coords.x] = this.gridNodes[coords.x] || [];
                this.gridNodes[coords.x][coords.y] = child;
            }
            else {
                // 如果无法从名称解析，尝试从位置计算
                var pos = child.getPosition();
                var coords_1 = this.getGridCoordinateFromPosition(pos);
                if (coords_1) {
                    this.setupGridTouchEvents(child, coords_1.x, coords_1.y);
                    this.gridNodes[coords_1.x] = this.gridNodes[coords_1.x] || [];
                    this.gridNodes[coords_1.x][coords_1.y] = child;
                }
            }
        }
    };
    // 从节点名称解析格子坐标
    ChessBoardController.prototype.parseGridCoordinateFromName = function (nodeName) {
        // 尝试匹配 Grid_x_y 格式
        var match = nodeName.match(/Grid_(\d+)_(\d+)/);
        if (match) {
            return { x: parseInt(match[1]), y: parseInt(match[2]) };
        }
        return null;
    };
    // 从位置计算格子坐标
    ChessBoardController.prototype.getGridCoordinateFromPosition = function (pos) {
        var x = Math.floor((pos.x + this.BOARD_WIDTH / 2) / this.GRID_SIZE);
        var y = Math.floor((pos.y + this.BOARD_HEIGHT / 2) / this.GRID_SIZE);
        if (this.isValidCoordinate(x, y)) {
            return { x: x, y: y };
        }
        return null;
    };
    // 为格子节点设置触摸事件
    ChessBoardController.prototype.setupGridTouchEvents = function (gridNode, x, y) {
        var _this = this;
        // 安全检查：确保坐标有效
        if (!this.isValidCoordinate(x, y)) {
            console.error("\u274C setupGridTouchEvents: \u5C1D\u8BD5\u4E3A\u65E0\u6548\u5750\u6807(" + x + "," + y + ")\u8BBE\u7F6E\u89E6\u6478\u4E8B\u4EF6");
            return;
        }
        // 长按相关变量
        var isLongPressing = false;
        var longPressTimer = 0;
        var longPressCallback = null;
        var LONG_PRESS_TIME = 1.0; // 1秒长按时间
        // 触摸开始事件
        gridNode.on(cc.Node.EventType.TOUCH_START, function (_event) {
            isLongPressing = true;
            longPressTimer = 0;
            // 开始长按检测
            longPressCallback = function () {
                if (isLongPressing) {
                    longPressTimer += 0.1;
                    if (longPressTimer >= LONG_PRESS_TIME) {
                        _this.onGridLongPress(x, y);
                        isLongPressing = false;
                        if (longPressCallback) {
                            _this.unschedule(longPressCallback);
                        }
                    }
                }
            };
            _this.schedule(longPressCallback, 0.1);
        }, this);
        // 触摸结束事件
        gridNode.on(cc.Node.EventType.TOUCH_END, function (event) {
            // 如果不是长按，则执行点击事件
            if (isLongPressing && longPressTimer < LONG_PRESS_TIME) {
                _this.onGridClick(x, y, event);
            }
            isLongPressing = false;
            if (longPressCallback) {
                _this.unschedule(longPressCallback);
            }
        }, this);
        // 触摸取消事件
        gridNode.on(cc.Node.EventType.TOUCH_CANCEL, function (_event) {
            isLongPressing = false;
            if (longPressCallback) {
                _this.unschedule(longPressCallback);
            }
        }, this);
        // 添加Button组件以确保触摸响应
        var button = gridNode.getComponent(cc.Button);
        if (!button) {
            button = gridNode.addComponent(cc.Button);
            button.transition = cc.Button.Transition.SCALE;
            button.zoomScale = 0.95;
        }
    };
    // 计算格子的世界坐标位置（左下角为(0,0)）
    ChessBoardController.prototype.getGridWorldPosition = function (x, y) {
        // 计算格子中心点位置
        // 左下角为(0,0)，所以y坐标需要从下往上计算
        var posX = (x * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_WIDTH / 2);
        var posY = (y * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_HEIGHT / 2);
        return cc.v2(posX, posY);
    };
    // 格子点击事件 - 发送挖掘操作
    ChessBoardController.prototype.onGridClick = function (x, y, _event) {
        // 检查坐标是否有效（确保在8x8棋盘范围内）
        if (!this.isValidCoordinate(x, y)) {
            return;
        }
        // 检查该位置是否已经有玩家预制体
        if (this.gridData[x][y].hasPlayer) {
            return;
        }
        // 发送挖掘操作事件 (action = 1)
        // 只发送事件，不直接生成预制体，等待GamePageController确认后再生成
        this.node.emit('chess-board-click', {
            x: x,
            y: y,
            action: 1 // 1 = 挖掘
        });
    };
    // 格子长按事件 - 发送标记操作
    ChessBoardController.prototype.onGridLongPress = function (x, y) {
        // 检查坐标是否有效（确保在8x8棋盘范围内）
        if (!this.isValidCoordinate(x, y)) {
            return;
        }
        // 检查该位置是否已经有玩家预制体
        if (this.gridData[x][y].hasPlayer) {
            return;
        }
        // 发送标记操作事件 (action = 2)
        // 只发送事件，不直接生成预制体，等待GamePageController确认后再生成
        this.node.emit('chess-board-click', {
            x: x,
            y: y,
            action: 2 // 2 = 标记
        });
    };
    // 在格子上放置玩家预制体
    ChessBoardController.prototype.placePlayerOnGrid = function (x, y, withFlag) {
        var _this = this;
        if (withFlag === void 0) { withFlag = false; }
        // 双重检查：确保坐标有效
        if (!this.isValidCoordinate(x, y)) {
            console.error("\u274C placePlayerOnGrid: \u65E0\u6548\u5750\u6807(" + x + "," + y + ")");
            return;
        }
        // 双重检查：确保格子为空
        var gridData = this.gridData[x][y];
        if (gridData.hasPlayer) {
            console.error("\u274C placePlayerOnGrid: \u683C\u5B50(" + x + "," + y + ")\u5DF2\u6709\u73A9\u5BB6\uFF0C\u4E0D\u80FD\u91CD\u590D\u653E\u7F6E");
            return;
        }
        if (!this.playerGamePrefab) {
            console.error("❌ 玩家预制体未设置！");
            return;
        }
        if (!this.boardNode) {
            console.error("❌ 棋盘节点未设置！");
            return;
        }
        // 实例化玩家预制体
        var playerNode = cc.instantiate(this.playerGamePrefab);
        // 计算正确的位置
        var correctPosition = this.calculateCorrectPosition(x, y);
        playerNode.setPosition(correctPosition);
        // 先隐藏节点，等头像加载完成后再显示
        playerNode.active = false;
        // 处理Layout限制问题
        this.addPlayerNodeSafely(playerNode);
        // 设置头像和用户数据（异步加载）
        this.setupPlayerAvatarAsync(playerNode, x, y, withFlag, function () {
            // 头像加载完成的回调，播放生成动画（点击生成和单人格子）
            _this.playAvatarSpawnAnimation(playerNode);
        });
        // 更新格子数据
        gridData.hasPlayer = true;
        gridData.playerNode = playerNode;
    };
    // 计算正确的位置（格子中心偏移(0, -16)）
    ChessBoardController.prototype.calculateCorrectPosition = function (x, y) {
        // 使用自定义偏移量
        var offsetX = this.customOffsetX;
        var offsetY = this.customOffsetY;
        // 方法1: 如果能找到对应的格子节点，使用其位置并添加偏移
        var targetGridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (targetGridNode) {
            var gridPos = targetGridNode.getPosition();
            var finalPos = cc.v2(gridPos.x + offsetX, gridPos.y + offsetY);
            return finalPos;
        }
        // 方法2: 基于棋盘的实际位置计算
        // 计算格子中心位置
        var centerX = (x * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_WIDTH / 2);
        var centerY = (y * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_HEIGHT / 2);
        // 添加偏移
        var finalX = centerX + offsetX;
        var finalY = centerY + offsetY;
        var calculatedPos = cc.v2(finalX, finalY);
        return calculatedPos;
    };
    /**
     * 计算预制体的精确位置（根据您提供的坐标规律）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @returns 预制体应该放置的精确位置
     */
    ChessBoardController.prototype.calculatePrefabPosition = function (x, y) {
        // 根据您提供的坐标规律计算：
        // (0,0) → (-314, -310)
        // (1,0) → (-224, -310)  // x增加90
        // (0,1) → (-314, -222)  // y增加88
        // (7,7) → (310, 312)
        var startX = -314; // 起始X坐标
        var startY = -310; // 起始Y坐标
        var stepX = 90; // X方向步长
        var stepY = 88; // Y方向步长
        var finalX = startX + (x * stepX);
        var finalY = startY + (y * stepY);
        var position = cc.v2(finalX, finalY);
        return position;
    };
    /**
     * 播放头像生成动画（由大变小）
     * @param playerNode 玩家节点
     */
    ChessBoardController.prototype.playAvatarSpawnAnimation = function (playerNode) {
        if (!playerNode) {
            console.warn("播放生成动画失败：节点为空");
            return;
        }
        // 显示节点
        playerNode.active = true;
        // 设置初始缩放为1.5倍（比正常大）
        var originalScale = playerNode.scaleX;
        var startScale = originalScale * 1.5;
        playerNode.setScale(startScale);
        // 使用cc.Tween创建由大变小的缩放动画
        cc.tween(playerNode)
            .to(0.3, { scaleX: originalScale, scaleY: originalScale }, { easing: 'backOut' })
            .start();
    };
    /**
     * 播放头像调整动画（平滑移动和缩小）
     * @param playerNode 玩家节点
     * @param newPosition 新位置
     * @param newScale 新缩放
     */
    ChessBoardController.prototype.playAvatarAdjustAnimation = function (playerNode, newPosition, newScale) {
        if (!playerNode) {
            console.warn("播放调整动画失败：节点为空");
            return;
        }
        // 使用cc.Tween同时播放移动和缩放动画
        cc.tween(playerNode)
            .to(0.3, {
            x: newPosition.x,
            y: newPosition.y,
            scaleX: newScale,
            scaleY: newScale
        }, { easing: 'sineOut' })
            .start();
    };
    /**
     * 根据格子总人数计算基础位置（统一逻辑）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param totalPlayers 该格子的总人数
     * @returns 基础位置
     */
    ChessBoardController.prototype.calculateBasePositionByPlayerCount = function (x, y, totalPlayers) {
        var offsetX = this.customOffsetX;
        var offsetY;
        if (totalPlayers === 1) {
            // 一个格子里只有一个人：需要偏移
            offsetY = this.customOffsetY; // -16
        }
        else {
            // 一个格子里有两个及以上：不偏移
            offsetY = 0;
        }
        // 方法1: 如果能找到对应的格子节点，使用其位置并添加偏移
        var targetGridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (targetGridNode) {
            var gridPos = targetGridNode.getPosition();
            var finalPos = cc.v2(gridPos.x + offsetX, gridPos.y + offsetY);
            return finalPos;
        }
        // 方法2: 基于棋盘的实际位置计算
        var centerX = (x * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_WIDTH / 2);
        var centerY = (y * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_HEIGHT / 2);
        var finalX = centerX + offsetX;
        var finalY = centerY + offsetY;
        var calculatedPos = cc.v2(finalX, finalY);
        return calculatedPos;
    };
    /**
     * 计算多人情况下的基础位置（不包含往下偏移，逻辑分开）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @returns 格子中心位置（多人专用，不偏移）
     */
    ChessBoardController.prototype.calculateMultiPlayerBasePosition = function (x, y) {
        // 多人情况使用独立的偏移逻辑
        var offsetX = this.customOffsetX;
        var offsetY = 0; // 多人时不往下偏移，逻辑分开
        // 方法1: 如果能找到对应的格子节点，使用其位置并添加偏移
        var targetGridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (targetGridNode) {
            var gridPos = targetGridNode.getPosition();
            var finalPos = cc.v2(gridPos.x + offsetX, gridPos.y + offsetY);
            return finalPos;
        }
        // 方法2: 基于棋盘的实际位置计算
        // 计算格子中心位置
        var centerX = (x * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_WIDTH / 2);
        var centerY = (y * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_HEIGHT / 2);
        // 添加偏移（不包含往下偏移）
        var finalX = centerX + offsetX;
        var finalY = centerY + offsetY;
        var calculatedPos = cc.v2(finalX, finalY);
        return calculatedPos;
    };
    // 安全地添加玩家节点（处理Layout限制）
    ChessBoardController.prototype.addPlayerNodeSafely = function (playerNode) {
        // 检查棋盘节点是否存在
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法添加玩家节点！");
            return;
        }
        // 检查棋盘节点是否有Layout组件
        var layout = this.boardNode.getComponent(cc.Layout);
        if (layout) {
            // 方案1: 临时禁用Layout
            layout.enabled = false;
            // 添加节点
            this.boardNode.addChild(playerNode);
        }
        else {
            this.boardNode.addChild(playerNode);
        }
        // 方案2备选：添加到Layout外部
        // this.addToParentNode(playerNode);
    };
    // 备选方案：添加到父节点（Layout外部）
    ChessBoardController.prototype.addToParentNode = function (playerNode) {
        // 检查棋盘节点是否存在
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法添加玩家节点！");
            return;
        }
        if (this.boardNode.parent) {
            // 需要转换坐标系
            var worldPos = this.boardNode.convertToWorldSpaceAR(playerNode.getPosition());
            var localPos = this.boardNode.parent.convertToNodeSpaceAR(worldPos);
            playerNode.setPosition(localPos);
            this.boardNode.parent.addChild(playerNode);
        }
        else {
            console.error("\u274C \u68CB\u76D8\u8282\u70B9\u6CA1\u6709\u7236\u8282\u70B9");
            // 回退到直接添加
            this.boardNode.addChild(playerNode);
        }
    };
    // 异步设置玩家头像（带回调）
    ChessBoardController.prototype.setupPlayerAvatarAsync = function (playerNode, x, y, withFlag, onComplete) {
        // 查找PlayerGameController组件
        var playerController = playerNode.getComponent("PlayerGameController") ||
            playerNode.getComponent("PlayerGameController ") ||
            playerNode.getComponentInChildren("PlayerGameController");
        if (playerController) {
            // 检查avatar节点是否存在
            if (playerController.avatar) {
                // 检查avatar节点是否有Sprite组件
                var avatarSprite = playerController.avatar.getComponent(cc.Sprite);
                if (!avatarSprite) {
                    avatarSprite = playerController.avatar.addComponent(cc.Sprite);
                }
                // 确保avatar节点可见
                playerController.avatar.active = true;
                playerController.avatar.opacity = 255;
            }
            else {
                console.error("❌ PlayerGameController中的avatar节点为null");
                onComplete();
                return;
            }
            // 设置旗子节点的显示状态 - 重点检查
            if (playerController.flagNode) {
                playerController.flagNode.active = withFlag;
                // 额外检查旗子节点的可见性
                if (withFlag) {
                    playerController.flagNode.opacity = 255;
                    // 确保旗子节点的父节点也是可见的
                    var parent = playerController.flagNode.parent;
                    while (parent && parent !== playerNode) {
                        parent.active = true;
                        parent = parent.parent;
                    }
                    // 延迟检查旗子是否真的显示了
                    this.scheduleOnce(function () {
                    }, 1.0);
                }
            }
            else {
                console.warn("\u26A0\uFE0F \u627E\u4E0D\u5230\u65D7\u5B50\u8282\u70B9 (" + x + "," + y + ")");
            }
            // 创建用户数据并设置头像
            var userData = {
                userId: "player_" + x + "_" + y,
                nickName: "\u73A9\u5BB6(" + x + "," + y + ")",
                avatar: this.getDefaultAvatarUrl(),
                score: 0,
                pos: 0,
                coin: 0,
                status: 0,
                rank: 0
            };
            // 使用PlayerGameController的setData方法来设置头像
            try {
                playerController.setData(userData);
                // 延迟设置旗子状态，确保在PlayerGameController初始化之后
                this.scheduleOnce(function () {
                    if (playerController.flagNode) {
                        playerController.flagNode.active = withFlag;
                    }
                    onComplete();
                }, 0.1);
            }
            catch (error) {
                console.error("设置头像数据失败:", error);
                onComplete();
            }
        }
        else {
            console.warn("⚠️ 找不到PlayerGameController组件");
            this.tryDirectAvatarSetupAsync(playerNode, x, y, onComplete);
        }
    };
    // 设置玩家头像（保留原方法用于其他地方）
    ChessBoardController.prototype.setupPlayerAvatar = function (playerNode, x, y) {
        var _this = this;
        // 查找PlayerGameController组件
        var playerController = playerNode.getComponent("PlayerGameController") ||
            playerNode.getComponent("PlayerGameController ") ||
            playerNode.getComponentInChildren("PlayerGameController");
        if (playerController) {
            // 检查avatar节点是否存在
            if (playerController.avatar) {
                // 检查avatar节点是否有Sprite组件
                var avatarSprite = playerController.avatar.getComponent(cc.Sprite);
                if (!avatarSprite) {
                    console.warn("⚠️ avatar节点缺少Sprite组件，正在添加...");
                    avatarSprite = playerController.avatar.addComponent(cc.Sprite);
                }
                // 确保avatar节点可见
                playerController.avatar.active = true;
                playerController.avatar.opacity = 255;
            }
            else {
                console.error("❌ PlayerGameController中的avatar节点为null");
                return;
            }
            // 创建用户数据
            var userData = {
                userId: "player_" + x + "_" + y,
                nickName: "\u73A9\u5BB6(" + x + "," + y + ")",
                avatar: this.getDefaultAvatarUrl(),
                score: 0,
                pos: 0,
                coin: 0,
                status: 0,
                rank: 0
            };
            // 安全地调用setData
            try {
                playerController.setData(userData);
                // 延迟检查头像是否加载成功
                this.scheduleOnce(function () {
                    _this.checkAvatarLoaded(playerController.avatar, x, y);
                }, 2.0);
            }
            catch (error) {
                console.error("❌ 设置头像时出错:", error);
            }
        }
        else {
            console.warn("⚠️ 找不到PlayerGameController组件，跳过头像设置");
            // 尝试直接在节点上查找avatar子节点
            this.tryDirectAvatarSetup(playerNode, x, y);
        }
    };
    // 检查头像是否加载成功
    ChessBoardController.prototype.checkAvatarLoaded = function (avatarNode, x, y) {
        if (!avatarNode) {
            console.error("\u274C \u4F4D\u7F6E(" + x + "," + y + ")\u7684avatar\u8282\u70B9\u4E3Anull");
            return;
        }
        var sprite = avatarNode.getComponent(cc.Sprite);
        if (!sprite) {
            console.error("\u274C \u4F4D\u7F6E(" + x + "," + y + ")\u7684avatar\u8282\u70B9\u6CA1\u6709Sprite\u7EC4\u4EF6");
            return;
        }
        if (!sprite.spriteFrame) {
            console.warn("\u26A0\uFE0F \u4F4D\u7F6E(" + x + "," + y + ")\u7684\u5934\u50CF\u53EF\u80FD\u52A0\u8F7D\u5931\u8D25\uFF0CspriteFrame\u4E3Anull");
            // 尝试设置一个默认的颜色作为备用显示
            this.setFallbackAvatar(avatarNode, x, y);
        }
        else {
        }
    };
    // 设置备用头像（纯色方块）
    ChessBoardController.prototype.setFallbackAvatar = function (avatarNode, x, y) {
        var sprite = avatarNode.getComponent(cc.Sprite);
        if (!sprite) {
            sprite = avatarNode.addComponent(cc.Sprite);
        }
        // 创建一个简单的纯色纹理
        var texture = new cc.Texture2D();
        var colors = [
            [255, 107, 107, 255],
            [78, 205, 196, 255],
            [69, 183, 209, 255],
            [150, 206, 180, 255],
            [255, 234, 167, 255] // 黄色
        ];
        var colorIndex = (x + y) % colors.length;
        var color = colors[colorIndex];
        texture.initWithData(new Uint8Array(color), cc.Texture2D.PixelFormat.RGBA8888, 1, 1);
        sprite.spriteFrame = new cc.SpriteFrame(texture);
        // 设置大小
        avatarNode.setContentSize(80, 80);
        avatarNode.active = true;
    };
    // 尝试直接设置头像（当找不到PlayerGameController时）
    ChessBoardController.prototype.tryDirectAvatarSetup = function (playerNode, x, y) {
        // 查找名为"avatar"的子节点
        var avatarNode = playerNode.getChildByName("avatar");
        if (avatarNode) {
            this.setFallbackAvatar(avatarNode, x, y);
        }
        else {
            console.warn("⚠️ 未找到avatar子节点");
            // 列出所有子节点名称
        }
    };
    // 获取默认头像URL
    ChessBoardController.prototype.getDefaultAvatarUrl = function () {
        // 使用真实的头像URL
        return "https://static.gooplay.com/online/user-avatar/1732786296530322669.jpg";
    };
    // 保存格子坐标（用于后续发送给后端）
    ChessBoardController.prototype.saveGridCoordinate = function (x, y) {
        // 这里可以将坐标保存到数组或发送给后端
        // 示例：可以调用网络管理器发送坐标
        this.sendCoordinateToServer(x, y);
        // 或者保存到本地数组以备后用
        this.addToCoordinateHistory(x, y);
    };
    // 发送坐标到服务器
    ChessBoardController.prototype.sendCoordinateToServer = function (x, y) {
        // 构造发送数据
        var moveData = {
            x: x,
            y: y,
            timestamp: Date.now(),
            playerId: this.getCurrentPlayerId()
        };
        // 暂时只是打印，避免未使用变量警告
        return moveData;
    };
    ChessBoardController.prototype.addToCoordinateHistory = function (x, y) {
        this.coordinateHistory.push({
            x: x,
            y: y,
            timestamp: Date.now()
        });
    };
    // 获取当前玩家ID（示例）
    ChessBoardController.prototype.getCurrentPlayerId = function () {
        // 这里应该从全局状态或用户数据中获取
        return "player_001"; // 示例ID
    };
    // 获取指定坐标的格子数据
    ChessBoardController.prototype.getGridData = function (x, y) {
        if (x < 0 || x >= this.BOARD_SIZE || y < 0 || y >= this.BOARD_SIZE) {
            return null;
        }
        return this.gridData[x][y];
    };
    // 清除指定格子的玩家
    ChessBoardController.prototype.clearGridPlayer = function (x, y) {
        var gridData = this.getGridData(x, y);
        if (!gridData || !gridData.hasPlayer) {
            return false;
        }
        // 移除玩家节点
        if (gridData.playerNode) {
            gridData.playerNode.removeFromParent();
            gridData.playerNode = null;
        }
        // 更新数据
        gridData.hasPlayer = false;
        return true;
    };
    // 清除所有玩家
    ChessBoardController.prototype.clearAllPlayers = function () {
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                this.clearGridPlayer(x, y);
            }
        }
    };
    // 获取所有已放置玩家的坐标
    ChessBoardController.prototype.getAllPlayerCoordinates = function () {
        var coordinates = [];
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                if (this.gridData[x][y].hasPlayer) {
                    coordinates.push({ x: x, y: y });
                }
            }
        }
        return coordinates;
    };
    // 检查坐标是否有效
    ChessBoardController.prototype.isValidCoordinate = function (x, y) {
        return x >= 0 && x < this.BOARD_SIZE && y >= 0 && y < this.BOARD_SIZE;
    };
    // 检查格子是否为空
    ChessBoardController.prototype.isGridEmpty = function (x, y) {
        if (!this.isValidCoordinate(x, y)) {
            return false;
        }
        return !this.gridData[x][y].hasPlayer;
    };
    // 获取坐标历史记录
    ChessBoardController.prototype.getCoordinateHistory = function () {
        return __spreadArrays(this.coordinateHistory); // 返回副本
    };
    // 清除坐标历史记录
    ChessBoardController.prototype.clearCoordinateHistory = function () {
        this.coordinateHistory = [];
    };
    // 根据世界坐标获取格子坐标
    ChessBoardController.prototype.getGridCoordinateFromWorldPos = function (worldPos) {
        // 检查棋盘节点是否存在
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法获取格子坐标！");
            return null;
        }
        // 将世界坐标转换为相对于棋盘的坐标
        var localPos = this.boardNode.convertToNodeSpaceAR(worldPos);
        // 计算格子坐标
        var x = Math.floor((localPos.x + this.BOARD_WIDTH / 2) / this.GRID_SIZE);
        var y = Math.floor((localPos.y + this.BOARD_HEIGHT / 2) / this.GRID_SIZE);
        if (this.isValidCoordinate(x, y)) {
            return { x: x, y: y };
        }
        return null;
    };
    // 高亮显示格子（可选功能）
    ChessBoardController.prototype.highlightGrid = function (x, y, highlight) {
        if (highlight === void 0) { highlight = true; }
        if (!this.isValidCoordinate(x, y)) {
            return;
        }
        var gridNode = this.gridNodes[x][y];
        if (gridNode) {
            // 这里可以添加高亮效果，比如改变颜色或添加边框
            if (highlight) {
                gridNode.color = cc.Color.YELLOW;
            }
            else {
                gridNode.color = cc.Color.WHITE;
            }
        }
    };
    // 批量放置玩家（用于从服务器同步数据）
    ChessBoardController.prototype.batchPlacePlayers = function (coordinates) {
        var _this = this;
        coordinates.forEach(function (coord) {
            if (_this.isValidCoordinate(coord.x, coord.y) && _this.isGridEmpty(coord.x, coord.y)) {
                _this.placePlayerOnGrid(coord.x, coord.y);
            }
        });
    };
    // 手动启用触摸事件（调试用）
    ChessBoardController.prototype.manualEnableTouch = function () {
        this.enableTouchForExistingGrids();
    };
    // 测试点击功能（调试用）
    ChessBoardController.prototype.testClick = function (x, y) {
        this.onGridClick(x, y);
    };
    // 获取棋盘状态信息（调试用）
    ChessBoardController.prototype.getBoardInfo = function () {
        var info = {
            boardSize: this.BOARD_SIZE,
            gridSize: this.GRID_SIZE,
            boardWidth: this.BOARD_WIDTH,
            boardHeight: this.BOARD_HEIGHT,
            totalGrids: this.BOARD_SIZE * this.BOARD_SIZE,
            boardNodeChildren: this.boardNode ? this.boardNode.children.length : 0,
            playerCount: this.getAllPlayerCoordinates().length,
            hasPlayerGamePrefab: !!this.playerGamePrefab,
            hasBoardNode: !!this.boardNode
        };
        return info;
    };
    // 简单测试方法 - 只测试位置，不加载头像
    ChessBoardController.prototype.simpleTest = function (x, y) {
        if (!this.boardNode) {
            console.error("❌ 棋盘节点未设置");
            return;
        }
        // 创建一个简单的彩色方块
        var testNode = new cc.Node("Test_" + x + "_" + y);
        // 添加一个彩色方块
        var sprite = testNode.addComponent(cc.Sprite);
        var texture = new cc.Texture2D();
        var color = [Math.random() * 255, Math.random() * 255, Math.random() * 255, 255];
        texture.initWithData(new Uint8Array(color), cc.Texture2D.PixelFormat.RGBA8888, 1, 1);
        sprite.spriteFrame = new cc.SpriteFrame(texture);
        // 设置大小
        testNode.setContentSize(60, 60);
        // 计算位置
        var pos = this.calculateCorrectPosition(x, y);
        testNode.setPosition(pos);
        // 添加坐标标签
        var labelNode = new cc.Node("Label");
        var label = labelNode.addComponent(cc.Label);
        label.string = "(" + x + "," + y + ")";
        label.fontSize = 16;
        label.node.color = cc.Color.WHITE;
        labelNode.setPosition(0, 0);
        testNode.addChild(labelNode);
        // 添加到棋盘（处理Layout问题）
        this.addPlayerNodeSafely(testNode);
    };
    // 清除所有测试节点
    ChessBoardController.prototype.clearTestNodes = function () {
        if (this.boardNode) {
            var children = this.boardNode.children.slice();
            children.forEach(function (child) {
                if (child.name.startsWith("Test_")) {
                    child.removeFromParent();
                }
            });
        }
    };
    // 切换到父节点添加模式（如果Layout问题仍然存在）
    ChessBoardController.prototype.useParentNodeMode = function () {
        // 重新定义添加方法
        this.addPlayerNodeSafely = this.addToParentNode;
    };
    // 重新启用Layout（如果需要）
    ChessBoardController.prototype.reEnableLayout = function () {
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法重新启用Layout！");
            return;
        }
        var layout = this.boardNode.getComponent(cc.Layout);
        if (layout) {
            layout.enabled = true;
        }
    };
    // 永久禁用Layout
    ChessBoardController.prototype.disableLayout = function () {
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法禁用Layout！");
            return;
        }
        var layout = this.boardNode.getComponent(cc.Layout);
        if (layout) {
            layout.enabled = false;
        }
    };
    // 设置自定义偏移量
    ChessBoardController.prototype.setCustomOffset = function (offsetX, offsetY) {
        this.customOffsetX = offsetX;
        this.customOffsetY = offsetY;
    };
    // 获取当前偏移量
    ChessBoardController.prototype.getCurrentOffset = function () {
        return { x: this.customOffsetX, y: this.customOffsetY };
    };
    // 测试不同偏移量
    ChessBoardController.prototype.testWithOffset = function (x, y, offsetX, offsetY) {
        // 临时保存当前偏移
        var originalOffsetX = this.customOffsetX;
        var originalOffsetY = this.customOffsetY;
        // 设置测试偏移
        this.setCustomOffset(offsetX, offsetY);
        // 执行测试
        this.simpleTest(x, y);
        // 恢复原偏移
        this.setCustomOffset(originalOffsetX, originalOffsetY);
    };
    // 测试头像显示功能
    ChessBoardController.prototype.testAvatarDisplay = function (x, y) {
        var _this = this;
        if (!this.isValidCoordinate(x, y)) {
            console.error("❌ 无效坐标");
            return;
        }
        if (this.gridData[x][y].hasPlayer) {
            console.warn("⚠️ 该位置已有玩家");
            return;
        }
        // 直接调用放置玩家方法
        this.placePlayerOnGrid(x, y);
        // 延迟检查结果
        this.scheduleOnce(function () {
            var gridData = _this.gridData[x][y];
            if (gridData.playerNode) {
                // 检查PlayerGameController
                var controller = gridData.playerNode.getComponent("PlayerGameController");
                if (controller && controller.avatar) {
                    var sprite = controller.avatar.getComponent(cc.Sprite);
                    if (sprite && sprite.spriteFrame) {
                    }
                    else {
                        console.warn("⚠️ 头像SpriteFrame不存在");
                    }
                }
                else {
                    console.warn("⚠️ PlayerGameController或avatar节点不存在");
                }
            }
            else {
                console.error("❌ 玩家节点创建失败");
            }
        }, 3.0);
    };
    // 调试预制体结构
    ChessBoardController.prototype.debugPrefabStructure = function () {
        if (!this.playerGamePrefab) {
            console.error("❌ playerGamePrefab为null");
            return;
        }
        // 实例化一个临时节点来检查结构
        var tempNode = cc.instantiate(this.playerGamePrefab);
        // 检查组件
        var controller = tempNode.getComponent("PlayerGameController");
        if (controller) {
            if (controller.avatar) {
                var sprite = controller.avatar.getComponent(cc.Sprite);
            }
            else {
                console.error("❌ avatar节点不存在");
            }
        }
        else {
            console.error("❌ 找不到PlayerGameController组件");
        }
        // 列出所有子节点
        this.logNodeHierarchy(tempNode, 0);
        // 清理临时节点
        tempNode.destroy();
    };
    // 递归打印节点层级
    ChessBoardController.prototype.logNodeHierarchy = function (node, depth) {
        var indent = "  ".repeat(depth);
        for (var _i = 0, _a = node.children; _i < _a.length; _i++) {
            var child = _a[_i];
            this.logNodeHierarchy(child, depth + 1);
        }
    };
    // 异步加载头像
    ChessBoardController.prototype.loadAvatarAsync = function (avatarNode, url, onComplete) {
        var _this = this;
        if (!avatarNode) {
            console.error("❌ avatar节点为null");
            onComplete();
            return;
        }
        var avatarSprite = avatarNode.getComponent(cc.Sprite);
        if (!avatarSprite) {
            console.warn("⚠️ avatar节点没有Sprite组件，正在添加...");
            avatarSprite = avatarNode.addComponent(cc.Sprite);
        }
        if (!url || url === '') {
            console.warn("⚠️ URL为空，设置备用头像");
            this.setFallbackAvatar(avatarNode, 0, 0);
            onComplete();
            return;
        }
        // 根据URL判断文件扩展名
        var ext = '.png';
        if (url.toLowerCase().includes('.jpg') || url.toLowerCase().includes('.jpeg')) {
            ext = '.jpg';
        }
        else if (url.toLowerCase().includes('.png')) {
            ext = '.png';
        }
        cc.assetManager.loadRemote(url, { ext: ext }, function (err, texture) {
            if (err) {
                console.error("\u274C \u5934\u50CF\u52A0\u8F7D\u5931\u8D25: " + (err.message || err));
                console.error("\u274C \u5931\u8D25\u7684URL: " + url);
                // 设置备用头像
                _this.setFallbackAvatar(avatarNode, 0, 0);
                onComplete();
                return;
            }
            texture.setPremultiplyAlpha(true);
            texture.packable = false;
            avatarSprite.spriteFrame = new cc.SpriteFrame(texture);
            // 确保节点可见
            avatarNode.active = true;
            avatarNode.opacity = 255;
            onComplete();
        });
    };
    // 异步直接设置头像（当找不到PlayerGameController时）
    ChessBoardController.prototype.tryDirectAvatarSetupAsync = function (playerNode, x, y, onComplete) {
        // 查找名为"avatar"的子节点
        var avatarNode = playerNode.getChildByName("avatar");
        if (avatarNode) {
            this.setFallbackAvatar(avatarNode, x, y);
            onComplete();
        }
        else {
            console.warn("⚠️ 未找到avatar子节点");
            // 列出所有子节点名称
            onComplete();
        }
    };
    /**
     * 显示玩家游戏加减分效果
     * @param userId 用户ID
     * @param score 分数变化（正数为加分，负数为减分）
     */
    ChessBoardController.prototype.showPlayerGameScore = function (userId, score) {
        var currentUserId = this.getCurrentUserId();
        var foundPlayer = false;
        // 1. 如果是当前用户，查找自己的玩家节点（存储在gridData中）
        if (userId === currentUserId) {
            foundPlayer = this.showScoreForCurrentUser(score);
        }
        else {
            // 2. 如果是其他用户，查找对应的玩家头像节点
            foundPlayer = this.showScoreForOtherUser(userId, score);
        }
        if (!foundPlayer) {
            console.warn("\u672A\u627E\u5230\u7528\u6237 " + userId + " \u7684\u5934\u50CF\u8282\u70B9\u6765\u663E\u793A\u5206\u6570\u6548\u679C");
        }
    };
    /**
     * 获取当前用户ID
     */
    ChessBoardController.prototype.getCurrentUserId = function () {
        var _a, _b;
        return ((_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId) || "";
    };
    /**
     * 为当前用户显示分数效果
     */
    ChessBoardController.prototype.showScoreForCurrentUser = function (score) {
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                var gridData = this.gridData[x][y];
                if (gridData.hasPlayer && gridData.playerNode) {
                    var playerController = gridData.playerNode.getComponent("PlayerGameController") ||
                        gridData.playerNode.getComponent("PlayerGameController ");
                    if (playerController) {
                        this.showScoreOnPlayerController(playerController, score);
                        return true;
                    }
                }
            }
        }
        return false;
    };
    /**
     * 为其他用户显示分数效果
     */
    ChessBoardController.prototype.showScoreForOtherUser = function (userId, score) {
        if (!this.boardNode) {
            return false;
        }
        // 遍历棋盘上的所有玩家头像节点
        // 由于目前没有在节点上存储userId，我们需要通过其他方式匹配
        // 临时方案：根据最近的操作位置来匹配
        return this.findPlayerNodeByRecentAction(userId, score);
    };
    /**
     * 根据userId查找对应的玩家节点
     */
    ChessBoardController.prototype.findPlayerNodeByRecentAction = function (userId, score) {
        if (!this.boardNode) {
            console.warn("\u68CB\u76D8\u8282\u70B9\u4E0D\u5B58\u5728\uFF0C\u65E0\u6CD5\u67E5\u627E\u7528\u6237 " + userId + " \u7684\u5934\u50CF");
            return false;
        }
        // 遍历棋盘上的所有玩家头像节点，根据存储的userId精确匹配
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            // 尝试多种方式获取PlayerGameController组件
            var playerController = child.getComponent("PlayerGameController");
            if (!playerController) {
                playerController = child.getComponent("PlayerGameController "); // 注意末尾有空格
            }
            if (!playerController) {
                // 尝试通过类名获取
                var components = child.getComponents(cc.Component);
                playerController = components.find(function (comp) {
                    return comp.constructor.name === 'PlayerGameController' ||
                        comp.constructor.name === 'PlayerGameController ';
                });
            }
            var storedUserId = child['userId'];
            // 先输出组件列表，帮助诊断问题
            if (storedUserId && (storedUserId === userId || i < 5)) { // 为前5个节点或匹配的节点输出组件列表
                var allComponents = child.getComponents(cc.Component);
            }
            if (storedUserId === userId) {
                if (playerController) {
                    // 找到匹配的用户ID和组件，显示分数效果
                    this.showScoreOnPlayerController(playerController, score);
                    return true;
                }
                else {
                    // 找到匹配的用户ID但没有组件
                    console.warn("\u26A0\uFE0F \u627E\u5230\u7528\u6237 " + userId + " \u7684\u8282\u70B9\u4F46\u6CA1\u6709PlayerGameController\u7EC4\u4EF6");
                    return false; // 找到节点但没有组件，返回false
                }
            }
        }
        console.warn("\u274C \u672A\u627E\u5230\u7528\u6237 " + userId + " \u7684\u5934\u50CF\u8282\u70B9");
        return false;
    };
    /**
     * 在PlayerController上显示分数效果
     */
    ChessBoardController.prototype.showScoreOnPlayerController = function (playerController, score) {
        // 临时提升节点层级，避免被其他头像遮挡
        var playerNode = playerController.node;
        var originalSiblingIndex = playerNode.getSiblingIndex();
        // 将节点移到最上层
        playerNode.setSiblingIndex(-1);
        // 同时确保加分/减分节点的层级更高
        this.ensureScoreNodeTopLevel(playerController);
        if (score > 0) {
            playerController.showAddScore(score);
        }
        else if (score < 0) {
            playerController.showSubScore(Math.abs(score));
        }
        // 延迟恢复原始层级（等分数动画播放完成）
        this.scheduleOnce(function () {
            if (playerNode && playerNode.isValid) {
                playerNode.setSiblingIndex(originalSiblingIndex);
            }
        }, 2.5); // 增加到2.5秒，确保动画完全结束
    };
    /**
     * 确保加分/减分节点在最高层级
     */
    ChessBoardController.prototype.ensureScoreNodeTopLevel = function (playerController) {
        // 设置加分节点的最高层级
        if (playerController.addScoreNode) {
            playerController.addScoreNode.zIndex = cc.macro.MAX_ZINDEX - 1;
        }
        // 设置减分节点的最高层级
        if (playerController.subScoreNode) {
            playerController.subScoreNode.zIndex = cc.macro.MAX_ZINDEX - 1;
        }
    };
    /**
     * 显示玩家游戏减分效果
     * @param userId 用户ID
     * @param subScore 减分数值
     */
    ChessBoardController.prototype.showPlayerGameSubScore = function (userId, subScore) {
        var foundPlayer = false;
        // 遍历所有格子，查找玩家节点
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                var gridData = this.gridData[x][y];
                if (gridData.hasPlayer && gridData.playerNode) {
                    var playerController = gridData.playerNode.getComponent("PlayerGameController") ||
                        gridData.playerNode.getComponent("PlayerGameController ");
                    if (playerController) {
                        playerController.showSubScore(subScore);
                        foundPlayer = true;
                        break;
                    }
                }
            }
            if (foundPlayer)
                break;
        }
        if (!foundPlayer) {
            console.warn("\u672A\u627E\u5230\u73A9\u5BB6\u8282\u70B9\u6765\u663E\u793A\u51CF\u5206\u6548\u679C: userId=" + userId);
        }
    };
    /**
     * 重置游戏场景（游戏开始时调用）
     * 清除数字、炸弹、标记预制体，重新显示所有小格子
     */
    ChessBoardController.prototype.resetGameScene = function () {
        if (!this.boardNode) {
            console.error("❌ 棋盘节点不存在，无法重置");
            return;
        }
        // 列出所有子节点
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
        }
        // 清除所有游戏元素（数字、炸弹、标记等）
        this.clearAllGameElements();
        // 显示所有小格子
        this.showAllGrids();
        // 重新初始化棋盘数据
        this.reinitializeBoardData();
        // 列出所有子节点
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
        }
    };
    /**
     * 测试重置功能（可以在浏览器控制台手动调用）
     */
    ChessBoardController.prototype.testReset = function () {
        this.resetGameScene();
    };
    /**
     * 恢复联机模式地图状态（断线重连时使用）
     * @param mapData 地图数据
     */
    ChessBoardController.prototype.restoreOnlineMapState = function (mapData) {
        var _this = this;
        console.log("ChessBoardController: 恢复联机模式地图状态", mapData);
        // 恢复已挖掘的方块
        if (mapData.revealedBlocks && Array.isArray(mapData.revealedBlocks)) {
            console.log("恢复已挖掘的方块数量:", mapData.revealedBlocks.length);
            mapData.revealedBlocks.forEach(function (block) {
                var x = block.x;
                var y = block.y;
                var neighborMines = block.neighborMines;
                if (_this.isValidCoordinate(x, y)) {
                    console.log("\u6062\u590D\u5DF2\u6316\u6398\u65B9\u5757: (" + x + ", " + y + "), \u5468\u56F4\u5730\u96F7\u6570: " + neighborMines);
                    // 立即隐藏格子（不播放动画）
                    _this.hideGridAt(x, y);
                    // 显示挖掘结果
                    if (neighborMines > 0) {
                        // 延迟创建数字预制体，确保格子先隐藏
                        _this.scheduleOnce(function () {
                            _this.createNumberPrefab(x, y, neighborMines);
                        }, 0.1);
                    }
                    // 标记格子已被处理
                    _this.gridData[x][y].hasPlayer = true;
                }
            });
        }
        // 恢复已标记的方块
        if (mapData.markedBlocks && Array.isArray(mapData.markedBlocks)) {
            console.log("恢复已标记的方块数量:", mapData.markedBlocks.length);
            mapData.markedBlocks.forEach(function (block) {
                var x = block.x;
                var y = block.y;
                if (_this.isValidCoordinate(x, y)) {
                    console.log("\u6062\u590D\u5DF2\u6807\u8BB0\u65B9\u5757: (" + x + ", " + y + ")");
                    // 创建标记预制体
                    _this.createBiaojiPrefab(x, y);
                }
            });
        }
    };
    /**
     * 清除所有游戏元素（数字、炸弹、标记、玩家头像等），但保留小格子
     */
    ChessBoardController.prototype.clearAllGameElements = function () {
        if (!this.boardNode) {
            return;
        }
        var childrenToRemove = [];
        var totalChildren = this.boardNode.children.length;
        // 遍历棋盘的所有子节点
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            var nodeName = child.name;
            // 检查是否是需要清除的游戏元素（不包括小格子）
            if (this.isGameElement(child, nodeName)) {
                childrenToRemove.push(child);
            }
            else {
            }
        }
        // 移除找到的游戏元素
        childrenToRemove.forEach(function (child, index) {
            child.removeFromParent();
        });
        // 暂时禁用强制清理，避免误删小格子
        // this.forceCleanNonGridNodes();
    };
    /**
     * 强制清理所有游戏预制体（除了Grid_开头的节点和分数控制器）
     */
    ChessBoardController.prototype.forceCleanNonGridNodes = function () {
        if (!this.boardNode) {
            return;
        }
        var childrenToRemove = [];
        // 再次遍历，强制清除所有游戏预制体
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            var nodeName = child.name;
            // 保留条件：
            // 1. Grid_开头的节点（小格子）
            // 2. 包含Score的节点（分数控制器）
            // 3. UI相关节点
            var shouldKeep = nodeName.startsWith("Grid_") ||
                nodeName.includes("Score") ||
                nodeName.includes("score") ||
                nodeName.includes("UI") ||
                nodeName.includes("ui") ||
                nodeName.includes("Canvas") ||
                nodeName.includes("Background");
            if (!shouldKeep) {
                childrenToRemove.push(child);
            }
            // 移除找到的节点
            childrenToRemove.forEach(function (child) {
                child.removeFromParent();
            });
        }
    };
    /**
     * 判断节点是否是游戏元素（需要清除的），小格子和分数控制器不会被清除
     */
    ChessBoardController.prototype.isGameElement = function (node, nodeName) {
        //  绝对不清除的节点（小格子）
        if (nodeName.startsWith("Grid_") || nodeName === "block") {
            return false;
        }
        //  分数控制器不清除
        if (nodeName.includes("Score") || nodeName.includes("score")) {
            return false;
        }
        //  UI相关节点不清除
        if (nodeName.includes("UI") || nodeName.includes("ui")) {
            return false;
        }
        // 🗑️明确需要清除的游戏预制体
        // 炸弹预制体
        if (nodeName === "Boom") {
            return true;
        }
        // 数字预制体（Boom1, Boom2, Boom3 等）
        if (nodeName.match(/^Boom\d+$/)) {
            return true;
        }
        // 临时数字节点（NeighborMines_1, NeighborMines_2 等）
        if (nodeName.match(/^NeighborMines_\d+$/)) {
            return true;
        }
        // 测试节点（Test_x_y 格式）
        if (nodeName.match(/^Test_\d+_\d+$/)) {
            return true;
        }
        // 玩家预制体（通过组件判断）
        if (node.getComponent("PlayerGameController")) {
            return true;
        }
        // 标记预制体
        if (nodeName.includes("Flag") || nodeName.includes("Mark") || nodeName.includes("flag") ||
            nodeName === "Biaoji" || nodeName.includes("Biaoji")) {
            return true;
        }
        // 玩家头像预制体
        if (nodeName.includes("Player") || nodeName.includes("Avatar")) {
            return true;
        }
        //  默认保留未知节点（保守策略）
        return false;
    };
    /**
     * 显示所有小格子（第二把游戏开始时恢复被隐藏的小格子）
     */
    ChessBoardController.prototype.showAllGrids = function () {
        if (!this.boardNode) {
            return;
        }
        var totalGrids = 0;
        var restoredGrids = 0;
        var alreadyVisibleGrids = 0;
        // 遍历棋盘的所有子节点，找到小格子并显示
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            // 如果是小格子节点
            if (child.name.startsWith("Grid_") || child.name === "block") {
                totalGrids++;
                // 记录恢复前的状态
                var wasHidden = !child.active || child.opacity < 255 || child.scaleX < 1 || child.scaleY < 1;
                if (wasHidden) {
                    restoredGrids++;
                }
                else {
                    alreadyVisibleGrids++;
                }
                // 停止所有可能正在进行的动画
                child.stopAllActions();
                // 恢复显示状态
                child.active = true;
                child.opacity = 255;
                child.scaleX = 1;
                child.scaleY = 1;
                child.angle = 0; // 重置旋转角度
                // 恢复原始位置（如果有保存的话）
                if (child['originalPosition']) {
                    child.setPosition(child['originalPosition']);
                }
                // 确保格子可以交互
                var button = child.getComponent(cc.Button);
                if (button) {
                    button.enabled = true;
                }
            }
        }
    };
    /**
     * 隐藏指定位置的小格子（点击时调用）
     */
    ChessBoardController.prototype.hideGridAt = function (x, y) {
        if (!this.isValidCoordinate(x, y)) {
            console.warn("\u9690\u85CF\u683C\u5B50\u5931\u8D25\uFF1A\u5750\u6807(" + x + ", " + y + ")\u65E0\u6548");
            return;
        }
        // 获取格子节点
        var gridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (gridNode) {
            // 使用动画隐藏格子
            cc.tween(gridNode)
                .to(0.3, { opacity: 0, scaleX: 0, scaleY: 0 }, { easing: 'sineIn' })
                .call(function () {
                gridNode.active = false;
            })
                .start();
        }
    };
    /**
     * 重新初始化棋盘数据
     */
    ChessBoardController.prototype.reinitializeBoardData = function () {
        // 重置gridData中的玩家状态
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                if (this.gridData[x][y]) {
                    this.gridData[x][y].hasPlayer = false;
                    this.gridData[x][y].playerNode = null;
                }
            }
        }
        // 清除坐标历史记录
        this.clearCoordinateHistory();
    };
    /**
     * 清理所有玩家预制体（新回合开始时调用）
     * 包括自己的头像和其他玩家的头像
     */
    ChessBoardController.prototype.clearAllPlayerNodes = function () {
        if (!this.boardNode) {
            console.warn("棋盘节点不存在，无法清理");
            return;
        }
        var totalCleared = 0;
        // 方法1: 清理存储在gridData中的玩家节点（自己的头像）
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                if (this.gridData[x][y].hasPlayer && this.gridData[x][y].playerNode) {
                    // 移除玩家节点
                    this.gridData[x][y].playerNode.removeFromParent();
                    this.gridData[x][y].playerNode = null;
                    this.gridData[x][y].hasPlayer = false;
                    totalCleared++;
                }
            }
        }
        // 方法2: 清理棋盘上所有的玩家预制体节点（包括其他玩家的头像）
        var childrenToRemove = [];
        // 遍历棋盘的所有子节点
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            // 检查是否是玩家预制体（通过组件判断）
            var playerController = child.getComponent(PlayerGameController_1.default);
            if (playerController) {
                childrenToRemove.push(child);
                totalCleared++;
            }
        }
        // 移除找到的玩家预制体
        childrenToRemove.forEach(function (child) {
            child.removeFromParent();
        });
    };
    /**
     * 在指定位置显示其他玩家的操作（参考自己头像的生成逻辑）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param actions 该位置的其他玩家操作列表
     */
    ChessBoardController.prototype.displayOtherPlayersAtPosition = function (x, y, actions) {
        if (!this.isValidCoordinate(x, y) || !actions || actions.length === 0) {
            console.warn("\u65E0\u6548\u53C2\u6570: (" + x + ", " + y + "), actions: " + ((actions === null || actions === void 0 ? void 0 : actions.length) || 0));
            return;
        }
        // 检查该位置是否已经有自己的头像
        if (this.gridData[x][y].hasPlayer) {
            // 只有当真的有其他玩家时，才调整自己的头像位置
            if (actions.length > 0) {
                // 如果已有自己的头像且有其他玩家，需要使用多人布局策略
                this.addOtherPlayersToExistingGrid(x, y, actions);
            }
            else {
            }
        }
        else {
            // 如果没有自己的头像，直接添加其他玩家头像
            this.addOtherPlayersToEmptyGrid(x, y, actions);
        }
    };
    /**
     * 在已有自己头像的格子上添加其他玩家头像，并调整自己的头像位置和缩放
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param actions 其他玩家操作列表
     */
    ChessBoardController.prototype.addOtherPlayersToExistingGrid = function (x, y, actions) {
        // 总玩家数 = 自己(1) + 其他玩家数量
        var totalPlayers = 1 + actions.length;
        var positions = this.getPlayerPositions(totalPlayers);
        // 第一步：调整自己的头像位置和缩放
        // 注意：如果自己的头像是通过点击生成的，位置是正确的，应该调整
        // 如果是通过后端消息生成的，也应该参与多人布局
        var myPosition = positions[0]; // 第一个位置是自己的
        this.adjustMyAvatarPosition(x, y, myPosition, actions);
        // 第二步：从第二个位置开始放置其他玩家
        for (var i = 0; i < actions.length; i++) {
            var action = actions[i];
            var position = positions[i + 1]; // 跳过第一个位置（自己的位置）
            // 使用棋盘坐标系创建其他玩家头像
            this.createOtherPlayerAtBoardPosition(x, y, action, position, totalPlayers);
        }
    };
    /**
     * 调整自己的头像位置和缩放（当多人在同一格子时）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param position 新的位置和缩放信息
     * @param actions 其他玩家操作列表
     */
    ChessBoardController.prototype.adjustMyAvatarPosition = function (x, y, position, actions) {
        // 查找自己的头像节点
        if (!this.gridData[x][y].hasPlayer || !this.gridData[x][y].playerNode) {
            console.warn("\u5728\u4F4D\u7F6E(" + x + ", " + y + ")\u627E\u4E0D\u5230\u81EA\u5DF1\u7684\u5934\u50CF\u8282\u70B9");
            return;
        }
        var myPlayerNode = this.gridData[x][y].playerNode;
        // 计算该格子的总人数（自己 + 其他玩家）
        var totalPlayers = 1 + (actions ? actions.length : 0);
        // 根据总人数计算基础位置
        var basePosition = this.calculateBasePositionByPlayerCount(x, y, totalPlayers);
        // 计算新的最终位置
        var newPosition = cc.v2(basePosition.x + position.x, basePosition.y + position.y);
        // 播放平滑移动和缩小动画（多人格子情况）
        this.playAvatarAdjustAnimation(myPlayerNode, newPosition, position.scale);
    };
    /**
     * 在空格子上添加其他玩家头像
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param actions 其他玩家操作列表
     */
    ChessBoardController.prototype.addOtherPlayersToEmptyGrid = function (x, y, actions) {
        var totalPlayers = actions.length; // 空格子上只有其他玩家
        var positions = this.getPlayerPositions(totalPlayers);
        for (var i = 0; i < actions.length; i++) {
            var action = actions[i];
            var position = positions[i];
            // 使用棋盘坐标系创建其他玩家头像
            this.createOtherPlayerAtBoardPosition(x, y, action, position, totalPlayers);
        }
    };
    /**
     * 在棋盘坐标系中创建其他玩家头像（参考自己头像的生成逻辑）
     * @param gridX 格子x坐标
     * @param gridY 格子y坐标
     * @param action 玩家操作数据
     * @param relativePosition 相对于格子中心的位置和缩放
     * @param totalPlayers 该格子的总人数
     */
    ChessBoardController.prototype.createOtherPlayerAtBoardPosition = function (gridX, gridY, action, relativePosition, totalPlayers) {
        var _this = this;
        if (!this.playerGamePrefab) {
            console.error("playerGamePrefab 预制体未设置");
            return;
        }
        if (!this.boardNode) {
            console.error("棋盘节点未设置");
            return;
        }
        // 创建玩家预制体实例
        var playerNode = cc.instantiate(this.playerGamePrefab);
        // 根据总人数计算基础位置（统一逻辑）
        var basePosition = this.calculateBasePositionByPlayerCount(gridX, gridY, totalPlayers);
        // 添加相对偏移
        var finalPosition = cc.v2(basePosition.x + relativePosition.x, basePosition.y + relativePosition.y);
        playerNode.setPosition(finalPosition);
        playerNode.setScale(relativePosition.scale);
        // 先隐藏节点，等头像加载完成后再显示
        playerNode.active = false;
        // 安全地添加到棋盘节点（参考自己头像的添加逻辑）
        this.addPlayerNodeSafely(playerNode);
        // 设置其他玩家的头像和数据
        this.setupOtherPlayerData(playerNode, action, function () {
            // 头像加载完成的回调
            if (totalPlayers === 1) {
                // 单人格子：播放生成动画
                _this.playAvatarSpawnAnimation(playerNode);
            }
            else {
                // 多人格子：直接显示（其他人是新生成的，不需要动画）
                playerNode.active = true;
            }
        });
    };
    /**
     * 设置其他玩家的数据（参考自己头像的设置逻辑）
     * @param playerNode 玩家节点
     * @param action 玩家操作数据
     * @param onComplete 完成回调
     */
    ChessBoardController.prototype.setupOtherPlayerData = function (playerNode, action, onComplete) {
        try {
            var playerController_1 = playerNode.getComponent(PlayerGameController_1.default);
            if (!playerController_1) {
                console.error("❌ 找不到PlayerGameController组件");
                return;
            }
            // 从GlobalBean中获取真实的玩家数据
            var realUserData_1 = this.getRealUserData(action.userId);
            if (!realUserData_1) {
                console.warn("\u627E\u4E0D\u5230\u7528\u6237 " + action.userId + " \u7684\u771F\u5B9E\u6570\u636E");
                return;
            }
            // 在节点上存储userId信息，用于后续分数显示匹配
            playerNode['userId'] = action.userId;
            // 使用延迟设置，参考自己头像的设置逻辑
            this.scheduleOnce(function () {
                if (typeof playerController_1.setData === 'function') {
                    playerController_1.setData(realUserData_1);
                }
                // 根据操作类型设置旗子显示
                var withFlag = (action.action === 2); // action=2表示标记操作，显示旗子
                if (playerController_1.flagNode) {
                    playerController_1.flagNode.active = withFlag;
                }
                // 调用完成回调
                if (onComplete) {
                    onComplete();
                }
            }, 0.1);
        }
        catch (error) {
            console.warn("\u8BBE\u7F6E\u5176\u4ED6\u73A9\u5BB6\u6570\u636E\u65F6\u51FA\u9519: " + error);
        }
    };
    /**
     * 根据玩家数量获取布局位置
     * @param playerCount 玩家数量
     * @returns 位置数组 {x: number, y: number, scale: number}[]
     */
    ChessBoardController.prototype.getPlayerPositions = function (playerCount) {
        switch (playerCount) {
            case 1:
                // 单个玩家，居中显示，正常大小
                return [{ x: 0, y: 0, scale: 1.0 }];
            case 2:
                // 两个玩家，左右分布，缩放0.5
                return [
                    { x: -22, y: -8, scale: 0.5 },
                    { x: 22, y: -8, scale: 0.5 } // 右
                ];
            case 3:
                // 三个玩家，上中下分布，缩放0.5
                return [
                    { x: 0, y: 12, scale: 0.5 },
                    { x: -23, y: -27, scale: 0.5 },
                    { x: 23, y: -27, scale: 0.5 } // 右下
                ];
            case 4:
                // 四个玩家，四角分布，缩放0.5
                return [
                    { x: -22, y: 12, scale: 0.5 },
                    { x: 22, y: 12, scale: 0.5 },
                    { x: -22, y: -30, scale: 0.5 },
                    { x: 22, y: -30, scale: 0.5 } // 右下
                ];
            default:
                // 超过4个玩家，只显示前4个
                console.warn("\u73A9\u5BB6\u6570\u91CF\u8FC7\u591A: " + playerCount + "\uFF0C\u53EA\u663E\u793A\u524D4\u4E2A");
                return this.getPlayerPositions(4);
        }
    };
    /**
     * 获取指定位置的格子节点
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @returns 格子节点或null
     */
    ChessBoardController.prototype.getGridNode = function (x, y) {
        if (!this.boardNode || !this.isValidCoordinate(x, y)) {
            return null;
        }
        // 计算在棋盘子节点中的索引 (8x8棋盘，从左到右，从上到下)
        var index = y * this.BOARD_SIZE + x;
        if (index >= 0 && index < this.boardNode.children.length) {
            return this.boardNode.children[index];
        }
        return null;
    };
    /**
     * 在指定位置创建玩家预制体节点
     * @param gridNode 格子节点
     * @param action 玩家操作数据
     * @param position 相对位置和缩放
     */
    ChessBoardController.prototype.createPlayerNodeAtPosition = function (gridNode, action, position) {
        if (!this.playerGamePrefab) {
            console.error("playerGamePrefab 预制体未设置");
            return;
        }
        // 创建玩家预制体实例
        var playerNode = cc.instantiate(this.playerGamePrefab);
        // 检查预制体上的组件
        var components = playerNode.getComponents(cc.Component);
        components.forEach(function (comp, index) {
        });
        // 设置位置和缩放
        playerNode.setPosition(position.x, position.y);
        playerNode.setScale(position.scale);
        // 添加到格子节点
        gridNode.addChild(playerNode);
        // 设置玩家数据
        this.setupPlayerNodeData(playerNode, action);
    };
    /**
     * 设置玩家节点数据
     * @param playerNode 玩家节点
     * @param action 玩家操作数据
     */
    ChessBoardController.prototype.setupPlayerNodeData = function (playerNode, action) {
        try {
            var playerController = playerNode.getComponent(PlayerGameController_1.default);
            if (!playerController) {
                console.error("❌ 找不到PlayerGameController组件");
                var allComponents = playerNode.getComponents(cc.Component);
                allComponents.forEach(function (comp, index) {
                });
                return;
            }
            // 从GlobalBean中获取真实的玩家数据
            var realUserData = this.getRealUserData(action.userId);
            if (!realUserData) {
                console.warn("\u627E\u4E0D\u5230\u7528\u6237 " + action.userId + " \u7684\u771F\u5B9E\u6570\u636E");
                return;
            }
            if (typeof playerController.setData === 'function') {
                playerController.setData(realUserData);
            }
            // 根据操作类型设置旗子显示
            var withFlag = (action.action === 2); // action=2表示标记操作，显示旗子
            if (playerController.flagNode) {
                playerController.flagNode.active = withFlag;
            }
            else {
                console.warn("找不到flagNode节点");
            }
        }
        catch (error) {
            console.warn("\u8BBE\u7F6E\u73A9\u5BB6\u8282\u70B9\u6570\u636E\u65F6\u51FA\u9519: " + error);
        }
    };
    /**
     * 从GlobalBean中获取真实的用户数据
     * @param userId 用户ID
     * @returns RoomUser 或 null
     */
    ChessBoardController.prototype.getRealUserData = function (userId) {
        try {
            if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
                console.warn("没有游戏数据，无法获取用户信息");
                return null;
            }
            var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
            var user = users.find(function (u) { return u.userId === userId; });
            if (user) {
                return user;
            }
            else {
                console.warn("\u672A\u627E\u5230\u7528\u6237 " + userId + " \u7684\u6570\u636E");
                return null;
            }
        }
        catch (error) {
            console.error("\u83B7\u53D6\u7528\u6237\u6570\u636E\u65F6\u51FA\u9519: " + error);
            return null;
        }
    };
    /**
     * 在指定位置的玩家节点上显示分数
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param score 分数
     * @param showPlusOne 是否显示+1（先手奖励）
     */
    ChessBoardController.prototype.showScoreOnPlayerNode = function (x, y, score, showPlusOne) {
        var _this = this;
        if (!this.isValidCoordinate(x, y)) {
            console.warn("\u65E0\u6548\u5750\u6807: (" + x + ", " + y + ")");
            return;
        }
        // 查找该位置的玩家节点
        var playerNode = this.findPlayerNodeAtPosition(x, y);
        if (!playerNode) {
            console.warn("\u5728\u4F4D\u7F6E(" + x + ", " + y + ")\u627E\u4E0D\u5230\u73A9\u5BB6\u8282\u70B9");
            return;
        }
        // 获取PlayerGameController组件
        var playerController = playerNode.getComponent(PlayerGameController_1.default);
        if (!playerController) {
            console.warn("找不到PlayerGameController组件");
            return;
        }
        // 显示分数动画
        if (showPlusOne) {
            // 先显示+1，再显示本回合得分
            this.showScoreAnimationOnNode(playerController, 1, function () {
                _this.scheduleOnce(function () {
                    _this.showScoreAnimationOnNode(playerController, score, null);
                }, 1.0);
            });
        }
        else {
            // 只显示本回合得分
            this.showScoreAnimationOnNode(playerController, score, null);
        }
    };
    /**
     * 查找指定位置的玩家节点
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @returns 玩家节点或null
     */
    ChessBoardController.prototype.findPlayerNodeAtPosition = function (x, y) {
        // 方法1: 从gridData中查找（自己的头像）
        if (this.gridData[x][y].hasPlayer && this.gridData[x][y].playerNode) {
            return this.gridData[x][y].playerNode;
        }
        // 方法2: 在棋盘上查找其他玩家的头像
        if (!this.boardNode) {
            return null;
        }
        // 计算该位置的世界坐标
        var targetPosition = this.calculateCorrectPosition(x, y);
        // 遍历棋盘上的所有玩家节点，找到最接近目标位置的
        var closestNode = null;
        var minDistance = Number.MAX_VALUE;
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            var playerController = child.getComponent(PlayerGameController_1.default);
            if (playerController) {
                var distance = cc.Vec2.distance(child.getPosition(), targetPosition);
                if (distance < minDistance && distance < 50) { // 50像素的容差
                    minDistance = distance;
                    closestNode = child;
                }
            }
        }
        return closestNode;
    };
    /**
     * 在节点上显示分数动画
     * @param playerController 玩家控制器
     * @param score 分数
     * @param onComplete 完成回调
     */
    ChessBoardController.prototype.showScoreAnimationOnNode = function (playerController, score, onComplete) {
        // TODO: 实现在player_game_pfb上显示分数动画的逻辑
        // 这里需要根据PlayerGameController的具体实现来显示分数
        if (onComplete) {
            this.scheduleOnce(onComplete, 1.0);
        }
    };
    /**
     * 让指定位置的所有头像消失（参考回合结束时的清理逻辑）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param onComplete 完成回调
     */
    ChessBoardController.prototype.hideAvatarsAtPosition = function (x, y, onComplete) {
        var _this = this;
        if (!this.boardNode) {
            console.warn("棋盘节点不存在，无法清理头像");
            onComplete();
            return;
        }
        // 收集所有头像节点（参考clearAllPlayerNodes的逻辑）
        var avatarNodes = [];
        // 方法1: 收集存储在gridData中的玩家节点（自己的头像）
        for (var gx = 0; gx < this.BOARD_SIZE; gx++) {
            for (var gy = 0; gy < this.BOARD_SIZE; gy++) {
                if (this.gridData[gx][gy].hasPlayer && this.gridData[gx][gy].playerNode) {
                    avatarNodes.push(this.gridData[gx][gy].playerNode);
                }
            }
        }
        // 方法2: 收集棋盘上所有的玩家预制体节点（包括其他玩家的头像）
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            // 检查是否是玩家预制体（通过组件判断，参考clearAllPlayerNodes）
            var playerController = child.getComponent(PlayerGameController_1.default);
            if (playerController) {
                // 避免重复添加（可能已经在方法1中添加过）
                if (!avatarNodes.includes(child)) {
                    avatarNodes.push(child);
                }
            }
        }
        if (avatarNodes.length === 0) {
            // 没有头像需要消失
            onComplete();
            return;
        }
        var completedCount = 0;
        var totalCount = avatarNodes.length;
        // 为每个头像播放消失动画
        avatarNodes.forEach(function (avatarNode, index) {
            // 使用cc.Tween播放消失动画
            cc.tween(avatarNode)
                .to(0.3, { opacity: 0, scaleX: 0.5, scaleY: 0.5 }, { easing: 'sineIn' })
                .call(function () {
                // 动画完成后移除节点
                avatarNode.removeFromParent();
                completedCount++;
                // 所有头像都消失完成后，执行回调
                if (completedCount >= totalCount) {
                    // 清除所有自己头像的引用（参考clearAllPlayerNodes）
                    _this.clearAllMyAvatarReferences();
                    onComplete();
                }
            })
                .start();
        });
    };
    /**
     * 清除所有自己头像的引用（参考clearAllPlayerNodes的逻辑）
     */
    ChessBoardController.prototype.clearAllMyAvatarReferences = function () {
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                if (this.gridData[x][y].hasPlayer) {
                    this.gridData[x][y].hasPlayer = false;
                    this.gridData[x][y].playerNode = null;
                }
            }
        }
    };
    /**
     * 隐藏指定位置的格子（不销毁，以便重置时可以重新显示）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param immediate 是否立即隐藏（不播放动画）
     */
    ChessBoardController.prototype.removeGridAt = function (x, y, immediate) {
        if (immediate === void 0) { immediate = false; }
        if (!this.isValidCoordinate(x, y)) {
            console.warn("\u9690\u85CF\u683C\u5B50\u5931\u8D25\uFF1A\u5750\u6807(" + x + ", " + y + ")\u65E0\u6548");
            return;
        }
        // 获取格子节点
        var gridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (gridNode) {
            if (immediate) {
                // 立即隐藏，不播放动画
                gridNode.active = false;
            }
            else {
                // 播放四边形格子消失动画
                this.playGridFallAnimation(gridNode);
            }
        }
    };
    /**
     * 播放四边形格子消失动画
     * 效果：格子持续旋转，给一个随机向上的力，然后旋转着自由落体
     * @param gridNode 格子节点
     */
    ChessBoardController.prototype.playGridFallAnimation = function (gridNode) {
        if (!gridNode)
            return;
        // 停止该格子上所有正在进行的动画（包括震动动画）
        gridNode.stopAllActions();
        // 保存格子的原始位置（用于重置时恢复）
        if (!gridNode['originalPosition']) {
            gridNode['originalPosition'] = gridNode.getPosition();
        }
        // 随机选择向上的力的方向：0=向上，1=右上15度，2=左上15度
        var forceDirection = Math.floor(Math.random() * 3);
        var moveX = 0;
        var moveY = 200; // 向上的基础距离（增加高度）
        switch (forceDirection) {
            case 0: // 向上
                moveX = 0;
                break;
            case 1: // 右上20度
                moveX = Math.sin(20 * Math.PI / 180) * moveY;
                break;
            case 2: // 左上20度
                moveX = -Math.sin(20 * Math.PI / 180) * moveY;
                break;
        }
        // 随机旋转速度
        var rotationSpeed = (Math.random() * 1440 + 720) * (Math.random() > 0.5 ? 1 : -1); // 720-2160度/秒，随机方向
        // 动画参数
        var upTime = 0.15; // 向上运动时间
        var fallTime = 0.3; // 下落时间
        var initialPosition = gridNode.getPosition();
        // 创建持续旋转的动画
        var rotationTween = cc.tween(gridNode)
            .repeatForever(cc.tween().by(0.1, { angle: rotationSpeed * 0.1 }));
        // 创建分阶段的运动动画
        var movementTween = cc.tween(gridNode)
            // 第一阶段：向上抛出
            .to(upTime, {
            x: initialPosition.x + moveX,
            y: initialPosition.y + moveY
        }, { easing: 'quadOut' })
            // 第二阶段：自由落体
            .to(fallTime, {
            x: initialPosition.x + moveX + (Math.random() - 0.5) * 100,
            y: initialPosition.y - 500 // 下落到屏幕下方更远处
        }, { easing: 'quadIn' })
            .call(function () {
            // 动画结束后隐藏格子
            gridNode.active = false;
            // 停止旋转动画
            gridNode.stopAllActions();
        });
        // 同时开始旋转和移动动画
        rotationTween.start();
        movementTween.start();
    };
    /**
     * 在指定位置创建boom预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param isCurrentUser 是否是当前用户点到的雷（可选，默认为true以保持向后兼容）
     */
    ChessBoardController.prototype.createBoomPrefab = function (x, y, isCurrentUser) {
        var _this = this;
        if (isCurrentUser === void 0) { isCurrentUser = true; }
        if (!this.boomPrefab) {
            console.error("boomPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }
        // 实例化您的boom预制体
        var boomNode = cc.instantiate(this.boomPrefab);
        boomNode.name = "Boom";
        // 设置位置（使用新的精确位置计算）
        var position = this.calculatePrefabPosition(x, y);
        boomNode.setPosition(position);
        // 添加到棋盘
        this.addPlayerNodeSafely(boomNode);
        // 播放出现动画
        boomNode.setScale(0);
        cc.tween(boomNode)
            .to(0.3, { scaleX: 1.2, scaleY: 1.2 }, { easing: 'backOut' })
            .to(0.1, { scaleX: 1.0, scaleY: 1.0 })
            .start();
        // 只有当前用户点到雷时才播放棋盘震动效果
        // 延迟0.45秒，等格子下落动画完成后再播放震动
        if (isCurrentUser) {
            this.scheduleOnce(function () {
                _this.playBoardShakeAnimation();
            }, 0.45);
        }
    };
    /**
     * 在指定位置创建biaoji预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     */
    ChessBoardController.prototype.createBiaojiPrefab = function (x, y) {
        if (!this.biaojiPrefab) {
            console.error("biaojiPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }
        // 实例化您的biaoji预制体
        var biaojiNode = cc.instantiate(this.biaojiPrefab);
        biaojiNode.name = "Biaoji";
        // 设置位置（使用新的精确位置计算）
        var position = this.calculatePrefabPosition(x, y);
        biaojiNode.setPosition(position);
        // 添加到棋盘
        this.addPlayerNodeSafely(biaojiNode);
        // 播放出现动画
        biaojiNode.setScale(0);
        cc.tween(biaojiNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    };
    /**
     * 更新指定位置的neighborMines显示（使用boom数字预制体）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param neighborMines 周围地雷数量
     */
    ChessBoardController.prototype.updateNeighborMinesDisplay = function (x, y, neighborMines) {
        // 0不需要显示数字
        if (neighborMines === 0) {
            return;
        }
        // 直接使用boom数字预制体
        this.createNumberPrefab(x, y, neighborMines);
    };
    /**
     * 创建数字预制体（boom1, boom2, ...）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param number 数字
     */
    ChessBoardController.prototype.createNumberPrefab = function (x, y, number) {
        // 根据数字选择对应的预制体
        var prefab = null;
        switch (number) {
            case 1:
                prefab = this.boom1Prefab;
                break;
            case 2:
                prefab = this.boom2Prefab;
                break;
            case 3:
                prefab = this.boom3Prefab;
                break;
            case 4:
                prefab = this.boom4Prefab;
                break;
            case 5:
                prefab = this.boom5Prefab;
                break;
            case 6:
                prefab = this.boom6Prefab;
                break;
            case 7:
                prefab = this.boom7Prefab;
                break;
            case 8:
                prefab = this.boom8Prefab;
                break;
            default:
                console.error("\u4E0D\u652F\u6301\u7684\u6570\u5B57: " + number);
                return;
        }
        if (!prefab) {
            console.error("boom" + number + "Prefab \u9884\u5236\u4F53\u672A\u8BBE\u7F6E\uFF0C\u8BF7\u5728\u7F16\u8F91\u5668\u4E2D\u6302\u8F7D");
            return;
        }
        // 实例化数字预制体
        var numberNode = cc.instantiate(prefab);
        numberNode.name = "Boom" + number;
        // 设置位置（使用新的精确位置计算）
        var position = this.calculatePrefabPosition(x, y);
        numberNode.setPosition(position);
        // 添加到棋盘
        this.addPlayerNodeSafely(numberNode);
        // 播放出现动画
        numberNode.setScale(0);
        cc.tween(numberNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    };
    /**
     * 加载数字预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param number 数字
     */
    ChessBoardController.prototype.loadNumberPrefab = function (x, y, number) {
        var prefabName = number + "boom";
        this.createTemporaryNumberNode(x, y, number);
    };
    /**
     * 创建临时的数字节点（在预制体加载失败时使用）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param number 数字
     */
    ChessBoardController.prototype.createTemporaryNumberNode = function (x, y, number) {
        // 创建数字显示节点
        var numberNode = new cc.Node("NeighborMines_" + number);
        var label = numberNode.addComponent(cc.Label);
        // 设置数字显示 - 更大的字体和居中对齐
        label.string = number.toString();
        label.fontSize = 48; // 增大字体
        label.node.color = this.getNumberColor(number);
        label.horizontalAlign = cc.Label.HorizontalAlign.CENTER;
        label.verticalAlign = cc.Label.VerticalAlign.CENTER;
        // 设置节点大小，确保居中
        numberNode.setContentSize(this.GRID_SIZE, this.GRID_SIZE);
        // 设置位置 - 使用格子中心位置
        var position = this.calculateCorrectPosition(x, y);
        numberNode.setPosition(position);
        // 添加到棋盘
        this.addPlayerNodeSafely(numberNode);
        // 播放出现动画
        this.playNumberAppearAnimation(numberNode, number);
    };
    /**
     * 设置数字节点（用于预制体）
     * @param numberNode 数字节点
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param number 数字
     */
    ChessBoardController.prototype.setupNumberNode = function (numberNode, x, y, number) {
        // 设置位置 - 使用格子中心位置
        var position = this.calculateCorrectPosition(x, y);
        numberNode.setPosition(position);
        // 添加到棋盘
        this.addPlayerNodeSafely(numberNode);
        // 播放出现动画
        this.playNumberAppearAnimation(numberNode, number);
    };
    /**
     * 播放数字出现动画
     * @param numberNode 数字节点
     * @param number 数字
     */
    ChessBoardController.prototype.playNumberAppearAnimation = function (numberNode, number) {
        // 使用cc.Tween播放数字出现动画
        numberNode.setScale(0);
        cc.tween(numberNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    };
    /**
     * 播放格子消失动画（连锁效果）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param neighborMines 周围地雷数量
     */
    ChessBoardController.prototype.playGridDisappearAnimation = function (x, y, neighborMines) {
        var _this = this;
        // 先删除格子
        this.removeGridAt(x, y);
        // 延迟0.3秒后显示数字（等格子消失动画完成）
        this.scheduleOnce(function () {
            _this.updateNeighborMinesDisplay(x, y, neighborMines);
        }, 0.3);
    };
    /**
     * 根据数字获取颜色
     * @param number 数字
     * @returns 颜色
     */
    ChessBoardController.prototype.getNumberColor = function (number) {
        switch (number) {
            case 1: return cc.Color.BLUE;
            case 2: return cc.Color.GREEN;
            case 3: return cc.Color.RED;
            case 4: return cc.Color.MAGENTA;
            case 5: return cc.Color.YELLOW;
            case 6: return cc.Color.CYAN;
            case 7: return cc.Color.BLACK;
            case 8: return cc.Color.GRAY;
            default: return cc.Color.BLACK;
        }
    };
    /**
     * 播放棋盘震动动画（包括所有小格子）
     */
    ChessBoardController.prototype.playBoardShakeAnimation = function () {
        if (!this.boardNode) {
            console.warn("boardNode 未设置，无法播放震动效果");
            return;
        }
        // 震动参数 - 增强震动效果
        var shakeIntensity = 30; // 震动强度
        var shakeDuration = 1.0; // 震动持续时间
        var shakeFrequency = 40; // 震动频率
        // 震动棋盘
        this.shakeBoardNode(shakeIntensity, shakeDuration, shakeFrequency);
        // 震动所有小格子
        this.shakeAllGrids(shakeIntensity * 0.6, shakeDuration, shakeFrequency);
    };
    /**
     * 震动棋盘节点
     */
    ChessBoardController.prototype.shakeBoardNode = function (intensity, duration, frequency) {
        // 保存原始位置
        var originalPosition = this.boardNode.position.clone();
        // 创建震动动画，使用递减强度
        var currentIntensity = intensity;
        var intensityDecay = 0.92; // 强度衰减系数
        var createShakeStep = function (shakeIntensity) {
            return cc.tween()
                .to(0.025, {
                x: originalPosition.x + (Math.random() - 0.5) * shakeIntensity * 2,
                y: originalPosition.y + (Math.random() - 0.5) * shakeIntensity * 2
            });
        };
        // 创建震动序列，强度逐渐衰减
        var shakeTween = cc.tween(this.boardNode);
        var totalSteps = Math.floor(duration * frequency);
        for (var i = 0; i < totalSteps; i++) {
            shakeTween = shakeTween.then(createShakeStep(currentIntensity));
            currentIntensity *= intensityDecay; // 逐渐减弱震动强度
        }
        // 最后恢复到原位置
        shakeTween.to(0.2, {
            x: originalPosition.x,
            y: originalPosition.y
        }, { easing: 'backOut' })
            .start();
    };
    /**
     * 震动所有小格子
     */
    ChessBoardController.prototype.shakeAllGrids = function (intensity, duration, frequency) {
        if (!this.gridNodes)
            return;
        // 遍历所有格子节点
        for (var x = 0; x < this.gridNodes.length; x++) {
            if (!this.gridNodes[x])
                continue;
            for (var y = 0; y < this.gridNodes[x].length; y++) {
                var gridNode = this.gridNodes[x][y];
                if (!gridNode || !gridNode.active)
                    continue;
                // 为每个格子创建独立的震动动画
                this.shakeGridNode(gridNode, intensity, duration, frequency);
            }
        }
    };
    /**
     * 震动单个格子节点
     */
    ChessBoardController.prototype.shakeGridNode = function (gridNode, intensity, duration, frequency) {
        // 保存原始位置
        var originalPosition = gridNode.position.clone();
        // 为每个格子添加随机延迟，创造波浪效果
        var randomDelay = Math.random() * 0.1;
        this.scheduleOnce(function () {
            // 创建震动动画，使用递减强度
            var currentIntensity = intensity;
            var intensityDecay = 0.94; // 格子震动衰减稍慢一些
            var createGridShakeStep = function (shakeIntensity) {
                return cc.tween()
                    .to(0.02, {
                    x: originalPosition.x + (Math.random() - 0.5) * shakeIntensity * 2,
                    y: originalPosition.y + (Math.random() - 0.5) * shakeIntensity * 2
                });
            };
            // 创建震动序列
            var shakeTween = cc.tween(gridNode);
            var totalSteps = Math.floor(duration * frequency * 0.8); // 格子震动时间稍短
            for (var i = 0; i < totalSteps; i++) {
                shakeTween = shakeTween.then(createGridShakeStep(currentIntensity));
                currentIntensity *= intensityDecay;
            }
            // 最后恢复到原位置
            shakeTween.to(0.15, {
                x: originalPosition.x,
                y: originalPosition.y
            }, { easing: 'backOut' })
                .start();
        }, randomDelay);
    };
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "playerGamePrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boomPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "biaojiPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom1Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom2Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom3Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom4Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom5Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom6Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom7Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom8Prefab", void 0);
    __decorate([
        property(cc.Node)
    ], ChessBoardController.prototype, "boardNode", void 0);
    ChessBoardController = __decorate([
        ccclass
    ], ChessBoardController);
    return ChessBoardController;
}(cc.Component));
exports.default = ChessBoardController;

cc._RF.pop();