{"version": 3, "sources": ["assets/scripts/game/Chess/ChessBoardController.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGtF,oDAAmD;AACnD,wEAAmE;AAE7D,IAAA,KAAsB,EAAE,CAAC,UAAU,EAAlC,OAAO,aAAA,EAAE,QAAQ,cAAiB,CAAC;AAY1C;IAAkD,wCAAY;IAA9D;QAAA,qEA6lGC;QA1lGG,sBAAgB,GAAc,IAAI,CAAC;QAGnC,gBAAU,GAAc,IAAI,CAAC;QAG7B,kBAAY,GAAc,IAAI,CAAC;QAG/B,iBAAW,GAAc,IAAI,CAAC;QAG9B,iBAAW,GAAc,IAAI,CAAC;QAG9B,iBAAW,GAAc,IAAI,CAAC;QAG9B,iBAAW,GAAc,IAAI,CAAC;QAG9B,iBAAW,GAAc,IAAI,CAAC;QAG9B,iBAAW,GAAc,IAAI,CAAC;QAG9B,iBAAW,GAAc,IAAI,CAAC;QAG9B,iBAAW,GAAc,IAAI,CAAC,CAAE,sBAAsB;QAGtD,eAAS,GAAY,IAAI,CAAC,CAAE,OAAO;QAInC,OAAO;QACU,gBAAU,GAAG,CAAC,CAAC,CAAE,QAAQ;QACzB,iBAAW,GAAG,GAAG,CAAC,CAAE,QAAQ;QAC5B,kBAAY,GAAG,GAAG,CAAC,CAAE,QAAQ;QAC7B,eAAS,GAAG,EAAE,CAAC,CAAE,gBAAgB;QAElD,SAAS;QACD,cAAQ,GAAiB,EAAE,CAAC,CAAE,aAAa;QAC3C,eAAS,GAAgB,EAAE,CAAC,CAAE,aAAa;QAoxBnD,YAAY;QACJ,uBAAiB,GAAgD,EAAE,CAAC;QA0Q5E,mBAAmB;QACX,mBAAa,GAAW,CAAC,CAAC;QAC1B,mBAAa,GAAW,CAAC,EAAE,CAAC,CAAC,oBAAoB;;QA2gEzD,iBAAiB;IACrB,CAAC;IA3iGG,qCAAM,GAAN;QACI,IAAI,CAAC,SAAS,EAAE,CAAC;IACrB,CAAC;IAED,oCAAK,GAAL;QAAA,iBAOC;QAJG,+BAA+B;QAC/B,IAAI,CAAC,YAAY,CAAC;YACd,KAAI,CAAC,2BAA2B,EAAE,CAAC;QACvC,CAAC,EAAE,GAAG,CAAC,CAAC;IACZ,CAAC;IAED,QAAQ;IACA,wCAAS,GAAjB;QACI,UAAU;QACV,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE;YACtC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;YACtB,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;YACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE;gBACtC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG;oBAClB,CAAC,EAAE,CAAC;oBACJ,CAAC,EAAE,CAAC;oBACJ,QAAQ,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,CAAC,CAAC;oBACzC,SAAS,EAAE,KAAK;iBACnB,CAAC;aACL;SACJ;QAED,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;IAED,cAAc;IACN,8CAAe,GAAvB;QACI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YAC1B,OAAO;SACV;QAED,oBAAoB;QACpB,IAAI,CAAC,2BAA2B,EAAE,CAAC;IACvC,CAAC;IAED,cAAc;IACN,0DAA2B,GAAnC;QACI,aAAa;QACb,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;YACnC,OAAO;SACV;QAED,eAAe;QACf,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;QAGvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtC,IAAI,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAExB,cAAc;YACd,IAAI,MAAM,GAAG,IAAI,CAAC,2BAA2B,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC1D,IAAI,MAAM,EAAE;gBACR,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;gBACrD,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;gBAC1D,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;aAC9C;iBAAM;gBACH,oBAAoB;gBACpB,IAAI,GAAG,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;gBAC9B,IAAI,QAAM,GAAG,IAAI,CAAC,6BAA6B,CAAC,GAAG,CAAC,CAAC;gBACrD,IAAI,QAAM,EAAE;oBACR,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,QAAM,CAAC,CAAC,EAAE,QAAM,CAAC,CAAC,CAAC,CAAC;oBACrD,IAAI,CAAC,SAAS,CAAC,QAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,QAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;oBAC1D,IAAI,CAAC,SAAS,CAAC,QAAM,CAAC,CAAC,CAAC,CAAC,QAAM,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;iBAC9C;aACJ;SACJ;IAEL,CAAC;IAED,cAAc;IACN,0DAA2B,GAAnC,UAAoC,QAAgB;QAChD,mBAAmB;QACnB,IAAI,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;QAC/C,IAAI,KAAK,EAAE;YACP,OAAO,EAAC,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC;SACzD;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,YAAY;IACJ,4DAA6B,GAArC,UAAsC,GAAY;QAC9C,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;QACpE,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;QAErE,IAAI,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAC9B,OAAO,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC,CAAC;SACvB;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,cAAc;IACN,mDAAoB,GAA5B,UAA6B,QAAiB,EAAE,CAAS,EAAE,CAAS;QAApE,iBAkEC;QAjEG,cAAc;QACd,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAC/B,OAAO,CAAC,KAAK,CAAC,6EAAmC,CAAC,SAAI,CAAC,0CAAS,CAAC,CAAC;YAClE,OAAO;SACV;QAID,SAAS;QACT,IAAI,cAAc,GAAG,KAAK,CAAC;QAC3B,IAAI,cAAc,GAAG,CAAC,CAAC;QACvB,IAAI,iBAAiB,GAAa,IAAI,CAAC;QACvC,IAAM,eAAe,GAAG,GAAG,CAAC,CAAC,SAAS;QAEtC,SAAS;QACT,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,UAAC,MAA2B;YACnE,cAAc,GAAG,IAAI,CAAC;YACtB,cAAc,GAAG,CAAC,CAAC;YAEnB,SAAS;YACT,iBAAiB,GAAG;gBAChB,IAAI,cAAc,EAAE;oBAChB,cAAc,IAAI,GAAG,CAAC;oBACtB,IAAI,cAAc,IAAI,eAAe,EAAE;wBAEnC,KAAI,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;wBAC3B,cAAc,GAAG,KAAK,CAAC;wBACvB,IAAI,iBAAiB,EAAE;4BACnB,KAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;yBACtC;qBACJ;iBACJ;YACL,CAAC,CAAC;YACF,KAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE,GAAG,CAAC,CAAC;QAC1C,CAAC,EAAE,IAAI,CAAC,CAAC;QAET,SAAS;QACT,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,UAAC,KAA0B;YAChE,iBAAiB;YACjB,IAAI,cAAc,IAAI,cAAc,GAAG,eAAe,EAAE;gBAEpD,KAAI,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;aACjC;YAED,cAAc,GAAG,KAAK,CAAC;YACvB,IAAI,iBAAiB,EAAE;gBACnB,KAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;aACtC;QACL,CAAC,EAAE,IAAI,CAAC,CAAC;QAET,SAAS;QACT,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,UAAC,MAA2B;YACpE,cAAc,GAAG,KAAK,CAAC;YACvB,IAAI,iBAAiB,EAAE;gBACnB,KAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;aACtC;QACL,CAAC,EAAE,IAAI,CAAC,CAAC;QAET,oBAAoB;QACpB,IAAI,MAAM,GAAG,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QAC9C,IAAI,CAAC,MAAM,EAAE;YACT,MAAM,GAAG,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;YAC1C,MAAM,CAAC,UAAU,GAAG,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC;YAC/C,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC;SAC3B;IACL,CAAC;IAED,yBAAyB;IACjB,mDAAoB,GAA5B,UAA6B,CAAS,EAAE,CAAS;QAC7C,YAAY;QACZ,0BAA0B;QAC1B,IAAI,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;QAChF,IAAI,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;QAEjF,OAAO,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC7B,CAAC;IAED,kBAAkB;IACV,0CAAW,GAAnB,UAAoB,CAAS,EAAE,CAAS,EAAE,MAA4B;QAGlE,wBAAwB;QACxB,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAE/B,OAAO;SACV;QAED,kBAAkB;QAClB,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE;YAE/B,OAAO;SACV;QAED,wBAAwB;QACxB,4CAA4C;QAC5C,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAChC,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,CAAC;YACJ,MAAM,EAAE,CAAC,CAAE,SAAS;SACvB,CAAC,CAAC;IACP,CAAC;IAED,kBAAkB;IACV,8CAAe,GAAvB,UAAwB,CAAS,EAAE,CAAS;QAGxC,wBAAwB;QACxB,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAE/B,OAAO;SACV;QAED,kBAAkB;QAClB,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE;YAE/B,OAAO;SACV;QAED,wBAAwB;QACxB,4CAA4C;QAC5C,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAChC,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,CAAC;YACJ,MAAM,EAAE,CAAC,CAAE,SAAS;SACvB,CAAC,CAAC;IACP,CAAC;IAED,cAAc;IACP,gDAAiB,GAAxB,UAAyB,CAAS,EAAE,CAAS,EAAE,QAAyB;QAAxE,iBAgDC;QAhD8C,yBAAA,EAAA,gBAAyB;QACpE,cAAc;QACd,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAC/B,OAAO,CAAC,KAAK,CAAC,wDAA6B,CAAC,SAAI,CAAC,MAAG,CAAC,CAAC;YACtD,OAAO;SACV;QAED,cAAc;QACd,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC,IAAI,QAAQ,CAAC,SAAS,EAAE;YACpB,OAAO,CAAC,KAAK,CAAC,4CAA2B,CAAC,SAAI,CAAC,wEAAc,CAAC,CAAC;YAC/D,OAAO;SACV;QAED,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YAC7B,OAAO;SACV;QAED,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YAC5B,OAAO;SACV;QAED,WAAW;QACX,IAAI,UAAU,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAEvD,UAAU;QACV,IAAI,eAAe,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1D,UAAU,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;QAIxC,oBAAoB;QACpB,UAAU,CAAC,MAAM,GAAG,KAAK,CAAC;QAE1B,eAAe;QACf,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;QAErC,kBAAkB;QAClB,IAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE;YACpD,8BAA8B;YAC9B,KAAI,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,SAAS;QACT,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC;QAC1B,QAAQ,CAAC,UAAU,GAAG,UAAU,CAAC;IACrC,CAAC;IAED,0BAA0B;IAClB,uDAAwB,GAAhC,UAAiC,CAAS,EAAE,CAAS;QACjD,WAAW;QACX,IAAI,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC;QACjC,IAAI,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC;QAEjC,+BAA+B;QAC/B,IAAI,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/D,IAAI,cAAc,EAAE;YAChB,IAAI,OAAO,GAAG,cAAc,CAAC,WAAW,EAAE,CAAC;YAC3C,IAAI,QAAQ,GAAG,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,GAAG,OAAO,EAAE,OAAO,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC;YAE/D,OAAO,QAAQ,CAAC;SACnB;QAED,mBAAmB;QAGnB,WAAW;QACX,IAAI,OAAO,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;QACnF,IAAI,OAAO,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;QAEpF,OAAO;QACP,IAAI,MAAM,GAAG,OAAO,GAAG,OAAO,CAAC;QAC/B,IAAI,MAAM,GAAG,OAAO,GAAG,OAAO,CAAC;QAE/B,IAAI,aAAa,GAAG,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAG1C,OAAO,aAAa,CAAC;IACzB,CAAC;IAED;;;;;OAKG;IACK,sDAAuB,GAA/B,UAAgC,CAAS,EAAE,CAAS;QAChD,gBAAgB;QAChB,uBAAuB;QACvB,iCAAiC;QACjC,iCAAiC;QACjC,qBAAqB;QAErB,IAAM,MAAM,GAAG,CAAC,GAAG,CAAC,CAAE,QAAQ;QAC9B,IAAM,MAAM,GAAG,CAAC,GAAG,CAAC,CAAE,QAAQ;QAC9B,IAAM,KAAK,GAAG,EAAE,CAAC,CAAK,QAAQ;QAC9B,IAAM,KAAK,GAAG,EAAE,CAAC,CAAK,QAAQ;QAE9B,IAAM,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QACpC,IAAM,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAEpC,IAAM,QAAQ,GAAG,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAGvC,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED;;;OAGG;IACK,uDAAwB,GAAhC,UAAiC,UAAmB;QAChD,IAAI,CAAC,UAAU,EAAE;YACb,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC9B,OAAO;SACV;QAID,OAAO;QACP,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC;QAEzB,oBAAoB;QACpB,IAAM,aAAa,GAAG,UAAU,CAAC,MAAM,CAAC;QACxC,IAAM,UAAU,GAAG,aAAa,GAAG,GAAG,CAAC;QACvC,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAEhC,wBAAwB;QACxB,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC;aACf,EAAE,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,aAAa,EAAE,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;aAChF,KAAK,EAAE,CAAC;IAGjB,CAAC;IAED;;;;;OAKG;IACK,wDAAyB,GAAjC,UAAkC,UAAmB,EAAE,WAAoB,EAAE,QAAgB;QACzF,IAAI,CAAC,UAAU,EAAE;YACb,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC9B,OAAO;SACV;QAID,wBAAwB;QACxB,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC;aACf,EAAE,CAAC,GAAG,EAAE;YACL,CAAC,EAAE,WAAW,CAAC,CAAC;YAChB,CAAC,EAAE,WAAW,CAAC,CAAC;YAChB,MAAM,EAAE,QAAQ;YAChB,MAAM,EAAE,QAAQ;SACnB,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;aACxB,KAAK,EAAE,CAAC;IAGjB,CAAC;IAED;;;;;;OAMG;IACK,iEAAkC,GAA1C,UAA2C,CAAS,EAAE,CAAS,EAAE,YAAoB;QACjF,IAAI,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC;QACjC,IAAI,OAAe,CAAC;QAEpB,IAAI,YAAY,KAAK,CAAC,EAAE;YACpB,kBAAkB;YAClB,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,MAAM;SAEvC;aAAM;YACH,kBAAkB;YAClB,OAAO,GAAG,CAAC,CAAC;SAEf;QAED,+BAA+B;QAC/B,IAAI,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/D,IAAI,cAAc,EAAE;YAChB,IAAI,OAAO,GAAG,cAAc,CAAC,WAAW,EAAE,CAAC;YAC3C,IAAI,QAAQ,GAAG,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,GAAG,OAAO,EAAE,OAAO,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC;YAE/D,OAAO,QAAQ,CAAC;SACnB;QAED,mBAAmB;QACnB,IAAI,OAAO,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;QACnF,IAAI,OAAO,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;QAEpF,IAAI,MAAM,GAAG,OAAO,GAAG,OAAO,CAAC;QAC/B,IAAI,MAAM,GAAG,OAAO,GAAG,OAAO,CAAC;QAE/B,IAAI,aAAa,GAAG,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAG1C,OAAO,aAAa,CAAC;IACzB,CAAC;IAED;;;;;OAKG;IACK,+DAAgC,GAAxC,UAAyC,CAAS,EAAE,CAAS;QACzD,gBAAgB;QAChB,IAAI,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC;QACjC,IAAI,OAAO,GAAG,CAAC,CAAC,CAAC,gBAAgB;QAEjC,+BAA+B;QAC/B,IAAI,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/D,IAAI,cAAc,EAAE;YAChB,IAAI,OAAO,GAAG,cAAc,CAAC,WAAW,EAAE,CAAC;YAC3C,IAAI,QAAQ,GAAG,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,GAAG,OAAO,EAAE,OAAO,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC;YAE/D,OAAO,QAAQ,CAAC;SACnB;QAED,mBAAmB;QACnB,WAAW;QACX,IAAI,OAAO,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;QACnF,IAAI,OAAO,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;QAEpF,gBAAgB;QAChB,IAAI,MAAM,GAAG,OAAO,GAAG,OAAO,CAAC;QAC/B,IAAI,MAAM,GAAG,OAAO,GAAG,OAAO,CAAC;QAE/B,IAAI,aAAa,GAAG,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAG1C,OAAO,aAAa,CAAC;IACzB,CAAC;IAED,wBAAwB;IAChB,kDAAmB,GAA3B,UAA4B,UAAmB;QAC3C,aAAa;QACb,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;YACnC,OAAO;SACV;QAED,oBAAoB;QACpB,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QACpD,IAAI,MAAM,EAAE;YAGR,kBAAkB;YAClB,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;YAGvB,OAAO;YACP,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;SAGvC;aAAM;YAEH,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;SACvC;QAED,oBAAoB;QACpB,oCAAoC;IACxC,CAAC;IAED,wBAAwB;IAChB,8CAAe,GAAvB,UAAwB,UAAmB;QACvC,aAAa;QACb,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;YACnC,OAAO;SACV;QAED,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;YACvB,UAAU;YACV,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC,CAAC;YAC9E,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YAEpE,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YACjC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;SAG9C;aAAM;YACH,OAAO,CAAC,KAAK,CAAC,+DAAa,CAAC,CAAC;YAC7B,UAAU;YACV,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;SACvC;IACL,CAAC;IAED,gBAAgB;IACR,qDAAsB,GAA9B,UAA+B,UAAmB,EAAE,CAAS,EAAE,CAAS,EAAE,QAAiB,EAAE,UAAsB;QAC/G,2BAA2B;QAC3B,IAAI,gBAAgB,GAAG,UAAU,CAAC,YAAY,CAAC,sBAAsB,CAAC;YAChD,UAAU,CAAC,YAAY,CAAC,uBAAuB,CAAC;YAChD,UAAU,CAAC,sBAAsB,CAAC,sBAAsB,CAAC,CAAC;QAEhF,IAAI,gBAAgB,EAAE;YAClB,iBAAiB;YACjB,IAAI,gBAAgB,CAAC,MAAM,EAAE;gBACzB,wBAAwB;gBACxB,IAAI,YAAY,GAAG,gBAAgB,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;gBACnE,IAAI,CAAC,YAAY,EAAE;oBACf,YAAY,GAAG,gBAAgB,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;iBAClE;gBAED,eAAe;gBACf,gBAAgB,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC;gBACtC,gBAAgB,CAAC,MAAM,CAAC,OAAO,GAAG,GAAG,CAAC;aACzC;iBAAM;gBACH,OAAO,CAAC,KAAK,CAAC,uCAAuC,CAAC,CAAC;gBACvD,UAAU,EAAE,CAAC;gBACb,OAAO;aACV;YAED,qBAAqB;YACrB,IAAI,gBAAgB,CAAC,QAAQ,EAAE;gBAE3B,gBAAgB,CAAC,QAAQ,CAAC,MAAM,GAAG,QAAQ,CAAC;gBAG5C,eAAe;gBACf,IAAI,QAAQ,EAAE;oBACV,gBAAgB,CAAC,QAAQ,CAAC,OAAO,GAAG,GAAG,CAAC;oBAGxC,kBAAkB;oBAClB,IAAI,MAAM,GAAG,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC;oBAC9C,OAAO,MAAM,IAAI,MAAM,KAAK,UAAU,EAAE;wBAEpC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC;wBACrB,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;qBAC1B;oBAED,gBAAgB;oBAChB,IAAI,CAAC,YAAY,CAAC;oBAElB,CAAC,EAAE,GAAG,CAAC,CAAC;iBACX;aACJ;iBAAM;gBACH,OAAO,CAAC,IAAI,CAAC,8DAAe,CAAC,SAAI,CAAC,MAAG,CAAC,CAAC;aAC1C;YAED,cAAc;YACd,IAAI,QAAQ,GAAG;gBACX,MAAM,EAAE,YAAU,CAAC,SAAI,CAAG;gBAC1B,QAAQ,EAAE,kBAAM,CAAC,SAAI,CAAC,MAAG;gBACzB,MAAM,EAAE,IAAI,CAAC,mBAAmB,EAAE;gBAClC,KAAK,EAAE,CAAC;gBACR,GAAG,EAAE,CAAC;gBACN,IAAI,EAAE,CAAC;gBACP,MAAM,EAAE,CAAC;gBACT,IAAI,EAAE,CAAC;aACE,CAAC;YAEd,wCAAwC;YACxC,IAAI;gBACA,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;gBAEnC,wCAAwC;gBACxC,IAAI,CAAC,YAAY,CAAC;oBACd,IAAI,gBAAgB,CAAC,QAAQ,EAAE;wBAC3B,gBAAgB,CAAC,QAAQ,CAAC,MAAM,GAAG,QAAQ,CAAC;qBAE/C;oBACD,UAAU,EAAE,CAAC;gBACjB,CAAC,EAAE,GAAG,CAAC,CAAC;aACX;YAAC,OAAO,KAAK,EAAE;gBACZ,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;gBAClC,UAAU,EAAE,CAAC;aAChB;SAEJ;aAAM;YACH,OAAO,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;YAC7C,IAAI,CAAC,yBAAyB,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;SAChE;IACL,CAAC;IAGD,sBAAsB;IACd,gDAAiB,GAAzB,UAA0B,UAAmB,EAAE,CAAS,EAAE,CAAS;QAAnE,iBAgEC;QA7DG,2BAA2B;QAC3B,IAAI,gBAAgB,GAAG,UAAU,CAAC,YAAY,CAAC,sBAAsB,CAAC;YAChD,UAAU,CAAC,YAAY,CAAC,uBAAuB,CAAC;YAChD,UAAU,CAAC,sBAAsB,CAAC,sBAAsB,CAAC,CAAC;QAEhF,IAAI,gBAAgB,EAAE;YAGlB,iBAAiB;YACjB,IAAI,gBAAgB,CAAC,MAAM,EAAE;gBAGzB,wBAAwB;gBACxB,IAAI,YAAY,GAAG,gBAAgB,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;gBACnE,IAAI,CAAC,YAAY,EAAE;oBACf,OAAO,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;oBAC9C,YAAY,GAAG,gBAAgB,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;iBAClE;gBAED,eAAe;gBACf,gBAAgB,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC;gBACtC,gBAAgB,CAAC,MAAM,CAAC,OAAO,GAAG,GAAG,CAAC;aAGzC;iBAAM;gBACH,OAAO,CAAC,KAAK,CAAC,uCAAuC,CAAC,CAAC;gBACvD,OAAO;aACV;YAED,SAAS;YACT,IAAI,QAAQ,GAAG;gBACX,MAAM,EAAE,YAAU,CAAC,SAAI,CAAG;gBAC1B,QAAQ,EAAE,kBAAM,CAAC,SAAI,CAAC,MAAG;gBACzB,MAAM,EAAE,IAAI,CAAC,mBAAmB,EAAE;gBAClC,KAAK,EAAE,CAAC;gBACR,GAAG,EAAE,CAAC;gBACN,IAAI,EAAE,CAAC;gBACP,MAAM,EAAE,CAAC;gBACT,IAAI,EAAE,CAAC;aACE,CAAC;YAId,eAAe;YACf,IAAI;gBACA,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;gBAEnC,eAAe;gBACf,IAAI,CAAC,YAAY,CAAC;oBACd,KAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC1D,CAAC,EAAE,GAAG,CAAC,CAAC;aAEX;YAAC,OAAO,KAAK,EAAE;gBACZ,OAAO,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;aACtC;SACJ;aAAM;YACH,OAAO,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;YAEpD,sBAAsB;YACtB,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SAC/C;IACL,CAAC;IAED,aAAa;IACL,gDAAiB,GAAzB,UAA0B,UAAmB,EAAE,CAAS,EAAE,CAAS;QAC/D,IAAI,CAAC,UAAU,EAAE;YACb,OAAO,CAAC,KAAK,CAAC,yBAAQ,CAAC,SAAI,CAAC,wCAAiB,CAAC,CAAC;YAC/C,OAAO;SACV;QAED,IAAI,MAAM,GAAG,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QAChD,IAAI,CAAC,MAAM,EAAE;YACT,OAAO,CAAC,KAAK,CAAC,yBAAQ,CAAC,SAAI,CAAC,4DAAsB,CAAC,CAAC;YACpD,OAAO;SACV;QAED,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;YACrB,OAAO,CAAC,IAAI,CAAC,+BAAS,CAAC,SAAI,CAAC,uFAA6B,CAAC,CAAC;YAE3D,oBAAoB;YACpB,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SAC5C;aAAM;SAEN;IAGL,CAAC;IAED,eAAe;IACP,gDAAiB,GAAzB,UAA0B,UAAmB,EAAE,CAAS,EAAE,CAAS;QAE/D,IAAI,MAAM,GAAG,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QAChD,IAAI,CAAC,MAAM,EAAE;YACT,MAAM,GAAG,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;SAC/C;QAED,cAAc;QACd,IAAI,OAAO,GAAG,IAAI,EAAE,CAAC,SAAS,EAAE,CAAC;QACjC,IAAI,MAAM,GAAG;YACT,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;YACpB,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;YACnB,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;YACnB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;YACpB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAE,KAAK;SAC9B,CAAC;QAEF,IAAI,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;QACzC,IAAI,KAAK,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC;QAE/B,OAAO,CAAC,YAAY,CAAC,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACrF,MAAM,CAAC,WAAW,GAAG,IAAI,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAEjD,OAAO;QACP,UAAU,CAAC,cAAc,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAClC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC;IAG7B,CAAC;IAED,sCAAsC;IAC9B,mDAAoB,GAA5B,UAA6B,UAAmB,EAAE,CAAS,EAAE,CAAS;QAElE,mBAAmB;QACnB,IAAI,UAAU,GAAG,UAAU,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QACrD,IAAI,UAAU,EAAE;YAEZ,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SAC5C;aAAM;YACH,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAEhC,YAAY;SAEf;IACL,CAAC;IAED,YAAY;IACJ,kDAAmB,GAA3B;QACI,aAAa;QACb,OAAO,uEAAuE,CAAC;IACnF,CAAC;IAED,oBAAoB;IACZ,iDAAkB,GAA1B,UAA2B,CAAS,EAAE,CAAS;QAC3C,qBAAqB;QAGrB,mBAAmB;QACnB,IAAI,CAAC,sBAAsB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAElC,gBAAgB;QAChB,IAAI,CAAC,sBAAsB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACtC,CAAC;IAED,WAAW;IACH,qDAAsB,GAA9B,UAA+B,CAAS,EAAE,CAAS;QAC/C,SAAS;QACT,IAAI,QAAQ,GAAG;YACX,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,CAAC;YACJ,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,QAAQ,EAAE,IAAI,CAAC,kBAAkB,EAAE;SACtC,CAAC;QAGF,mBAAmB;QACnB,OAAO,QAAQ,CAAC;IACpB,CAAC;IAKO,qDAAsB,GAA9B,UAA+B,CAAS,EAAE,CAAS;QAC/C,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;YACxB,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,CAAC;YACJ,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACxB,CAAC,CAAC;IAGP,CAAC;IAED,eAAe;IACP,iDAAkB,GAA1B;QACI,oBAAoB;QACpB,OAAO,YAAY,CAAC,CAAE,OAAO;IACjC,CAAC;IAED,cAAc;IACP,0CAAW,GAAlB,UAAmB,CAAS,EAAE,CAAS;QACnC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE;YAChE,OAAO,IAAI,CAAC;SACf;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC;IAED,YAAY;IACL,8CAAe,GAAtB,UAAuB,CAAS,EAAE,CAAS;QACvC,IAAI,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACtC,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE;YAClC,OAAO,KAAK,CAAC;SAChB;QAED,SAAS;QACT,IAAI,QAAQ,CAAC,UAAU,EAAE;YACrB,QAAQ,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC;YACvC,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC;SAC9B;QAED,OAAO;QACP,QAAQ,CAAC,SAAS,GAAG,KAAK,CAAC;QAG3B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,SAAS;IACF,8CAAe,GAAtB;QACI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE;YACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE;gBACtC,IAAI,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;aAC9B;SACJ;IAEL,CAAC;IAED,eAAe;IACR,sDAAuB,GAA9B;QACI,IAAI,WAAW,GAA6B,EAAE,CAAC;QAE/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE;YACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE;gBACtC,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE;oBAC/B,WAAW,CAAC,IAAI,CAAC,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC,CAAC,CAAC;iBAClC;aACJ;SACJ;QAED,OAAO,WAAW,CAAC;IACvB,CAAC;IAED,WAAW;IACJ,gDAAiB,GAAxB,UAAyB,CAAS,EAAE,CAAS;QACzC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC;IAC1E,CAAC;IAED,WAAW;IACJ,0CAAW,GAAlB,UAAmB,CAAS,EAAE,CAAS;QACnC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAC/B,OAAO,KAAK,CAAC;SAChB;QACD,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IAC1C,CAAC;IAED,WAAW;IACJ,mDAAoB,GAA3B;QACI,sBAAW,IAAI,CAAC,iBAAiB,EAAE,CAAE,OAAO;IAChD,CAAC;IAED,WAAW;IACJ,qDAAsB,GAA7B;QACI,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;IAEhC,CAAC;IAED,eAAe;IACR,4DAA6B,GAApC,UAAqC,QAAiB;QAClD,aAAa;QACb,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;YACnC,OAAO,IAAI,CAAC;SACf;QAED,mBAAmB;QACnB,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QAE7D,SAAS;QACT,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;QACzE,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;QAE1E,IAAI,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAC9B,OAAO,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC,CAAC;SACvB;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,eAAe;IACR,4CAAa,GAApB,UAAqB,CAAS,EAAE,CAAS,EAAE,SAAyB;QAAzB,0BAAA,EAAA,gBAAyB;QAChE,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAC/B,OAAO;SACV;QAED,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,IAAI,QAAQ,EAAE;YACV,yBAAyB;YACzB,IAAI,SAAS,EAAE;gBACX,QAAQ,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC;aAEpC;iBAAM;gBACH,QAAQ,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;aAEnC;SACJ;IACL,CAAC;IAED,qBAAqB;IACd,gDAAiB,GAAxB,UAAyB,WAAqC;QAA9D,iBAQC;QALG,WAAW,CAAC,OAAO,CAAC,UAAA,KAAK;YACrB,IAAI,KAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,KAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE;gBAChF,KAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;aAC5C;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED,gBAAgB;IACT,gDAAiB,GAAxB;QAEI,IAAI,CAAC,2BAA2B,EAAE,CAAC;IACvC,CAAC;IAED,cAAc;IACP,wCAAS,GAAhB,UAAiB,CAAS,EAAE,CAAS;QAEjC,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED,gBAAgB;IACT,2CAAY,GAAnB;QACI,IAAI,IAAI,GAAG;YACP,SAAS,EAAE,IAAI,CAAC,UAAU;YAC1B,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,UAAU,EAAE,IAAI,CAAC,WAAW;YAC5B,WAAW,EAAE,IAAI,CAAC,YAAY;YAC9B,UAAU,EAAE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU;YAC7C,iBAAiB,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YACtE,WAAW,EAAE,IAAI,CAAC,uBAAuB,EAAE,CAAC,MAAM;YAClD,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,gBAAgB;YAC5C,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS;SACjC,CAAC;QAGF,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,uBAAuB;IAChB,yCAAU,GAAjB,UAAkB,CAAS,EAAE,CAAS;QAGlC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;YAC3B,OAAO;SACV;QAED,cAAc;QACd,IAAI,QAAQ,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,UAAQ,CAAC,SAAI,CAAG,CAAC,CAAC;QAE7C,WAAW;QACX,IAAI,MAAM,GAAG,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QAC9C,IAAI,OAAO,GAAG,IAAI,EAAE,CAAC,SAAS,EAAE,CAAC;QACjC,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;QACjF,OAAO,CAAC,YAAY,CAAC,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACrF,MAAM,CAAC,WAAW,GAAG,IAAI,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAEjD,OAAO;QACP,QAAQ,CAAC,cAAc,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAEhC,OAAO;QACP,IAAI,GAAG,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9C,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QAE1B,SAAS;QACT,IAAI,SAAS,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACrC,IAAI,KAAK,GAAG,SAAS,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;QAC7C,KAAK,CAAC,MAAM,GAAG,MAAI,CAAC,SAAI,CAAC,MAAG,CAAC;QAC7B,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;QACpB,KAAK,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;QAClC,SAAS,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5B,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QAE7B,oBAAoB;QACpB,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;IAGvC,CAAC;IAED,WAAW;IACJ,6CAAc,GAArB;QACI,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;YAC/C,QAAQ,CAAC,OAAO,CAAC,UAAA,KAAK;gBAClB,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;oBAChC,KAAK,CAAC,gBAAgB,EAAE,CAAC;iBAC5B;YACL,CAAC,CAAC,CAAC;SACN;IAEL,CAAC;IAED,6BAA6B;IACtB,gDAAiB,GAAxB;QAEI,WAAW;QACX,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,eAAe,CAAC;IACpD,CAAC;IAED,mBAAmB;IACZ,6CAAc,GAArB;QACI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;YACvC,OAAO;SACV;QAED,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QACpD,IAAI,MAAM,EAAE;YACR,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;SAEzB;IACL,CAAC;IAED,aAAa;IACN,4CAAa,GAApB;QACI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC;YACrC,OAAO;SACV;QAED,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QACpD,IAAI,MAAM,EAAE;YACR,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;SAE1B;IACL,CAAC;IAMD,WAAW;IACJ,8CAAe,GAAtB,UAAuB,OAAe,EAAE,OAAe;QACnD,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC;QAC7B,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC;IAEjC,CAAC;IAED,UAAU;IACH,+CAAgB,GAAvB;QACI,OAAO,EAAC,CAAC,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC,EAAE,IAAI,CAAC,aAAa,EAAC,CAAC;IAC1D,CAAC;IAED,UAAU;IACH,6CAAc,GAArB,UAAsB,CAAS,EAAE,CAAS,EAAE,OAAe,EAAE,OAAe;QAGxE,WAAW;QACX,IAAI,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC;QACzC,IAAI,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC;QAEzC,SAAS;QACT,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAEvC,OAAO;QACP,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEtB,QAAQ;QACR,IAAI,CAAC,eAAe,CAAC,eAAe,EAAE,eAAe,CAAC,CAAC;IAC3D,CAAC;IAED,WAAW;IACJ,gDAAiB,GAAxB,UAAyB,CAAS,EAAE,CAAS;QAA7C,iBAwCC;QArCG,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAC/B,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YACxB,OAAO;SACV;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE;YAC/B,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC3B,OAAO;SACV;QAED,aAAa;QACb,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAE7B,SAAS;QACT,IAAI,CAAC,YAAY,CAAC;YACd,IAAI,QAAQ,GAAG,KAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnC,IAAI,QAAQ,CAAC,UAAU,EAAE;gBAGrB,yBAAyB;gBACzB,IAAI,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC,YAAY,CAAC,sBAAsB,CAAC,CAAC;gBAC1E,IAAI,UAAU,IAAI,UAAU,CAAC,MAAM,EAAE;oBAGjC,IAAI,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;oBACvD,IAAI,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE;qBAEjC;yBAAM;wBACH,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;qBACvC;iBACJ;qBAAM;oBACH,OAAO,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;iBACvD;aACJ;iBAAM;gBACH,OAAO,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;aAC/B;QACL,CAAC,EAAE,GAAG,CAAC,CAAC;IACZ,CAAC;IAED,UAAU;IACH,mDAAoB,GAA3B;QAGI,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,OAAO,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;YACzC,OAAO;SACV;QAED,iBAAiB;QACjB,IAAI,QAAQ,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAIrD,OAAO;QACP,IAAI,UAAU,GAAG,QAAQ,CAAC,YAAY,CAAC,sBAAsB,CAAC,CAAC;QAC/D,IAAI,UAAU,EAAE;YAEZ,IAAI,UAAU,CAAC,MAAM,EAAE;gBAGnB,IAAI,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;aAE1D;iBAAM;gBACH,OAAO,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;aAClC;SACJ;aAAM;YACH,OAAO,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;SAChD;QAED,UAAU;QAEV,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;QAEnC,SAAS;QACT,QAAQ,CAAC,OAAO,EAAE,CAAC;IACvB,CAAC;IAED,WAAW;IACH,+CAAgB,GAAxB,UAAyB,IAAa,EAAE,KAAa;QACjD,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAGhC,KAAkB,UAAa,EAAb,KAAA,IAAI,CAAC,QAAQ,EAAb,cAAa,EAAb,IAAa,EAAE;YAA5B,IAAI,KAAK,SAAA;YACV,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;SAC3C;IACL,CAAC;IAED,SAAS;IACD,8CAAe,GAAvB,UAAwB,UAAmB,EAAE,GAAW,EAAE,UAAsB;QAAhF,iBAuDC;QArDG,IAAI,CAAC,UAAU,EAAE;YACb,OAAO,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;YACjC,UAAU,EAAE,CAAC;YACb,OAAO;SACV;QAED,IAAI,YAAY,GAAG,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QACtD,IAAI,CAAC,YAAY,EAAE;YACf,OAAO,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;YAC9C,YAAY,GAAG,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;SACrD;QAED,IAAI,CAAC,GAAG,IAAI,GAAG,KAAK,EAAE,EAAE;YACpB,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAChC,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACzC,UAAU,EAAE,CAAC;YACb,OAAO;SACV;QAED,eAAe;QACf,IAAI,GAAG,GAAG,MAAM,CAAC;QACjB,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;YAC3E,GAAG,GAAG,MAAM,CAAC;SAChB;aAAM,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;YAC3C,GAAG,GAAG,MAAM,CAAC;SAChB;QAID,EAAE,CAAC,YAAY,CAAC,UAAU,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,UAAC,GAAG,EAAE,OAAqB;YACrE,IAAI,GAAG,EAAE;gBACL,OAAO,CAAC,KAAK,CAAC,mDAAa,GAAG,CAAC,OAAO,IAAI,GAAG,CAAE,CAAC,CAAC;gBACjD,OAAO,CAAC,KAAK,CAAC,mCAAa,GAAK,CAAC,CAAC;gBAElC,SAAS;gBACT,KAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBACzC,UAAU,EAAE,CAAC;gBACb,OAAO;aACV;YAID,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;YAClC,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC;YACzB,YAAY,CAAC,WAAW,GAAG,IAAI,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAEvD,SAAS;YACT,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC;YACzB,UAAU,CAAC,OAAO,GAAG,GAAG,CAAC;YAGzB,UAAU,EAAE,CAAC;QACjB,CAAC,CAAC,CAAC;IACP,CAAC;IAED,sCAAsC;IAC9B,wDAAyB,GAAjC,UAAkC,UAAmB,EAAE,CAAS,EAAE,CAAS,EAAE,UAAsB;QAG/F,mBAAmB;QACnB,IAAI,UAAU,GAAG,UAAU,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QACrD,IAAI,UAAU,EAAE;YAEZ,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACzC,UAAU,EAAE,CAAC;SAChB;aAAM;YACH,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAEhC,YAAY;YAGZ,UAAU,EAAE,CAAC;SAChB;IACL,CAAC;IAED;;;;OAIG;IACH,kDAAmB,GAAnB,UAAoB,MAAc,EAAE,KAAa;QAC7C,IAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC9C,IAAI,WAAW,GAAG,KAAK,CAAC;QAExB,qCAAqC;QACrC,IAAI,MAAM,KAAK,aAAa,EAAE;YAC1B,WAAW,GAAG,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC;SACrD;aAAM;YACH,yBAAyB;YACzB,WAAW,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;SAC3D;QAED,IAAI,CAAC,WAAW,EAAE;YACd,OAAO,CAAC,IAAI,CAAC,oCAAS,MAAM,8EAAe,CAAC,CAAC;SAChD;IACL,CAAC;IAED;;OAEG;IACK,+CAAgB,GAAxB;;QACI,OAAO,aAAA,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,0CAAE,QAAQ,0CAAE,MAAM,KAAI,EAAE,CAAC;IACtE,CAAC;IAED;;OAEG;IACK,sDAAuB,GAA/B,UAAgC,KAAa;QACzC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE;YACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE;gBACtC,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnC,IAAI,QAAQ,CAAC,SAAS,IAAI,QAAQ,CAAC,UAAU,EAAE;oBAC3C,IAAI,gBAAgB,GAAG,QAAQ,CAAC,UAAU,CAAC,YAAY,CAAC,sBAAsB,CAAC;wBAC1D,QAAQ,CAAC,UAAU,CAAC,YAAY,CAAC,uBAAuB,CAAC,CAAC;oBAE/E,IAAI,gBAAgB,EAAE;wBAClB,IAAI,CAAC,2BAA2B,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;wBAC1D,OAAO,IAAI,CAAC;qBACf;iBACJ;aACJ;SACJ;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,oDAAqB,GAA7B,UAA8B,MAAc,EAAE,KAAa;QACvD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO,KAAK,CAAC;SAChB;QAED,iBAAiB;QACjB,kCAAkC;QAClC,oBAAoB;QACpB,OAAO,IAAI,CAAC,4BAA4B,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IAC5D,CAAC;IAED;;OAEG;IACK,2DAA4B,GAApC,UAAqC,MAAc,EAAE,KAAa;QAC9D,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO,CAAC,IAAI,CAAC,0FAAkB,MAAM,wBAAM,CAAC,CAAC;YAC7C,OAAO,KAAK,CAAC;SAChB;QAID,iCAAiC;QACjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrD,IAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAEzC,iCAAiC;YACjC,IAAI,gBAAgB,GAAG,KAAK,CAAC,YAAY,CAAC,sBAAsB,CAAC,CAAC;YAClE,IAAI,CAAC,gBAAgB,EAAE;gBACnB,gBAAgB,GAAG,KAAK,CAAC,YAAY,CAAC,uBAAuB,CAAC,CAAC,CAAE,UAAU;aAC9E;YACD,IAAI,CAAC,gBAAgB,EAAE;gBACnB,WAAW;gBACX,IAAM,UAAU,GAAG,KAAK,CAAC,aAAa,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC;gBACrD,gBAAgB,GAAG,UAAU,CAAC,IAAI,CAAC,UAAA,IAAI;oBACnC,OAAA,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,sBAAsB;wBAChD,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,uBAAuB;gBADjD,CACiD,CACpD,CAAC;aACL;YAED,IAAM,YAAY,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC;YAIrC,iBAAiB;YACjB,IAAI,YAAY,IAAI,CAAC,YAAY,KAAK,MAAM,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAG,qBAAqB;gBAC5E,IAAM,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC;aAE3D;YAED,IAAI,YAAY,KAAK,MAAM,EAAE;gBACzB,IAAI,gBAAgB,EAAE;oBAClB,sBAAsB;oBAEtB,IAAI,CAAC,2BAA2B,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;oBAC1D,OAAO,IAAI,CAAC;iBACf;qBAAM;oBACH,iBAAiB;oBACjB,OAAO,CAAC,IAAI,CAAC,2CAAW,MAAM,0EAA+B,CAAC,CAAC;oBAC/D,OAAO,KAAK,CAAC,CAAE,oBAAoB;iBACtC;aACJ;SACJ;QAED,OAAO,CAAC,IAAI,CAAC,2CAAW,MAAM,oCAAQ,CAAC,CAAC;QACxC,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,0DAA2B,GAAnC,UAAoC,gBAAqB,EAAE,KAAa;QACpE,qBAAqB;QACrB,IAAM,UAAU,GAAG,gBAAgB,CAAC,IAAI,CAAC;QACzC,IAAM,oBAAoB,GAAG,UAAU,CAAC,eAAe,EAAE,CAAC;QAE1D,WAAW;QACX,UAAU,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;QAE/B,mBAAmB;QACnB,IAAI,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,CAAC;QAE/C,IAAI,KAAK,GAAG,CAAC,EAAE;YACX,gBAAgB,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;SACxC;aAAM,IAAI,KAAK,GAAG,CAAC,EAAE;YAClB,gBAAgB,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;SAClD;QAED,sBAAsB;QACtB,IAAI,CAAC,YAAY,CAAC;YACd,IAAI,UAAU,IAAI,UAAU,CAAC,OAAO,EAAE;gBAClC,UAAU,CAAC,eAAe,CAAC,oBAAoB,CAAC,CAAC;aACpD;QACL,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,mBAAmB;IAChC,CAAC;IAED;;OAEG;IACK,sDAAuB,GAA/B,UAAgC,gBAAqB;QACjD,cAAc;QACd,IAAI,gBAAgB,CAAC,YAAY,EAAE;YAC/B,gBAAgB,CAAC,YAAY,CAAC,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,UAAU,GAAG,CAAC,CAAC;SAClE;QAED,cAAc;QACd,IAAI,gBAAgB,CAAC,YAAY,EAAE;YAC/B,gBAAgB,CAAC,YAAY,CAAC,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,UAAU,GAAG,CAAC,CAAC;SAClE;IAGL,CAAC;IAGD;;;;OAIG;IACH,qDAAsB,GAAtB,UAAuB,MAAc,EAAE,QAAgB;QAGnD,IAAI,WAAW,GAAG,KAAK,CAAC;QAExB,gBAAgB;QAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE;YACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE;gBACtC,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnC,IAAI,QAAQ,CAAC,SAAS,IAAI,QAAQ,CAAC,UAAU,EAAE;oBAC3C,IAAI,gBAAgB,GAAG,QAAQ,CAAC,UAAU,CAAC,YAAY,CAAC,sBAAsB,CAAC;wBAC1D,QAAQ,CAAC,UAAU,CAAC,YAAY,CAAC,uBAAuB,CAAC,CAAC;oBAE/E,IAAI,gBAAgB,EAAE;wBAClB,gBAAgB,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;wBACxC,WAAW,GAAG,IAAI,CAAC;wBAEnB,MAAM;qBACT;iBACJ;aACJ;YACD,IAAI,WAAW;gBAAE,MAAM;SAC1B;QAED,IAAI,CAAC,WAAW,EAAE;YACd,OAAO,CAAC,IAAI,CAAC,kGAA0B,MAAQ,CAAC,CAAC;SACpD;IACL,CAAC;IAED;;;OAGG;IACI,6CAAc,GAArB;QAGI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;YAChC,OAAO;SACV;QAID,UAAU;QACV,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrD,IAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;SAE5C;QAED,sBAAsB;QAEtB,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE5B,UAAU;QAEV,IAAI,CAAC,YAAY,EAAE,CAAC;QAEpB,YAAY;QAEZ,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAI7B,UAAU;QACV,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrD,IAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;SAE5C;IAGL,CAAC;IAED;;OAEG;IACI,wCAAS,GAAhB;QAEI,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACI,oDAAqB,GAA5B,UAA6B,OAAY;QACrC,OAAO,CAAC,GAAG,CAAC,kCAAkC,EAAE,OAAO,CAAC,CAAC;QAEzD,SAAS;QACT,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YAC9C,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;gBAC3B,iCAAiC;gBACjC,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,OAAO,CAAC,MAAM,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;gBAC7E,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;aACtC;iBAAM,IAAI,OAAO,CAAC,CAAC,CAAC,IAAI,OAAO,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAI,CAAC,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;gBACjG,uDAAuD;gBACvD,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;gBACvD,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;aACxC;iBAAM;gBACH,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;aACxC;SACJ;aAAM,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc,IAAI,OAAO,CAAC,YAAY,CAAC,EAAE;YACpE,gDAAgD;YAChD,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;YACjC,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;SACvC;aAAM;YACH,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;SAC1C;IACL,CAAC;IAED;;;OAGG;IACK,mDAAoB,GAA5B,UAA6B,SAAkB;QAA/C,iBAiEC;QAhEG,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,SAAS,CAAC,CAAC;QAE5C,IAAI,aAAa,GAAG,CAAC,CAAC;gCAEb,CAAC;oCACG,CAAC;gBACN,IAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAEjC,IAAI,CAAC,OAAK,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;;iBAElC;gBAED,gBAAgB;gBAChB,IAAI,QAAQ,KAAK,IAAI,IAAI,QAAQ,KAAK,SAAS,IAAI,QAAQ,KAAK,CAAC,EAAE;oBAC/D,OAAO,CAAC,GAAG,CAAC,+BAAS,CAAC,UAAK,CAAC,2BAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,QAAQ,CAAC,CAAC;oBACzE,aAAa,EAAE,CAAC;oBAEhB,eAAe;oBACf,IAAM,QAAQ,GAAG,OAAK,SAAS,CAAC,CAAC,CAAC,IAAI,OAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC3D,IAAI,CAAC,QAAQ,EAAE;wBACX,OAAO,CAAC,IAAI,CAAC,kDAAa,CAAC,UAAK,CAAC,MAAG,CAAC,CAAC;;qBAEzC;oBAED,OAAO,CAAC,GAAG,CAAC,mBAAO,CAAC,UAAK,CAAC,2CAAkB,QAAQ,CAAC,MAAM,kBAAa,QAAQ,CAAC,OAAS,CAAC,CAAC;oBAE5F,gBAAgB;oBAChB,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC;oBACxB,QAAQ,CAAC,OAAO,GAAG,CAAC,CAAC;oBACrB,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;oBACpB,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;oBAEpB,OAAO,CAAC,GAAG,CAAC,mBAAO,CAAC,UAAK,CAAC,iDAAmB,QAAQ,CAAC,MAAM,kBAAa,QAAQ,CAAC,OAAS,CAAC,CAAC;oBAE7F,qBAAqB;oBACrB,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,QAAQ,GAAG,CAAC,IAAI,QAAQ,IAAI,CAAC,EAAE;wBAC/D,aAAa;wBACb,OAAO,CAAC,GAAG,CAAC,iDAAY,CAAC,UAAK,CAAC,qBAAQ,EAAE,QAAQ,CAAC,CAAC;wBACnD,OAAK,YAAY,CAAC;4BACd,KAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC;wBAC5C,CAAC,EAAE,GAAG,CAAC,CAAC;qBACX;yBAAM,IAAI,QAAQ,KAAK,CAAC,CAAC,IAAI,QAAQ,KAAK,MAAM,IAAI,QAAQ,KAAK,MAAM,EAAE;wBACtE,KAAK;wBACL,OAAO,CAAC,GAAG,CAAC,iDAAY,CAAC,UAAK,CAAC,MAAG,CAAC,CAAC;wBACpC,OAAK,YAAY,CAAC;4BACd,KAAI,CAAC,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;wBAChC,CAAC,EAAE,GAAG,CAAC,CAAC;qBACX;yBAAM,IAAI,QAAQ,KAAK,MAAM,IAAI,QAAQ,KAAK,QAAQ,EAAE;wBACrD,KAAK;wBACL,OAAO,CAAC,GAAG,CAAC,iDAAY,CAAC,UAAK,CAAC,MAAG,CAAC,CAAC;wBACpC,OAAK,kBAAkB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;qBACjC;yBAAM;wBACH,OAAO,CAAC,GAAG,CAAC,iDAAY,CAAC,UAAK,CAAC,OAAI,EAAE,QAAQ,CAAC,CAAC;qBAClD;oBAED,WAAW;oBACX,IAAI,OAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,OAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;wBACzC,OAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC;qBACxC;iBACJ;;YAtDL,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE;wBAAnC,CAAC;aAuDT;;;QAxDL,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE;oBAAhC,CAAC;SAyDT;QAED,OAAO,CAAC,GAAG,CAAC,oCAAS,aAAa,0CAAS,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACK,mDAAoB,GAA5B;QACI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO;SACV;QAED,IAAM,gBAAgB,GAAc,EAAE,CAAC;QACvC,IAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC;QAGrD,aAAa;QACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrD,IAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACzC,IAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC;YAI5B,yBAAyB;YACzB,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE;gBACrC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aAEhC;iBAAM;aAEN;SACJ;QAED,YAAY;QACZ,gBAAgB,CAAC,OAAO,CAAC,UAAC,KAAK,EAAE,KAAK;YAElC,KAAK,CAAC,gBAAgB,EAAE,CAAC;QAC7B,CAAC,CAAC,CAAC;QAIH,mBAAmB;QACnB,iCAAiC;IACrC,CAAC;IAED;;OAEG;IACK,qDAAsB,GAA9B;QACI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO;SACV;QAED,IAAM,gBAAgB,GAAc,EAAE,CAAC;QAIvC,mBAAmB;QACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrD,IAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACzC,IAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC;YAE5B,QAAQ;YACR,qBAAqB;YACrB,uBAAuB;YACvB,YAAY;YACZ,IAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC;gBAC9B,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC;gBAC1B,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC;gBAC1B,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACvB,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACvB,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBAC3B,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;YAEjD,IAAI,CAAC,UAAU,EAAE;gBACb,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aAEpC;YAED,UAAU;YACV,gBAAgB,CAAC,OAAO,CAAC,UAAA,KAAK;gBAC1B,KAAK,CAAC,gBAAgB,EAAE,CAAC;YAC7B,CAAC,CAAC,CAAC;SAGN;IACD,CAAC;IACD;;OAEG;IACK,4CAAa,GAArB,UAAsB,IAAa,EAAE,QAAgB;QAGjD,iBAAiB;QACjB,IAAI,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,QAAQ,KAAK,OAAO,EAAE;YAEtD,OAAO,KAAK,CAAC;SAChB;QAED,YAAY;QACZ,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;YAE1D,OAAO,KAAK,CAAC;SAChB;QAED,aAAa;QACb,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YAEpD,OAAO,KAAK,CAAC;SAChB;QAED,kBAAkB;QAElB,QAAQ;QACR,IAAI,QAAQ,KAAK,MAAM,EAAE;YAErB,OAAO,IAAI,CAAC;SACf;QAED,+BAA+B;QAC/B,IAAI,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE;YAE7B,OAAO,IAAI,CAAC;SACf;QAED,6CAA6C;QAC7C,IAAI,QAAQ,CAAC,KAAK,CAAC,qBAAqB,CAAC,EAAE;YAEvC,OAAO,IAAI,CAAC;SACf;QAED,oBAAoB;QACpB,IAAI,QAAQ,CAAC,KAAK,CAAC,gBAAgB,CAAC,EAAE;YAElC,OAAO,IAAI,CAAC;SACf;QAED,gBAAgB;QAChB,IAAI,IAAI,CAAC,YAAY,CAAC,sBAAsB,CAAC,EAAE;YAE3C,OAAO,IAAI,CAAC;SACf;QAED,QAAQ;QACR,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC;YACnF,QAAQ,KAAK,QAAQ,IAAI,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;YAEtD,OAAO,IAAI,CAAC;SACf;QAED,UAAU;QACV,IAAI,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;YAE5D,OAAO,IAAI,CAAC;SACf;QAED,kBAAkB;QAElB,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,2CAAY,GAApB;QACI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO;SACV;QAED,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI,mBAAmB,GAAG,CAAC,CAAC;QAG5B,sBAAsB;QACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrD,IAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAEzC,WAAW;YACX,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE;gBAC1D,UAAU,EAAE,CAAC;gBAEb,WAAW;gBACX,IAAM,SAAS,GAAG,CAAC,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,GAAG,GAAG,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;gBAE/F,IAAI,SAAS,EAAE;oBAEX,aAAa,EAAE,CAAC;iBACnB;qBAAM;oBACH,mBAAmB,EAAE,CAAC;iBACzB;gBAED,gBAAgB;gBAChB,KAAK,CAAC,cAAc,EAAE,CAAC;gBAEvB,SAAS;gBACT,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;gBACpB,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC;gBACpB,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;gBACjB,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;gBACjB,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,SAAS;gBAE1B,kBAAkB;gBAClB,IAAI,KAAK,CAAC,kBAAkB,CAAC,EAAE;oBAC3B,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,CAAC;iBAChD;gBAED,WAAW;gBACX,IAAM,MAAM,GAAG,KAAK,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;gBAC7C,IAAI,MAAM,EAAE;oBACR,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;iBACzB;aACJ;SACJ;IAEL,CAAC;IAED;;OAEG;IACI,yCAAU,GAAjB,UAAkB,CAAS,EAAE,CAAS;QAClC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAC/B,OAAO,CAAC,IAAI,CAAC,4DAAa,CAAC,UAAK,CAAC,kBAAK,CAAC,CAAC;YACxC,OAAO;SACV;QAED,SAAS;QACT,IAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3D,IAAI,QAAQ,EAAE;YACV,WAAW;YACX,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC;iBACb,EAAE,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;iBACnE,IAAI,CAAC;gBACF,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC;YAC5B,CAAC,CAAC;iBACD,KAAK,EAAE,CAAC;SAChB;IACL,CAAC;IAED;;OAEG;IACK,oDAAqB,GAA7B;QACI,mBAAmB;QACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE;YACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE;gBACtC,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;oBACrB,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC;oBACtC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,IAAI,CAAC;iBACzC;aACJ;SACJ;QAED,WAAW;QACX,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAGlC,CAAC;IAED;;;OAGG;IACI,kDAAmB,GAA1B;QAGI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC7B,OAAO;SACV;QAED,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,kCAAkC;QAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE;YACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE;gBACtC,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE;oBACjE,SAAS;oBACT,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC;oBAClD,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,IAAI,CAAC;oBACtC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC;oBACtC,YAAY,EAAE,CAAC;iBAElB;aACJ;SACJ;QAED,kCAAkC;QAClC,IAAM,gBAAgB,GAAc,EAAE,CAAC;QAEvC,aAAa;QACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrD,IAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAEzC,qBAAqB;YACrB,IAAM,gBAAgB,GAAG,KAAK,CAAC,YAAY,CAAC,8BAAoB,CAAC,CAAC;YAClE,IAAI,gBAAgB,EAAE;gBAClB,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC7B,YAAY,EAAE,CAAC;aAElB;SACJ;QAED,aAAa;QACb,gBAAgB,CAAC,OAAO,CAAC,UAAA,KAAK;YAC1B,KAAK,CAAC,gBAAgB,EAAE,CAAC;QAE7B,CAAC,CAAC,CAAC;IAGP,CAAC;IAED;;;;;OAKG;IACI,4DAA6B,GAApC,UAAqC,CAAS,EAAE,CAAS,EAAE,OAAc;QACrE,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YACnE,OAAO,CAAC,IAAI,CAAC,gCAAU,CAAC,UAAK,CAAC,qBAAe,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,KAAI,CAAC,CAAE,CAAC,CAAC;YACrE,OAAO;SACV;QAGD,kBAAkB;QAClB,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE;YAE/B,yBAAyB;YACzB,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;gBACpB,6BAA6B;gBAC7B,IAAI,CAAC,6BAA6B,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;aACrD;iBAAM;aAEN;SACJ;aAAM;YAEH,uBAAuB;YACvB,IAAI,CAAC,0BAA0B,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;SAClD;IACL,CAAC;IAED;;;;;OAKG;IACK,4DAA6B,GAArC,UAAsC,CAAS,EAAE,CAAS,EAAE,OAAc;QACtE,wBAAwB;QACxB,IAAM,YAAY,GAAG,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;QACxC,IAAM,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;QAGxD,mBAAmB;QACnB,iCAAiC;QACjC,yBAAyB;QACzB,IAAM,UAAU,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY;QAC7C,IAAI,CAAC,sBAAsB,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;QAEvD,qBAAqB;QACrB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrC,IAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC1B,IAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,iBAAiB;YAGpD,kBAAkB;YAClB,IAAI,CAAC,gCAAgC,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;SAC/E;IACL,CAAC;IAED;;;;;;OAMG;IACK,qDAAsB,GAA9B,UAA+B,CAAS,EAAE,CAAS,EAAE,QAA+C,EAAE,OAAc;QAEhH,YAAY;QACZ,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE;YACnE,OAAO,CAAC,IAAI,CAAC,wBAAO,CAAC,UAAK,CAAC,kEAAa,CAAC,CAAC;YAC1C,OAAO;SACV;QAED,IAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;QAEpD,uBAAuB;QACvB,IAAM,YAAY,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAExD,cAAc;QACd,IAAM,YAAY,GAAG,IAAI,CAAC,kCAAkC,CAAC,CAAC,EAAE,CAAC,EAAE,YAAY,CAAC,CAAC;QAEjF,WAAW;QACX,IAAM,WAAW,GAAG,EAAE,CAAC,EAAE,CACrB,YAAY,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,EAC3B,YAAY,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAC9B,CAAC;QAEF,sBAAsB;QACtB,IAAI,CAAC,yBAAyB,CAAC,YAAY,EAAE,WAAW,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;IAG9E,CAAC;IAED;;;;;OAKG;IACK,yDAA0B,GAAlC,UAAmC,CAAS,EAAE,CAAS,EAAE,OAAc;QACnE,IAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,aAAa;QAClD,IAAM,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;QAExD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrC,IAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC1B,IAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YAI9B,kBAAkB;YAClB,IAAI,CAAC,gCAAgC,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;SAC/E;IACL,CAAC;IAED;;;;;;;OAOG;IACK,+DAAgC,GAAxC,UAAyC,KAAa,EAAE,KAAa,EAAE,MAAW,EAAE,gBAAuD,EAAE,YAAoB;QAAjK,iBA+CC;QA9CG,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,OAAO,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;YACzC,OAAO;SACV;QAED,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YACzB,OAAO;SACV;QAED,YAAY;QACZ,IAAM,UAAU,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAGzD,oBAAoB;QACpB,IAAM,YAAY,GAAG,IAAI,CAAC,kCAAkC,CAAC,KAAK,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC;QAEzF,SAAS;QACT,IAAM,aAAa,GAAG,EAAE,CAAC,EAAE,CACvB,YAAY,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,EACnC,YAAY,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CACtC,CAAC;QAEF,UAAU,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;QACtC,UAAU,CAAC,QAAQ,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAI5C,oBAAoB;QACpB,UAAU,CAAC,MAAM,GAAG,KAAK,CAAC;QAE1B,0BAA0B;QAC1B,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;QAErC,eAAe;QACf,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE,MAAM,EAAE;YAC1C,YAAY;YACZ,IAAI,YAAY,KAAK,CAAC,EAAE;gBACpB,cAAc;gBACd,KAAI,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC;aAC7C;iBAAM;gBACH,4BAA4B;gBAC5B,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC;aAC5B;QAEL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;OAKG;IACK,mDAAoB,GAA5B,UAA6B,UAAmB,EAAE,MAAW,EAAE,UAAsB;QACjF,IAAI;YAGA,IAAM,kBAAgB,GAAG,UAAU,CAAC,YAAY,CAAC,8BAAoB,CAAC,CAAC;YACvE,IAAI,CAAC,kBAAgB,EAAE;gBACnB,OAAO,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;gBAC7C,OAAO;aACV;YAID,wBAAwB;YACxB,IAAM,cAAY,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACzD,IAAI,CAAC,cAAY,EAAE;gBACf,OAAO,CAAC,IAAI,CAAC,oCAAS,MAAM,CAAC,MAAM,oCAAQ,CAAC,CAAC;gBAC7C,OAAO;aACV;YAID,4BAA4B;YAC5B,UAAU,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;YAGrC,qBAAqB;YACrB,IAAI,CAAC,YAAY,CAAC;gBACd,IAAI,OAAO,kBAAgB,CAAC,OAAO,KAAK,UAAU,EAAE;oBAChD,kBAAgB,CAAC,OAAO,CAAC,cAAY,CAAC,CAAC;iBAC1C;gBAED,eAAe;gBACf,IAAM,QAAQ,GAAG,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,sBAAsB;gBAC9D,IAAI,kBAAgB,CAAC,QAAQ,EAAE;oBAC3B,kBAAgB,CAAC,QAAQ,CAAC,MAAM,GAAG,QAAQ,CAAC;iBAE/C;gBAED,SAAS;gBACT,IAAI,UAAU,EAAE;oBACZ,UAAU,EAAE,CAAC;iBAChB;YACL,CAAC,EAAE,GAAG,CAAC,CAAC;SAEX;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,IAAI,CAAC,yEAAgB,KAAO,CAAC,CAAC;SACzC;IACL,CAAC;IAED;;;;OAIG;IACK,iDAAkB,GAA1B,UAA2B,WAAmB;QAC1C,QAAQ,WAAW,EAAE;YACjB,KAAK,CAAC;gBACF,iBAAiB;gBACjB,OAAO,CAAC,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,EAAC,CAAC,CAAC;YAEtC,KAAK,CAAC;gBACF,kBAAkB;gBAClB,OAAO;oBACH,EAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,EAAC;oBAC3B,EAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,EAAC,CAAG,IAAI;iBACpC,CAAC;YAEN,KAAK,CAAC;gBACF,mBAAmB;gBACnB,OAAO;oBACH,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG,EAAC;oBACzB,EAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,GAAG,EAAC;oBAC5B,EAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,GAAG,EAAC,CAAG,KAAK;iBACtC,CAAC;YAEN,KAAK,CAAC;gBACF,kBAAkB;gBAClB,OAAO;oBACH,EAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG,EAAC;oBAC3B,EAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG,EAAC;oBAC1B,EAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,GAAG,EAAC;oBAC5B,EAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,GAAG,EAAC,CAAG,KAAK;iBACtC,CAAC;YAEN;gBACI,gBAAgB;gBAChB,OAAO,CAAC,IAAI,CAAC,2CAAW,WAAW,0CAAS,CAAC,CAAC;gBAC9C,OAAO,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;SACzC;IACL,CAAC;IAED;;;;;OAKG;IACK,0CAAW,GAAnB,UAAoB,CAAS,EAAE,CAAS;QACpC,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAClD,OAAO,IAAI,CAAC;SACf;QAED,iCAAiC;QACjC,IAAM,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;QAEtC,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE;YACtD,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;SACzC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACK,yDAA0B,GAAlC,UAAmC,QAAiB,EAAE,MAAW,EAAE,QAA+C;QAC9G,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,OAAO,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;YACzC,OAAO;SACV;QAED,YAAY;QACZ,IAAM,UAAU,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAGzD,YAAY;QACZ,IAAM,UAAU,GAAG,UAAU,CAAC,aAAa,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC;QAE1D,UAAU,CAAC,OAAO,CAAC,UAAC,IAAI,EAAE,KAAK;QAE/B,CAAC,CAAC,CAAC;QAEH,UAAU;QACV,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC/C,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAGpC,UAAU;QACV,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAE9B,SAAS;QACT,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;IAGjD,CAAC;IAED;;;;OAIG;IACK,kDAAmB,GAA3B,UAA4B,UAAmB,EAAE,MAAW;QACxD,IAAI;YAGA,IAAM,gBAAgB,GAAG,UAAU,CAAC,YAAY,CAAC,8BAAoB,CAAC,CAAC;YACvE,IAAI,CAAC,gBAAgB,EAAE;gBACnB,OAAO,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;gBAE7C,IAAM,aAAa,GAAG,UAAU,CAAC,aAAa,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC;gBAC7D,aAAa,CAAC,OAAO,CAAC,UAAC,IAAI,EAAE,KAAK;gBAElC,CAAC,CAAC,CAAC;gBACH,OAAO;aACV;YAGD,wBAAwB;YACxB,IAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACzD,IAAI,CAAC,YAAY,EAAE;gBACf,OAAO,CAAC,IAAI,CAAC,oCAAS,MAAM,CAAC,MAAM,oCAAQ,CAAC,CAAC;gBAC7C,OAAO;aACV;YAID,IAAI,OAAO,gBAAgB,CAAC,OAAO,KAAK,UAAU,EAAE;gBAChD,gBAAgB,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;aAC1C;YAED,eAAe;YACf,IAAM,QAAQ,GAAG,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,sBAAsB;YAC9D,IAAI,gBAAgB,CAAC,QAAQ,EAAE;gBAC3B,gBAAgB,CAAC,QAAQ,CAAC,MAAM,GAAG,QAAQ,CAAC;aAE/C;iBAAM;gBACH,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;aACjC;SAEJ;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,IAAI,CAAC,yEAAgB,KAAO,CAAC,CAAC;SACzC;IACL,CAAC;IAED;;;;OAIG;IACK,8CAAe,GAAvB,UAAwB,MAAc;QAClC,IAAI;YACA,IAAI,CAAC,uBAAU,CAAC,WAAW,EAAE,CAAC,eAAe,IAAI,CAAC,uBAAU,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC,KAAK,EAAE;gBAC9F,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;gBAChC,OAAO,IAAI,CAAC;aACf;YAED,IAAM,KAAK,GAAG,uBAAU,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC,KAAK,CAAC;YAC7D,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,UAAC,CAAW,IAAK,OAAA,CAAC,CAAC,MAAM,KAAK,MAAM,EAAnB,CAAmB,CAAC,CAAC;YAE9D,IAAI,IAAI,EAAE;gBAEN,OAAO,IAAI,CAAC;aACf;iBAAM;gBACH,OAAO,CAAC,IAAI,CAAC,oCAAS,MAAM,wBAAM,CAAC,CAAC;gBACpC,OAAO,IAAI,CAAC;aACf;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,6DAAc,KAAO,CAAC,CAAC;YACrC,OAAO,IAAI,CAAC;SACf;IACL,CAAC;IAED;;;;;;OAMG;IACI,oDAAqB,GAA5B,UAA6B,CAAS,EAAE,CAAS,EAAE,KAAa,EAAE,WAAoB;QAAtF,iBAiCC;QA/BG,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAC/B,OAAO,CAAC,IAAI,CAAC,gCAAU,CAAC,UAAK,CAAC,MAAG,CAAC,CAAC;YACnC,OAAO;SACV;QAED,aAAa;QACb,IAAM,UAAU,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACvD,IAAI,CAAC,UAAU,EAAE;YACb,OAAO,CAAC,IAAI,CAAC,wBAAO,CAAC,UAAK,CAAC,gDAAU,CAAC,CAAC;YACvC,OAAO;SACV;QAED,2BAA2B;QAC3B,IAAM,gBAAgB,GAAG,UAAU,CAAC,YAAY,CAAC,8BAAoB,CAAC,CAAC;QACvE,IAAI,CAAC,gBAAgB,EAAE;YACnB,OAAO,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YAC1C,OAAO;SACV;QAED,SAAS;QACT,IAAI,WAAW,EAAE;YACb,iBAAiB;YACjB,IAAI,CAAC,wBAAwB,CAAC,gBAAgB,EAAE,CAAC,EAAE;gBAC/C,KAAI,CAAC,YAAY,CAAC;oBACd,KAAI,CAAC,wBAAwB,CAAC,gBAAgB,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;gBACjE,CAAC,EAAE,GAAG,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC;SACN;aAAM;YACH,WAAW;YACX,IAAI,CAAC,wBAAwB,CAAC,gBAAgB,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;SAChE;IACL,CAAC;IAED;;;;;OAKG;IACK,uDAAwB,GAAhC,UAAiC,CAAS,EAAE,CAAS;QACjD,2BAA2B;QAC3B,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE;YACjE,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;SACzC;QAED,qBAAqB;QACrB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO,IAAI,CAAC;SACf;QAED,aAAa;QACb,IAAM,cAAc,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAE3D,0BAA0B;QAC1B,IAAI,WAAW,GAAmB,IAAI,CAAC;QACvC,IAAI,WAAW,GAAG,MAAM,CAAC,SAAS,CAAC;QAEnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrD,IAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACzC,IAAM,gBAAgB,GAAG,KAAK,CAAC,YAAY,CAAC,8BAAoB,CAAC,CAAC;YAElE,IAAI,gBAAgB,EAAE;gBAClB,IAAM,QAAQ,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,cAAc,CAAC,CAAC;gBACvE,IAAI,QAAQ,GAAG,WAAW,IAAI,QAAQ,GAAG,EAAE,EAAE,EAAE,UAAU;oBACrD,WAAW,GAAG,QAAQ,CAAC;oBACvB,WAAW,GAAG,KAAK,CAAC;iBACvB;aACJ;SACJ;QAED,OAAO,WAAW,CAAC;IACvB,CAAC;IAED;;;;;OAKG;IACK,uDAAwB,GAAhC,UAAiC,gBAAqB,EAAE,KAAa,EAAE,UAA+B;QAGlG,qCAAqC;QACrC,uCAAuC;QAEvC,IAAI,UAAU,EAAE;YACZ,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;SACtC;IACL,CAAC;IAED;;;;;OAKG;IACI,oDAAqB,GAA5B,UAA6B,CAAS,EAAE,CAAS,EAAE,UAAsB;QAAzE,iBAwEC;QArEG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC/B,UAAU,EAAE,CAAC;YACb,OAAO;SACV;QAED,qCAAqC;QACrC,IAAM,WAAW,GAAc,EAAE,CAAC;QAElC,kCAAkC;QAClC,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,UAAU,EAAE,EAAE,EAAE,EAAE;YACzC,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,UAAU,EAAE,EAAE,EAAE,EAAE;gBACzC,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE;oBACrE,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC;iBAEtD;aACJ;SACJ;QAED,kCAAkC;QAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrD,IAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAEzC,2CAA2C;YAC3C,IAAM,gBAAgB,GAAG,KAAK,CAAC,YAAY,CAAC,8BAAoB,CAAC,CAAC;YAClE,IAAI,gBAAgB,EAAE;gBAClB,uBAAuB;gBACvB,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;oBAC9B,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;iBAE3B;aACJ;SACJ;QAID,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;YAC1B,WAAW;YAEX,UAAU,EAAE,CAAC;YACb,OAAO;SACV;QAED,IAAI,cAAc,GAAG,CAAC,CAAC;QACvB,IAAM,UAAU,GAAG,WAAW,CAAC,MAAM,CAAC;QAEtC,cAAc;QACd,WAAW,CAAC,OAAO,CAAC,UAAC,UAAU,EAAE,KAAK;YAElC,mBAAmB;YACnB,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC;iBACf,EAAE,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;iBACvE,IAAI,CAAC;gBACF,YAAY;gBACZ,UAAU,CAAC,gBAAgB,EAAE,CAAC;gBAC9B,cAAc,EAAE,CAAC;gBAGjB,kBAAkB;gBAClB,IAAI,cAAc,IAAI,UAAU,EAAE;oBAE9B,qCAAqC;oBACrC,KAAI,CAAC,0BAA0B,EAAE,CAAC;oBAElC,UAAU,EAAE,CAAC;iBAChB;YACL,CAAC,CAAC;iBACD,KAAK,EAAE,CAAC;QACjB,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACK,yDAA0B,GAAlC;QAEI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE;YACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE;gBACtC,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE;oBAC/B,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC;oBACtC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,IAAI,CAAC;iBAEzC;aACJ;SACJ;IACL,CAAC;IAED;;;;;OAKG;IACI,2CAAY,GAAnB,UAAoB,CAAS,EAAE,CAAS,EAAE,SAA0B;QAA1B,0BAAA,EAAA,iBAA0B;QAChE,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAC/B,OAAO,CAAC,IAAI,CAAC,4DAAa,CAAC,UAAK,CAAC,kBAAK,CAAC,CAAC;YACxC,OAAO;SACV;QAED,SAAS;QACT,IAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3D,IAAI,QAAQ,EAAE;YACV,IAAI,SAAS,EAAE;gBACX,aAAa;gBACb,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC;aAC3B;iBAAM;gBACH,cAAc;gBACd,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;aACxC;SACJ;IACL,CAAC;IAED;;;;OAIG;IACK,oDAAqB,GAA7B,UAA8B,QAAiB;QAC3C,IAAI,CAAC,QAAQ;YAAE,OAAO;QAEtB,0BAA0B;QAC1B,QAAQ,CAAC,cAAc,EAAE,CAAC;QAE1B,qBAAqB;QACrB,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE;YAC/B,QAAQ,CAAC,kBAAkB,CAAC,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;SACzD;QAED,mCAAmC;QACnC,IAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;QACrD,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,KAAK,GAAG,GAAG,CAAC,CAAC,gBAAgB;QAEjC,QAAQ,cAAc,EAAE;YACpB,KAAK,CAAC,EAAE,KAAK;gBACT,KAAK,GAAG,CAAC,CAAC;gBACV,MAAM;YACV,KAAK,CAAC,EAAE,QAAQ;gBACZ,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,KAAK,CAAC;gBAC7C,MAAM;YACV,KAAK,CAAC,EAAE,QAAQ;gBACZ,KAAK,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,KAAK,CAAC;gBAC9C,MAAM;SACb;QAED,SAAS;QACT,IAAM,aAAa,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,mBAAmB;QAExG,OAAO;QACP,IAAM,MAAM,GAAG,IAAI,CAAC,CAAC,SAAS;QAC9B,IAAM,QAAQ,GAAG,GAAG,CAAC,CAAC,OAAO;QAC7B,IAAM,eAAe,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;QAE/C,YAAY;QACZ,IAAM,aAAa,GAAG,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC;aACnC,aAAa,CACV,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,aAAa,GAAG,GAAG,EAAE,CAAC,CACrD,CAAC;QAEN,aAAa;QACb,IAAM,aAAa,GAAG,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC;YACpC,YAAY;aACX,EAAE,CAAC,MAAM,EAAE;YACR,CAAC,EAAE,eAAe,CAAC,CAAC,GAAG,KAAK;YAC5B,CAAC,EAAE,eAAe,CAAC,CAAC,GAAG,KAAK;SAC/B,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;YACzB,YAAY;aACX,EAAE,CAAC,QAAQ,EAAE;YACV,CAAC,EAAE,eAAe,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG;YAC1D,CAAC,EAAE,eAAe,CAAC,CAAC,GAAG,GAAG,CAAC,aAAa;SAC3C,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;aACvB,IAAI,CAAC;YACF,YAAY;YACZ,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC;YACxB,SAAS;YACT,QAAQ,CAAC,cAAc,EAAE,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEP,cAAc;QACd,aAAa,CAAC,KAAK,EAAE,CAAC;QACtB,aAAa,CAAC,KAAK,EAAE,CAAC;IAC1B,CAAC;IAED;;;;;OAKG;IACI,+CAAgB,GAAvB,UAAwB,CAAS,EAAE,CAAS,EAAE,aAA6B;QAA3E,iBAkCC;QAlC6C,8BAAA,EAAA,oBAA6B;QAGvE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,OAAO,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;YAC5C,OAAO;SACV;QAED,eAAe;QACf,IAAM,QAAQ,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACjD,QAAQ,CAAC,IAAI,GAAG,MAAM,CAAC;QAEvB,mBAAmB;QACnB,IAAM,QAAQ,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACpD,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAE/B,QAAQ;QACR,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QAEnC,SAAS;QACT,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACrB,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC;aACb,EAAE,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;aAC5D,EAAE,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;aACrC,KAAK,EAAE,CAAC;QAEb,sBAAsB;QACtB,0BAA0B;QAC1B,IAAI,aAAa,EAAE;YACf,IAAI,CAAC,YAAY,CAAC;gBACd,KAAI,CAAC,uBAAuB,EAAE,CAAC;YACnC,CAAC,EAAE,IAAI,CAAC,CAAC;SACZ;IAEL,CAAC;IAED;;;;OAIG;IACI,iDAAkB,GAAzB,UAA0B,CAAS,EAAE,CAAS;QAE1C,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACpB,OAAO,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAC;YAC9C,OAAO;SACV;QAED,iBAAiB;QACjB,IAAM,UAAU,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACrD,UAAU,CAAC,IAAI,GAAG,QAAQ,CAAC;QAE3B,mBAAmB;QACnB,IAAM,QAAQ,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACpD,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEjC,QAAQ;QACR,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;QAErC,SAAS;QACT,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACvB,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC;aACf,EAAE,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;aAC5D,KAAK,EAAE,CAAC;IAGjB,CAAC;IAED;;;;;OAKG;IACI,yDAA0B,GAAjC,UAAkC,CAAS,EAAE,CAAS,EAAE,aAAqB;QAGzE,WAAW;QACX,IAAI,aAAa,KAAK,CAAC,EAAE;YAErB,OAAO;SACV;QAED,gBAAgB;QAChB,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,CAAC,EAAE,aAAa,CAAC,CAAC;IACjD,CAAC;IAED;;;;;OAKG;IACK,iDAAkB,GAA1B,UAA2B,CAAS,EAAE,CAAS,EAAE,MAAc;QAG3D,eAAe;QACf,IAAI,MAAM,GAAc,IAAI,CAAC;QAC7B,QAAQ,MAAM,EAAE;YACZ,KAAK,CAAC;gBAAE,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;gBAAC,MAAM;YACzC,KAAK,CAAC;gBAAE,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;gBAAC,MAAM;YACzC,KAAK,CAAC;gBAAE,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;gBAAC,MAAM;YACzC,KAAK,CAAC;gBAAE,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;gBAAC,MAAM;YACzC,KAAK,CAAC;gBAAE,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;gBAAC,MAAM;YACzC,KAAK,CAAC;gBAAE,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;gBAAC,MAAM;YACzC,KAAK,CAAC;gBAAE,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;gBAAC,MAAM;YACzC,KAAK,CAAC;gBAAE,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;gBAAC,MAAM;YACzC;gBACI,OAAO,CAAC,KAAK,CAAC,2CAAW,MAAQ,CAAC,CAAC;gBACnC,OAAO;SACd;QAED,IAAI,CAAC,MAAM,EAAE;YACT,OAAO,CAAC,KAAK,CAAC,SAAO,MAAM,sGAAwB,CAAC,CAAC;YACrD,OAAO;SACV;QAED,WAAW;QACX,IAAM,UAAU,GAAG,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAC1C,UAAU,CAAC,IAAI,GAAG,SAAO,MAAQ,CAAC;QAElC,mBAAmB;QACnB,IAAM,QAAQ,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACpD,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEjC,QAAQ;QACR,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;QAErC,SAAS;QACT,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACvB,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC;aACf,EAAE,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;aAC5D,KAAK,EAAE,CAAC;IAGjB,CAAC;IAED;;;;;OAKG;IACK,+CAAgB,GAAxB,UAAyB,CAAS,EAAE,CAAS,EAAE,MAAc;QACzD,IAAM,UAAU,GAAM,MAAM,SAAM,CAAC;QAGnC,IAAI,CAAC,yBAAyB,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;IACjD,CAAC;IAED;;;;;OAKG;IACK,wDAAyB,GAAjC,UAAkC,CAAS,EAAE,CAAS,EAAE,MAAc;QAElE,WAAW;QACX,IAAM,UAAU,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,mBAAiB,MAAQ,CAAC,CAAC;QAC1D,IAAM,KAAK,GAAG,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;QAEhD,sBAAsB;QACtB,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;QACjC,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC,CAAC,OAAO;QAC5B,KAAK,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAC/C,KAAK,CAAC,eAAe,GAAG,EAAE,CAAC,KAAK,CAAC,eAAe,CAAC,MAAM,CAAC;QACxD,KAAK,CAAC,aAAa,GAAG,EAAE,CAAC,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC;QAEpD,cAAc;QACd,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAE1D,kBAAkB;QAClB,IAAM,QAAQ,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACrD,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEjC,QAAQ;QACR,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;QAErC,SAAS;QACT,IAAI,CAAC,yBAAyB,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;IAGvD,CAAC;IAED;;;;;;OAMG;IACK,8CAAe,GAAvB,UAAwB,UAAmB,EAAE,CAAS,EAAE,CAAS,EAAE,MAAc;QAG7E,kBAAkB;QAClB,IAAM,QAAQ,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACrD,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEjC,QAAQ;QACR,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;QAErC,SAAS;QACT,IAAI,CAAC,yBAAyB,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;IAGvD,CAAC;IAED;;;;OAIG;IACK,wDAAyB,GAAjC,UAAkC,UAAmB,EAAE,MAAc;QACjE,qBAAqB;QACrB,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACvB,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC;aACf,EAAE,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;aAC5D,KAAK,EAAE,CAAC;IAGjB,CAAC;IAED;;;;;OAKG;IACI,yDAA0B,GAAjC,UAAkC,CAAS,EAAE,CAAS,EAAE,aAAqB;QAA7E,iBAUC;QAPG,QAAQ;QACR,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAExB,yBAAyB;QACzB,IAAI,CAAC,YAAY,CAAC;YACd,KAAI,CAAC,0BAA0B,CAAC,CAAC,EAAE,CAAC,EAAE,aAAa,CAAC,CAAC;QACzD,CAAC,EAAE,GAAG,CAAC,CAAC;IACZ,CAAC;IAED;;;;OAIG;IACK,6CAAc,GAAtB,UAAuB,MAAc;QACjC,QAAQ,MAAM,EAAE;YACZ,KAAK,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC;YAC7B,KAAK,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;YAC9B,KAAK,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC;YAC5B,KAAK,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC;YAChC,KAAK,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC;YAC/B,KAAK,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC;YAC7B,KAAK,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;YAC9B,KAAK,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC;YAC7B,OAAO,CAAC,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;SAClC;IACL,CAAC;IAED;;OAEG;IACK,sDAAuB,GAA/B;QACI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YACvC,OAAO;SACV;QAED,gBAAgB;QAChB,IAAM,cAAc,GAAG,EAAE,CAAC,CAAC,OAAO;QAClC,IAAM,aAAa,GAAG,GAAG,CAAC,CAAC,SAAS;QACpC,IAAM,cAAc,GAAG,EAAE,CAAC,CAAC,OAAO;QAElC,OAAO;QACP,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;QAEnE,UAAU;QACV,IAAI,CAAC,aAAa,CAAC,cAAc,GAAG,GAAG,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;IAC5E,CAAC;IAED;;OAEG;IACK,6CAAc,GAAtB,UAAuB,SAAiB,EAAE,QAAgB,EAAE,SAAiB;QACzE,SAAS;QACT,IAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QAEzD,gBAAgB;QAChB,IAAI,gBAAgB,GAAG,SAAS,CAAC;QACjC,IAAM,cAAc,GAAG,IAAI,CAAC,CAAC,SAAS;QAEtC,IAAM,eAAe,GAAG,UAAC,cAAsB;YAC3C,OAAO,EAAE,CAAC,KAAK,EAAE;iBACZ,EAAE,CAAC,KAAK,EAAE;gBACP,CAAC,EAAE,gBAAgB,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC;gBAClE,CAAC,EAAE,gBAAgB,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC;aACrE,CAAC,CAAC;QACX,CAAC,CAAC;QAEF,gBAAgB;QAChB,IAAI,UAAU,GAAG,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC1C,IAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,SAAS,CAAC,CAAC;QAEpD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;YACjC,UAAU,GAAG,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC,CAAC;YAChE,gBAAgB,IAAI,cAAc,CAAC,CAAC,WAAW;SAClD;QAED,WAAW;QACX,UAAU,CAAC,EAAE,CAAC,GAAG,EAAE;YACf,CAAC,EAAE,gBAAgB,CAAC,CAAC;YACrB,CAAC,EAAE,gBAAgB,CAAC,CAAC;SACxB,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;aACxB,KAAK,EAAE,CAAC;IACb,CAAC;IAED;;OAEG;IACK,4CAAa,GAArB,UAAsB,SAAiB,EAAE,QAAgB,EAAE,SAAiB;QACxE,IAAI,CAAC,IAAI,CAAC,SAAS;YAAE,OAAO;QAE5B,WAAW;QACX,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC5C,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;gBAAE,SAAS;YAEjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC/C,IAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtC,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,MAAM;oBAAE,SAAS;gBAE5C,iBAAiB;gBACjB,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;aAChE;SACJ;IACL,CAAC;IAED;;OAEG;IACK,4CAAa,GAArB,UAAsB,QAAiB,EAAE,SAAiB,EAAE,QAAgB,EAAE,SAAiB;QAC3F,SAAS;QACT,IAAM,gBAAgB,GAAG,QAAQ,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QAEnD,qBAAqB;QACrB,IAAM,WAAW,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC;QAExC,IAAI,CAAC,YAAY,CAAC;YACd,gBAAgB;YAChB,IAAI,gBAAgB,GAAG,SAAS,CAAC;YACjC,IAAM,cAAc,GAAG,IAAI,CAAC,CAAC,aAAa;YAE1C,IAAM,mBAAmB,GAAG,UAAC,cAAsB;gBAC/C,OAAO,EAAE,CAAC,KAAK,EAAE;qBACZ,EAAE,CAAC,IAAI,EAAE;oBACN,CAAC,EAAE,gBAAgB,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC;oBAClE,CAAC,EAAE,gBAAgB,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC;iBACrE,CAAC,CAAC;YACX,CAAC,CAAC;YAEF,SAAS;YACT,IAAI,UAAU,GAAG,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YACpC,IAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,SAAS,GAAG,GAAG,CAAC,CAAC,CAAC,WAAW;YAEtE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;gBACjC,UAAU,GAAG,UAAU,CAAC,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,CAAC,CAAC;gBACpE,gBAAgB,IAAI,cAAc,CAAC;aACtC;YAED,WAAW;YACX,UAAU,CAAC,EAAE,CAAC,IAAI,EAAE;gBAChB,CAAC,EAAE,gBAAgB,CAAC,CAAC;gBACrB,CAAC,EAAE,gBAAgB,CAAC,CAAC;aACxB,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;iBACxB,KAAK,EAAE,CAAC;QACb,CAAC,EAAE,WAAW,CAAC,CAAC;IACpB,CAAC;IAED;;;OAGG;IACK,qDAAsB,GAA9B,UAA+B,WAAkB;QAAjD,iBA8DC;QA7DG,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,WAAW,CAAC,CAAC;QAEhD,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,WAAW,CAAC,OAAO,CAAC,UAAC,QAAa;YAC9B,IAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;YACrB,IAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;YAErB,IAAI,CAAC,KAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;gBAC/B,OAAO;aACV;YAED,UAAU;YACV,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE;gBACtB,OAAO;aACV;YAED,SAAS;YACT,IAAM,QAAQ,GAAG,KAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,KAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3D,IAAI,CAAC,QAAQ,EAAE;gBACX,OAAO,CAAC,IAAI,CAAC,kDAAa,CAAC,UAAK,CAAC,MAAG,CAAC,CAAC;gBACtC,OAAO;aACV;YAED,qBAAqB;YACrB,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,OAAO,KAAK,CAAC,EAAE;gBAC5C,OAAO,CAAC,GAAG,CAAC,wDAAc,CAAC,UAAK,CAAC,kBAAa,QAAQ,CAAC,MAAM,kBAAa,QAAQ,CAAC,OAAS,CAAC,CAAC;gBAC9F,YAAY,EAAE,CAAC;gBACf,OAAO;aACV;YAED,OAAO,CAAC,GAAG,CAAC,kDAAa,CAAC,UAAK,CAAC,2CAAa,QAAQ,CAAC,aAAe,CAAC,CAAC;YACvE,OAAO,CAAC,GAAG,CAAC,mBAAO,CAAC,UAAK,CAAC,2CAAkB,QAAQ,CAAC,MAAM,kBAAa,QAAQ,CAAC,OAAS,CAAC,CAAC;YAC5F,aAAa,EAAE,CAAC;YAEhB,gBAAgB;YAChB,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC;YACxB,QAAQ,CAAC,OAAO,GAAG,CAAC,CAAC;YACrB,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;YACpB,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;YAEpB,OAAO,CAAC,GAAG,CAAC,mBAAO,CAAC,UAAK,CAAC,iDAAmB,QAAQ,CAAC,MAAM,kBAAa,QAAQ,CAAC,OAAS,CAAC,CAAC;YAE7F,SAAS;YACT,IAAI,QAAQ,CAAC,aAAa,GAAG,CAAC,EAAE;gBAC5B,OAAO,CAAC,GAAG,CAAC,iDAAY,CAAC,UAAK,CAAC,qBAAQ,EAAE,QAAQ,CAAC,aAAa,CAAC,CAAC;gBACjE,KAAI,CAAC,YAAY,CAAC;oBACd,KAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,aAAa,CAAC,CAAC;gBAC1D,CAAC,EAAE,GAAG,CAAC,CAAC;aACX;iBAAM;gBACH,OAAO,CAAC,GAAG,CAAC,mBAAO,CAAC,UAAK,CAAC,2FAAkB,CAAC,CAAC;aACjD;YAED,WAAW;YACX,IAAI,KAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,KAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACzC,KAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC;aACxC;QACL,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,kGAAqB,aAAa,+CAAY,YAAY,mCAAU,WAAW,CAAC,MAAM,WAAG,CAAC,CAAC;IAC3G,CAAC;IAED;;;OAGG;IACK,oDAAqB,GAA7B,UAA8B,OAAY;QAA1C,iBA+CC;QA9CG,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAE3B,WAAW;QACX,IAAI,OAAO,CAAC,cAAc,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE;YACjE,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAC1D,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,UAAC,KAAU;gBACtC,IAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;gBAClB,IAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;gBAClB,IAAM,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC;gBAE1C,IAAI,KAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;oBAC9B,OAAO,CAAC,GAAG,CAAC,kDAAa,CAAC,UAAK,CAAC,2CAAa,aAAe,CAAC,CAAC;oBAE9D,gBAAgB;oBAChB,KAAI,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;oBAEtB,SAAS;oBACT,IAAI,aAAa,GAAG,CAAC,EAAE;wBACnB,oBAAoB;wBACpB,KAAI,CAAC,YAAY,CAAC;4BACd,KAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,CAAC,EAAE,aAAa,CAAC,CAAC;wBACjD,CAAC,EAAE,GAAG,CAAC,CAAC;qBACX;oBAED,WAAW;oBACX,IAAI,KAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,KAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;wBACzC,KAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC;qBACxC;iBACJ;YACL,CAAC,CAAC,CAAC;SACN;QAED,WAAW;QACX,IAAI,OAAO,CAAC,YAAY,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;YAC7D,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;YACxD,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,UAAC,KAAU;gBACpC,IAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;gBAClB,IAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;gBAElB,IAAI,KAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;oBAC9B,OAAO,CAAC,GAAG,CAAC,kDAAa,CAAC,UAAK,CAAC,MAAG,CAAC,CAAC;oBACrC,UAAU;oBACV,KAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;iBACjC;YACL,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IAvlGD;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;kEACe;IAGnC;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;4DACS;IAG7B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;8DACW;IAG/B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;6DACU;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;6DACU;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;6DACU;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;6DACU;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;6DACU;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;6DACU;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;6DACU;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;6DACU;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;2DACQ;IApCT,oBAAoB;QADxC,OAAO;OACa,oBAAoB,CA6lGxC;IAAD,2BAAC;CA7lGD,AA6lGC,CA7lGiD,EAAE,CAAC,SAAS,GA6lG7D;kBA7lGoB,oBAAoB", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\nimport { RoomUser, HexCoord } from \"../../bean/GameBean\";\nimport { GlobalBean } from \"../../bean/GlobalBean\";\nimport PlayerGameController from \"../../pfb/PlayerGameController \";\n\nconst {ccclass, property} = cc._decorator;\n\n// 棋盘格子数据接口\nexport interface GridData {\n    x: number;  // 格子的x坐标 (0-7)\n    y: number;  // 格子的y坐标 (0-7)\n    worldPos: cc.Vec2;  // 格子在世界坐标系中的位置\n    hasPlayer: boolean;  // 是否已经放置了玩家预制体\n    playerNode?: cc.Node;  // 放置的玩家节点引用\n}\n\n@ccclass\nexport default class ChessBoardController extends cc.Component {\n\n    @property(cc.Prefab)\n    playerGamePrefab: cc.Prefab = null;\n\n    @property(cc.Prefab)\n    boomPrefab: cc.Prefab = null;\n\n    @property(cc.Prefab)\n    biaojiPrefab: cc.Prefab = null;\n\n    @property(cc.Prefab)\n    boom1Prefab: cc.Prefab = null;\n\n    @property(cc.Prefab)\n    boom2Prefab: cc.Prefab = null;\n\n    @property(cc.Prefab)\n    boom3Prefab: cc.Prefab = null;\n\n    @property(cc.Prefab)\n    boom4Prefab: cc.Prefab = null;\n\n    @property(cc.Prefab)\n    boom5Prefab: cc.Prefab = null;\n\n    @property(cc.Prefab)\n    boom6Prefab: cc.Prefab = null;\n\n    @property(cc.Prefab)\n    boom7Prefab: cc.Prefab = null;\n\n    @property(cc.Prefab)\n    boom8Prefab: cc.Prefab = null;  // player_game_pfb 预制体\n\n    @property(cc.Node)\n    boardNode: cc.Node = null;  // 棋盘节点\n\n\n\n    // 棋盘配置\n    private readonly BOARD_SIZE = 8;  // 8x8棋盘\n    private readonly BOARD_WIDTH = 750;  // 棋盘总宽度\n    private readonly BOARD_HEIGHT = 750;  // 棋盘总高度\n    private readonly GRID_SIZE = 88;  // 每个格子的大小 88x88\n\n    // 格子数据存储\n    private gridData: GridData[][] = [];  // 二维数组存储格子数据\n    private gridNodes: cc.Node[][] = [];  // 二维数组存储格子节点\n\n    onLoad() {\n        this.initBoard();\n    }\n\n    start() {\n       \n\n        // 延迟一帧后再次尝试启用触摸事件，确保所有节点都已创建完成\n        this.scheduleOnce(() => {\n            this.enableTouchForExistingGrids();\n        }, 0.1);\n    }\n\n    // 初始化棋盘\n    private initBoard() {\n        // 初始化数据数组\n        for (let x = 0; x < this.BOARD_SIZE; x++) {\n            this.gridData[x] = [];\n            this.gridNodes[x] = [];\n            for (let y = 0; y < this.BOARD_SIZE; y++) {\n                this.gridData[x][y] = {\n                    x: x,\n                    y: y,\n                    worldPos: this.getGridWorldPosition(x, y),\n                    hasPlayer: false\n                };\n            }\n        }\n\n        this.createGridNodes();\n    }\n\n    // 启用现有格子的触摸事件\n    private createGridNodes() {\n        if (!this.boardNode) {\n            console.error(\"棋盘节点未设置！\");\n            return;\n        }\n\n        // 如果格子已经存在，直接启用触摸事件\n        this.enableTouchForExistingGrids();\n    }\n\n    // 为现有格子启用触摸事件\n    private enableTouchForExistingGrids() {\n        // 检查棋盘节点是否存在\n        if (!this.boardNode) {\n            console.error(\"棋盘节点未设置，无法启用触摸事件！\");\n            return;\n        }\n\n        // 遍历棋盘节点的所有子节点\n        let children = this.boardNode.children;\n\n\n        for (let i = 0; i < children.length; i++) {\n            let child = children[i];\n\n            // 尝试从节点名称解析坐标\n            let coords = this.parseGridCoordinateFromName(child.name);\n            if (coords) {\n                this.setupGridTouchEvents(child, coords.x, coords.y);\n                this.gridNodes[coords.x] = this.gridNodes[coords.x] || [];\n                this.gridNodes[coords.x][coords.y] = child;\n            } else {\n                // 如果无法从名称解析，尝试从位置计算\n                let pos = child.getPosition();\n                let coords = this.getGridCoordinateFromPosition(pos);\n                if (coords) {\n                    this.setupGridTouchEvents(child, coords.x, coords.y);\n                    this.gridNodes[coords.x] = this.gridNodes[coords.x] || [];\n                    this.gridNodes[coords.x][coords.y] = child;\n                }\n            }\n        }\n\n    }\n\n    // 从节点名称解析格子坐标\n    private parseGridCoordinateFromName(nodeName: string): {x: number, y: number} | null {\n        // 尝试匹配 Grid_x_y 格式\n        let match = nodeName.match(/Grid_(\\d+)_(\\d+)/);\n        if (match) {\n            return {x: parseInt(match[1]), y: parseInt(match[2])};\n        }\n        return null;\n    }\n\n    // 从位置计算格子坐标\n    private getGridCoordinateFromPosition(pos: cc.Vec2): {x: number, y: number} | null {\n        let x = Math.floor((pos.x + this.BOARD_WIDTH / 2) / this.GRID_SIZE);\n        let y = Math.floor((pos.y + this.BOARD_HEIGHT / 2) / this.GRID_SIZE);\n\n        if (this.isValidCoordinate(x, y)) {\n            return {x: x, y: y};\n        }\n        return null;\n    }\n\n    // 为格子节点设置触摸事件\n    private setupGridTouchEvents(gridNode: cc.Node, x: number, y: number) {\n        // 安全检查：确保坐标有效\n        if (!this.isValidCoordinate(x, y)) {\n            console.error(`❌ setupGridTouchEvents: 尝试为无效坐标(${x},${y})设置触摸事件`);\n            return;\n        }\n\n \n\n        // 长按相关变量\n        let isLongPressing = false;\n        let longPressTimer = 0;\n        let longPressCallback: Function = null;\n        const LONG_PRESS_TIME = 1.0; // 1秒长按时间\n\n        // 触摸开始事件\n        gridNode.on(cc.Node.EventType.TOUCH_START, (_event: cc.Event.EventTouch) => {\n            isLongPressing = true;\n            longPressTimer = 0;\n\n            // 开始长按检测\n            longPressCallback = () => {\n                if (isLongPressing) {\n                    longPressTimer += 0.1;\n                    if (longPressTimer >= LONG_PRESS_TIME) {\n                \n                        this.onGridLongPress(x, y);\n                        isLongPressing = false;\n                        if (longPressCallback) {\n                            this.unschedule(longPressCallback);\n                        }\n                    }\n                }\n            };\n            this.schedule(longPressCallback, 0.1);\n        }, this);\n\n        // 触摸结束事件\n        gridNode.on(cc.Node.EventType.TOUCH_END, (event: cc.Event.EventTouch) => {\n            // 如果不是长按，则执行点击事件\n            if (isLongPressing && longPressTimer < LONG_PRESS_TIME) {\n   \n                this.onGridClick(x, y, event);\n            }\n\n            isLongPressing = false;\n            if (longPressCallback) {\n                this.unschedule(longPressCallback);\n            }\n        }, this);\n\n        // 触摸取消事件\n        gridNode.on(cc.Node.EventType.TOUCH_CANCEL, (_event: cc.Event.EventTouch) => {\n            isLongPressing = false;\n            if (longPressCallback) {\n                this.unschedule(longPressCallback);\n            }\n        }, this);\n\n        // 添加Button组件以确保触摸响应\n        let button = gridNode.getComponent(cc.Button);\n        if (!button) {\n            button = gridNode.addComponent(cc.Button);\n            button.transition = cc.Button.Transition.SCALE;\n            button.zoomScale = 0.95;\n        }\n    }\n\n    // 计算格子的世界坐标位置（左下角为(0,0)）\n    private getGridWorldPosition(x: number, y: number): cc.Vec2 {\n        // 计算格子中心点位置\n        // 左下角为(0,0)，所以y坐标需要从下往上计算\n        let posX = (x * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_WIDTH / 2);\n        let posY = (y * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_HEIGHT / 2);\n        \n        return cc.v2(posX, posY);\n    }\n\n    // 格子点击事件 - 发送挖掘操作\n    private onGridClick(x: number, y: number, _event?: cc.Event.EventTouch) {\n       \n\n        // 检查坐标是否有效（确保在8x8棋盘范围内）\n        if (!this.isValidCoordinate(x, y)) {\n            \n            return;\n        }\n\n        // 检查该位置是否已经有玩家预制体\n        if (this.gridData[x][y].hasPlayer) {\n          \n            return;\n        }\n\n        // 发送挖掘操作事件 (action = 1)\n        // 只发送事件，不直接生成预制体，等待GamePageController确认后再生成\n        this.node.emit('chess-board-click', {\n            x: x,\n            y: y,\n            action: 1  // 1 = 挖掘\n        });\n    }\n\n    // 格子长按事件 - 发送标记操作\n    private onGridLongPress(x: number, y: number) {\n        \n\n        // 检查坐标是否有效（确保在8x8棋盘范围内）\n        if (!this.isValidCoordinate(x, y)) {\n            \n            return;\n        }\n\n        // 检查该位置是否已经有玩家预制体\n        if (this.gridData[x][y].hasPlayer) {\n           \n            return;\n        }\n\n        // 发送标记操作事件 (action = 2)\n        // 只发送事件，不直接生成预制体，等待GamePageController确认后再生成\n        this.node.emit('chess-board-click', {\n            x: x,\n            y: y,\n            action: 2  // 2 = 标记\n        });\n    }\n\n    // 在格子上放置玩家预制体\n    public placePlayerOnGrid(x: number, y: number, withFlag: boolean = false) {\n        // 双重检查：确保坐标有效\n        if (!this.isValidCoordinate(x, y)) {\n            console.error(`❌ placePlayerOnGrid: 无效坐标(${x},${y})`);\n            return;\n        }\n\n        // 双重检查：确保格子为空\n        let gridData = this.gridData[x][y];\n        if (gridData.hasPlayer) {\n            console.error(`❌ placePlayerOnGrid: 格子(${x},${y})已有玩家，不能重复放置`);\n            return;\n        }\n\n        if (!this.playerGamePrefab) {\n            console.error(\"❌ 玩家预制体未设置！\");\n            return;\n        }\n\n        if (!this.boardNode) {\n            console.error(\"❌ 棋盘节点未设置！\");\n            return;\n        }\n\n        // 实例化玩家预制体\n        let playerNode = cc.instantiate(this.playerGamePrefab);\n\n        // 计算正确的位置\n        let correctPosition = this.calculateCorrectPosition(x, y);\n        playerNode.setPosition(correctPosition);\n\n      \n\n        // 先隐藏节点，等头像加载完成后再显示\n        playerNode.active = false;\n\n        // 处理Layout限制问题\n        this.addPlayerNodeSafely(playerNode);\n\n        // 设置头像和用户数据（异步加载）\n        this.setupPlayerAvatarAsync(playerNode, x, y, withFlag, () => {\n            // 头像加载完成的回调，播放生成动画（点击生成和单人格子）\n            this.playAvatarSpawnAnimation(playerNode);\n        });\n\n        // 更新格子数据\n        gridData.hasPlayer = true;\n        gridData.playerNode = playerNode;\n    }\n\n    // 计算正确的位置（格子中心偏移(0, -16)）\n    private calculateCorrectPosition(x: number, y: number): cc.Vec2 {\n        // 使用自定义偏移量\n        let offsetX = this.customOffsetX;\n        let offsetY = this.customOffsetY;\n\n        // 方法1: 如果能找到对应的格子节点，使用其位置并添加偏移\n        let targetGridNode = this.gridNodes[x] && this.gridNodes[x][y];\n        if (targetGridNode) {\n            let gridPos = targetGridNode.getPosition();\n            let finalPos = cc.v2(gridPos.x + offsetX, gridPos.y + offsetY);\n        \n            return finalPos;\n        }\n\n        // 方法2: 基于棋盘的实际位置计算\n       \n\n        // 计算格子中心位置\n        let centerX = (x * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_WIDTH / 2);\n        let centerY = (y * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_HEIGHT / 2);\n\n        // 添加偏移\n        let finalX = centerX + offsetX;\n        let finalY = centerY + offsetY;\n\n        let calculatedPos = cc.v2(finalX, finalY);\n       \n\n        return calculatedPos;\n    }\n\n    /**\n     * 计算预制体的精确位置（根据您提供的坐标规律）\n     * @param x 格子x坐标\n     * @param y 格子y坐标\n     * @returns 预制体应该放置的精确位置\n     */\n    private calculatePrefabPosition(x: number, y: number): cc.Vec2 {\n        // 根据您提供的坐标规律计算：\n        // (0,0) → (-314, -310)\n        // (1,0) → (-224, -310)  // x增加90\n        // (0,1) → (-314, -222)  // y增加88\n        // (7,7) → (310, 312)\n\n        const startX = -314;  // 起始X坐标\n        const startY = -310;  // 起始Y坐标\n        const stepX = 90;     // X方向步长\n        const stepY = 88;     // Y方向步长\n\n        const finalX = startX + (x * stepX);\n        const finalY = startY + (y * stepY);\n\n        const position = cc.v2(finalX, finalY);\n       \n\n        return position;\n    }\n\n    /**\n     * 播放头像生成动画（由大变小）\n     * @param playerNode 玩家节点\n     */\n    private playAvatarSpawnAnimation(playerNode: cc.Node) {\n        if (!playerNode) {\n            console.warn(\"播放生成动画失败：节点为空\");\n            return;\n        }\n\n       \n\n        // 显示节点\n        playerNode.active = true;\n\n        // 设置初始缩放为1.5倍（比正常大）\n        const originalScale = playerNode.scaleX;\n        const startScale = originalScale * 1.5;\n        playerNode.setScale(startScale);\n\n        // 使用cc.Tween创建由大变小的缩放动画\n        cc.tween(playerNode)\n            .to(0.3, { scaleX: originalScale, scaleY: originalScale }, { easing: 'backOut' })\n            .start();\n\n      \n    }\n\n    /**\n     * 播放头像调整动画（平滑移动和缩小）\n     * @param playerNode 玩家节点\n     * @param newPosition 新位置\n     * @param newScale 新缩放\n     */\n    private playAvatarAdjustAnimation(playerNode: cc.Node, newPosition: cc.Vec2, newScale: number) {\n        if (!playerNode) {\n            console.warn(\"播放调整动画失败：节点为空\");\n            return;\n        }\n\n       \n\n        // 使用cc.Tween同时播放移动和缩放动画\n        cc.tween(playerNode)\n            .to(0.3, {\n                x: newPosition.x,\n                y: newPosition.y,\n                scaleX: newScale,\n                scaleY: newScale\n            }, { easing: 'sineOut' })\n            .start();\n\n       \n    }\n\n    /**\n     * 根据格子总人数计算基础位置（统一逻辑）\n     * @param x 格子x坐标\n     * @param y 格子y坐标\n     * @param totalPlayers 该格子的总人数\n     * @returns 基础位置\n     */\n    private calculateBasePositionByPlayerCount(x: number, y: number, totalPlayers: number): cc.Vec2 {\n        let offsetX = this.customOffsetX;\n        let offsetY: number;\n\n        if (totalPlayers === 1) {\n            // 一个格子里只有一个人：需要偏移\n            offsetY = this.customOffsetY; // -16\n           \n        } else {\n            // 一个格子里有两个及以上：不偏移\n            offsetY = 0;\n           \n        }\n\n        // 方法1: 如果能找到对应的格子节点，使用其位置并添加偏移\n        let targetGridNode = this.gridNodes[x] && this.gridNodes[x][y];\n        if (targetGridNode) {\n            let gridPos = targetGridNode.getPosition();\n            let finalPos = cc.v2(gridPos.x + offsetX, gridPos.y + offsetY);\n           \n            return finalPos;\n        }\n\n        // 方法2: 基于棋盘的实际位置计算\n        let centerX = (x * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_WIDTH / 2);\n        let centerY = (y * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_HEIGHT / 2);\n\n        let finalX = centerX + offsetX;\n        let finalY = centerY + offsetY;\n\n        let calculatedPos = cc.v2(finalX, finalY);\n        \n\n        return calculatedPos;\n    }\n\n    /**\n     * 计算多人情况下的基础位置（不包含往下偏移，逻辑分开）\n     * @param x 格子x坐标\n     * @param y 格子y坐标\n     * @returns 格子中心位置（多人专用，不偏移）\n     */\n    private calculateMultiPlayerBasePosition(x: number, y: number): cc.Vec2 {\n        // 多人情况使用独立的偏移逻辑\n        let offsetX = this.customOffsetX;\n        let offsetY = 0; // 多人时不往下偏移，逻辑分开\n\n        // 方法1: 如果能找到对应的格子节点，使用其位置并添加偏移\n        let targetGridNode = this.gridNodes[x] && this.gridNodes[x][y];\n        if (targetGridNode) {\n            let gridPos = targetGridNode.getPosition();\n            let finalPos = cc.v2(gridPos.x + offsetX, gridPos.y + offsetY);\n          \n            return finalPos;\n        }\n\n        // 方法2: 基于棋盘的实际位置计算\n        // 计算格子中心位置\n        let centerX = (x * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_WIDTH / 2);\n        let centerY = (y * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_HEIGHT / 2);\n\n        // 添加偏移（不包含往下偏移）\n        let finalX = centerX + offsetX;\n        let finalY = centerY + offsetY;\n\n        let calculatedPos = cc.v2(finalX, finalY);\n       \n\n        return calculatedPos;\n    }\n\n    // 安全地添加玩家节点（处理Layout限制）\n    private addPlayerNodeSafely(playerNode: cc.Node) {\n        // 检查棋盘节点是否存在\n        if (!this.boardNode) {\n            console.error(\"棋盘节点未设置，无法添加玩家节点！\");\n            return;\n        }\n\n        // 检查棋盘节点是否有Layout组件\n        let layout = this.boardNode.getComponent(cc.Layout);\n        if (layout) {\n           \n\n            // 方案1: 临时禁用Layout\n            layout.enabled = false;\n           \n\n            // 添加节点\n            this.boardNode.addChild(playerNode);\n\n\n        } else {\n           \n            this.boardNode.addChild(playerNode);\n        }\n\n        // 方案2备选：添加到Layout外部\n        // this.addToParentNode(playerNode);\n    }\n\n    // 备选方案：添加到父节点（Layout外部）\n    private addToParentNode(playerNode: cc.Node) {\n        // 检查棋盘节点是否存在\n        if (!this.boardNode) {\n            console.error(\"棋盘节点未设置，无法添加玩家节点！\");\n            return;\n        }\n\n        if (this.boardNode.parent) {\n            // 需要转换坐标系\n            let worldPos = this.boardNode.convertToWorldSpaceAR(playerNode.getPosition());\n            let localPos = this.boardNode.parent.convertToNodeSpaceAR(worldPos);\n\n            playerNode.setPosition(localPos);\n            this.boardNode.parent.addChild(playerNode);\n\n            \n        } else {\n            console.error(`❌ 棋盘节点没有父节点`);\n            // 回退到直接添加\n            this.boardNode.addChild(playerNode);\n        }\n    }\n\n    // 异步设置玩家头像（带回调）\n    private setupPlayerAvatarAsync(playerNode: cc.Node, x: number, y: number, withFlag: boolean, onComplete: () => void) {\n        // 查找PlayerGameController组件\n        let playerController = playerNode.getComponent(\"PlayerGameController\") ||\n                              playerNode.getComponent(\"PlayerGameController \") ||\n                              playerNode.getComponentInChildren(\"PlayerGameController\");\n\n        if (playerController) {\n            // 检查avatar节点是否存在\n            if (playerController.avatar) {\n                // 检查avatar节点是否有Sprite组件\n                let avatarSprite = playerController.avatar.getComponent(cc.Sprite);\n                if (!avatarSprite) {\n                    avatarSprite = playerController.avatar.addComponent(cc.Sprite);\n                }\n\n                // 确保avatar节点可见\n                playerController.avatar.active = true;\n                playerController.avatar.opacity = 255;\n            } else {\n                console.error(\"❌ PlayerGameController中的avatar节点为null\");\n                onComplete();\n                return;\n            }\n\n            // 设置旗子节点的显示状态 - 重点检查\n            if (playerController.flagNode) {\n              \n                playerController.flagNode.active = withFlag;\n              \n\n                // 额外检查旗子节点的可见性\n                if (withFlag) {\n                    playerController.flagNode.opacity = 255;\n                   \n\n                    // 确保旗子节点的父节点也是可见的\n                    let parent = playerController.flagNode.parent;\n                    while (parent && parent !== playerNode) {\n                        \n                        parent.active = true;\n                        parent = parent.parent;\n                    }\n\n                    // 延迟检查旗子是否真的显示了\n                    this.scheduleOnce(() => {\n                       \n                    }, 1.0);\n                }\n            } else {\n                console.warn(`⚠️ 找不到旗子节点 (${x},${y})`);\n            }\n\n            // 创建用户数据并设置头像\n            let userData = {\n                userId: `player_${x}_${y}`,\n                nickName: `玩家(${x},${y})`,\n                avatar: this.getDefaultAvatarUrl(),\n                score: 0,\n                pos: 0,\n                coin: 0,\n                status: 0,\n                rank: 0\n            } as RoomUser;\n\n            // 使用PlayerGameController的setData方法来设置头像\n            try {\n                playerController.setData(userData);\n\n                // 延迟设置旗子状态，确保在PlayerGameController初始化之后\n                this.scheduleOnce(() => {\n                    if (playerController.flagNode) {\n                        playerController.flagNode.active = withFlag;\n                       \n                    }\n                    onComplete();\n                }, 0.1);\n            } catch (error) {\n                console.error(\"设置头像数据失败:\", error);\n                onComplete();\n            }\n\n        } else {\n            console.warn(\"⚠️ 找不到PlayerGameController组件\");\n            this.tryDirectAvatarSetupAsync(playerNode, x, y, onComplete);\n        }\n    }\n\n   \n    // 设置玩家头像（保留原方法用于其他地方）\n    private setupPlayerAvatar(playerNode: cc.Node, x: number, y: number) {\n       \n\n        // 查找PlayerGameController组件\n        let playerController = playerNode.getComponent(\"PlayerGameController\") ||\n                              playerNode.getComponent(\"PlayerGameController \") ||\n                              playerNode.getComponentInChildren(\"PlayerGameController\");\n\n        if (playerController) {\n         \n\n            // 检查avatar节点是否存在\n            if (playerController.avatar) {\n                \n\n                // 检查avatar节点是否有Sprite组件\n                let avatarSprite = playerController.avatar.getComponent(cc.Sprite);\n                if (!avatarSprite) {\n                    console.warn(\"⚠️ avatar节点缺少Sprite组件，正在添加...\");\n                    avatarSprite = playerController.avatar.addComponent(cc.Sprite);\n                }\n\n                // 确保avatar节点可见\n                playerController.avatar.active = true;\n                playerController.avatar.opacity = 255;\n\n               \n            } else {\n                console.error(\"❌ PlayerGameController中的avatar节点为null\");\n                return;\n            }\n\n            // 创建用户数据\n            let userData = {\n                userId: `player_${x}_${y}`,\n                nickName: `玩家(${x},${y})`,\n                avatar: this.getDefaultAvatarUrl(),\n                score: 0,\n                pos: 0,\n                coin: 0,\n                status: 0,\n                rank: 0\n            } as RoomUser;\n\n            \n\n            // 安全地调用setData\n            try {\n                playerController.setData(userData);\n               \n                // 延迟检查头像是否加载成功\n                this.scheduleOnce(() => {\n                    this.checkAvatarLoaded(playerController.avatar, x, y);\n                }, 2.0);\n\n            } catch (error) {\n                console.error(\"❌ 设置头像时出错:\", error);\n            }\n        } else {\n            console.warn(\"⚠️ 找不到PlayerGameController组件，跳过头像设置\");\n\n            // 尝试直接在节点上查找avatar子节点\n            this.tryDirectAvatarSetup(playerNode, x, y);\n        }\n    }\n\n    // 检查头像是否加载成功\n    private checkAvatarLoaded(avatarNode: cc.Node, x: number, y: number) {\n        if (!avatarNode) {\n            console.error(`❌ 位置(${x},${y})的avatar节点为null`);\n            return;\n        }\n\n        let sprite = avatarNode.getComponent(cc.Sprite);\n        if (!sprite) {\n            console.error(`❌ 位置(${x},${y})的avatar节点没有Sprite组件`);\n            return;\n        }\n\n        if (!sprite.spriteFrame) {\n            console.warn(`⚠️ 位置(${x},${y})的头像可能加载失败，spriteFrame为null`);\n\n            // 尝试设置一个默认的颜色作为备用显示\n            this.setFallbackAvatar(avatarNode, x, y);\n        } else {\n           \n        }\n\n        \n    }\n\n    // 设置备用头像（纯色方块）\n    private setFallbackAvatar(avatarNode: cc.Node, x: number, y: number) {\n       \n        let sprite = avatarNode.getComponent(cc.Sprite);\n        if (!sprite) {\n            sprite = avatarNode.addComponent(cc.Sprite);\n        }\n\n        // 创建一个简单的纯色纹理\n        let texture = new cc.Texture2D();\n        let colors = [\n            [255, 107, 107, 255], // 红色\n            [78, 205, 196, 255],  // 青色\n            [69, 183, 209, 255],  // 蓝色\n            [150, 206, 180, 255], // 绿色\n            [255, 234, 167, 255]  // 黄色\n        ];\n\n        let colorIndex = (x + y) % colors.length;\n        let color = colors[colorIndex];\n\n        texture.initWithData(new Uint8Array(color), cc.Texture2D.PixelFormat.RGBA8888, 1, 1);\n        sprite.spriteFrame = new cc.SpriteFrame(texture);\n\n        // 设置大小\n        avatarNode.setContentSize(80, 80);\n        avatarNode.active = true;\n\n       \n    }\n\n    // 尝试直接设置头像（当找不到PlayerGameController时）\n    private tryDirectAvatarSetup(playerNode: cc.Node, x: number, y: number) {\n        \n        // 查找名为\"avatar\"的子节点\n        let avatarNode = playerNode.getChildByName(\"avatar\");\n        if (avatarNode) {\n           \n            this.setFallbackAvatar(avatarNode, x, y);\n        } else {\n            console.warn(\"⚠️ 未找到avatar子节点\");\n\n            // 列出所有子节点名称\n        \n        }\n    }\n\n    // 获取默认头像URL\n    private getDefaultAvatarUrl(): string {\n        // 使用真实的头像URL\n        return \"https://static.gooplay.com/online/user-avatar/1732786296530322669.jpg\";\n    }\n\n    // 保存格子坐标（用于后续发送给后端）\n    private saveGridCoordinate(x: number, y: number) {\n        // 这里可以将坐标保存到数组或发送给后端\n       \n\n        // 示例：可以调用网络管理器发送坐标\n        this.sendCoordinateToServer(x, y);\n\n        // 或者保存到本地数组以备后用\n        this.addToCoordinateHistory(x, y);\n    }\n\n    // 发送坐标到服务器\n    private sendCoordinateToServer(x: number, y: number) {\n        // 构造发送数据\n        let moveData = {\n            x: x,\n            y: y,\n            timestamp: Date.now(),\n            playerId: this.getCurrentPlayerId()\n        };\n\n\n        // 暂时只是打印，避免未使用变量警告\n        return moveData;\n    }\n\n    // 添加到坐标历史记录\n    private coordinateHistory: {x: number, y: number, timestamp: number}[] = [];\n\n    private addToCoordinateHistory(x: number, y: number) {\n        this.coordinateHistory.push({\n            x: x,\n            y: y,\n            timestamp: Date.now()\n        });\n\n       \n    }\n\n    // 获取当前玩家ID（示例）\n    private getCurrentPlayerId(): string {\n        // 这里应该从全局状态或用户数据中获取\n        return \"player_001\";  // 示例ID\n    }\n\n    // 获取指定坐标的格子数据\n    public getGridData(x: number, y: number): GridData | null {\n        if (x < 0 || x >= this.BOARD_SIZE || y < 0 || y >= this.BOARD_SIZE) {\n            return null;\n        }\n        return this.gridData[x][y];\n    }\n\n    // 清除指定格子的玩家\n    public clearGridPlayer(x: number, y: number): boolean {\n        let gridData = this.getGridData(x, y);\n        if (!gridData || !gridData.hasPlayer) {\n            return false;\n        }\n\n        // 移除玩家节点\n        if (gridData.playerNode) {\n            gridData.playerNode.removeFromParent();\n            gridData.playerNode = null;\n        }\n\n        // 更新数据\n        gridData.hasPlayer = false;\n        \n       \n        return true;\n    }\n\n    // 清除所有玩家\n    public clearAllPlayers() {\n        for (let x = 0; x < this.BOARD_SIZE; x++) {\n            for (let y = 0; y < this.BOARD_SIZE; y++) {\n                this.clearGridPlayer(x, y);\n            }\n        }\n       \n    }\n\n    // 获取所有已放置玩家的坐标\n    public getAllPlayerCoordinates(): {x: number, y: number}[] {\n        let coordinates: {x: number, y: number}[] = [];\n\n        for (let x = 0; x < this.BOARD_SIZE; x++) {\n            for (let y = 0; y < this.BOARD_SIZE; y++) {\n                if (this.gridData[x][y].hasPlayer) {\n                    coordinates.push({x: x, y: y});\n                }\n            }\n        }\n\n        return coordinates;\n    }\n\n    // 检查坐标是否有效\n    public isValidCoordinate(x: number, y: number): boolean {\n        return x >= 0 && x < this.BOARD_SIZE && y >= 0 && y < this.BOARD_SIZE;\n    }\n\n    // 检查格子是否为空\n    public isGridEmpty(x: number, y: number): boolean {\n        if (!this.isValidCoordinate(x, y)) {\n            return false;\n        }\n        return !this.gridData[x][y].hasPlayer;\n    }\n\n    // 获取坐标历史记录\n    public getCoordinateHistory(): {x: number, y: number, timestamp: number}[] {\n        return [...this.coordinateHistory];  // 返回副本\n    }\n\n    // 清除坐标历史记录\n    public clearCoordinateHistory() {\n        this.coordinateHistory = [];\n      \n    }\n\n    // 根据世界坐标获取格子坐标\n    public getGridCoordinateFromWorldPos(worldPos: cc.Vec2): {x: number, y: number} | null {\n        // 检查棋盘节点是否存在\n        if (!this.boardNode) {\n            console.error(\"棋盘节点未设置，无法获取格子坐标！\");\n            return null;\n        }\n\n        // 将世界坐标转换为相对于棋盘的坐标\n        let localPos = this.boardNode.convertToNodeSpaceAR(worldPos);\n\n        // 计算格子坐标\n        let x = Math.floor((localPos.x + this.BOARD_WIDTH / 2) / this.GRID_SIZE);\n        let y = Math.floor((localPos.y + this.BOARD_HEIGHT / 2) / this.GRID_SIZE);\n\n        if (this.isValidCoordinate(x, y)) {\n            return {x: x, y: y};\n        }\n\n        return null;\n    }\n\n    // 高亮显示格子（可选功能）\n    public highlightGrid(x: number, y: number, highlight: boolean = true) {\n        if (!this.isValidCoordinate(x, y)) {\n            return;\n        }\n\n        let gridNode = this.gridNodes[x][y];\n        if (gridNode) {\n            // 这里可以添加高亮效果，比如改变颜色或添加边框\n            if (highlight) {\n                gridNode.color = cc.Color.YELLOW;\n                \n            } else {\n                gridNode.color = cc.Color.WHITE;\n              \n            }\n        }\n    }\n\n    // 批量放置玩家（用于从服务器同步数据）\n    public batchPlacePlayers(coordinates: {x: number, y: number}[]) {\n       \n\n        coordinates.forEach(coord => {\n            if (this.isValidCoordinate(coord.x, coord.y) && this.isGridEmpty(coord.x, coord.y)) {\n                this.placePlayerOnGrid(coord.x, coord.y);\n            }\n        });\n    }\n\n    // 手动启用触摸事件（调试用）\n    public manualEnableTouch() {\n        \n        this.enableTouchForExistingGrids();\n    }\n\n    // 测试点击功能（调试用）\n    public testClick(x: number, y: number) {\n        \n        this.onGridClick(x, y);\n    }\n\n    // 获取棋盘状态信息（调试用）\n    public getBoardInfo() {\n        let info = {\n            boardSize: this.BOARD_SIZE,\n            gridSize: this.GRID_SIZE,\n            boardWidth: this.BOARD_WIDTH,\n            boardHeight: this.BOARD_HEIGHT,\n            totalGrids: this.BOARD_SIZE * this.BOARD_SIZE,\n            boardNodeChildren: this.boardNode ? this.boardNode.children.length : 0,\n            playerCount: this.getAllPlayerCoordinates().length,\n            hasPlayerGamePrefab: !!this.playerGamePrefab,\n            hasBoardNode: !!this.boardNode\n        };\n\n \n        return info;\n    }\n\n    // 简单测试方法 - 只测试位置，不加载头像\n    public simpleTest(x: number, y: number) {\n       \n\n        if (!this.boardNode) {\n            console.error(\"❌ 棋盘节点未设置\");\n            return;\n        }\n\n        // 创建一个简单的彩色方块\n        let testNode = new cc.Node(`Test_${x}_${y}`);\n\n        // 添加一个彩色方块\n        let sprite = testNode.addComponent(cc.Sprite);\n        let texture = new cc.Texture2D();\n        let color = [Math.random() * 255, Math.random() * 255, Math.random() * 255, 255];\n        texture.initWithData(new Uint8Array(color), cc.Texture2D.PixelFormat.RGBA8888, 1, 1);\n        sprite.spriteFrame = new cc.SpriteFrame(texture);\n\n        // 设置大小\n        testNode.setContentSize(60, 60);\n\n        // 计算位置\n        let pos = this.calculateCorrectPosition(x, y);\n        testNode.setPosition(pos);\n\n        // 添加坐标标签\n        let labelNode = new cc.Node(\"Label\");\n        let label = labelNode.addComponent(cc.Label);\n        label.string = `(${x},${y})`;\n        label.fontSize = 16;\n        label.node.color = cc.Color.WHITE;\n        labelNode.setPosition(0, 0);\n        testNode.addChild(labelNode);\n\n        // 添加到棋盘（处理Layout问题）\n        this.addPlayerNodeSafely(testNode);\n\n        \n    }\n\n    // 清除所有测试节点\n    public clearTestNodes() {\n        if (this.boardNode) {\n            let children = this.boardNode.children.slice();\n            children.forEach(child => {\n                if (child.name.startsWith(\"Test_\")) {\n                    child.removeFromParent();\n                }\n            });\n        }\n        \n    }\n\n    // 切换到父节点添加模式（如果Layout问题仍然存在）\n    public useParentNodeMode() {\n     \n        // 重新定义添加方法\n        this.addPlayerNodeSafely = this.addToParentNode;\n    }\n\n    // 重新启用Layout（如果需要）\n    public reEnableLayout() {\n        if (!this.boardNode) {\n            console.error(\"棋盘节点未设置，无法重新启用Layout！\");\n            return;\n        }\n\n        let layout = this.boardNode.getComponent(cc.Layout);\n        if (layout) {\n            layout.enabled = true;\n\n        }\n    }\n\n    // 永久禁用Layout\n    public disableLayout() {\n        if (!this.boardNode) {\n            console.error(\"棋盘节点未设置，无法禁用Layout！\");\n            return;\n        }\n\n        let layout = this.boardNode.getComponent(cc.Layout);\n        if (layout) {\n            layout.enabled = false;\n\n        }\n    }\n\n    // 自定义偏移量（如果需要调整位置）\n    private customOffsetX: number = 0;\n    private customOffsetY: number = -16; // 恢复原来的值，保持点击生成位置正确\n\n    // 设置自定义偏移量\n    public setCustomOffset(offsetX: number, offsetY: number) {\n        this.customOffsetX = offsetX;\n        this.customOffsetY = offsetY;\n      \n    }\n\n    // 获取当前偏移量\n    public getCurrentOffset(): {x: number, y: number} {\n        return {x: this.customOffsetX, y: this.customOffsetY};\n    }\n\n    // 测试不同偏移量\n    public testWithOffset(x: number, y: number, offsetX: number, offsetY: number) {\n       \n\n        // 临时保存当前偏移\n        let originalOffsetX = this.customOffsetX;\n        let originalOffsetY = this.customOffsetY;\n\n        // 设置测试偏移\n        this.setCustomOffset(offsetX, offsetY);\n\n        // 执行测试\n        this.simpleTest(x, y);\n\n        // 恢复原偏移\n        this.setCustomOffset(originalOffsetX, originalOffsetY);\n    }\n\n    // 测试头像显示功能\n    public testAvatarDisplay(x: number, y: number) {\n     \n\n        if (!this.isValidCoordinate(x, y)) {\n            console.error(\"❌ 无效坐标\");\n            return;\n        }\n\n        if (this.gridData[x][y].hasPlayer) {\n            console.warn(\"⚠️ 该位置已有玩家\");\n            return;\n        }\n\n        // 直接调用放置玩家方法\n        this.placePlayerOnGrid(x, y);\n\n        // 延迟检查结果\n        this.scheduleOnce(() => {\n            let gridData = this.gridData[x][y];\n            if (gridData.playerNode) {\n               \n\n                // 检查PlayerGameController\n                let controller = gridData.playerNode.getComponent(\"PlayerGameController\");\n                if (controller && controller.avatar) {\n                 \n\n                    let sprite = controller.avatar.getComponent(cc.Sprite);\n                    if (sprite && sprite.spriteFrame) {\n                       \n                    } else {\n                        console.warn(\"⚠️ 头像SpriteFrame不存在\");\n                    }\n                } else {\n                    console.warn(\"⚠️ PlayerGameController或avatar节点不存在\");\n                }\n            } else {\n                console.error(\"❌ 玩家节点创建失败\");\n            }\n        }, 3.0);\n    }\n\n    // 调试预制体结构\n    public debugPrefabStructure() {\n        \n\n        if (!this.playerGamePrefab) {\n            console.error(\"❌ playerGamePrefab为null\");\n            return;\n        }\n\n        // 实例化一个临时节点来检查结构\n        let tempNode = cc.instantiate(this.playerGamePrefab);\n\n       \n\n        // 检查组件\n        let controller = tempNode.getComponent(\"PlayerGameController\");\n        if (controller) {\n           \n            if (controller.avatar) {\n               \n\n                let sprite = controller.avatar.getComponent(cc.Sprite);\n               \n            } else {\n                console.error(\"❌ avatar节点不存在\");\n            }\n        } else {\n            console.error(\"❌ 找不到PlayerGameController组件\");\n        }\n\n        // 列出所有子节点\n      \n        this.logNodeHierarchy(tempNode, 0);\n\n        // 清理临时节点\n        tempNode.destroy();\n    }\n\n    // 递归打印节点层级\n    private logNodeHierarchy(node: cc.Node, depth: number) {\n        let indent = \"  \".repeat(depth);\n     \n\n        for (let child of node.children) {\n            this.logNodeHierarchy(child, depth + 1);\n        }\n    }\n\n    // 异步加载头像\n    private loadAvatarAsync(avatarNode: cc.Node, url: string, onComplete: () => void) {\n       \n        if (!avatarNode) {\n            console.error(\"❌ avatar节点为null\");\n            onComplete();\n            return;\n        }\n\n        let avatarSprite = avatarNode.getComponent(cc.Sprite);\n        if (!avatarSprite) {\n            console.warn(\"⚠️ avatar节点没有Sprite组件，正在添加...\");\n            avatarSprite = avatarNode.addComponent(cc.Sprite);\n        }\n\n        if (!url || url === '') {\n            console.warn(\"⚠️ URL为空，设置备用头像\");\n            this.setFallbackAvatar(avatarNode, 0, 0);\n            onComplete();\n            return;\n        }\n\n        // 根据URL判断文件扩展名\n        let ext = '.png';\n        if (url.toLowerCase().includes('.jpg') || url.toLowerCase().includes('.jpeg')) {\n            ext = '.jpg';\n        } else if (url.toLowerCase().includes('.png')) {\n            ext = '.png';\n        }\n\n       \n\n        cc.assetManager.loadRemote(url, { ext: ext }, (err, texture: cc.Texture2D) => {\n            if (err) {\n                console.error(`❌ 头像加载失败: ${err.message || err}`);\n                console.error(`❌ 失败的URL: ${url}`);\n\n                // 设置备用头像\n                this.setFallbackAvatar(avatarNode, 0, 0);\n                onComplete();\n                return;\n            }\n\n            \n\n            texture.setPremultiplyAlpha(true);\n            texture.packable = false;\n            avatarSprite.spriteFrame = new cc.SpriteFrame(texture);\n\n            // 确保节点可见\n            avatarNode.active = true;\n            avatarNode.opacity = 255;\n\n          \n            onComplete();\n        });\n    }\n\n    // 异步直接设置头像（当找不到PlayerGameController时）\n    private tryDirectAvatarSetupAsync(playerNode: cc.Node, x: number, y: number, onComplete: () => void) {\n     \n\n        // 查找名为\"avatar\"的子节点\n        let avatarNode = playerNode.getChildByName(\"avatar\");\n        if (avatarNode) {\n           \n            this.setFallbackAvatar(avatarNode, x, y);\n            onComplete();\n        } else {\n            console.warn(\"⚠️ 未找到avatar子节点\");\n\n            // 列出所有子节点名称\n           \n           \n            onComplete();\n        }\n    }\n\n    /**\n     * 显示玩家游戏加减分效果\n     * @param userId 用户ID\n     * @param score 分数变化（正数为加分，负数为减分）\n     */\n    showPlayerGameScore(userId: string, score: number) {\n        const currentUserId = this.getCurrentUserId();\n        let foundPlayer = false;\n\n        // 1. 如果是当前用户，查找自己的玩家节点（存储在gridData中）\n        if (userId === currentUserId) {\n            foundPlayer = this.showScoreForCurrentUser(score);\n        } else {\n            // 2. 如果是其他用户，查找对应的玩家头像节点\n            foundPlayer = this.showScoreForOtherUser(userId, score);\n        }\n\n        if (!foundPlayer) {\n            console.warn(`未找到用户 ${userId} 的头像节点来显示分数效果`);\n        }\n    }\n\n    /**\n     * 获取当前用户ID\n     */\n    private getCurrentUserId(): string {\n        return GlobalBean.GetInstance().loginData?.userInfo?.userId || \"\";\n    }\n\n    /**\n     * 为当前用户显示分数效果\n     */\n    private showScoreForCurrentUser(score: number): boolean {\n        for (let x = 0; x < this.BOARD_SIZE; x++) {\n            for (let y = 0; y < this.BOARD_SIZE; y++) {\n                let gridData = this.gridData[x][y];\n                if (gridData.hasPlayer && gridData.playerNode) {\n                    let playerController = gridData.playerNode.getComponent(\"PlayerGameController\") ||\n                                         gridData.playerNode.getComponent(\"PlayerGameController \");\n\n                    if (playerController) {\n                        this.showScoreOnPlayerController(playerController, score);\n                        return true;\n                    }\n                }\n            }\n        }\n        return false;\n    }\n\n    /**\n     * 为其他用户显示分数效果\n     */\n    private showScoreForOtherUser(userId: string, score: number): boolean {\n        if (!this.boardNode) {\n            return false;\n        }\n\n        // 遍历棋盘上的所有玩家头像节点\n        // 由于目前没有在节点上存储userId，我们需要通过其他方式匹配\n        // 临时方案：根据最近的操作位置来匹配\n        return this.findPlayerNodeByRecentAction(userId, score);\n    }\n\n    /**\n     * 根据userId查找对应的玩家节点\n     */\n    private findPlayerNodeByRecentAction(userId: string, score: number): boolean {\n        if (!this.boardNode) {\n            console.warn(`棋盘节点不存在，无法查找用户 ${userId} 的头像`);\n            return false;\n        }\n\n       \n\n        // 遍历棋盘上的所有玩家头像节点，根据存储的userId精确匹配\n        for (let i = 0; i < this.boardNode.children.length; i++) {\n            const child = this.boardNode.children[i];\n\n            // 尝试多种方式获取PlayerGameController组件\n            let playerController = child.getComponent(\"PlayerGameController\");\n            if (!playerController) {\n                playerController = child.getComponent(\"PlayerGameController \");  // 注意末尾有空格\n            }\n            if (!playerController) {\n                // 尝试通过类名获取\n                const components = child.getComponents(cc.Component);\n                playerController = components.find(comp =>\n                    comp.constructor.name === 'PlayerGameController' ||\n                    comp.constructor.name === 'PlayerGameController '\n                );\n            }\n\n            const storedUserId = child['userId'];\n\n          \n\n            // 先输出组件列表，帮助诊断问题\n            if (storedUserId && (storedUserId === userId || i < 5)) {  // 为前5个节点或匹配的节点输出组件列表\n                const allComponents = child.getComponents(cc.Component);\n               \n            }\n\n            if (storedUserId === userId) {\n                if (playerController) {\n                    // 找到匹配的用户ID和组件，显示分数效果\n                    \n                    this.showScoreOnPlayerController(playerController, score);\n                    return true;\n                } else {\n                    // 找到匹配的用户ID但没有组件\n                    console.warn(`⚠️ 找到用户 ${userId} 的节点但没有PlayerGameController组件`);\n                    return false;  // 找到节点但没有组件，返回false\n                }\n            }\n        }\n\n        console.warn(`❌ 未找到用户 ${userId} 的头像节点`);\n        return false;\n    }\n\n    /**\n     * 在PlayerController上显示分数效果\n     */\n    private showScoreOnPlayerController(playerController: any, score: number) {\n        // 临时提升节点层级，避免被其他头像遮挡\n        const playerNode = playerController.node;\n        const originalSiblingIndex = playerNode.getSiblingIndex();\n\n        // 将节点移到最上层\n        playerNode.setSiblingIndex(-1);\n\n        // 同时确保加分/减分节点的层级更高\n        this.ensureScoreNodeTopLevel(playerController);\n\n        if (score > 0) {\n            playerController.showAddScore(score);\n        } else if (score < 0) {\n            playerController.showSubScore(Math.abs(score));\n        }\n\n        // 延迟恢复原始层级（等分数动画播放完成）\n        this.scheduleOnce(() => {\n            if (playerNode && playerNode.isValid) {\n                playerNode.setSiblingIndex(originalSiblingIndex);\n            }\n        }, 2.5); // 增加到2.5秒，确保动画完全结束\n    }\n\n    /**\n     * 确保加分/减分节点在最高层级\n     */\n    private ensureScoreNodeTopLevel(playerController: any) {\n        // 设置加分节点的最高层级\n        if (playerController.addScoreNode) {\n            playerController.addScoreNode.zIndex = cc.macro.MAX_ZINDEX - 1;\n        }\n\n        // 设置减分节点的最高层级\n        if (playerController.subScoreNode) {\n            playerController.subScoreNode.zIndex = cc.macro.MAX_ZINDEX - 1;\n        }\n\n        \n    }\n\n   \n    /**\n     * 显示玩家游戏减分效果\n     * @param userId 用户ID\n     * @param subScore 减分数值\n     */\n    showPlayerGameSubScore(userId: string, subScore: number) {\n     \n\n        let foundPlayer = false;\n\n        // 遍历所有格子，查找玩家节点\n        for (let x = 0; x < this.BOARD_SIZE; x++) {\n            for (let y = 0; y < this.BOARD_SIZE; y++) {\n                let gridData = this.gridData[x][y];\n                if (gridData.hasPlayer && gridData.playerNode) {\n                    let playerController = gridData.playerNode.getComponent(\"PlayerGameController\") ||\n                                         gridData.playerNode.getComponent(\"PlayerGameController \");\n\n                    if (playerController) {\n                        playerController.showSubScore(subScore);\n                        foundPlayer = true;\n                       \n                        break;\n                    }\n                }\n            }\n            if (foundPlayer) break;\n        }\n\n        if (!foundPlayer) {\n            console.warn(`未找到玩家节点来显示减分效果: userId=${userId}`);\n        }\n    }\n\n    /**\n     * 重置游戏场景（游戏开始时调用）\n     * 清除数字、炸弹、标记预制体，重新显示所有小格子\n     */\n    public resetGameScene() {\n  \n\n        if (!this.boardNode) {\n            console.error(\"❌ 棋盘节点不存在，无法重置\");\n            return;\n        }\n\n       \n\n        // 列出所有子节点\n        for (let i = 0; i < this.boardNode.children.length; i++) {\n            const child = this.boardNode.children[i];\n            \n        }\n\n        // 清除所有游戏元素（数字、炸弹、标记等）\n        \n        this.clearAllGameElements();\n\n        // 显示所有小格子\n     \n        this.showAllGrids();\n\n        // 重新初始化棋盘数据\n       \n        this.reinitializeBoardData();\n\n     \n\n        // 列出所有子节点\n        for (let i = 0; i < this.boardNode.children.length; i++) {\n            const child = this.boardNode.children[i];\n            \n        }\n\n     \n    }\n\n    /**\n     * 测试重置功能（可以在浏览器控制台手动调用）\n     */\n    public testReset() {\n\n        this.resetGameScene();\n    }\n\n    /**\n     * 恢复联机模式地图状态（断线重连时使用）\n     * @param mapData 地图数据\n     */\n    public restoreOnlineMapState(mapData: any) {\n        console.log(\"ChessBoardController: 恢复联机模式地图状态\", mapData);\n\n        // 检查数据格式\n        if (Array.isArray(mapData) && mapData.length > 0) {\n            if (Array.isArray(mapData[0])) {\n                // 二维数组格式：mapData[x][y] 表示每个格子的状态\n                console.log(\"检测到二维数组格式的mapData，尺寸:\", mapData.length, \"x\", mapData[0].length);\n                this.restoreFromGridArray(mapData);\n            } else if (mapData[0] && typeof mapData[0] === 'object' && ('x' in mapData[0] && 'y' in mapData[0])) {\n                // 联机模式格式：[{x, y, isRevealed, neighborMines, ...}, ...]\n                console.log(\"检测到联机模式格式的mapData，格子数量:\", mapData.length);\n                this.restoreFromOnlineArray(mapData);\n            } else {\n                console.warn(\"未知的数组格式:\", mapData[0]);\n            }\n        } else if (mapData && (mapData.revealedBlocks || mapData.markedBlocks)) {\n            // 关卡模式格式：{revealedBlocks: [], markedBlocks: []}\n            console.log(\"检测到关卡模式格式的mapData\");\n            this.restoreFromBlockLists(mapData);\n        } else {\n            console.warn(\"未知的mapData格式:\", mapData);\n        }\n    }\n\n    /**\n     * 从二维数组格式恢复地图状态\n     * @param gridArray 二维数组，gridArray[x][y] 表示格子状态\n     */\n    private restoreFromGridArray(gridArray: any[][]) {\n        console.log(\"从二维数组恢复地图状态，数组内容:\", gridArray);\n\n        let restoredCount = 0;\n\n        for (let x = 0; x < gridArray.length; x++) {\n            for (let y = 0; y < gridArray[x].length; y++) {\n                const cellData = gridArray[x][y];\n\n                if (!this.isValidCoordinate(x, y)) {\n                    continue;\n                }\n\n                // 检查所有非零值（包括负数）\n                if (cellData !== null && cellData !== undefined && cellData !== 0) {\n                    console.log(`恢复格子 (${x}, ${y}), 状态值:`, cellData, \"类型:\", typeof cellData);\n                    restoredCount++;\n\n                    // 获取格子节点，确保它存在\n                    const gridNode = this.gridNodes[x] && this.gridNodes[x][y];\n                    if (!gridNode) {\n                        console.warn(`格子节点不存在: (${x}, ${y})`);\n                        continue;\n                    }\n\n                    console.log(`格子 (${x}, ${y}) 当前状态: active=${gridNode.active}, opacity=${gridNode.opacity}`);\n\n                    // 立即隐藏格子（不播放动画）\n                    gridNode.active = false;\n                    gridNode.opacity = 0;\n                    gridNode.scaleX = 0;\n                    gridNode.scaleY = 0;\n\n                    console.log(`格子 (${x}, ${y}) 隐藏后状态: active=${gridNode.active}, opacity=${gridNode.opacity}`);\n\n                    // 根据cellData的值显示相应内容\n                    if (typeof cellData === 'number' && cellData > 0 && cellData <= 8) {\n                        // 数字：表示周围地雷数\n                        console.log(`创建数字预制体 (${x}, ${y}), 数字:`, cellData);\n                        this.scheduleOnce(() => {\n                            this.createNumberPrefab(x, y, cellData);\n                        }, 0.1);\n                    } else if (cellData === -1 || cellData === 'mine' || cellData === 'bomb') {\n                        // 地雷\n                        console.log(`创建地雷预制体 (${x}, ${y})`);\n                        this.scheduleOnce(() => {\n                            this.createBoomPrefab(x, y);\n                        }, 0.1);\n                    } else if (cellData === 'flag' || cellData === 'marked') {\n                        // 标记\n                        console.log(`创建标记预制体 (${x}, ${y})`);\n                        this.createBiaojiPrefab(x, y);\n                    } else {\n                        console.log(`未知的格子状态 (${x}, ${y}):`, cellData);\n                    }\n\n                    // 标记格子已被处理\n                    if (this.gridData[x] && this.gridData[x][y]) {\n                        this.gridData[x][y].hasPlayer = true;\n                    }\n                }\n            }\n        }\n\n        console.log(`总共恢复了 ${restoredCount} 个格子的状态`);\n    }\n\n    /**\n     * 清除所有游戏元素（数字、炸弹、标记、玩家头像等），但保留小格子\n     */\n    private clearAllGameElements() {\n        if (!this.boardNode) {\n            return;\n        }\n\n        const childrenToRemove: cc.Node[] = [];\n        const totalChildren = this.boardNode.children.length;\n\n      \n        // 遍历棋盘的所有子节点\n        for (let i = 0; i < this.boardNode.children.length; i++) {\n            const child = this.boardNode.children[i];\n            const nodeName = child.name;\n\n            \n\n            // 检查是否是需要清除的游戏元素（不包括小格子）\n            if (this.isGameElement(child, nodeName)) {\n                childrenToRemove.push(child);\n               \n            } else {\n             \n            }\n        }\n\n        // 移除找到的游戏元素\n        childrenToRemove.forEach((child, index) => {\n           \n            child.removeFromParent();\n        });\n\n       \n\n        // 暂时禁用强制清理，避免误删小格子\n        // this.forceCleanNonGridNodes();\n    }\n\n    /**\n     * 强制清理所有游戏预制体（除了Grid_开头的节点和分数控制器）\n     */\n    private forceCleanNonGridNodes() {\n        if (!this.boardNode) {\n            return;\n        }\n\n        const childrenToRemove: cc.Node[] = [];\n\n       \n\n        // 再次遍历，强制清除所有游戏预制体\n        for (let i = 0; i < this.boardNode.children.length; i++) {\n            const child = this.boardNode.children[i];\n            const nodeName = child.name;\n\n            // 保留条件：\n            // 1. Grid_开头的节点（小格子）\n            // 2. 包含Score的节点（分数控制器）\n            // 3. UI相关节点\n            const shouldKeep = nodeName.startsWith(\"Grid_\") ||\n                             nodeName.includes(\"Score\") ||\n                             nodeName.includes(\"score\") ||\n                             nodeName.includes(\"UI\") ||\n                             nodeName.includes(\"ui\") ||\n                             nodeName.includes(\"Canvas\") ||\n                             nodeName.includes(\"Background\");\n\n            if (!shouldKeep) {\n                childrenToRemove.push(child);\n               \n        }\n\n        // 移除找到的节点\n        childrenToRemove.forEach(child => {\n            child.removeFromParent();\n        });\n\n    \n    }\n    }\n    /**\n     * 判断节点是否是游戏元素（需要清除的），小格子和分数控制器不会被清除\n     */\n    private isGameElement(node: cc.Node, nodeName: string): boolean {\n        \n\n        //  绝对不清除的节点（小格子）\n        if (nodeName.startsWith(\"Grid_\") || nodeName === \"block\") {\n          \n            return false;\n        }\n\n        //  分数控制器不清除\n        if (nodeName.includes(\"Score\") || nodeName.includes(\"score\")) {\n            \n            return false;\n        }\n\n        //  UI相关节点不清除\n        if (nodeName.includes(\"UI\") || nodeName.includes(\"ui\")) {\n            \n            return false;\n        }\n\n        // 🗑️明确需要清除的游戏预制体\n\n        // 炸弹预制体\n        if (nodeName === \"Boom\") {\n           \n            return true;\n        }\n\n        // 数字预制体（Boom1, Boom2, Boom3 等）\n        if (nodeName.match(/^Boom\\d+$/)) {\n            \n            return true;\n        }\n\n        // 临时数字节点（NeighborMines_1, NeighborMines_2 等）\n        if (nodeName.match(/^NeighborMines_\\d+$/)) {\n          \n            return true;\n        }\n\n        // 测试节点（Test_x_y 格式）\n        if (nodeName.match(/^Test_\\d+_\\d+$/)) {\n          \n            return true;\n        }\n\n        // 玩家预制体（通过组件判断）\n        if (node.getComponent(\"PlayerGameController\")) {\n            \n            return true;\n        }\n\n        // 标记预制体\n        if (nodeName.includes(\"Flag\") || nodeName.includes(\"Mark\") || nodeName.includes(\"flag\") ||\n            nodeName === \"Biaoji\" || nodeName.includes(\"Biaoji\")) {\n            \n            return true;\n        }\n\n        // 玩家头像预制体\n        if (nodeName.includes(\"Player\") || nodeName.includes(\"Avatar\")) {\n            \n            return true;\n        }\n\n        //  默认保留未知节点（保守策略）\n       \n        return false;\n    }\n\n    /**\n     * 显示所有小格子（第二把游戏开始时恢复被隐藏的小格子）\n     */\n    private showAllGrids() {\n        if (!this.boardNode) {\n            return;\n        }\n\n        let totalGrids = 0;\n        let restoredGrids = 0;\n        let alreadyVisibleGrids = 0;\n\n      \n        // 遍历棋盘的所有子节点，找到小格子并显示\n        for (let i = 0; i < this.boardNode.children.length; i++) {\n            const child = this.boardNode.children[i];\n\n            // 如果是小格子节点\n            if (child.name.startsWith(\"Grid_\") || child.name === \"block\") {\n                totalGrids++;\n\n                // 记录恢复前的状态\n                const wasHidden = !child.active || child.opacity < 255 || child.scaleX < 1 || child.scaleY < 1;\n\n                if (wasHidden) {\n                   \n                    restoredGrids++;\n                } else {\n                    alreadyVisibleGrids++;\n                }\n\n                // 停止所有可能正在进行的动画\n                child.stopAllActions();\n\n                // 恢复显示状态\n                child.active = true;\n                child.opacity = 255;\n                child.scaleX = 1;\n                child.scaleY = 1;\n                child.angle = 0; // 重置旋转角度\n\n                // 恢复原始位置（如果有保存的话）\n                if (child['originalPosition']) {\n                    child.setPosition(child['originalPosition']);\n                }\n\n                // 确保格子可以交互\n                const button = child.getComponent(cc.Button);\n                if (button) {\n                    button.enabled = true;\n                }\n            }\n        }\n\n    }\n\n    /**\n     * 隐藏指定位置的小格子（点击时调用）\n     */\n    public hideGridAt(x: number, y: number) {\n        if (!this.isValidCoordinate(x, y)) {\n            console.warn(`隐藏格子失败：坐标(${x}, ${y})无效`);\n            return;\n        }\n\n        // 获取格子节点\n        const gridNode = this.gridNodes[x] && this.gridNodes[x][y];\n        if (gridNode) {\n            // 使用动画隐藏格子\n            cc.tween(gridNode)\n                .to(0.3, { opacity: 0, scaleX: 0, scaleY: 0 }, { easing: 'sineIn' })\n                .call(() => {\n                    gridNode.active = false;\n                })\n                .start();\n        }\n    }\n\n    /**\n     * 重新初始化棋盘数据\n     */\n    private reinitializeBoardData() {\n        // 重置gridData中的玩家状态\n        for (let x = 0; x < this.BOARD_SIZE; x++) {\n            for (let y = 0; y < this.BOARD_SIZE; y++) {\n                if (this.gridData[x][y]) {\n                    this.gridData[x][y].hasPlayer = false;\n                    this.gridData[x][y].playerNode = null;\n                }\n            }\n        }\n\n        // 清除坐标历史记录\n        this.clearCoordinateHistory();\n\n      \n    }\n\n    /**\n     * 清理所有玩家预制体（新回合开始时调用）\n     * 包括自己的头像和其他玩家的头像\n     */\n    public clearAllPlayerNodes() {\n\n\n        if (!this.boardNode) {\n            console.warn(\"棋盘节点不存在，无法清理\");\n            return;\n        }\n\n        let totalCleared = 0;\n\n        // 方法1: 清理存储在gridData中的玩家节点（自己的头像）\n        for (let x = 0; x < this.BOARD_SIZE; x++) {\n            for (let y = 0; y < this.BOARD_SIZE; y++) {\n                if (this.gridData[x][y].hasPlayer && this.gridData[x][y].playerNode) {\n                    // 移除玩家节点\n                    this.gridData[x][y].playerNode.removeFromParent();\n                    this.gridData[x][y].playerNode = null;\n                    this.gridData[x][y].hasPlayer = false;\n                    totalCleared++;\n                  \n                }\n            }\n        }\n\n        // 方法2: 清理棋盘上所有的玩家预制体节点（包括其他玩家的头像）\n        const childrenToRemove: cc.Node[] = [];\n\n        // 遍历棋盘的所有子节点\n        for (let i = 0; i < this.boardNode.children.length; i++) {\n            const child = this.boardNode.children[i];\n\n            // 检查是否是玩家预制体（通过组件判断）\n            const playerController = child.getComponent(PlayerGameController);\n            if (playerController) {\n                childrenToRemove.push(child);\n                totalCleared++;\n              \n            }\n        }\n\n        // 移除找到的玩家预制体\n        childrenToRemove.forEach(child => {\n            child.removeFromParent();\n           \n        });\n\n    \n    }\n\n    /**\n     * 在指定位置显示其他玩家的操作（参考自己头像的生成逻辑）\n     * @param x 格子x坐标\n     * @param y 格子y坐标\n     * @param actions 该位置的其他玩家操作列表\n     */\n    public displayOtherPlayersAtPosition(x: number, y: number, actions: any[]) {\n        if (!this.isValidCoordinate(x, y) || !actions || actions.length === 0) {\n            console.warn(`无效参数: (${x}, ${y}), actions: ${actions?.length || 0}`);\n            return;\n        }\n\n        \n        // 检查该位置是否已经有自己的头像\n        if (this.gridData[x][y].hasPlayer) {\n       \n            // 只有当真的有其他玩家时，才调整自己的头像位置\n            if (actions.length > 0) {\n                // 如果已有自己的头像且有其他玩家，需要使用多人布局策略\n                this.addOtherPlayersToExistingGrid(x, y, actions);\n            } else {\n               \n            }\n        } else {\n          \n            // 如果没有自己的头像，直接添加其他玩家头像\n            this.addOtherPlayersToEmptyGrid(x, y, actions);\n        }\n    }\n\n    /**\n     * 在已有自己头像的格子上添加其他玩家头像，并调整自己的头像位置和缩放\n     * @param x 格子x坐标\n     * @param y 格子y坐标\n     * @param actions 其他玩家操作列表\n     */\n    private addOtherPlayersToExistingGrid(x: number, y: number, actions: any[]) {\n        // 总玩家数 = 自己(1) + 其他玩家数量\n        const totalPlayers = 1 + actions.length;\n        const positions = this.getPlayerPositions(totalPlayers);\n\n       \n        // 第一步：调整自己的头像位置和缩放\n        // 注意：如果自己的头像是通过点击生成的，位置是正确的，应该调整\n        // 如果是通过后端消息生成的，也应该参与多人布局\n        const myPosition = positions[0]; // 第一个位置是自己的\n        this.adjustMyAvatarPosition(x, y, myPosition, actions);\n\n        // 第二步：从第二个位置开始放置其他玩家\n        for (let i = 0; i < actions.length; i++) {\n            const action = actions[i];\n            const position = positions[i + 1]; // 跳过第一个位置（自己的位置）\n\n         \n            // 使用棋盘坐标系创建其他玩家头像\n            this.createOtherPlayerAtBoardPosition(x, y, action, position, totalPlayers);\n        }\n    }\n\n    /**\n     * 调整自己的头像位置和缩放（当多人在同一格子时）\n     * @param x 格子x坐标\n     * @param y 格子y坐标\n     * @param position 新的位置和缩放信息\n     * @param actions 其他玩家操作列表\n     */\n    private adjustMyAvatarPosition(x: number, y: number, position: {x: number, y: number, scale: number}, actions: any[]) {\n      \n        // 查找自己的头像节点\n        if (!this.gridData[x][y].hasPlayer || !this.gridData[x][y].playerNode) {\n            console.warn(`在位置(${x}, ${y})找不到自己的头像节点`);\n            return;\n        }\n\n        const myPlayerNode = this.gridData[x][y].playerNode;\n\n        // 计算该格子的总人数（自己 + 其他玩家）\n        const totalPlayers = 1 + (actions ? actions.length : 0);\n\n        // 根据总人数计算基础位置\n        const basePosition = this.calculateBasePositionByPlayerCount(x, y, totalPlayers);\n\n        // 计算新的最终位置\n        const newPosition = cc.v2(\n            basePosition.x + position.x,\n            basePosition.y + position.y\n        );\n\n        // 播放平滑移动和缩小动画（多人格子情况）\n        this.playAvatarAdjustAnimation(myPlayerNode, newPosition, position.scale);\n\n      \n    }\n\n    /**\n     * 在空格子上添加其他玩家头像\n     * @param x 格子x坐标\n     * @param y 格子y坐标\n     * @param actions 其他玩家操作列表\n     */\n    private addOtherPlayersToEmptyGrid(x: number, y: number, actions: any[]) {\n        const totalPlayers = actions.length; // 空格子上只有其他玩家\n        const positions = this.getPlayerPositions(totalPlayers);\n\n        for (let i = 0; i < actions.length; i++) {\n            const action = actions[i];\n            const position = positions[i];\n\n          \n\n            // 使用棋盘坐标系创建其他玩家头像\n            this.createOtherPlayerAtBoardPosition(x, y, action, position, totalPlayers);\n        }\n    }\n\n    /**\n     * 在棋盘坐标系中创建其他玩家头像（参考自己头像的生成逻辑）\n     * @param gridX 格子x坐标\n     * @param gridY 格子y坐标\n     * @param action 玩家操作数据\n     * @param relativePosition 相对于格子中心的位置和缩放\n     * @param totalPlayers 该格子的总人数\n     */\n    private createOtherPlayerAtBoardPosition(gridX: number, gridY: number, action: any, relativePosition: {x: number, y: number, scale: number}, totalPlayers: number) {\n        if (!this.playerGamePrefab) {\n            console.error(\"playerGamePrefab 预制体未设置\");\n            return;\n        }\n\n        if (!this.boardNode) {\n            console.error(\"棋盘节点未设置\");\n            return;\n        }\n\n        // 创建玩家预制体实例\n        const playerNode = cc.instantiate(this.playerGamePrefab);\n       \n\n        // 根据总人数计算基础位置（统一逻辑）\n        const basePosition = this.calculateBasePositionByPlayerCount(gridX, gridY, totalPlayers);\n\n        // 添加相对偏移\n        const finalPosition = cc.v2(\n            basePosition.x + relativePosition.x,\n            basePosition.y + relativePosition.y\n        );\n\n        playerNode.setPosition(finalPosition);\n        playerNode.setScale(relativePosition.scale);\n\n       \n\n        // 先隐藏节点，等头像加载完成后再显示\n        playerNode.active = false;\n\n        // 安全地添加到棋盘节点（参考自己头像的添加逻辑）\n        this.addPlayerNodeSafely(playerNode);\n\n        // 设置其他玩家的头像和数据\n        this.setupOtherPlayerData(playerNode, action, () => {\n            // 头像加载完成的回调\n            if (totalPlayers === 1) {\n                // 单人格子：播放生成动画\n                this.playAvatarSpawnAnimation(playerNode);\n            } else {\n                // 多人格子：直接显示（其他人是新生成的，不需要动画）\n                playerNode.active = true;\n            }\n           \n        });\n    }\n\n    /**\n     * 设置其他玩家的数据（参考自己头像的设置逻辑）\n     * @param playerNode 玩家节点\n     * @param action 玩家操作数据\n     * @param onComplete 完成回调\n     */\n    private setupOtherPlayerData(playerNode: cc.Node, action: any, onComplete: () => void) {\n        try {\n           \n\n            const playerController = playerNode.getComponent(PlayerGameController);\n            if (!playerController) {\n                console.error(\"❌ 找不到PlayerGameController组件\");\n                return;\n            }\n\n          \n\n            // 从GlobalBean中获取真实的玩家数据\n            const realUserData = this.getRealUserData(action.userId);\n            if (!realUserData) {\n                console.warn(`找不到用户 ${action.userId} 的真实数据`);\n                return;\n            }\n\n           \n\n            // 在节点上存储userId信息，用于后续分数显示匹配\n            playerNode['userId'] = action.userId;\n           \n\n            // 使用延迟设置，参考自己头像的设置逻辑\n            this.scheduleOnce(() => {\n                if (typeof playerController.setData === 'function') {\n                    playerController.setData(realUserData);\n                }\n\n                // 根据操作类型设置旗子显示\n                const withFlag = (action.action === 2); // action=2表示标记操作，显示旗子\n                if (playerController.flagNode) {\n                    playerController.flagNode.active = withFlag;\n\n                }\n\n                // 调用完成回调\n                if (onComplete) {\n                    onComplete();\n                }\n            }, 0.1);\n\n        } catch (error) {\n            console.warn(`设置其他玩家数据时出错: ${error}`);\n        }\n    }\n\n    /**\n     * 根据玩家数量获取布局位置\n     * @param playerCount 玩家数量\n     * @returns 位置数组 {x: number, y: number, scale: number}[]\n     */\n    private getPlayerPositions(playerCount: number): {x: number, y: number, scale: number}[] {\n        switch (playerCount) {\n            case 1:\n                // 单个玩家，居中显示，正常大小\n                return [{x: 0, y: 0, scale: 1.0}];\n\n            case 2:\n                // 两个玩家，左右分布，缩放0.5\n                return [\n                    {x: -22, y: -8, scale: 0.5}, // 左\n                    {x: 22, y: -8, scale: 0.5}   // 右\n                ];\n\n            case 3:\n                // 三个玩家，上中下分布，缩放0.5\n                return [\n                    {x: 0, y: 12, scale: 0.5},    // 上\n                    {x: -23, y: -27, scale: 0.5}, // 左下\n                    {x: 23, y: -27, scale: 0.5}   // 右下\n                ];\n\n            case 4:\n                // 四个玩家，四角分布，缩放0.5\n                return [\n                    {x: -22, y: 12, scale: 0.5},  // 左上\n                    {x: 22, y: 12, scale: 0.5},   // 右上\n                    {x: -22, y: -30, scale: 0.5}, // 左下\n                    {x: 22, y: -30, scale: 0.5}   // 右下\n                ];\n\n            default:\n                // 超过4个玩家，只显示前4个\n                console.warn(`玩家数量过多: ${playerCount}，只显示前4个`);\n                return this.getPlayerPositions(4);\n        }\n    }\n\n    /**\n     * 获取指定位置的格子节点\n     * @param x 格子x坐标\n     * @param y 格子y坐标\n     * @returns 格子节点或null\n     */\n    private getGridNode(x: number, y: number): cc.Node | null {\n        if (!this.boardNode || !this.isValidCoordinate(x, y)) {\n            return null;\n        }\n\n        // 计算在棋盘子节点中的索引 (8x8棋盘，从左到右，从上到下)\n        const index = y * this.BOARD_SIZE + x;\n\n        if (index >= 0 && index < this.boardNode.children.length) {\n            return this.boardNode.children[index];\n        }\n\n        return null;\n    }\n\n    /**\n     * 在指定位置创建玩家预制体节点\n     * @param gridNode 格子节点\n     * @param action 玩家操作数据\n     * @param position 相对位置和缩放\n     */\n    private createPlayerNodeAtPosition(gridNode: cc.Node, action: any, position: {x: number, y: number, scale: number}) {\n        if (!this.playerGamePrefab) {\n            console.error(\"playerGamePrefab 预制体未设置\");\n            return;\n        }\n\n        // 创建玩家预制体实例\n        const playerNode = cc.instantiate(this.playerGamePrefab);\n      \n\n        // 检查预制体上的组件\n        const components = playerNode.getComponents(cc.Component);\n     \n        components.forEach((comp, index) => {\n            \n        });\n\n        // 设置位置和缩放\n        playerNode.setPosition(position.x, position.y);\n        playerNode.setScale(position.scale);\n       \n\n        // 添加到格子节点\n        gridNode.addChild(playerNode);\n\n        // 设置玩家数据\n        this.setupPlayerNodeData(playerNode, action);\n\n        \n    }\n\n    /**\n     * 设置玩家节点数据\n     * @param playerNode 玩家节点\n     * @param action 玩家操作数据\n     */\n    private setupPlayerNodeData(playerNode: cc.Node, action: any) {\n        try {\n           \n\n            const playerController = playerNode.getComponent(PlayerGameController);\n            if (!playerController) {\n                console.error(\"❌ 找不到PlayerGameController组件\");\n             \n                const allComponents = playerNode.getComponents(cc.Component);\n                allComponents.forEach((comp, index) => {\n                   \n                });\n                return;\n            }\n\n         \n            // 从GlobalBean中获取真实的玩家数据\n            const realUserData = this.getRealUserData(action.userId);\n            if (!realUserData) {\n                console.warn(`找不到用户 ${action.userId} 的真实数据`);\n                return;\n            }\n\n           \n\n            if (typeof playerController.setData === 'function') {\n                playerController.setData(realUserData);\n            }\n\n            // 根据操作类型设置旗子显示\n            const withFlag = (action.action === 2); // action=2表示标记操作，显示旗子\n            if (playerController.flagNode) {\n                playerController.flagNode.active = withFlag;\n               \n            } else {\n                console.warn(\"找不到flagNode节点\");\n            }\n\n        } catch (error) {\n            console.warn(`设置玩家节点数据时出错: ${error}`);\n        }\n    }\n\n    /**\n     * 从GlobalBean中获取真实的用户数据\n     * @param userId 用户ID\n     * @returns RoomUser 或 null\n     */\n    private getRealUserData(userId: string): RoomUser | null {\n        try {\n            if (!GlobalBean.GetInstance().noticeStartGame || !GlobalBean.GetInstance().noticeStartGame.users) {\n                console.warn(\"没有游戏数据，无法获取用户信息\");\n                return null;\n            }\n\n            const users = GlobalBean.GetInstance().noticeStartGame.users;\n            const user = users.find((u: RoomUser) => u.userId === userId);\n\n            if (user) {\n                \n                return user;\n            } else {\n                console.warn(`未找到用户 ${userId} 的数据`);\n                return null;\n            }\n        } catch (error) {\n            console.error(`获取用户数据时出错: ${error}`);\n            return null;\n        }\n    }\n\n    /**\n     * 在指定位置的玩家节点上显示分数\n     * @param x 格子x坐标\n     * @param y 格子y坐标\n     * @param score 分数\n     * @param showPlusOne 是否显示+1（先手奖励）\n     */\n    public showScoreOnPlayerNode(x: number, y: number, score: number, showPlusOne: boolean) {\n       \n        if (!this.isValidCoordinate(x, y)) {\n            console.warn(`无效坐标: (${x}, ${y})`);\n            return;\n        }\n\n        // 查找该位置的玩家节点\n        const playerNode = this.findPlayerNodeAtPosition(x, y);\n        if (!playerNode) {\n            console.warn(`在位置(${x}, ${y})找不到玩家节点`);\n            return;\n        }\n\n        // 获取PlayerGameController组件\n        const playerController = playerNode.getComponent(PlayerGameController);\n        if (!playerController) {\n            console.warn(\"找不到PlayerGameController组件\");\n            return;\n        }\n\n        // 显示分数动画\n        if (showPlusOne) {\n            // 先显示+1，再显示本回合得分\n            this.showScoreAnimationOnNode(playerController, 1, () => {\n                this.scheduleOnce(() => {\n                    this.showScoreAnimationOnNode(playerController, score, null);\n                }, 1.0);\n            });\n        } else {\n            // 只显示本回合得分\n            this.showScoreAnimationOnNode(playerController, score, null);\n        }\n    }\n\n    /**\n     * 查找指定位置的玩家节点\n     * @param x 格子x坐标\n     * @param y 格子y坐标\n     * @returns 玩家节点或null\n     */\n    private findPlayerNodeAtPosition(x: number, y: number): cc.Node | null {\n        // 方法1: 从gridData中查找（自己的头像）\n        if (this.gridData[x][y].hasPlayer && this.gridData[x][y].playerNode) {\n            return this.gridData[x][y].playerNode;\n        }\n\n        // 方法2: 在棋盘上查找其他玩家的头像\n        if (!this.boardNode) {\n            return null;\n        }\n\n        // 计算该位置的世界坐标\n        const targetPosition = this.calculateCorrectPosition(x, y);\n\n        // 遍历棋盘上的所有玩家节点，找到最接近目标位置的\n        let closestNode: cc.Node | null = null;\n        let minDistance = Number.MAX_VALUE;\n\n        for (let i = 0; i < this.boardNode.children.length; i++) {\n            const child = this.boardNode.children[i];\n            const playerController = child.getComponent(PlayerGameController);\n\n            if (playerController) {\n                const distance = cc.Vec2.distance(child.getPosition(), targetPosition);\n                if (distance < minDistance && distance < 50) { // 50像素的容差\n                    minDistance = distance;\n                    closestNode = child;\n                }\n            }\n        }\n\n        return closestNode;\n    }\n\n    /**\n     * 在节点上显示分数动画\n     * @param playerController 玩家控制器\n     * @param score 分数\n     * @param onComplete 完成回调\n     */\n    private showScoreAnimationOnNode(playerController: any, score: number, onComplete: (() => void) | null) {\n      \n\n        // TODO: 实现在player_game_pfb上显示分数动画的逻辑\n        // 这里需要根据PlayerGameController的具体实现来显示分数\n\n        if (onComplete) {\n            this.scheduleOnce(onComplete, 1.0);\n        }\n    }\n\n    /**\n     * 让指定位置的所有头像消失（参考回合结束时的清理逻辑）\n     * @param x 格子x坐标\n     * @param y 格子y坐标\n     * @param onComplete 完成回调\n     */\n    public hideAvatarsAtPosition(x: number, y: number, onComplete: () => void) {\n        \n\n        if (!this.boardNode) {\n            console.warn(\"棋盘节点不存在，无法清理头像\");\n            onComplete();\n            return;\n        }\n\n        // 收集所有头像节点（参考clearAllPlayerNodes的逻辑）\n        const avatarNodes: cc.Node[] = [];\n\n        // 方法1: 收集存储在gridData中的玩家节点（自己的头像）\n        for (let gx = 0; gx < this.BOARD_SIZE; gx++) {\n            for (let gy = 0; gy < this.BOARD_SIZE; gy++) {\n                if (this.gridData[gx][gy].hasPlayer && this.gridData[gx][gy].playerNode) {\n                    avatarNodes.push(this.gridData[gx][gy].playerNode);\n                   \n                }\n            }\n        }\n\n        // 方法2: 收集棋盘上所有的玩家预制体节点（包括其他玩家的头像）\n        for (let i = 0; i < this.boardNode.children.length; i++) {\n            const child = this.boardNode.children[i];\n\n            // 检查是否是玩家预制体（通过组件判断，参考clearAllPlayerNodes）\n            const playerController = child.getComponent(PlayerGameController);\n            if (playerController) {\n                // 避免重复添加（可能已经在方法1中添加过）\n                if (!avatarNodes.includes(child)) {\n                    avatarNodes.push(child);\n                  \n                }\n            }\n        }\n\n      \n\n        if (avatarNodes.length === 0) {\n            // 没有头像需要消失\n          \n            onComplete();\n            return;\n        }\n\n        let completedCount = 0;\n        const totalCount = avatarNodes.length;\n\n        // 为每个头像播放消失动画\n        avatarNodes.forEach((avatarNode, index) => {\n            \n            // 使用cc.Tween播放消失动画\n            cc.tween(avatarNode)\n                .to(0.3, { opacity: 0, scaleX: 0.5, scaleY: 0.5 }, { easing: 'sineIn' })\n                .call(() => {\n                    // 动画完成后移除节点\n                    avatarNode.removeFromParent();\n                    completedCount++;\n                   \n\n                    // 所有头像都消失完成后，执行回调\n                    if (completedCount >= totalCount) {\n                       \n                        // 清除所有自己头像的引用（参考clearAllPlayerNodes）\n                        this.clearAllMyAvatarReferences();\n\n                        onComplete();\n                    }\n                })\n                .start();\n        });\n    }\n\n    /**\n     * 清除所有自己头像的引用（参考clearAllPlayerNodes的逻辑）\n     */\n    private clearAllMyAvatarReferences() {\n       \n        for (let x = 0; x < this.BOARD_SIZE; x++) {\n            for (let y = 0; y < this.BOARD_SIZE; y++) {\n                if (this.gridData[x][y].hasPlayer) {\n                    this.gridData[x][y].hasPlayer = false;\n                    this.gridData[x][y].playerNode = null;\n                  \n                }\n            }\n        }\n    }\n\n    /**\n     * 隐藏指定位置的格子（不销毁，以便重置时可以重新显示）\n     * @param x 格子x坐标\n     * @param y 格子y坐标\n     * @param immediate 是否立即隐藏（不播放动画）\n     */\n    public removeGridAt(x: number, y: number, immediate: boolean = false) {\n        if (!this.isValidCoordinate(x, y)) {\n            console.warn(`隐藏格子失败：坐标(${x}, ${y})无效`);\n            return;\n        }\n\n        // 获取格子节点\n        const gridNode = this.gridNodes[x] && this.gridNodes[x][y];\n        if (gridNode) {\n            if (immediate) {\n                // 立即隐藏，不播放动画\n                gridNode.active = false;\n            } else {\n                // 播放四边形格子消失动画\n                this.playGridFallAnimation(gridNode);\n            }\n        }\n    }\n\n    /**\n     * 播放四边形格子消失动画\n     * 效果：格子持续旋转，给一个随机向上的力，然后旋转着自由落体\n     * @param gridNode 格子节点\n     */\n    private playGridFallAnimation(gridNode: cc.Node) {\n        if (!gridNode) return;\n\n        // 停止该格子上所有正在进行的动画（包括震动动画）\n        gridNode.stopAllActions();\n\n        // 保存格子的原始位置（用于重置时恢复）\n        if (!gridNode['originalPosition']) {\n            gridNode['originalPosition'] = gridNode.getPosition();\n        }\n\n        // 随机选择向上的力的方向：0=向上，1=右上15度，2=左上15度\n        const forceDirection = Math.floor(Math.random() * 3);\n        let moveX = 0;\n        let moveY = 200; // 向上的基础距离（增加高度）\n\n        switch (forceDirection) {\n            case 0: // 向上\n                moveX = 0;\n                break;\n            case 1: // 右上20度\n                moveX = Math.sin(20 * Math.PI / 180) * moveY;\n                break;\n            case 2: // 左上20度\n                moveX = -Math.sin(20 * Math.PI / 180) * moveY;\n                break;\n        }\n\n        // 随机旋转速度\n        const rotationSpeed = (Math.random() * 1440 + 720) * (Math.random() > 0.5 ? 1 : -1); // 720-2160度/秒，随机方向\n\n        // 动画参数\n        const upTime = 0.15; // 向上运动时间\n        const fallTime = 0.3; // 下落时间\n        const initialPosition = gridNode.getPosition();\n\n        // 创建持续旋转的动画\n        const rotationTween = cc.tween(gridNode)\n            .repeatForever(\n                cc.tween().by(0.1, { angle: rotationSpeed * 0.1 })\n            );\n\n        // 创建分阶段的运动动画\n        const movementTween = cc.tween(gridNode)\n            // 第一阶段：向上抛出\n            .to(upTime, {\n                x: initialPosition.x + moveX,\n                y: initialPosition.y + moveY\n            }, { easing: 'quadOut' })\n            // 第二阶段：自由落体\n            .to(fallTime, {\n                x: initialPosition.x + moveX + (Math.random() - 0.5) * 100, // 添加更多随机水平偏移\n                y: initialPosition.y - 500 // 下落到屏幕下方更远处\n            }, { easing: 'quadIn' })\n            .call(() => {\n                // 动画结束后隐藏格子\n                gridNode.active = false;\n                // 停止旋转动画\n                gridNode.stopAllActions();\n            });\n\n        // 同时开始旋转和移动动画\n        rotationTween.start();\n        movementTween.start();\n    }\n\n    /**\n     * 在指定位置创建boom预制体\n     * @param x 格子x坐标\n     * @param y 格子y坐标\n     * @param isCurrentUser 是否是当前用户点到的雷（可选，默认为true以保持向后兼容）\n     */\n    public createBoomPrefab(x: number, y: number, isCurrentUser: boolean = true) {\n\n\n        if (!this.boomPrefab) {\n            console.error(\"boomPrefab 预制体未设置，请在编辑器中挂载\");\n            return;\n        }\n\n        // 实例化您的boom预制体\n        const boomNode = cc.instantiate(this.boomPrefab);\n        boomNode.name = \"Boom\";\n\n        // 设置位置（使用新的精确位置计算）\n        const position = this.calculatePrefabPosition(x, y);\n        boomNode.setPosition(position);\n\n        // 添加到棋盘\n        this.addPlayerNodeSafely(boomNode);\n\n        // 播放出现动画\n        boomNode.setScale(0);\n        cc.tween(boomNode)\n            .to(0.3, { scaleX: 1.2, scaleY: 1.2 }, { easing: 'backOut' })\n            .to(0.1, { scaleX: 1.0, scaleY: 1.0 })\n            .start();\n\n        // 只有当前用户点到雷时才播放棋盘震动效果\n        // 延迟0.45秒，等格子下落动画完成后再播放震动\n        if (isCurrentUser) {\n            this.scheduleOnce(() => {\n                this.playBoardShakeAnimation();\n            }, 0.45);\n        }\n\n    }\n\n    /**\n     * 在指定位置创建biaoji预制体\n     * @param x 格子x坐标\n     * @param y 格子y坐标\n     */\n    public createBiaojiPrefab(x: number, y: number) {\n       \n        if (!this.biaojiPrefab) {\n            console.error(\"biaojiPrefab 预制体未设置，请在编辑器中挂载\");\n            return;\n        }\n\n        // 实例化您的biaoji预制体\n        const biaojiNode = cc.instantiate(this.biaojiPrefab);\n        biaojiNode.name = \"Biaoji\";\n\n        // 设置位置（使用新的精确位置计算）\n        const position = this.calculatePrefabPosition(x, y);\n        biaojiNode.setPosition(position);\n\n        // 添加到棋盘\n        this.addPlayerNodeSafely(biaojiNode);\n\n        // 播放出现动画\n        biaojiNode.setScale(0);\n        cc.tween(biaojiNode)\n            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })\n            .start();\n\n       \n    }\n\n    /**\n     * 更新指定位置的neighborMines显示（使用boom数字预制体）\n     * @param x 格子x坐标\n     * @param y 格子y坐标\n     * @param neighborMines 周围地雷数量\n     */\n    public updateNeighborMinesDisplay(x: number, y: number, neighborMines: number) {\n      \n\n        // 0不需要显示数字\n        if (neighborMines === 0) {\n          \n            return;\n        }\n\n        // 直接使用boom数字预制体\n        this.createNumberPrefab(x, y, neighborMines);\n    }\n\n    /**\n     * 创建数字预制体（boom1, boom2, ...）\n     * @param x 格子x坐标\n     * @param y 格子y坐标\n     * @param number 数字\n     */\n    private createNumberPrefab(x: number, y: number, number: number) {\n       \n\n        // 根据数字选择对应的预制体\n        let prefab: cc.Prefab = null;\n        switch (number) {\n            case 1: prefab = this.boom1Prefab; break;\n            case 2: prefab = this.boom2Prefab; break;\n            case 3: prefab = this.boom3Prefab; break;\n            case 4: prefab = this.boom4Prefab; break;\n            case 5: prefab = this.boom5Prefab; break;\n            case 6: prefab = this.boom6Prefab; break;\n            case 7: prefab = this.boom7Prefab; break;\n            case 8: prefab = this.boom8Prefab; break;\n            default:\n                console.error(`不支持的数字: ${number}`);\n                return;\n        }\n\n        if (!prefab) {\n            console.error(`boom${number}Prefab 预制体未设置，请在编辑器中挂载`);\n            return;\n        }\n\n        // 实例化数字预制体\n        const numberNode = cc.instantiate(prefab);\n        numberNode.name = `Boom${number}`;\n\n        // 设置位置（使用新的精确位置计算）\n        const position = this.calculatePrefabPosition(x, y);\n        numberNode.setPosition(position);\n\n        // 添加到棋盘\n        this.addPlayerNodeSafely(numberNode);\n\n        // 播放出现动画\n        numberNode.setScale(0);\n        cc.tween(numberNode)\n            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })\n            .start();\n\n       \n    }\n\n    /**\n     * 加载数字预制体\n     * @param x 格子x坐标\n     * @param y 格子y坐标\n     * @param number 数字\n     */\n    private loadNumberPrefab(x: number, y: number, number: number) {\n        const prefabName = `${number}boom`;\n       \n     \n        this.createTemporaryNumberNode(x, y, number);\n    }\n\n    /**\n     * 创建临时的数字节点（在预制体加载失败时使用）\n     * @param x 格子x坐标\n     * @param y 格子y坐标\n     * @param number 数字\n     */\n    private createTemporaryNumberNode(x: number, y: number, number: number) {\n        \n        // 创建数字显示节点\n        const numberNode = new cc.Node(`NeighborMines_${number}`);\n        const label = numberNode.addComponent(cc.Label);\n\n        // 设置数字显示 - 更大的字体和居中对齐\n        label.string = number.toString();\n        label.fontSize = 48; // 增大字体\n        label.node.color = this.getNumberColor(number);\n        label.horizontalAlign = cc.Label.HorizontalAlign.CENTER;\n        label.verticalAlign = cc.Label.VerticalAlign.CENTER;\n\n        // 设置节点大小，确保居中\n        numberNode.setContentSize(this.GRID_SIZE, this.GRID_SIZE);\n\n        // 设置位置 - 使用格子中心位置\n        const position = this.calculateCorrectPosition(x, y);\n        numberNode.setPosition(position);\n\n        // 添加到棋盘\n        this.addPlayerNodeSafely(numberNode);\n\n        // 播放出现动画\n        this.playNumberAppearAnimation(numberNode, number);\n\n       \n    }\n\n    /**\n     * 设置数字节点（用于预制体）\n     * @param numberNode 数字节点\n     * @param x 格子x坐标\n     * @param y 格子y坐标\n     * @param number 数字\n     */\n    private setupNumberNode(numberNode: cc.Node, x: number, y: number, number: number) {\n       \n\n        // 设置位置 - 使用格子中心位置\n        const position = this.calculateCorrectPosition(x, y);\n        numberNode.setPosition(position);\n\n        // 添加到棋盘\n        this.addPlayerNodeSafely(numberNode);\n\n        // 播放出现动画\n        this.playNumberAppearAnimation(numberNode, number);\n\n       \n    }\n\n    /**\n     * 播放数字出现动画\n     * @param numberNode 数字节点\n     * @param number 数字\n     */\n    private playNumberAppearAnimation(numberNode: cc.Node, number: number) {\n        // 使用cc.Tween播放数字出现动画\n        numberNode.setScale(0);\n        cc.tween(numberNode)\n            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })\n            .start();\n\n      \n    }\n\n    /**\n     * 播放格子消失动画（连锁效果）\n     * @param x 格子x坐标\n     * @param y 格子y坐标\n     * @param neighborMines 周围地雷数量\n     */\n    public playGridDisappearAnimation(x: number, y: number, neighborMines: number) {\n     \n\n        // 先删除格子\n        this.removeGridAt(x, y);\n\n        // 延迟0.3秒后显示数字（等格子消失动画完成）\n        this.scheduleOnce(() => {\n            this.updateNeighborMinesDisplay(x, y, neighborMines);\n        }, 0.3);\n    }\n\n    /**\n     * 根据数字获取颜色\n     * @param number 数字\n     * @returns 颜色\n     */\n    private getNumberColor(number: number): cc.Color {\n        switch (number) {\n            case 1: return cc.Color.BLUE;\n            case 2: return cc.Color.GREEN;\n            case 3: return cc.Color.RED;\n            case 4: return cc.Color.MAGENTA;\n            case 5: return cc.Color.YELLOW;\n            case 6: return cc.Color.CYAN;\n            case 7: return cc.Color.BLACK;\n            case 8: return cc.Color.GRAY;\n            default: return cc.Color.BLACK;\n        }\n    }\n\n    /**\n     * 播放棋盘震动动画（包括所有小格子）\n     */\n    private playBoardShakeAnimation() {\n        if (!this.boardNode) {\n            console.warn(\"boardNode 未设置，无法播放震动效果\");\n            return;\n        }\n\n        // 震动参数 - 增强震动效果\n        const shakeIntensity = 30; // 震动强度\n        const shakeDuration = 1.0; // 震动持续时间\n        const shakeFrequency = 40; // 震动频率\n\n        // 震动棋盘\n        this.shakeBoardNode(shakeIntensity, shakeDuration, shakeFrequency);\n\n        // 震动所有小格子\n        this.shakeAllGrids(shakeIntensity * 0.6, shakeDuration, shakeFrequency);\n    }\n\n    /**\n     * 震动棋盘节点\n     */\n    private shakeBoardNode(intensity: number, duration: number, frequency: number) {\n        // 保存原始位置\n        const originalPosition = this.boardNode.position.clone();\n\n        // 创建震动动画，使用递减强度\n        let currentIntensity = intensity;\n        const intensityDecay = 0.92; // 强度衰减系数\n\n        const createShakeStep = (shakeIntensity: number) => {\n            return cc.tween()\n                .to(0.025, {\n                    x: originalPosition.x + (Math.random() - 0.5) * shakeIntensity * 2,\n                    y: originalPosition.y + (Math.random() - 0.5) * shakeIntensity * 2\n                });\n        };\n\n        // 创建震动序列，强度逐渐衰减\n        let shakeTween = cc.tween(this.boardNode);\n        const totalSteps = Math.floor(duration * frequency);\n\n        for (let i = 0; i < totalSteps; i++) {\n            shakeTween = shakeTween.then(createShakeStep(currentIntensity));\n            currentIntensity *= intensityDecay; // 逐渐减弱震动强度\n        }\n\n        // 最后恢复到原位置\n        shakeTween.to(0.2, {\n            x: originalPosition.x,\n            y: originalPosition.y\n        }, { easing: 'backOut' })\n        .start();\n    }\n\n    /**\n     * 震动所有小格子\n     */\n    private shakeAllGrids(intensity: number, duration: number, frequency: number) {\n        if (!this.gridNodes) return;\n\n        // 遍历所有格子节点\n        for (let x = 0; x < this.gridNodes.length; x++) {\n            if (!this.gridNodes[x]) continue;\n\n            for (let y = 0; y < this.gridNodes[x].length; y++) {\n                const gridNode = this.gridNodes[x][y];\n                if (!gridNode || !gridNode.active) continue;\n\n                // 为每个格子创建独立的震动动画\n                this.shakeGridNode(gridNode, intensity, duration, frequency);\n            }\n        }\n    }\n\n    /**\n     * 震动单个格子节点\n     */\n    private shakeGridNode(gridNode: cc.Node, intensity: number, duration: number, frequency: number) {\n        // 保存原始位置\n        const originalPosition = gridNode.position.clone();\n\n        // 为每个格子添加随机延迟，创造波浪效果\n        const randomDelay = Math.random() * 0.1;\n\n        this.scheduleOnce(() => {\n            // 创建震动动画，使用递减强度\n            let currentIntensity = intensity;\n            const intensityDecay = 0.94; // 格子震动衰减稍慢一些\n\n            const createGridShakeStep = (shakeIntensity: number) => {\n                return cc.tween()\n                    .to(0.02, {\n                        x: originalPosition.x + (Math.random() - 0.5) * shakeIntensity * 2,\n                        y: originalPosition.y + (Math.random() - 0.5) * shakeIntensity * 2\n                    });\n            };\n\n            // 创建震动序列\n            let shakeTween = cc.tween(gridNode);\n            const totalSteps = Math.floor(duration * frequency * 0.8); // 格子震动时间稍短\n\n            for (let i = 0; i < totalSteps; i++) {\n                shakeTween = shakeTween.then(createGridShakeStep(currentIntensity));\n                currentIntensity *= intensityDecay;\n            }\n\n            // 最后恢复到原位置\n            shakeTween.to(0.15, {\n                x: originalPosition.x,\n                y: originalPosition.y\n            }, { easing: 'backOut' })\n            .start();\n        }, randomDelay);\n    }\n\n    /**\n     * 从联机模式数组格式恢复地图状态\n     * @param onlineArray 联机模式数组，每个元素包含格子的完整状态信息\n     */\n    private restoreFromOnlineArray(onlineArray: any[]) {\n        console.log(\"从联机模式数组恢复地图状态，数组内容:\", onlineArray);\n\n        let restoredCount = 0;\n        let skippedCount = 0;\n\n        onlineArray.forEach((cellInfo: any) => {\n            const x = cellInfo.x;\n            const y = cellInfo.y;\n\n            if (!this.isValidCoordinate(x, y)) {\n                return;\n            }\n\n            // 检查是否已挖掘\n            if (!cellInfo.isRevealed) {\n                return;\n            }\n\n            // 获取格子节点\n            const gridNode = this.gridNodes[x] && this.gridNodes[x][y];\n            if (!gridNode) {\n                console.warn(`格子节点不存在: (${x}, ${y})`);\n                return;\n            }\n\n            // 检查格子是否已经被处理过（已经隐藏）\n            if (!gridNode.active || gridNode.opacity === 0) {\n                console.log(`跳过已处理的格子: (${x}, ${y}), active=${gridNode.active}, opacity=${gridNode.opacity}`);\n                skippedCount++;\n                return;\n            }\n\n            console.log(`恢复已挖掘方块: (${x}, ${y}), 周围地雷数: ${cellInfo.neighborMines}`);\n            console.log(`格子 (${x}, ${y}) 当前状态: active=${gridNode.active}, opacity=${gridNode.opacity}`);\n            restoredCount++;\n\n            // 立即隐藏格子（不播放动画）\n            gridNode.active = false;\n            gridNode.opacity = 0;\n            gridNode.scaleX = 0;\n            gridNode.scaleY = 0;\n\n            console.log(`格子 (${x}, ${y}) 隐藏后状态: active=${gridNode.active}, opacity=${gridNode.opacity}`);\n\n            // 显示挖掘结果\n            if (cellInfo.neighborMines > 0) {\n                console.log(`创建数字预制体 (${x}, ${y}), 数字:`, cellInfo.neighborMines);\n                this.scheduleOnce(() => {\n                    this.createNumberPrefab(x, y, cellInfo.neighborMines);\n                }, 0.1);\n            } else {\n                console.log(`格子 (${x}, ${y}) 周围无地雷，不创建数字预制体`);\n            }\n\n            // 标记格子已被处理\n            if (this.gridData[x] && this.gridData[x][y]) {\n                this.gridData[x][y].hasPlayer = true;\n            }\n        });\n\n        console.log(`联机模式断线重连恢复统计: 新恢复=${restoredCount}个, 跳过已处理=${skippedCount}个, 总数据=${onlineArray.length}个`);\n    }\n\n    /**\n     * 从对象格式恢复地图状态\n     * @param mapData 包含revealedBlocks和markedBlocks的对象\n     */\n    private restoreFromBlockLists(mapData: any) {\n        console.log(\"从对象格式恢复地图状态\");\n\n        // 恢复已挖掘的方块\n        if (mapData.revealedBlocks && Array.isArray(mapData.revealedBlocks)) {\n            console.log(\"恢复已挖掘的方块数量:\", mapData.revealedBlocks.length);\n            mapData.revealedBlocks.forEach((block: any) => {\n                const x = block.x;\n                const y = block.y;\n                const neighborMines = block.neighborMines;\n\n                if (this.isValidCoordinate(x, y)) {\n                    console.log(`恢复已挖掘方块: (${x}, ${y}), 周围地雷数: ${neighborMines}`);\n\n                    // 立即隐藏格子（不播放动画）\n                    this.hideGridAt(x, y);\n\n                    // 显示挖掘结果\n                    if (neighborMines > 0) {\n                        // 延迟创建数字预制体，确保格子先隐藏\n                        this.scheduleOnce(() => {\n                            this.createNumberPrefab(x, y, neighborMines);\n                        }, 0.1);\n                    }\n\n                    // 标记格子已被处理\n                    if (this.gridData[x] && this.gridData[x][y]) {\n                        this.gridData[x][y].hasPlayer = true;\n                    }\n                }\n            });\n        }\n\n        // 恢复已标记的方块\n        if (mapData.markedBlocks && Array.isArray(mapData.markedBlocks)) {\n            console.log(\"恢复已标记的方块数量:\", mapData.markedBlocks.length);\n            mapData.markedBlocks.forEach((block: any) => {\n                const x = block.x;\n                const y = block.y;\n\n                if (this.isValidCoordinate(x, y)) {\n                    console.log(`恢复已标记方块: (${x}, ${y})`);\n                    // 创建标记预制体\n                    this.createBiaojiPrefab(x, y);\n                }\n            });\n        }\n    }\n\n    // update (dt) {}\n}\n"]}