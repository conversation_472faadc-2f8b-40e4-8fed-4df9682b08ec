{"version": 3, "sources": ["assets/scripts/level/LevelPageController.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;AAGtF,uEAAkE;AAClE,uCAAsC;AACtC,yCAAwC;AACxC,sEAA+E;AAC/E,uFAAkF;AAClF,6FAAwF;AACxF,4DAA2D;AAC3D,8CAA6C;AAC7C,6CAA4C;AAC5C,qDAAkD;AAG5C,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAG5C;IAAiD,uCAAY;IAA7D;QAAA,qEAssCC;QApsCG,OAAO;QAEP,gBAAU,GAAY,IAAI,CAAC;QAE3B,SAAS;QAET,qBAAe,GAAc,IAAI,CAAC;QAElC,UAAU;QAEV,oBAAc,GAAa,IAAI,CAAC;QAEhC,YAAY;QAEZ,uBAAiB,GAAa,IAAI,CAAC;QAEnC,SAAS;QAET,2BAAqB,GAA0B,IAAI,CAAC;QAEpD,eAAe;QAEf,mBAAa,GAAY,IAAI,CAAC;QAE9B,eAAe;QAEf,kBAAY,GAAY,IAAI,CAAC;QAE7B,eAAe;QAEf,kBAAY,GAAY,IAAI,CAAC;QAE7B,WAAW;QAEX,kBAAY,GAAY,IAAI,CAAC,CAAC,0CAA0C;QAGxE,kBAAY,GAAY,IAAI,CAAC,CAAC,0CAA0C;QAGxE,kBAAY,GAAY,IAAI,CAAC,CAAC,0CAA0C;QAGxE,mBAAa,GAAY,IAAI,CAAC,CAAC,2CAA2C;QAG1E,oBAAc,GAAY,IAAI,CAAC,CAAC,4CAA4C;QAE5E,WAAW;QAEX,mBAAa,GAAY,IAAI,CAAC,CAAC,2CAA2C;QAG1E,mBAAa,GAAY,IAAI,CAAC,CAAC,2CAA2C;QAG1E,mBAAa,GAAY,IAAI,CAAC,CAAC,2CAA2C;QAG1E,mBAAa,GAAY,IAAI,CAAC,CAAC,2CAA2C;QAG1E,mBAAa,GAAY,IAAI,CAAC,CAAC,2CAA2C;QAG1E,mBAAa,GAAY,IAAI,CAAC,CAAC,2CAA2C;QAE1E,YAAY;QAEZ,gCAA0B,GAA+B,IAAI,CAAC;QAE9D,eAAe;QAEf,mCAA6B,GAAkC,IAAI,CAAC;QAEpE,mBAAmB;QAEnB,0BAAoB,GAAc,IAAI,CAAC;QAEvC,kBAAkB;QAElB,qBAAe,GAAc,IAAI,CAAC;QAElC,oBAAoB;QACZ,oBAAc,GAAc,EAAE,CAAC;QAEvC,WAAW;QAEX,yBAAmB,GAAY,IAAI,CAAC,CAAC,qBAAqB;QAG1D,iBAAW,GAAY,IAAI,CAAC,CAAC,8BAA8B;QAG3D,gBAAU,GAAY,IAAI,CAAC,CAAC,sCAAsC;QAGlE,eAAS,GAAY,IAAI,CAAC,CAAC,qCAAqC;QAGhE,iBAAW,GAAc,IAAI,CAAC,CAAC,SAAS;QAGxC,qBAAe,GAAc,IAAI,CAAC,CAAC,QAAQ;QAG3C,gBAAU,GAAc,IAAI,CAAC,CAAC,OAAO;QAErC,SAAS;QACD,kBAAY,GAAW,CAAC,CAAC;QACzB,sBAAgB,GAA4B,IAAI,CAAC;QACjD,mBAAa,GAAW,CAAC,CAAC,CAAC,cAAc;QACzC,6BAAuB,GAA+D,IAAI,CAAC,CAAC,YAAY;QAEhH,gBAAgB;QACR,sBAAgB,GAAY,KAAK,CAAC;QAE1C,SAAS;QACD,sBAAgB,GAAY,IAAI,CAAC,CAAC,cAAc;QAChD,gBAAU,GAAY,KAAK,CAAC,CAAC,SAAS;;IA6kClD,CAAC;IA3kCG,oCAAM,GAAN;QAAA,iBAoBC;QAnBG,0CAA0C;QAC1C,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,aAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,EAAE,eAAM,CAAC,SAAS,GAAG,sBAAsB,EAAE,eAAM,CAAC,SAAS,GAAG,uBAAuB,EAAE;gBAC3H,KAAI,CAAC,iBAAiB,EAAE,CAAC;YAC7B,CAAC,CAAC,CAAC;SACN;QAED,eAAe;QACf,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,CAAC;SAC5E;QAED,aAAa;QACb,IAAI,CAAC,iCAAiC,EAAE,CAAC;QAEzC,aAAa;QACb,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAE9B,iDAAiD;IACrD,CAAC;IAED,mCAAK,GAAL;QACI,eAAe;QACf,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,aAAa;QACb,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACK,+CAAiB,GAAzB;QACI,mCAAmC;QACnC,IAAI,IAAI,CAAC,aAAa,IAAI,CAAC,EAAE;YAEzB,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3B,OAAO;SACV;QAED,oCAAoC;QACpC,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAE5B,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,EAAE;YAEnC,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;SAC1B;aAAM;YACH,EAAE,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;SACxC;IACL,CAAC;IAED;;OAEG;IACK,iDAAmB,GAA3B;QACI,oCAAoC;QACpC,IAAM,iBAAiB,GAAG,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAClF,IAAI,iBAAiB,EAAE;YACnB,IAAM,aAAa,GAAG,iBAAiB,CAAC,YAAY,CAAC,iCAAuB,CAAC,CAAC;YAC9E,IAAI,aAAa,EAAE;gBACf,aAAa,CAAC,cAAc,CAAC,kCAAQ,CAAC,SAAS,CAAC,CAAC;aACpD;SACJ;IACL,CAAC;IAED;;OAEG;IACK,+CAAiB,GAAzB;QACI,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,cAAc;YACd,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;YACnD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;YACjD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;YAEpD,kBAAkB;YAClB,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,GAAG,CAAC;SAGjC;IACL,CAAC;IAED;;OAEG;IACK,8CAAgB,GAAxB;QAAA,iBAYC;QAXG,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,UAAU;YACV,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,GAAG,CAAC;YAE9B,eAAe;YACf,aAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,EAAE,eAAM,CAAC,SAAS,GAAG,sBAAsB,EAAE,eAAM,CAAC,SAAS,GAAG,uBAAuB,EAAE;gBAC3H,KAAI,CAAC,iBAAiB,EAAE,CAAC;YAC7B,CAAC,CAAC,CAAC;SAGN;IACL,CAAC;IAED;;OAEG;IACK,oDAAsB,GAA9B;QACI,kDAAkD;QAClD,kBAAkB;IAEtB,CAAC;IAED;;;OAGG;IACI,+CAAiB,GAAxB,UAAyB,SAAkC;QAEvD,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;QAElC,iBAAiB;QACjB,IAAI,SAAS,CAAC,MAAM,EAAE;YAClB,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC,MAAM,CAAC;SACzC;QAED,YAAY;QACZ,IAAI,SAAS,CAAC,WAAW,KAAK,IAAI,EAAE;YAChC,iBAAO,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;YAEvC,6BAA6B;YAC7B,IAAI,SAAS,CAAC,OAAO,EAAE;gBACnB,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC,OAAO,CAAC;aACzC;YAED,UAAU;YACV,IAAI,SAAS,CAAC,cAAc,KAAK,SAAS,EAAE;gBACxC,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;aACpD;iBAAM;gBACH,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;aAC/C;YAED,OAAO;YACP,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAEnC,uBAAuB;YACvB,IAAI,IAAI,CAAC,uBAAuB,EAAE;gBAC9B,kBAAkB;gBAClB,IAAI,4BAA4B,IAAI,IAAI,CAAC,uBAAuB,EAAE;oBAC7D,IAAI,CAAC,uBAA+B,CAAC,0BAA0B,CAAC,SAAS,CAAC,CAAC;iBAC/E;qBAAM;oBACH,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;iBACpC;aACJ;SACJ;aAAM;YACH,UAAU;YACV,iBAAO,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;YAEjC,oBAAoB;YACpB,IAAI,CAAC,eAAe,EAAE,CAAC;YAEvB,WAAW;YACX,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;YAE9B,2BAA2B;YAC3B,IAAI,IAAI,CAAC,uBAAuB,EAAE;gBAC9B,IAAI,CAAC,uBAAuB,CAAC,UAAU,EAAE,CAAC;aAC7C;YAED,UAAU;YACV,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;YAE5C,8BAA8B;YAC9B,4BAA4B;YAC5B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAEnC,kCAAkC;YAClC,IAAI,IAAI,CAAC,uBAAuB,EAAE;gBAC9B,IAAI,CAAC,uBAAuB,CAAC,iBAAiB,EAAE,CAAC;aACpD;SACJ;IACL,CAAC;IAED;;;OAGG;IACK,+CAAiB,GAAzB,UAA0B,SAAiB;QACvC,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC;SAErD;IACL,CAAC;IAED;;;OAGG;IACK,kDAAoB,GAA5B,UAA6B,WAAmB;QAC5C,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,WAAI,WAAW,WAAG,CAAC;SAEtD;IACL,CAAC;IAED;;;OAGG;IACK,wCAAU,GAAlB,UAAmB,WAAmB;QAClC,SAAS;QACT,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,OAAO;SACV;QACD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QAEvB,YAAY;QACZ,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;QAEvC,cAAc;QACd,IAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;QAC1D,IAAI,CAAC,aAAa,EAAE;YAChB,EAAE,CAAC,IAAI,CAAC,iDAAY,WAAa,CAAC,CAAC;YACnC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;YACxB,OAAO;SACV;QAED,0BAA0B;QAC1B,IAAI,IAAI,CAAC,gBAAgB,KAAK,aAAa,CAAC,OAAO,EAAE;YACjD,eAAe;YACf,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACvB,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,KAAK,CAAC;aACxC;YAED,cAAc;YACd,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;YACnD,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,OAAO,EAAE,aAAa,CAAC,OAAO,CAAC,CAAC;YAExE,YAAY;YACZ,IAAI,CAAC,gBAAgB,GAAG,aAAa,CAAC,OAAO,CAAC;SACjD;QAED,iBAAiB;QACjB,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE;YAC9B,gBAAgB;YAChB,IAAI,IAAI,CAAC,6BAA6B,EAAE;gBACpC,IAAM,YAAY,GAAG,IAAI,CAAC,sBAAsB,CAAC,WAAW,CAAC,CAAC;gBAE9D,IAAI,CAAC,6BAA6B,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;gBAC3D,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,6BAAoC,CAAC;aAC5E;iBAAM;gBACH,OAAO,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;gBAChC,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;aACvC;SACJ;aAAM;YACH,gBAAgB;YAChB,IAAI,IAAI,CAAC,0BAA0B,EAAE;gBACjC,IAAM,SAAS,GAAG,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;gBAExD,IAAI,CAAC,0BAA0B,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;gBACrD,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,0BAA0B,CAAC;aAClE;iBAAM;gBACH,OAAO,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;gBAChC,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;aACvC;SACJ;QAED,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACK,+CAAiB,GAAzB,UAA0B,WAAmB;QACzC,IAAI,WAAW,IAAI,CAAC,IAAI,WAAW,IAAI,CAAC,EAAE;YACtC,OAAO,EAAC,OAAO,EAAE,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,aAAa,EAAE,MAAM,EAAC,CAAC;SACnF;aAAM,IAAI,WAAW,KAAK,CAAC,EAAE;YAC1B,OAAO,EAAC,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,YAAY,EAAE,aAAa,EAAE,MAAM,EAAC,CAAC;SACtF;aAAM,IAAI,WAAW,IAAI,CAAC,IAAI,WAAW,IAAI,CAAC,EAAE;YAC7C,OAAO,EAAC,OAAO,EAAE,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,aAAa,EAAE,MAAM,EAAC,CAAC;SACnF;aAAM,IAAI,WAAW,KAAK,EAAE,EAAE;YAC3B,OAAO,EAAC,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,YAAY,EAAE,aAAa,EAAE,MAAM,EAAC,CAAC;SACtF;aAAM,IAAI,WAAW,IAAI,EAAE,IAAI,WAAW,IAAI,EAAE,EAAE;YAC/C,OAAO,EAAC,OAAO,EAAE,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,aAAa,EAAE,MAAM,EAAC,CAAC;SACnF;aAAM,IAAI,WAAW,KAAK,EAAE,EAAE;YAC3B,OAAO,EAAC,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,YAAY,EAAE,aAAa,EAAE,MAAM,EAAC,CAAC;SACtF;aAAM,IAAI,WAAW,IAAI,EAAE,IAAI,WAAW,IAAI,EAAE,EAAE;YAC/C,OAAO,EAAC,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,WAAW,EAAE,aAAa,EAAE,MAAM,EAAC,CAAC;SACrF;aAAM,IAAI,WAAW,KAAK,EAAE,EAAE;YAC3B,OAAO,EAAC,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,YAAY,EAAE,aAAa,EAAE,MAAM,EAAC,CAAC;SACtF;aAAM,IAAI,WAAW,IAAI,EAAE,IAAI,WAAW,IAAI,EAAE,EAAE;YAC/C,OAAO,EAAC,OAAO,EAAE,IAAI,CAAC,cAAc,EAAE,OAAO,EAAE,YAAY,EAAE,aAAa,EAAE,MAAM,EAAC,CAAC;SACvF;aAAM,IAAI,WAAW,KAAK,EAAE,EAAE;YAC3B,OAAO,EAAC,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,YAAY,EAAE,aAAa,EAAE,MAAM,EAAC,CAAC;SACtF;aAAM,IAAI,WAAW,IAAI,EAAE,IAAI,WAAW,IAAI,EAAE,EAAE;YAC/C,OAAO,EAAC,OAAO,EAAE,IAAI,CAAC,cAAc,EAAE,OAAO,EAAE,YAAY,EAAE,aAAa,EAAE,MAAM,EAAC,CAAC;SACvF;aAAM,IAAI,WAAW,KAAK,EAAE,EAAE;YAC3B,OAAO,EAAC,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,YAAY,EAAE,aAAa,EAAE,MAAM,EAAC,CAAC;SACtF;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAID;;;OAGG;IACK,iDAAmB,GAA3B,UAA4B,WAAmB;QAC3C,IAAI,WAAW,IAAI,CAAC,IAAI,WAAW,IAAI,CAAC,EAAE;YACtC,OAAO,KAAK,CAAC;SAChB;aAAM,IAAI,WAAW,IAAI,CAAC,IAAI,WAAW,IAAI,CAAC,EAAE;YAC7C,OAAO,KAAK,CAAC;SAChB;aAAM,IAAI,WAAW,IAAI,EAAE,IAAI,WAAW,IAAI,EAAE,EAAE;YAC/C,OAAO,KAAK,CAAC;SAChB;aAAM,IAAI,WAAW,IAAI,EAAE,IAAI,WAAW,IAAI,EAAE,EAAE;YAC/C,OAAO,MAAM,CAAC;SACjB;aAAM,IAAI,WAAW,IAAI,EAAE,IAAI,WAAW,IAAI,EAAE,IAAI,WAAW,IAAI,EAAE,IAAI,WAAW,IAAI,EAAE,EAAE;YACzF,OAAO,OAAO,CAAC;SAClB;QACD,OAAO,KAAK,CAAC,CAAC,KAAK;IACvB,CAAC;IAED;;;OAGG;IACK,wCAAU,GAAlB,UAAmB,WAAmB;QAClC,cAAc;QACd,OAAO,WAAW,KAAK,CAAC,IAAI,WAAW,KAAK,EAAE,IAAI,WAAW,KAAK,EAAE;YAC7D,WAAW,KAAK,EAAE,IAAI,WAAW,KAAK,EAAE,IAAI,WAAW,KAAK,EAAE,CAAC;IAC1E,CAAC;IAED;;;OAGG;IACK,oDAAsB,GAA9B,UAA+B,WAAmB;QAC9C,cAAc;QACd,IAAI,WAAW,KAAK,CAAC,EAAE;YACnB,OAAO,WAAW,CAAC,CAAE,SAAS;SACjC;aAAM,IAAI,WAAW,KAAK,EAAE,EAAE;YAC3B,OAAO,WAAW,CAAC,CAAE,SAAS;SACjC;aAAM,IAAI,WAAW,KAAK,EAAE,EAAE;YAC3B,OAAO,WAAW,CAAC,CAAE,SAAS;SACjC;aAAM,IAAI,WAAW,KAAK,EAAE,EAAE;YAC3B,OAAO,WAAW,CAAC,CAAE,SAAS;SACjC;aAAM,IAAI,WAAW,KAAK,EAAE,EAAE;YAC3B,OAAO,WAAW,CAAC,CAAE,SAAS;SACjC;aAAM,IAAI,WAAW,KAAK,EAAE,EAAE;YAC3B,OAAO,WAAW,CAAC,CAAE,SAAS;SACjC;QACD,OAAO,WAAW,CAAC,CAAC,KAAK;IAC7B,CAAC;IAED;;;;OAIG;IACK,kDAAoB,GAA5B,UAA6B,OAAgB,EAAE,OAAe;QAC1D,IAAI,OAAO,EAAE;YACT,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;SACzB;aAAM;YACH,EAAE,CAAC,IAAI,CAAC,wDAAc,OAAS,CAAC,CAAC;SACpC;IACL,CAAC;IAED;;;OAGG;IACK,8CAAgB,GAAxB,UAAyB,aAA8B;QACnD,uBAAuB;QACvB,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC;SACpC;QAED,qBAAqB;QACrB,IAAI,aAAa,KAAK,MAAM,EAAE;YAC1B,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;gBAChD,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC;aACnC;YACD,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;gBAC/C,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC;aACpC;SACJ;aAAM;YACH,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;gBAChD,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC;aACnC;YACD,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;gBAC/C,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC;aACpC;SACJ;IACL,CAAC;IAED;;OAEG;IACK,6CAAe,GAAvB;QACI,IAAM,WAAW,GAAG;YAChB,IAAI,CAAC,YAAY;YACjB,IAAI,CAAC,YAAY;YACjB,IAAI,CAAC,YAAY;YACjB,IAAI,CAAC,aAAa;YAClB,IAAI,CAAC,cAAc;YACnB,IAAI,CAAC,aAAa;YAClB,IAAI,CAAC,aAAa;YAClB,IAAI,CAAC,aAAa;YAClB,IAAI,CAAC,aAAa;YAClB,IAAI,CAAC,aAAa;YAClB,IAAI,CAAC,aAAa;SACrB,CAAC;QAEF,WAAW,CAAC,OAAO,CAAC,UAAA,IAAI;YACpB,IAAI,IAAI,EAAE;gBACN,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;aACvB;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;OAGG;IACI,6CAAe,GAAtB,UAAuB,WAAmB;QAGtC,oBAAoB;QACpB,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAEhC,gBAAgB;QAChB,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,6CAAe,GAAtB;QACI,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;OAEG;IACI,iDAAmB,GAA1B;QACI,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,8CAAgB,GAAvB;QACI,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;OAEG;IACK,kDAAoB,GAA5B;QACI,uBAAuB;QACvB,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC;SAEpC;QAED,cAAc;QACd,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC;SAEpC;QACD,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC;SAEpC;QAED,gBAAgB;QAChB,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACK,0CAAY,GAApB;QACI,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC;SAEnC;aAAM;YACH,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;SACjC;IACL,CAAC;IAED;;OAEG;IACK,0CAAY,GAApB;QACI,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC;SAEnC;aAAM;YACH,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;SACjC;IACL,CAAC;IAED;;OAEG;IACI,wDAA0B,GAAjC;QACI,OAAO,IAAI,CAAC,uBAAuB,CAAC;IACxC,CAAC;IAED;;;OAGG;IACI,2DAA6B,GAApC,UAAqC,QAAa;QAC9C,IAAI,IAAI,CAAC,uBAAuB,EAAE;YACtB,IAAA,CAAC,GAA+B,QAAQ,EAAvC,EAAE,CAAC,GAA4B,QAAQ,EAApC,EAAE,MAAM,GAAoB,QAAQ,OAA5B,EAAE,aAAa,GAAK,QAAQ,cAAb,CAAc;YAEjD,SAAS;YACT,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,SAAS,IAAI,MAAM,KAAK,SAAS,EAAE;gBAC5D,IAAI,CAAC,uBAAuB,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;aAClE;YAED,SAAS;YACT,IAAI,aAAa,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;gBAC/C,IAAI,CAAC,uBAAuB,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC;aACnE;SACJ;IACL,CAAC;IAED;;;OAGG;IACI,qDAAuB,GAA9B,UAA+B,WAAgB;QAC3C,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAC9B,SAAS;YACT,IAAI,CAAC,uBAAuB,CAAC,mBAAmB,EAAE,CAAC;YAEnD,SAAS;YACT,IAAI,CAAC,uBAAuB,CAAC,cAAc,EAAE,CAAC;SACjD;IACL,CAAC;IAED;;OAEG;IACI,0DAA4B,GAAnC;QAEI,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAC9B,uBAAuB;YACvB,IAAI,CAAC,uBAAuB,CAAC,UAAU,EAAE,CAAC;YAE1C,WAAW;YACX,IAAI,CAAC,uBAAuB,CAAC,kBAAkB,EAAE,CAAC;SACrD;IACL,CAAC;IAED;;OAEG;IACK,+DAAiC,GAAzC;QACI,gBAAgB;QAChB,iBAAO,CAAC,KAAK,CAAC,gBAAgB,CAAC,uBAAS,CAAC,cAAc,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;IAC1F,CAAC;IAED;;OAEG;IACK,iEAAmC,GAA3C;QACI,iBAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,uBAAS,CAAC,cAAc,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;IAC7F,CAAC;IAED;;;OAGG;IACK,8CAAgB,GAAxB,UAAyB,WAAgC;QACrD,QAAQ,WAAW,CAAC,KAAK,EAAE;YACvB,KAAK,qBAAS,CAAC,sBAAsB;gBACjC,IAAI,CAAC,yBAAyB,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;gBACjD,MAAM;YACV,KAAK,qBAAS,CAAC,mBAAmB;gBAC9B,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;gBACtC,MAAM;SACb;IACL,CAAC;IAED;;;OAGG;IACI,uDAAyB,GAAhC,UAAiC,QAAa;QAE1C,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAC9B,oBAAoB;YAEhB,IAAA,CAAC,GAGD,QAAQ,EAHP,EAAE,CAAC,GAGJ,QAAQ,EAHJ,EAAE,CAAC,GAGP,QAAQ,EAHD,EAAE,CAAC,GAGV,QAAQ,EAHE,EAAE,MAAM,GAGlB,QAAQ,OAHU,EAAE,MAAM,GAG1B,QAAQ,OAHkB,EAC1B,aAAa,GAEb,QAAQ,cAFK,EAAE,aAAa,GAE5B,QAAQ,cAFoB,EAAE,SAAS,GAEvC,QAAQ,UAF+B,EAAE,cAAc,GAEvD,QAAQ,eAF+C,EACvD,gBAAgB,GAChB,QAAQ,iBADQ,CAAE,aAAa;YAAf,CACP;YAEb,gBAAgB;YAChB,IAAM,oBAAoB,GAAG,CAAC,IAAI,CAAC,uBAAuB,KAAK,IAAI,CAAC,6BAAoC,CAAC,CAAC;YAE1G,kBAAkB;YAClB,IAAI,MAAM,SAAQ,EAAE,MAAM,SAAQ,EAAE,MAAM,SAAQ,EAAE,MAAM,SAAQ,CAAC;YACnE,IAAI,aAAa,GAAG,KAAK,CAAC;YAE1B,IAAI,oBAAoB,EAAE;gBACtB,mCAAmC;gBACnC,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,SAAS,EAAE;oBACpC,MAAM,GAAG,CAAC,CAAC;oBACX,MAAM,GAAG,CAAC,CAAC;oBACX,aAAa,GAAG,IAAI,CAAC;iBAExB;qBAAM,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,SAAS,EAAE;oBAC3C,gCAAgC;oBAChC,MAAM,GAAG,CAAC,CAAC,CAAE,SAAS;oBACtB,MAAM,GAAG,CAAC,CAAC,CAAE,SAAS;oBACtB,aAAa,GAAG,IAAI,CAAC;iBAExB;aACJ;iBAAM;gBACH,iBAAiB;gBACjB,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,SAAS,EAAE;oBACpC,MAAM,GAAG,CAAC,CAAC;oBACX,MAAM,GAAG,CAAC,CAAC;oBACX,aAAa,GAAG,IAAI,CAAC;iBAExB;qBAAM,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,SAAS,EAAE;oBAC3C,OAAO,CAAC,IAAI,CAAC,kGAAqB,CAAC,UAAK,CAAC,gDAAU,CAAC,CAAC;oBACrD,aAAa,GAAG,KAAK,CAAC;iBACzB;aACJ;YAED,IAAI,aAAa,IAAI,MAAM,KAAK,SAAS,EAAE;gBAGvC,IAAI,MAAM,KAAK,CAAC,EAAE;oBACd,0CAA0C;oBAE1C,wCAAwC;iBAC3C;qBAAM,IAAI,MAAM,KAAK,CAAC,EAAE;oBACrB,OAAO;oBAGP,WAAW;oBACX,IAAI,MAAM,KAAK,MAAM,IAAI,MAAM,KAAK,MAAM,EAAE;wBAExC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;qBAChC;oBAED,iBAAiB;oBACjB,IAAI,oBAAoB,EAAE;wBACtB,SAAS;wBACR,IAAI,CAAC,uBAA+B,CAAC,mBAAmB,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;qBACrF;yBAAM;wBACH,SAAS;wBACT,IAAI,CAAC,uBAAuB,CAAC,mBAAmB,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;qBAC5E;oBAED,WAAW;oBACX,IAAI,gBAAgB,IAAI,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE;wBAGpF,IAAI,oBAAoB,EAAE;4BACtB,gCAAgC;4BAE/B,IAAI,CAAC,uBAA+B,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;yBAC/E;6BAAM;4BACH,mCAAmC;4BAElC,IAAI,CAAC,uBAAsD,CAAC,sBAAsB,CAAC,gBAAgB,CAAC,CAAC;yBACzG;qBACJ;iBACJ;qBAAM;oBACH,eAAe;oBAEf,IAAI,oBAAoB,EAAE;wBACrB,IAAI,CAAC,uBAA+B,CAAC,mBAAmB,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;qBACrF;yBAAM;wBACH,IAAI,CAAC,uBAAuB,CAAC,mBAAmB,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;qBAC5E;iBACJ;aACJ;iBAAM;gBACH,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBACnC,OAAO,CAAC,IAAI,CAAC,yCAAa,oBAAoB,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAE,CAAC,CAAC;gBAClE,OAAO,CAAC,IAAI,CAAC,oCAAc,CAAC,YAAO,CAAC,YAAO,CAAC,YAAO,CAAG,CAAC,CAAC;gBACxD,OAAO,CAAC,IAAI,CAAC,sBAAU,MAAQ,CAAC,CAAC;aACpC;SAOJ;IACL,CAAC;IAED;;;OAGG;IACI,4CAAc,GAArB,UAAsB,WAAgB;QAAtC,iBAqCC;QAlCG,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAC9B,SAAS;YACT,IAAI,CAAC,uBAAuB,CAAC,mBAAmB,EAAE,CAAC;YAEnD,gBAAgB;YAChB,IAAI,CAAC,uBAAuB,CAAC,cAAc,EAAE,CAAC;SACjD;QAED,sBAAsB;QACtB,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEzB,mCAAmC;QACnC,IAAM,YAAY,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC;QAC1C,IAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB;YACtB,CAAC,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,uBAAuB,CAAC,yBAAyB,EAAE,CAAC,CAAC;QAIlH,IAAI,YAAY,IAAI,eAAe,EAAE;YACjC,2BAA2B;YAE3B,IAAI,CAAC,YAAY,CAAC;gBAEd,KAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;gBACtC,OAAO;gBACP,KAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;YAClC,CAAC,EAAE,GAAG,CAAC,CAAC;SACX;aAAM;YACH,gBAAgB;YAEhB,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;YACtC,OAAO;YACP,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;SACjC;IACL,CAAC;IAED;;OAEG;IACK,oDAAsB,GAA9B;QAAA,iBAiBC;QAhBG,SAAS;QACT,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC;SACpE;QAED,QAAQ;QACR,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,CAAC;SAC5E;QAED,yBAAyB;QACzB,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,aAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,eAAM,CAAC,SAAS,GAAG,uBAAuB,EAAE,eAAM,CAAC,SAAS,GAAG,wBAAwB,EAAE;gBAClI,KAAI,CAAC,iBAAiB,EAAE,CAAC;YAC7B,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IAED;;;OAGG;IACK,iDAAmB,GAA3B,UAA4B,WAAgB;QAExC,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC3B,OAAO,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;YACzC,OAAO;SACV;QAED,SAAS;QAET,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,IAAI,CAAC;QAEvC,2BAA2B;QAC3B,IAAM,SAAS,GAAG,WAAW,CAAC,SAAS,IAAI,WAAW,CAAC,OAAO,IAAI,WAAW,CAAC,KAAK,IAAI,WAAW,CAAC,UAAU,KAAK,CAAC,CAAC;QAIpH,IAAI,SAAS,EAAE;YACX,cAAc;YACd,IAAI,IAAI,CAAC,SAAS,EAAE;gBAChB,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC;aAChC;YACD,IAAI,IAAI,CAAC,UAAU,EAAE;gBACjB,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,KAAK,CAAC;aAClC;SAEJ;aAAM;YACH,cAAc;YACd,IAAI,IAAI,CAAC,UAAU,EAAE;gBACjB,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC;aACjC;YACD,IAAI,IAAI,CAAC,SAAS,EAAE;gBAChB,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,KAAK,CAAC;aACjC;SAEJ;IACL,CAAC;IAED;;OAEG;IACK,gDAAkB,GAA1B;QAGI,SAAS;QACT,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAE3B,WAAW;QACX,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAExB,uCAAuC;QACvC,iCAAiC;QAEjC,yBAAyB;QACzB,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACK,oDAAsB,GAA9B;QAGI,SAAS;QACT,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAE3B,WAAW;QACX,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAExB,QAAQ;QACR,IAAM,SAAS,GAAG,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QACxC,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;QAEhC,uCAAuC;QACvC,iCAAiC;QAEjC,wBAAwB;QACxB,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACK,+CAAiB,GAAzB;QAGI,SAAS;QACT,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAE3B,0BAA0B;QAC1B,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAExB,kBAAkB;QAClB,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC/B,CAAC;IAED;;OAEG;IACK,iDAAmB,GAA3B;QACI,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,KAAK,CAAC;SAC3C;IACL,CAAC;IAED;;;OAGG;IACK,iDAAmB,GAA3B,UAA4B,OAAe;QAGvC,IAAM,OAAO,GAAG;YACZ,OAAO,EAAE,OAAO;SACnB,CAAC;QAEF,mCAAgB,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,qBAAS,CAAC,sBAAsB,EAAE,OAAO,CAAC,CAAC;IACtF,CAAC;IAED;;OAEG;IACK,8CAAgB,GAAxB;QACI,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC;SAChF;IACL,CAAC;IAED;;OAEG;IACK,mDAAqB,GAA7B;QAEI,mCAAgB,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,qBAAS,CAAC,qBAAqB,EAAE,EAAE,CAAC,CAAC;IAChF,CAAC;IAED;;OAEG;IACK,4CAAc,GAAtB;QACI,+BAA+B;QAC/B,OAAO,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;IAClC,CAAC;IAED;;;OAGG;IACI,kDAAoB,GAA3B,UAA4B,aAA4C;QAAxE,iBA0EC;QAxEG,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACvB,EAAE,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;YAC3C,OAAO;SACV;QAED,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;YAC/B,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACvB,OAAO;SACV;QAED,IAAI,CAAC,aAAa,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE;YAC/E,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;YACpC,OAAO;SACV;QAID,uBAAuB;QACvB,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1B,IAAM,aAAa,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;YAEvC,SAAS;YACT,IAAI,MAAM,SAAQ,EAAE,MAAM,SAAQ,CAAC;YACnC,IAAM,GAAG,GAAG,aAAoB,CAAC,CAAE,wBAAwB;YAE3D,IAAI,GAAG,CAAC,CAAC,KAAK,SAAS,IAAI,GAAG,CAAC,CAAC,KAAK,SAAS,EAAE;gBAC5C,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC;gBACf,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC;aAElB;iBAAM,IAAI,GAAG,CAAC,CAAC,KAAK,SAAS,IAAI,GAAG,CAAC,CAAC,KAAK,SAAS,EAAE;gBACnD,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC;gBACf,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC;aAElB;iBAAM;gBACH,OAAO,CAAC,KAAK,CAAC,0DAAa,EAAE,aAAa,CAAC,CAAC;gBAC5C,OAAO;aACV;YAED,aAAa;YACb,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;SAC9C;QAED,iBAAiB;QACjB,aAAa,CAAC,OAAO,CAAC,UAAC,QAAQ,EAAE,KAAK;YAClC,IAAM,GAAG,GAAG,QAAe,CAAC,CAAE,wBAAwB;YAEtD,OAAO;YACP,IAAI,MAAc,EAAE,MAAc,CAAC;YACnC,IAAI,GAAG,CAAC,CAAC,KAAK,SAAS,IAAI,GAAG,CAAC,CAAC,KAAK,SAAS,EAAE;gBAC5C,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC;gBACf,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC;aAClB;iBAAM,IAAI,GAAG,CAAC,CAAC,KAAK,SAAS,IAAI,GAAG,CAAC,CAAC,KAAK,SAAS,EAAE;gBACnD,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC;gBACf,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC;aAClB;iBAAM;gBACH,OAAO,CAAC,KAAK,CAAC,oCAAS,KAAK,0CAAS,EAAE,QAAQ,CAAC,CAAC;gBACjD,OAAO;aACV;YAED,IAAI,KAAK,KAAK,CAAC,EAAE;gBACb,4BAA4B;gBAC5B,OAAO;aACV;iBAAM;gBACH,mBAAmB;gBACnB,IAAM,WAAS,GAAG,MAAM,CAAC,CAAE,QAAQ;gBACnC,IAAM,WAAS,GAAG,MAAM,CAAC,CAAE,QAAQ;gBACnC,KAAI,CAAC,YAAY,CAAC;oBAEd,KAAI,CAAC,qBAAqB,CAAC,WAAS,EAAE,WAAS,CAAC,CAAC;gBACrD,CAAC,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC;aACnB;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;OAIG;IACK,mDAAqB,GAA7B,UAA8B,CAAS,EAAE,CAAS;QAE9C,WAAW;QACX,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE;YAChE,OAAO,CAAC,KAAK,CAAC,0DAAgB,CAAC,YAAO,CAAG,CAAC,CAAC;YAC3C,OAAO,CAAC,KAAK,CAAC,uBAAW,OAAO,CAAC,yBAAU,OAAO,CAAG,CAAC,CAAC;YACvD,OAAO,CAAC,KAAK,CAAC,8BAAU,EAAE,IAAI,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC;YAC7C,OAAO;SACV;QAED,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACvB,EAAE,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAC;YACzC,OAAO;SACV;QAED,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;YAC/B,EAAE,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC;YACjD,OAAO;SACV;QAED,IAAI;YACA,gBAAgB;YAChB,IAAM,oBAAoB,GAAG,CAAC,IAAI,CAAC,uBAAuB,KAAK,IAAI,CAAC,6BAAoC,CAAC,CAAC;YAE1G,IAAI,SAAS,GAAY,IAAI,CAAC;YAE9B,IAAI,oBAAoB,EAAE;gBACtB,qBAAqB;gBAErB,SAAS,GAAI,IAAI,CAAC,uBAA+B,CAAC,kBAAkB,CAChE,CAAC,EAAE,CAAC,EAAG,kBAAkB;gBACzB,IAAI,CAAC,eAAe,EACpB,eAAa,CAAC,SAAI,CAAG,CACxB,CAAC;aACL;iBAAM;gBACH,SAAS;gBAET,SAAS,GAAG,IAAI,CAAC,uBAAuB,CAAC,kBAAkB,CACvD,CAAC,EAAE,CAAC,EACJ,IAAI,CAAC,eAAe,EACpB,eAAa,CAAC,SAAI,CAAG,CACxB,CAAC;aACL;YAED,IAAI,SAAS,EAAE;gBACX,oBAAoB;gBACpB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aAEvC;iBAAM;gBACH,EAAE,CAAC,KAAK,CAAC,gCAAU,CAAC,UAAK,CAAC,iGAAmB,CAAC,CAAC;aAClD;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,EAAE,CAAC,KAAK,CAAC,kFAAiB,EAAE,KAAK,CAAC,CAAC;SACtC;IACL,CAAC;IAED;;OAEG;IACI,6CAAe,GAAtB;QAGI,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,UAAC,IAAI,EAAE,KAAK;YACpC,IAAI,IAAI,IAAI,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBAE1B,IAAI,CAAC,OAAO,EAAE,CAAC;aAClB;QACL,CAAC,CAAC,CAAC;QAEH,OAAO;QACP,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;IAE7B,CAAC;IAED;;OAEG;IACI,6CAAe,GAAtB;QAEI,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,kBAAkB;IACtB,CAAC;IAED,uCAAS,GAAT;QACI,SAAS;QACT,IAAI,CAAC,mCAAmC,EAAE,CAAC;QAE3C,UAAU;QACV,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,SAAS;QACT,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC;SACrE;QACD,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,CAAC;SAC7E;QACD,+CAA+C;QAC/C,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC;SACjF;IACL,CAAC;IAjsCD;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;2DACS;IAI3B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;gEACc;IAIlC;QADC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC;+DACa;IAIhC;QADC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC;kEACgB;IAInC;QADC,QAAQ,CAAC,+BAAqB,CAAC;sEACoB;IAIpD;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACY;IAI9B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;6DACW;IAI7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;6DACW;IAI7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;6DACW;IAG7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;6DACW;IAG7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;6DACW;IAG7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACY;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;+DACa;IAI/B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACY;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACY;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACY;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACY;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACY;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACY;IAI9B;QADC,QAAQ,CAAC,oCAA0B,CAAC;2EACyB;IAI9D;QADC,QAAQ,CAAC,uCAA6B,CAAC;8EAC4B;IAIpE;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;qEACmB;IAIvC;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;gEACc;IAOlC;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;oEACkB;IAGpC;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;4DACU;IAG5B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;2DACS;IAG3B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;0DACQ;IAG1B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;4DACU;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;gEACc;IAGlC;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;2DACS;IA5GZ,mBAAmB;QADvC,OAAO;OACa,mBAAmB,CAssCvC;IAAD,0BAAC;CAtsCD,AAssCC,CAtsCgD,EAAE,CAAC,SAAS,GAssC5D;kBAtsCoB,mBAAmB", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\nimport { ExtendLevelInfoResponse } from \"../bean/GameBean\";\nimport LeaveDialogController from \"../hall/LeaveDialogController\";\nimport { Tools } from \"../util/Tools\";\nimport { Config } from \"../util/Config\";\nimport GlobalManagerController, { PageType } from \"../GlobalManagerController\";\nimport SingleChessBoardController from \"../game/Chess/SingleChessBoardController\";\nimport HexSingleChessBoardController from \"../game/Chess/HexSingleChessBoardController\";\nimport { WebSocketManager } from \"../net/WebSocketManager\";\nimport { MessageId } from \"../net/MessageId\";\nimport { GameMgr } from \"../common/GameMgr\";\nimport { EventType } from \"../common/EventCenter\";\nimport { ReceivedMessageBean } from \"../net/MessageBaseBean\";\n\nconst { ccclass, property } = cc._decorator;\n\n@ccclass\nexport default class LevelPageController extends cc.Component {\n\n    // 返回按钮\n    @property(cc.Node)\n    backButton: cc.Node = null;\n\n    // 开始游戏按钮\n    @property(cc.Button)\n    startGameButton: cc.Button = null;\n\n    // 地雷数UI标签\n    @property(cc.Label)\n    mineCountLabel: cc.Label = null;\n\n    // 当前关卡数UI标签\n    @property(cc.Label)\n    currentLevelLabel: cc.Label = null;\n\n    // 退出游戏弹窗\n    @property(LeaveDialogController)\n    leaveDialogController: LeaveDialogController = null;\n\n    // level_page节点\n    @property(cc.Node)\n    levelPageNode: cc.Node = null;\n\n    // game_map_1节点\n    @property(cc.Node)\n    gameMap1Node: cc.Node = null;\n\n    // game_map_2节点\n    @property(cc.Node)\n    gameMap2Node: cc.Node = null;\n\n    // 方形地图节点引用\n    @property(cc.Node)\n    qipan8x8Node: cc.Node = null; // level_page/game_map_1/chess_bg/qipan8*8\n\n    @property(cc.Node)\n    qipan8x9Node: cc.Node = null; // level_page/game_map_1/chess_bg/qipan8*9\n\n    @property(cc.Node)\n    qipan9x9Node: cc.Node = null; // level_page/game_map_1/chess_bg/qipan9*9\n\n    @property(cc.Node)\n    qipan9x10Node: cc.Node = null; // level_page/game_map_1/chess_bg/qipan9*10\n\n    @property(cc.Node)\n    qipan10x10Node: cc.Node = null; // level_page/game_map_1/chess_bg/qipan10*10\n\n    // 特殊关卡节点引用\n    @property(cc.Node)\n    levelS001Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S001\n\n    @property(cc.Node)\n    levelS002Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S002\n\n    @property(cc.Node)\n    levelS003Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S003\n\n    @property(cc.Node)\n    levelS004Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S004\n\n    @property(cc.Node)\n    levelS005Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S005\n\n    @property(cc.Node)\n    levelS006Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S006\n\n    // 单机模式棋盘控制器\n    @property(SingleChessBoardController)\n    singleChessBoardController: SingleChessBoardController = null;\n\n    // 六边形单机模式棋盘控制器\n    @property(HexSingleChessBoardController)\n    hexSingleChessBoardController: HexSingleChessBoardController = null;\n\n    // 测试按钮（用于调试显示地雷位置）\n    @property(cc.Button)\n    debugShowMinesButton: cc.Button = null;\n\n    // 测试预制体（用于显示地雷位置）\n    @property(cc.Prefab)\n    debugMinePrefab: cc.Prefab = null;\n\n    // 存储创建的测试预制体节点，用于清理\n    private debugMineNodes: cc.Node[] = [];\n\n    // 结算页面相关节点\n    @property(cc.Node)\n    levelSettlementNode: cc.Node = null; // level_settlement节点\n\n    @property(cc.Node)\n    boardBgNode: cc.Node = null; // level_settlement/board_bg节点\n\n    @property(cc.Node)\n    loseBgNode: cc.Node = null; // level_settlement/board_bg/lose_bg节点\n\n    @property(cc.Node)\n    winBgNode: cc.Node = null; // level_settlement/board_bg/win_bg节点\n\n    @property(cc.Button)\n    retryButton: cc.Button = null; // 再来一次按钮\n\n    @property(cc.Button)\n    nextLevelButton: cc.Button = null; // 下一关按钮\n\n    @property(cc.Button)\n    exitButton: cc.Button = null; // 退出按钮\n\n    // 当前关卡数据\n    private currentLevel: number = 1;\n    private currentLevelInfo: ExtendLevelInfoResponse = null;\n    private currentRoomId: number = 0; // 当前关卡游戏的房间ID\n    private currentSingleChessBoard: SingleChessBoardController | HexSingleChessBoardController = null; // 当前激活的单机棋盘\n\n    // 记录最后一次点击是否是炸弹\n    private lastClickWasBomb: boolean = false;\n\n    // 性能优化相关\n    private lastShownMapNode: cc.Node = null; // 记录上次显示的地图节点\n    private isUpdating: boolean = false; // 防止重复更新\n\n    onLoad() {\n        // 设置返回按钮点击事件 - 使用与GamePageController相同的样式\n        if (this.backButton) {\n            Tools.imageButtonClick(this.backButton, Config.buttonRes + 'side_btn_back_normal', Config.buttonRes + 'side_btn_back_pressed', () => {\n                this.onBackButtonClick();\n            });\n        }\n\n        // 设置开始游戏按钮点击事件\n        if (this.startGameButton) {\n            this.startGameButton.node.on('click', this.onStartGameButtonClick, this);\n        }\n\n        // 注册单机模式消息监听\n        this.registerSingleModeMessageHandlers();\n\n        // 设置结算页面按钮事件\n        this.setupSettlementButtons();\n\n        // 注意：不在onLoad中初始化关卡数UI，等待外部调用setCurrentLevel时再更新\n    }\n\n    start() {\n        // 初始化时隐藏所有地图节点\n        this.hideAllMapNodes();\n\n        // 设置测试按钮点击事件\n        this.setupDebugButton();\n    }\n\n    /**\n     * 返回按钮点击事件\n     */\n    private onBackButtonClick() {\n        // 如果没有有效的房间ID，说明还没有进入游戏，直接返回关卡选择页面\n        if (this.currentRoomId <= 0) {\n           \n            this.returnToLevelSelect();\n            return;\n        }\n\n        // 弹出确认退出对话框，type=1表示退出本局游戏，传递当前房间ID\n        if (this.leaveDialogController) {\n\n            this.leaveDialogController.show(1, () => {\n\n            }, this.currentRoomId);\n        } else {\n            cc.warn(\"LeaveDialogController 未配置\");\n        }\n    }\n\n    /**\n     * 返回到关卡选择页面\n     */\n    private returnToLevelSelect() {\n        // 查找GlobalManagerController并切换到大厅页面\n        const globalManagerNode = cc.find(\"Canvas/global_node\") || cc.find(\"global_node\");\n        if (globalManagerNode) {\n            const globalManager = globalManagerNode.getComponent(GlobalManagerController);\n            if (globalManager) {\n                globalManager.setCurrentPage(PageType.HALL_PAGE);\n            }\n        }\n    }\n\n    /**\n     * 禁用返回按钮\n     */\n    private disableBackButton() {\n        if (this.backButton) {\n            // 移除所有触摸事件监听器\n            this.backButton.off(cc.Node.EventType.TOUCH_START);\n            this.backButton.off(cc.Node.EventType.TOUCH_END);\n            this.backButton.off(cc.Node.EventType.TOUCH_CANCEL);\n\n            // 设置按钮为半透明状态，表示禁用\n            this.backButton.opacity = 128;\n\n           \n        }\n    }\n\n    /**\n     * 启用返回按钮\n     */\n    private enableBackButton() {\n        if (this.backButton) {\n            // 恢复按钮透明度\n            this.backButton.opacity = 255;\n\n            // 重新设置返回按钮点击事件\n            Tools.imageButtonClick(this.backButton, Config.buttonRes + 'side_btn_back_normal', Config.buttonRes + 'side_btn_back_pressed', () => {\n                this.onBackButtonClick();\n            });\n\n          \n        }\n    }\n\n    /**\n     * 开始游戏按钮点击事件\n     */\n    private onStartGameButtonClick() {\n        // ExtendLevelInfo消息现在由LevelSelectPageController发送\n        // 这里直接进入游戏，等待后端响应\n        \n    }\n\n    /**\n     * 处理ExtendLevelInfo响应\n     * @param levelInfo 关卡信息响应数据\n     */\n    public onExtendLevelInfo(levelInfo: ExtendLevelInfoResponse) {\n\n        this.currentLevelInfo = levelInfo;\n\n        // 保存房间ID，用于退出时使用\n        if (levelInfo.roomId) {\n            this.currentRoomId = levelInfo.roomId;\n        }\n\n        // 检查是否为断线重连\n        if (levelInfo.reconnected === true) {\n            GameMgr.Console.Log(\"关卡模式断线重连，恢复游戏状态\");\n\n            // 断线重连时，使用后端返回的levelId设置当前关卡\n            if (levelInfo.levelId) {\n                this.currentLevel = levelInfo.levelId;\n            }\n\n            // 恢复地雷数UI\n            if (levelInfo.remainingMines !== undefined) {\n                this.updateMineCountUI(levelInfo.remainingMines);\n            } else {\n                this.updateMineCountUI(levelInfo.mineCount);\n            }\n\n            // 进入关卡\n            this.enterLevel(this.currentLevel);\n\n            // 通知当前激活的单机棋盘控制器处理断线重连\n            if (this.currentSingleChessBoard) {\n                // 检查控制器类型并调用相应的方法\n                if ('onExtendLevelInfoReconnect' in this.currentSingleChessBoard) {\n                    (this.currentSingleChessBoard as any).onExtendLevelInfoReconnect(levelInfo);\n                } else {\n                    console.warn(\"当前棋盘控制器不支持断线重连方法\");\n                }\n            }\n        } else {\n            // 正常开始新游戏\n            GameMgr.Console.Log(\"关卡模式开始新游戏\");\n\n            // 重置关卡状态（包括清除测试预制体）\n            this.resetLevelState();\n\n            // 重置炸弹点击标记\n            this.lastClickWasBomb = false;\n\n            // 开始新游戏时，先重置当前棋盘（清理上一局的痕迹）\n            if (this.currentSingleChessBoard) {\n                this.currentSingleChessBoard.resetBoard();\n            }\n\n            // 更新地雷数UI\n            this.updateMineCountUI(levelInfo.mineCount);\n\n            // 使用当前设置的关卡编号，而不是后端返回的levelId\n            // 因为后端的levelId可能与前端的关卡编号不一致\n            this.enterLevel(this.currentLevel);\n\n            // 通知当前激活的单机棋盘控制器处理ExtendLevelInfo\n            if (this.currentSingleChessBoard) {\n                this.currentSingleChessBoard.onExtendLevelInfo();\n            }\n        }\n    }\n\n    /**\n     * 更新地雷数UI\n     * @param mineCount 地雷数量\n     */\n    private updateMineCountUI(mineCount: number) {\n        if (this.mineCountLabel) {\n            this.mineCountLabel.string = mineCount.toString();\n           \n        }\n    }\n\n    /**\n     * 更新当前关卡数UI\n     * @param levelNumber 关卡编号\n     */\n    private updateCurrentLevelUI(levelNumber: number) {\n        if (this.currentLevelLabel) {\n            this.currentLevelLabel.string = `第${levelNumber}关`;\n           \n        }\n    }\n\n    /**\n     * 根据关卡数进入相应的关卡（优化版本）\n     * @param levelNumber 关卡编号\n     */\n    private enterLevel(levelNumber: number) {\n        // 防止重复更新\n        if (this.isUpdating) {\n            return;\n        }\n        this.isUpdating = true;\n\n        // 更新关卡数UI显示\n        this.updateCurrentLevelUI(levelNumber);\n\n        // 获取目标地图节点和容器\n        const targetMapInfo = this.getMapNodeByLevel(levelNumber);\n        if (!targetMapInfo) {\n            cc.warn(`未知的关卡编号: ${levelNumber}`);\n            this.isUpdating = false;\n            return;\n        }\n\n        // 只有当目标节点与当前显示的节点不同时才进行切换\n        if (this.lastShownMapNode !== targetMapInfo.mapNode) {\n            // 隐藏上一个显示的地图节点\n            if (this.lastShownMapNode) {\n                this.lastShownMapNode.active = false;\n            }\n\n            // 显示目标容器和地图节点\n            this.showMapContainer(targetMapInfo.containerType);\n            this.showMapNodeOptimized(targetMapInfo.mapNode, targetMapInfo.mapName);\n\n            // 记录当前显示的节点\n            this.lastShownMapNode = targetMapInfo.mapNode;\n        }\n\n        // 设置当前激活的单机棋盘控制器\n        if (this.isHexLevel(levelNumber)) {\n            // 六边形关卡使用六边形控制器\n            if (this.hexSingleChessBoardController) {\n                const hexBoardType = this.getHexBoardTypeByLevel(levelNumber);\n              \n                this.hexSingleChessBoardController.initBoard(hexBoardType);\n                this.currentSingleChessBoard = this.hexSingleChessBoardController as any;\n            } else {\n                console.error(\"❌ 六边形单机控制器未配置！\");\n                this.currentSingleChessBoard = null;\n            }\n        } else {\n            // 四边形关卡使用四边形控制器\n            if (this.singleChessBoardController) {\n                const boardType = this.getBoardTypeByLevel(levelNumber);\n               \n                this.singleChessBoardController.initBoard(boardType);\n                this.currentSingleChessBoard = this.singleChessBoardController;\n            } else {\n                console.error(\"❌ 四边形单机控制器未配置！\");\n                this.currentSingleChessBoard = null;\n            }\n        }\n\n        this.isUpdating = false;\n    }\n\n    /**\n     * 根据关卡数获取对应的地图节点信息\n     * @param levelNumber 关卡编号\n     */\n    private getMapNodeByLevel(levelNumber: number): {mapNode: cc.Node, mapName: string, containerType: 'map1' | 'map2'} | null {\n        if (levelNumber >= 1 && levelNumber <= 4) {\n            return {mapNode: this.qipan8x8Node, mapName: \"qipan8*8\", containerType: 'map1'};\n        } else if (levelNumber === 5) {\n            return {mapNode: this.levelS001Node, mapName: \"Level_S001\", containerType: 'map2'};\n        } else if (levelNumber >= 6 && levelNumber <= 9) {\n            return {mapNode: this.qipan8x9Node, mapName: \"qipan8*9\", containerType: 'map1'};\n        } else if (levelNumber === 10) {\n            return {mapNode: this.levelS002Node, mapName: \"Level_S002\", containerType: 'map2'};\n        } else if (levelNumber >= 11 && levelNumber <= 14) {\n            return {mapNode: this.qipan9x9Node, mapName: \"qipan9*9\", containerType: 'map1'};\n        } else if (levelNumber === 15) {\n            return {mapNode: this.levelS003Node, mapName: \"Level_S003\", containerType: 'map2'};\n        } else if (levelNumber >= 16 && levelNumber <= 19) {\n            return {mapNode: this.qipan9x10Node, mapName: \"qipan9*10\", containerType: 'map1'};\n        } else if (levelNumber === 20) {\n            return {mapNode: this.levelS004Node, mapName: \"Level_S004\", containerType: 'map2'};\n        } else if (levelNumber >= 21 && levelNumber <= 24) {\n            return {mapNode: this.qipan10x10Node, mapName: \"qipan10*10\", containerType: 'map1'};\n        } else if (levelNumber === 25) {\n            return {mapNode: this.levelS005Node, mapName: \"Level_S005\", containerType: 'map2'};\n        } else if (levelNumber >= 26 && levelNumber <= 29) {\n            return {mapNode: this.qipan10x10Node, mapName: \"qipan10*10\", containerType: 'map1'};\n        } else if (levelNumber === 30) {\n            return {mapNode: this.levelS006Node, mapName: \"Level_S006\", containerType: 'map2'};\n        }\n        return null;\n    }\n\n\n\n    /**\n     * 根据关卡编号获取棋盘类型\n     * @param levelNumber 关卡编号\n     */\n    private getBoardTypeByLevel(levelNumber: number): string {\n        if (levelNumber >= 1 && levelNumber <= 4) {\n            return \"8x8\";\n        } else if (levelNumber >= 6 && levelNumber <= 9) {\n            return \"8x9\";\n        } else if (levelNumber >= 11 && levelNumber <= 14) {\n            return \"9x9\";\n        } else if (levelNumber >= 16 && levelNumber <= 19) {\n            return \"9x10\";\n        } else if (levelNumber >= 21 && levelNumber <= 24 || levelNumber >= 26 && levelNumber <= 29) {\n            return \"10x10\";\n        }\n        return \"8x8\"; // 默认\n    }\n\n    /**\n     * 判断是否为六边形关卡\n     * @param levelNumber 关卡编号\n     */\n    private isHexLevel(levelNumber: number): boolean {\n        // 特殊关卡使用六边形棋盘\n        return levelNumber === 5 || levelNumber === 10 || levelNumber === 15 ||\n               levelNumber === 20 || levelNumber === 25 || levelNumber === 30;\n    }\n\n    /**\n     * 根据关卡编号获取六边形棋盘类型\n     * @param levelNumber 关卡编号\n     */\n    private getHexBoardTypeByLevel(levelNumber: number): string {\n        // 六关对应六个六边形棋盘\n        if (levelNumber === 5) {\n            return \"hexBoard1\";  // 第1关六边形\n        } else if (levelNumber === 10) {\n            return \"hexBoard2\";  // 第2关六边形\n        } else if (levelNumber === 15) {\n            return \"hexBoard3\";  // 第3关六边形\n        } else if (levelNumber === 20) {\n            return \"hexBoard4\";  // 第4关六边形\n        } else if (levelNumber === 25) {\n            return \"hexBoard5\";  // 第5关六边形\n        } else if (levelNumber === 30) {\n            return \"hexBoard6\";  // 第6关六边形\n        }\n        return \"hexBoard1\"; // 默认\n    }\n\n    /**\n     * 显示指定的地图节点（优化版本）\n     * @param mapNode 要显示的地图节点\n     * @param mapName 地图名称（用于日志）\n     */\n    private showMapNodeOptimized(mapNode: cc.Node, mapName: string) {\n        if (mapNode) {\n            mapNode.active = true;\n        } else {\n            cc.warn(`❌ 地图节点未找到: ${mapName}`);\n        }\n    }\n\n    /**\n     * 显示指定的地图容器\n     * @param containerType 容器类型\n     */\n    private showMapContainer(containerType: 'map1' | 'map2') {\n        // 确保 level_page 节点是激活的\n        if (this.levelPageNode) {\n            this.levelPageNode.active = true;\n        }\n\n        // 根据容器类型显示对应容器，隐藏另一个\n        if (containerType === 'map1') {\n            if (this.gameMap1Node && !this.gameMap1Node.active) {\n                this.gameMap1Node.active = true;\n            }\n            if (this.gameMap2Node && this.gameMap2Node.active) {\n                this.gameMap2Node.active = false;\n            }\n        } else {\n            if (this.gameMap2Node && !this.gameMap2Node.active) {\n                this.gameMap2Node.active = true;\n            }\n            if (this.gameMap1Node && this.gameMap1Node.active) {\n                this.gameMap1Node.active = false;\n            }\n        }\n    }\n\n    /**\n     * 隐藏所有地图节点\n     */\n    private hideAllMapNodes() {\n        const allMapNodes = [\n            this.qipan8x8Node,\n            this.qipan8x9Node,\n            this.qipan9x9Node,\n            this.qipan9x10Node,\n            this.qipan10x10Node,\n            this.levelS001Node,\n            this.levelS002Node,\n            this.levelS003Node,\n            this.levelS004Node,\n            this.levelS005Node,\n            this.levelS006Node\n        ];\n\n        allMapNodes.forEach(node => {\n            if (node) {\n                node.active = false;\n            }\n        });\n    }\n\n    /**\n     * 设置当前关卡（从外部调用）\n     * @param levelNumber 关卡编号\n     */\n    public setCurrentLevel(levelNumber: number) {\n       \n\n        // 重置关卡状态（包括清除测试预制体）\n        this.resetLevelState();\n\n        this.currentLevel = levelNumber;\n\n        // 立即根据关卡数切换地图显示\n        this.enterLevel(levelNumber);\n    }\n\n    /**\n     * 获取当前关卡编号\n     */\n    public getCurrentLevel(): number {\n        return this.currentLevel;\n    }\n\n    /**\n     * 获取当前关卡信息\n     */\n    public getCurrentLevelInfo(): ExtendLevelInfoResponse {\n        return this.currentLevelInfo;\n    }\n\n    /**\n     * 获取当前房间ID\n     */\n    public getCurrentRoomId(): number {\n        return this.currentRoomId;\n    }\n\n    /**\n     * 隐藏所有地图容器\n     */\n    private hideAllMapContainers() {\n        // 确保 level_page 节点是激活的\n        if (this.levelPageNode) {\n            this.levelPageNode.active = true;\n          \n        }\n\n        // 隐藏两个主要的地图容器\n        if (this.gameMap1Node) {\n            this.gameMap1Node.active = false;\n            \n        }\n        if (this.gameMap2Node) {\n            this.gameMap2Node.active = false;\n           \n        }\n\n        // 同时隐藏所有具体的地图节点\n        this.hideAllMapNodes();\n    }\n\n    /**\n     * 显示 game_map_1 容器（方形地图）\n     */\n    private showGameMap1() {\n        if (this.gameMap1Node) {\n            this.gameMap1Node.active = true;\n            \n        } else {\n            cc.warn(\"❌ game_map_1 节点未找到\");\n        }\n    }\n\n    /**\n     * 显示 game_map_2 容器（特殊关卡）\n     */\n    private showGameMap2() {\n        if (this.gameMap2Node) {\n            this.gameMap2Node.active = true;\n\n        } else {\n            cc.warn(\"❌ game_map_2 节点未找到\");\n        }\n    }\n\n    /**\n     * 获取当前激活的单机棋盘控制器\n     */\n    public getCurrentSingleChessBoard(): SingleChessBoardController | HexSingleChessBoardController | null {\n        return this.currentSingleChessBoard;\n    }\n\n    /**\n     * 处理单机模式的点击响应\n     * @param response 点击响应数据\n     */\n    public handleSingleModeClickResponse(response: any) {\n        if (this.currentSingleChessBoard) {\n            const { x, y, result, chainReaction } = response;\n\n            // 处理点击结果\n            if (x !== undefined && y !== undefined && result !== undefined) {\n                this.currentSingleChessBoard.handleClickResponse(x, y, result);\n            }\n\n            // 处理连锁反应\n            if (chainReaction && Array.isArray(chainReaction)) {\n                this.currentSingleChessBoard.handleChainReaction(chainReaction);\n            }\n        }\n    }\n\n    /**\n     * 处理单机模式游戏结束\n     * @param gameEndData 游戏结束数据\n     */\n    public handleSingleModeGameEnd(gameEndData: any) {\n        if (this.currentSingleChessBoard) {\n            // 禁用棋盘触摸\n            this.currentSingleChessBoard.disableAllGridTouch();\n\n            // 处理游戏结束\n            this.currentSingleChessBoard.onLevelGameEnd();\n        }\n    }\n\n    /**\n     * 重置当前单机棋盘（仅在开始新游戏时调用）\n     */\n    public resetCurrentSingleChessBoard() {\n      \n        if (this.currentSingleChessBoard) {\n            // 重置棋盘状态（清理所有预制体和格子状态）\n            this.currentSingleChessBoard.resetBoard();\n\n            // 重新启用触摸事件\n            this.currentSingleChessBoard.enableAllGridTouch();\n        }\n    }\n\n    /**\n     * 注册单机模式消息处理器\n     */\n    private registerSingleModeMessageHandlers() {\n        // 监听WebSocket消息\n        GameMgr.Event.AddEventListener(EventType.ReceiveMessage, this.onReceiveMessage, this);\n    }\n\n    /**\n     * 取消单机模式消息监听\n     */\n    private unregisterSingleModeMessageHandlers() {\n        GameMgr.Event.RemoveEventListener(EventType.ReceiveMessage, this.onReceiveMessage, this);\n    }\n\n    /**\n     * 处理接收到的WebSocket消息\n     * @param messageBean 消息数据\n     */\n    private onReceiveMessage(messageBean: ReceivedMessageBean) {\n        switch (messageBean.msgId) {\n            case MessageId.MsgTypeLevelClickBlock:\n                this.onLevelClickBlockResponse(messageBean.data);\n                break;\n            case MessageId.MsgTypeLevelGameEnd:\n                this.onLevelGameEnd(messageBean.data);\n                break;\n        }\n    }\n\n    /**\n     * 处理LevelClickBlock响应\n     * @param response 点击响应数据\n     */\n    public onLevelClickBlockResponse(response: any) {\n      \n        if (this.currentSingleChessBoard) {\n            // 解构响应数据，支持多种可能的字段名\n            const {\n                x, y, q, r, result, action,\n                chainReaction, revealedGrids, floodFill, revealedBlocks,\n                floodFillResults  // 单机模式使用这个字段\n            } = response;\n\n            // 判断当前使用的是哪种控制器\n            const isUsingHexController = (this.currentSingleChessBoard === this.hexSingleChessBoardController as any);\n\n            // 根据控制器类型决定坐标处理方式\n            let coordX: number, coordY: number, coordQ: number, coordR: number;\n            let hasValidCoord = false;\n\n            if (isUsingHexController) {\n                // 六边形控制器：优先使用六边形坐标，如果没有则将x,y映射为q,r\n                if (q !== undefined && r !== undefined) {\n                    coordQ = q;\n                    coordR = r;\n                    hasValidCoord = true;\n                   \n                } else if (x !== undefined && y !== undefined) {\n                    // 服务器返回x,y字段，但实际是六边形坐标：x=q, y=r\n                    coordQ = x;  // x 就是 q\n                    coordR = y;  // y 就是 r\n                    hasValidCoord = true;\n                    \n                }\n            } else {\n                // 四边形控制器：使用四边形坐标\n                if (x !== undefined && y !== undefined) {\n                    coordX = x;\n                    coordY = y;\n                    hasValidCoord = true;\n                   \n                } else if (q !== undefined && r !== undefined) {\n                    console.warn(`⚠️ 四边形控制器收到六边形坐标 (${q}, ${r})，这可能不正确`);\n                    hasValidCoord = false;\n                }\n            }\n\n            if (hasValidCoord && result !== undefined) {\n               \n\n                if (action === 2) {\n                    // 标记/取消标记操作，不调用handleClickResponse，避免格子消失\n                  \n                    // 不调用 handleClickResponse，因为标记操作不应该隐藏格子\n                } else if (action === 1) {\n                    // 挖掘操作\n                   \n\n                    // 检查是否点到炸弹\n                    if (result === \"boom\" || result === \"mine\") {\n                       \n                        this.lastClickWasBomb = true;\n                    }\n\n                    // 根据控制器类型调用对应的方法\n                    if (isUsingHexController) {\n                        // 六边形控制器\n                        (this.currentSingleChessBoard as any).handleClickResponse(coordQ, coordR, result);\n                    } else {\n                        // 四边形控制器\n                        this.currentSingleChessBoard.handleClickResponse(coordX, coordY, result);\n                    }\n\n                    // 处理连锁展开数据\n                    if (floodFillResults && Array.isArray(floodFillResults) && floodFillResults.length > 0) {\n                        \n\n                        if (isUsingHexController) {\n                            // 六边形控制器使用handleChainReaction方法\n                         \n                            (this.currentSingleChessBoard as any).handleChainReaction(floodFillResults);\n                        } else {\n                            // 四边形控制器使用handleFloodFillResults方法\n\n                            (this.currentSingleChessBoard as SingleChessBoardController).handleFloodFillResults(floodFillResults);\n                        }\n                    }\n                } else {\n                    // 其他操作，默认按挖掘处理\n                   \n                    if (isUsingHexController) {\n                        (this.currentSingleChessBoard as any).handleClickResponse(coordQ, coordR, result);\n                    } else {\n                        this.currentSingleChessBoard.handleClickResponse(coordX, coordY, result);\n                    }\n                }\n            } else {\n                console.warn(\"⚠️ 响应数据缺少有效坐标或结果信息\");\n                console.warn(`   控制器类型: ${isUsingHexController ? '六边形' : '四边形'}`);\n                console.warn(`   坐标数据: x=${x}, y=${y}, q=${q}, r=${r}`);\n                console.warn(`   结果: ${result}`);\n            }\n\n         \n\n            \n\n            \n        }\n    }\n\n    /**\n     * 处理LevelGameEnd通知\n     * @param gameEndData 游戏结束数据\n     */\n    public onLevelGameEnd(gameEndData: any) {\n\n\n        if (this.currentSingleChessBoard) {\n            // 禁用棋盘触摸\n            this.currentSingleChessBoard.disableAllGridTouch();\n\n            // 处理游戏结束（不清理数据）\n            this.currentSingleChessBoard.onLevelGameEnd();\n        }\n\n        // 禁用返回按钮，防止游戏结束时玩家误点击\n        this.disableBackButton();\n\n        // 检查是否点到了炸弹，如果是游戏失败且点到炸弹，则延迟显示结算页面\n        const isGameFailed = !gameEndData.success;\n        const hasBombExploded = this.lastClickWasBomb ||\n                               (this.currentSingleChessBoard && this.currentSingleChessBoard.hasBombExplodedInThisGame());\n\n\n\n        if (isGameFailed && hasBombExploded) {\n            // 点到炸弹导致的游戏失败，延迟1.5秒显示结算页面\n\n            this.scheduleOnce(() => {\n\n                this.showLevelSettlement(gameEndData);\n                // 重置标记\n                this.lastClickWasBomb = false;\n            }, 1.5);\n        } else {\n            // 其他情况，立即显示结算页面\n\n            this.showLevelSettlement(gameEndData);\n            // 重置标记\n            this.lastClickWasBomb = false;\n        }\n    }\n\n    /**\n     * 设置结算页面按钮事件\n     */\n    private setupSettlementButtons() {\n        // 再来一次按钮\n        if (this.retryButton) {\n            this.retryButton.node.on('click', this.onRetryButtonClick, this);\n        }\n\n        // 下一关按钮\n        if (this.nextLevelButton) {\n            this.nextLevelButton.node.on('click', this.onNextLevelButtonClick, this);\n        }\n\n        // 退出按钮 - 使用按压效果，模仿其他返回按钮\n        if (this.exitButton) {\n            Tools.imageButtonClick(this.exitButton.node, Config.buttonRes + 'board_btn_back_normal', Config.buttonRes + 'board_btn_back_pressed', () => {\n                this.onExitButtonClick();\n            });\n        }\n    }\n\n    /**\n     * 显示结算页面\n     * @param gameEndData 游戏结束数据\n     */\n    private showLevelSettlement(gameEndData: any) {\n       \n        if (!this.levelSettlementNode) {\n            console.error(\"levelSettlementNode 未配置\");\n            return;\n        }\n\n        // 显示结算页面\n        \n        this.levelSettlementNode.active = true;\n\n        // 根据游戏结果显示对应的背景 - 修复成功判断逻辑\n        const isSuccess = gameEndData.isSuccess || gameEndData.success || gameEndData.isWin || gameEndData.gameStatus === 1;\n\n       \n\n        if (isSuccess) {\n            // 成功 - 显示胜利背景\n            if (this.winBgNode) {\n                this.winBgNode.active = true;\n            }\n            if (this.loseBgNode) {\n                this.loseBgNode.active = false;\n            }\n            \n        } else {\n            // 失败 - 显示失败背景\n            if (this.loseBgNode) {\n                this.loseBgNode.active = true;\n            }\n            if (this.winBgNode) {\n                this.winBgNode.active = false;\n            }\n           \n        }\n    }\n\n    /**\n     * 再来一次按钮点击事件\n     */\n    private onRetryButtonClick() {\n\n\n        // 关闭结算页面\n        this.hideLevelSettlement();\n\n        // 重新启用返回按钮\n        this.enableBackButton();\n\n        // 注意：不在这里重置棋盘，等收到ExtendLevelInfo响应时再重置\n        // 这样可以避免过早清理，让玩家在点击按钮后还能短暂看到游玩痕迹\n\n        // 发送当前关卡的ExtendLevelInfo\n        this.sendExtendLevelInfo(this.currentLevel);\n    }\n\n    /**\n     * 下一关按钮点击事件\n     */\n    private onNextLevelButtonClick() {\n\n\n        // 关闭结算页面\n        this.hideLevelSettlement();\n\n        // 重新启用返回按钮\n        this.enableBackButton();\n\n        // 进入下一关\n        const nextLevel = this.currentLevel + 1;\n        this.setCurrentLevel(nextLevel);\n\n        // 注意：不在这里重置棋盘，等收到ExtendLevelInfo响应时再重置\n        // 这样可以避免过早清理，让玩家在点击按钮后还能短暂看到游玩痕迹\n\n        // 发送下一关的ExtendLevelInfo\n        this.sendExtendLevelInfo(nextLevel);\n    }\n\n    /**\n     * 退出按钮点击事件\n     */\n    private onExitButtonClick() {\n\n\n        // 关闭结算页面\n        this.hideLevelSettlement();\n\n        // 重新启用返回按钮（虽然要退出了，但保持一致性）\n        this.enableBackButton();\n\n        // 返回到关卡选择页面（匹配界面）\n        this.returnToLevelSelect();\n    }\n\n    /**\n     * 隐藏结算页面\n     */\n    private hideLevelSettlement() {\n        if (this.levelSettlementNode) {\n            this.levelSettlementNode.active = false;\n        }\n    }\n\n    /**\n     * 发送ExtendLevelInfo消息\n     * @param levelId 关卡ID\n     */\n    private sendExtendLevelInfo(levelId: number) {\n       \n\n        const request = {\n            levelId: levelId\n        };\n\n        WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeExtendLevelInfo, request);\n    }\n\n    /**\n     * 设置测试按钮\n     */\n    private setupDebugButton() {\n        if (this.debugShowMinesButton) {\n            this.debugShowMinesButton.node.on('click', this.onDebugShowMinesClick, this);\n        }\n    }\n\n    /**\n     * 测试按钮点击事件 - 发送DebugShowMines消息\n     */\n    private onDebugShowMinesClick() {\n       \n        WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeDebugShowMines, {});\n    }\n\n    /**\n     * 判断是否在单机模式\n     */\n    private isInSingleMode(): boolean {\n        // 单机模式的判断逻辑：当前页面是关卡页面且有有效的房间ID\n        return this.currentRoomId > 0;\n    }\n\n    /**\n     * 处理DebugShowMines响应，在炸弹位置生成测试预制体\n     * @param minePositions 炸弹位置数组 [{x: number, y: number}]\n     */\n    public handleDebugShowMines(minePositions: Array<{x: number, y: number}>) {\n        \n        if (!this.debugMinePrefab) {\n            cc.warn(\"debugMinePrefab 预制体未设置，无法显示测试标记\");\n            return;\n        }\n\n        if (!this.currentSingleChessBoard) {\n            cc.warn(\"当前没有激活的单机棋盘\");\n            return;\n        }\n\n        if (!minePositions || !Array.isArray(minePositions) || minePositions.length === 0) {\n            cc.warn(\"地雷位置数据无效:\", minePositions);\n            return;\n        }\n\n       \n\n        // 先尝试直接创建一个测试预制体，不使用延迟\n        if (minePositions.length > 0) {\n            const firstPosition = minePositions[0];\n           \n            // 检查坐标字段\n            let coordX: number, coordY: number;\n            const pos = firstPosition as any;  // 使用any类型避免TypeScript报错\n\n            if (pos.x !== undefined && pos.y !== undefined) {\n                coordX = pos.x;\n                coordY = pos.y;\n               \n            } else if (pos.q !== undefined && pos.r !== undefined) {\n                coordX = pos.q;\n                coordY = pos.r;\n                \n            } else {\n                console.error(`❌ 无法识别坐标字段:`, firstPosition);\n                return;\n            }\n\n            // 直接调用，不使用延迟\n            this.createDebugMinePrefab(coordX, coordY);\n        }\n\n        // 在每个炸弹位置生成测试预制体\n        minePositions.forEach((position, index) => {\n            const pos = position as any;  // 使用any类型避免TypeScript报错\n\n            // 获取坐标\n            let coordX: number, coordY: number;\n            if (pos.x !== undefined && pos.y !== undefined) {\n                coordX = pos.x;\n                coordY = pos.y;\n            } else if (pos.q !== undefined && pos.r !== undefined) {\n                coordX = pos.q;\n                coordY = pos.r;\n            } else {\n                console.error(`❌ 地雷位置${index}坐标字段无效:`, position);\n                return;\n            }\n\n            if (index === 0) {\n                // 第一个不延迟，立即执行（已经在上面处理过了，跳过）\n                return;\n            } else {\n                // 其他的使用延迟 - 修复闭包问题\n                const capturedX = coordX;  // 捕获当前值\n                const capturedY = coordY;  // 捕获当前值\n                this.scheduleOnce(() => {\n                    \n                    this.createDebugMinePrefab(capturedX, capturedY);\n                }, index * 0.1);\n            }\n        });\n    }\n\n    /**\n     * 在指定位置创建测试预制体\n     * @param x 格子x坐标（四边形）或q坐标（六边形）\n     * @param y 格子y坐标（四边形）或r坐标（六边形）\n     */\n    private createDebugMinePrefab(x: number, y: number) {\n       \n        // 检查坐标是否有效\n        if (x === undefined || y === undefined || x === null || y === null) {\n            console.error(`❌ 无效的坐标参数: x=${x}, y=${y}`);\n            console.error(`   x类型: ${typeof x}, y类型: ${typeof y}`);\n            console.error(`   调用堆栈:`, new Error().stack);\n            return;\n        }\n\n        if (!this.debugMinePrefab) {\n            cc.error(\"debugMinePrefab 为空，无法创建测试预制体\");\n            return;\n        }\n\n        if (!this.currentSingleChessBoard) {\n            cc.error(\"currentSingleChessBoard 为空，无法创建测试预制体\");\n            return;\n        }\n\n        try {\n            // 判断当前使用的是哪种控制器\n            const isUsingHexController = (this.currentSingleChessBoard === this.hexSingleChessBoardController as any);\n\n            let debugNode: cc.Node = null;\n\n            if (isUsingHexController) {\n                // 六边形控制器：x实际是q，y实际是r\n               \n                debugNode = (this.currentSingleChessBoard as any).createCustomPrefab(\n                    x, y,  // 对于六边形，x就是q，y就是r\n                    this.debugMinePrefab,\n                    `DebugMine_${x}_${y}`\n                );\n            } else {\n                // 四边形控制器\n               \n                debugNode = this.currentSingleChessBoard.createCustomPrefab(\n                    x, y,\n                    this.debugMinePrefab,\n                    `DebugMine_${x}_${y}`\n                );\n            }\n\n            if (debugNode) {\n                // 将创建的节点存储起来，用于后续清理\n                this.debugMineNodes.push(debugNode);\n               \n            } else {\n                cc.error(`❌ 在位置 (${x}, ${y}) 创建测试预制体失败，返回值为空`);\n            }\n        } catch (error) {\n            cc.error(`❌ 创建测试预制体时发生错误:`, error);\n        }\n    }\n\n    /**\n     * 清除所有测试预制体\n     */\n    public clearDebugMines() {\n       \n\n        this.debugMineNodes.forEach((node, index) => {\n            if (node && cc.isValid(node)) {\n             \n                node.destroy();\n            }\n        });\n\n        // 清空数组\n        this.debugMineNodes = [];\n       \n    }\n\n    /**\n     * 重置关卡状态（包括清除测试预制体）\n     */\n    public resetLevelState() {\n       \n        this.clearDebugMines();\n        // 这里可以添加其他需要重置的状态\n    }\n\n    onDestroy() {\n        // 取消消息监听\n        this.unregisterSingleModeMessageHandlers();\n\n        // 清理测试预制体\n        this.clearDebugMines();\n\n        // 清理按钮事件\n        if (this.retryButton) {\n            this.retryButton.node.off('click', this.onRetryButtonClick, this);\n        }\n        if (this.nextLevelButton) {\n            this.nextLevelButton.node.off('click', this.onNextLevelButtonClick, this);\n        }\n        // 退出按钮使用 Tools.imageButtonClick，会自动管理事件，无需手动清理\n        if (this.debugShowMinesButton) {\n            this.debugShowMinesButton.node.off('click', this.onDebugShowMinesClick, this);\n        }\n    }\n}\n"]}