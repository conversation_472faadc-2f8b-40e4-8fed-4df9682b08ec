{"version": 3, "sources": ["assets/meshTools/tools/MeshSdkApi.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA,0CAAyC;AACzC,sCAAqC;AACrC,qEAAmF;AACnF,wDAAuD;AACvD,gEAA6D;AAE7D,IAAI,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;AACjC,0CAA0C;AAE1C;IAAwC,8BAAO;IAA/C;QAAA,qEA+GC;QA7GW,cAAQ,GAAQ,OAAO,CAAC,OAAO,CAAC,CAAC,wBAAwB;QACzD,uBAAiB,GAAY,KAAK,CAAC;QACpC,wBAAkB,GAAY,KAAK,CAAC;QACpC,yBAAmB,GAAY,KAAK,CAAC;;IA0GhD,CAAC;IAxGG,uBAAuB;IAChB,gCAAW,GAAlB;QAEI,uCAAuC;QACvC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC;YAC5B,OAAO;YACP,YAAY,EAAE;gBAEV,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;gBAC5B,IAAI,eAAe,GAAoB;oBACnC,OAAO,EAAE,+BAAa,CAAC,eAAe;oBACtC,MAAM,EAAE,EAAE;iBACb,CAAA;gBACD,iBAAO,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAS,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;YAC/D,CAAC;YAED,YAAY;YACZ,gBAAgB,EAAE,UAAU,IAAS;gBAEjC,IAAI,CAAC,IAAI,GAAG,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAE1C,IAAI,eAAe,GAAoB;oBACnC,OAAO,EAAE,+BAAa,CAAC,mBAAmB;oBAC1C,MAAM,EAAE,EAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAC;iBAC5B,CAAA;gBACD,iBAAO,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAS,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;YAC/D,CAAC;SACJ,CAAC,CAAC;IACP,CAAC;IAED,qBAAqB;IACd,kCAAa,GAApB,UAAqB,QAAkB,EAAE,WAAqB;QAE1D,QAAQ,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC;IAClC,CAAC;IAED,sBAAsB;IACf,8BAAS,GAAhB,UAAkB,QAA6B;QAE3C,IAAI,qBAAS,CAAC,OAAO,CAAC,WAAW,EAAE;YAC/B,QAAQ,CAAC;gBACL,YAAY,EAAE;oBACV,cAAc,EAAE,EAAE;oBAClB,WAAW,EAAE,CAAC;iBACjB;gBACD,MAAM,EAAE,qBAAS,CAAC,OAAO,CAAC,IAAI;gBAC9B,OAAO,EAAG,qBAAS,CAAC,OAAO,CAAC,KAAK;gBACjC,UAAU,EAAG,qBAAS,CAAC,OAAO,CAAC,QAAQ;gBACvC,UAAU,EAAG,qBAAS,CAAC,OAAO,CAAC,QAAQ;gBACvC,QAAQ,EAAG,qBAAS,CAAC,OAAO,CAAC,MAAM;gBACnC,QAAQ,EAAG,qBAAS,CAAC,OAAO,CAAC,MAAM;gBACnC,YAAY,EAAG,qBAAS,CAAC,OAAO,CAAC,UAAU;gBAC3C,KAAK,EAAG,qBAAS,CAAC,OAAO,CAAC,GAAG;aAChC,CAAC,CAAC;YACH,OAAO;SACV;QAED,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC;gBACL,YAAY,EAAE;oBACV,cAAc,EAAE,wEAAwE;oBACxF,WAAW,EAAE,CAAC;iBACjB;gBACD,MAAM,EAAE,8DAA8D;gBACtE,OAAO,EAAE,QAAQ;gBACjB,UAAU,EAAE,GAAG;gBACf,UAAU,EAAE,CAAC;gBACb,QAAQ,EAAE,KAAK;gBACf,QAAQ,EAAE,QAAQ;gBAClB,YAAY,EAAE,OAAO;gBACrB,KAAK,EAAC,IAAI;aACb,CAAC,CAAC;YACH,OAAO;SACV;QAED,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,UAAC,GAAQ;YAEpD,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC;IACP,CAAC;IAED,iBAAiB;IACV,gCAAW,GAAlB;QAEI,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,EACnC;YACI,OAAO;SACV;QACD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC/B,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC;IAC/B,CAAC;IAED,aAAa;IACN,iCAAY,GAAnB;QAEI,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;IAC5B,CAAC;IAED,kBAAkB;IACX,gCAAW,GAAlB,UAAoB,IAAa;QAE7B,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACjB,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC;IACjC,CAAC;IACL,iBAAC;AAAD,CA/GA,AA+GC,CA/GuC,iBAAO,GA+G9C", "file": "", "sourceRoot": "/", "sourcesContent": ["import { MeshTools } from \"../MeshTools\";\r\nimport { BaseSDK } from \"../BaseSDK\";\r\nimport { AutoMessageBean, AutoMessageId } from \"../../scripts/net/MessageBaseBean\";\r\nimport { GameMgr } from \"../../scripts/common/GameMgr\";\r\nimport { EventType } from \"../../scripts/common/EventCenter\";\r\n\r\nvar MeshSdk = require(\"MeshSdk\");\r\n// var ZegoGameClient = ZG.ZegoGameClient;\r\n\r\nexport default class MeshSdkApi extends BaseSDK\r\n{\r\n    private _meshSdk: any = MeshSdk.meshSDK; // new ZegoGameClient();\r\n    private _isChangeVoluming: boolean = false;\r\n    public IsNotifyGameLoaded: boolean = false;\r\n    public IsNotifyGameLoading: boolean = false;\r\n    \r\n    // 监听 APP 事件  APP -> H5\r\n    public AddAPPEvent (): void\r\n    {\r\n        // regReceiveMessage  注册 APP 回调到 H5 的函数\r\n        this._meshSdk.regReceiveMessage({\r\n            // 余额变更\r\n            walletUpdate: function () \r\n            {\r\n                console.log(\"walletUpdate\");\r\n                let autoMessageBean: AutoMessageBean = {\r\n                    'msgId': AutoMessageId.WalletUpdateMsg,//更新金豆余额的通知\r\n                    'data': {}\r\n                }\r\n                GameMgr.Event.Send(EventType.AutoMessage, autoMessageBean);\r\n            },\r\n            \r\n            // code 变更回调\r\n            serverCodeUpdate: function (data: any)\r\n            {\r\n                data.code = encodeURIComponent(data.code);\r\n            \r\n                let autoMessageBean: AutoMessageBean = {\r\n                    'msgId': AutoMessageId.ServerCodeUpdateMsg,//更新 code 的通知\r\n                    'data': {code: data.code}\r\n                }\r\n                GameMgr.Event.Send(EventType.AutoMessage, autoMessageBean);\r\n            }\r\n        });\r\n    }\r\n\r\n    // H5 调用APP 获取APP 版本号\r\n    public GetAppVersion(callback: Function, errCallback: Function): void \r\n    {\r\n        callback && callback(\"1.0.0\");\r\n    }\r\n  \r\n    // H5 调用 APP  获取用户信息数据\r\n    public GetConfig (callback: (data: any) => void): void\r\n    {\r\n        if (MeshTools.Publish.isDataByURL) {\r\n            callback({\r\n                \"gameConfig\": {\r\n                    \"currencyIcon\": \"\",\r\n                    \"sceneMode\": 0\r\n                },\r\n                \"code\": MeshTools.Publish.code,\r\n                \"appId\":  MeshTools.Publish.appId,\r\n                \"language\":  MeshTools.Publish.language,\r\n                \"gameMode\":  MeshTools.Publish.gameMode,\r\n                \"userId\":  MeshTools.Publish.userId,\r\n                \"roomId\":  MeshTools.Publish.roomId,\r\n                \"appChannel\":  MeshTools.Publish.appChannel,\r\n                \"gsp\":  MeshTools.Publish.gsp\r\n            });\r\n            return;\r\n        }\r\n\r\n        if (CC_DEBUG) {\r\n            callback({\r\n                \"gameConfig\": {\r\n                    \"currencyIcon\": \"https://game-center-test.jieyou.shop/static/images/index/game_bean.png\", // 货币图标地址\r\n                    \"sceneMode\": 0 \r\n                },\r\n                \"code\": \"qFwaAVKyEYTmPey1vWA4Huq7bvto4xexT0UJRnh03vlwTghRwFyVsbO4JRLV\", \r\n                \"appId\": 66666666,\r\n                \"language\": \"0\",\r\n                \"gameMode\": 3,\r\n                \"userId\": 10804,//new Date(),\r\n                \"roomId\": \"room01\",\r\n                \"appChannel\": \"debug\",\r\n                \"gsp\":8001\r\n            });\r\n            return;\r\n        }\r\n\r\n        this._meshSdk.getConfig().then(callback).catch((err: any) =>\r\n        {\r\n            cc.error(err);\r\n        });\r\n    }\r\n\r\n    // 通知APP 游戏资源加载完了\r\n    public HideLoading (): void\r\n    {\r\n        if (this.IsNotifyGameLoaded == true)\r\n        {\r\n            return;\r\n        }\r\n        this.IsNotifyGameLoaded = true;\r\n        this._meshSdk.gameLoaded();\r\n    }\r\n\r\n    // 销毁 WebView\r\n    public CloseWebView (): void\r\n    {   \r\n        this._meshSdk.destroy();\r\n    }\r\n\r\n    // 余额不足  H5 调用 APP\r\n    public ShowAppShop (type?: number): void\r\n    {\r\n        cc.log(\"余额不足回调\");\r\n        this._meshSdk.gameRecharge();\r\n    }\r\n}\r\n\r\ndeclare var require: any;"]}