
(function () {
var scripts = [{"deps":{"./joui18n-script/LocalizedSprite":1,"./joui18n-script/LocalizedLabel":2,"./assets/meshTools/Singleton":7,"./assets/meshTools/BaseSDK":15,"./assets/meshTools/tools/MeshSdkApi":22,"./assets/meshTools/tools/Publish":16,"./assets/meshTools/tools/MeshSdk":3,"./assets/scripts/TipsDialogController":17,"./assets/scripts/ToastController":18,"./assets/scripts/GlobalManagerController":24,"./assets/scripts/bean/GameBean":4,"./assets/scripts/bean/GlobalBean":27,"./assets/scripts/bean/LanguageType":19,"./assets/scripts/bean/EnumBean":21,"./assets/scripts/common/GameData":23,"./assets/scripts/common/GameMgr":9,"./assets/scripts/common/GameTools":20,"./assets/scripts/common/MineConsole":25,"./assets/scripts/common/EventCenter":58,"./assets/scripts/game/BtnController":26,"./assets/scripts/game/CongratsDialogController":28,"./assets/scripts/game/GamePageController":33,"./assets/scripts/game/GameScoreController":42,"./assets/scripts/game/AIManagedDialogController":31,"./assets/scripts/game/Chess/GridController":5,"./assets/scripts/game/Chess/HexChessBoardController":40,"./assets/scripts/game/Chess/HexSingleChessBoardController":29,"./assets/scripts/game/Chess/SingleChessBoardController":30,"./assets/scripts/game/Chess/ChessBoardController":41,"./assets/scripts/hall/HallCenterLayController":59,"./assets/scripts/hall/HallCreateRoomController":32,"./assets/scripts/hall/HallJoinRoomController":34,"./assets/scripts/hall/HallPageController":35,"./assets/scripts/hall/HallParentController":36,"./assets/scripts/hall/InfoDialogController":37,"./assets/scripts/hall/KickOutDialogController":38,"./assets/scripts/hall/LeaveDialogController":48,"./assets/scripts/hall/LevelSelectDemo":39,"./assets/scripts/hall/MatchParentController":69,"./assets/scripts/hall/PlayerLayoutController":43,"./assets/scripts/hall/SettingDialogController":44,"./assets/scripts/hall/TopUpDialogController":46,"./assets/scripts/hall/HallAutoController":45,"./assets/scripts/hall/Level/LevelSelectController":6,"./assets/scripts/hall/Level/LevelSelectExample":47,"./assets/scripts/hall/Level/LevelSelectPageController":49,"./assets/scripts/hall/Level/ScrollViewHelper":51,"./assets/scripts/hall/Level/LevelItemController":50,"./assets/scripts/level/LevelPageController":11,"./assets/scripts/net/GameServerUrl":10,"./assets/scripts/net/HttpManager":52,"./assets/scripts/net/HttpUtils":53,"./assets/scripts/net/IHttpMsgBody":56,"./assets/scripts/net/MessageBaseBean":54,"./assets/scripts/net/MessageId":63,"./assets/scripts/net/WebSocketManager":55,"./assets/scripts/net/WebSocketTool":57,"./assets/scripts/net/ErrorCode":67,"./assets/scripts/pfb/InfoItemController":61,"./assets/scripts/pfb/InfoItemOneController":12,"./assets/scripts/pfb/MatchItemController":73,"./assets/scripts/pfb/PlayerGameController ":60,"./assets/scripts/pfb/PlayerScoreController":62,"./assets/scripts/pfb/SeatItemController":66,"./assets/scripts/pfb/CongratsItemController":64,"./assets/scripts/start_up/StartUpPageController":13,"./assets/scripts/start_up/StartUpCenterController":65,"./assets/scripts/util/AudioMgr":14,"./assets/scripts/util/BlockingQueue":79,"./assets/scripts/util/Config":68,"./assets/scripts/util/Dictionary":77,"./assets/scripts/util/LocalStorageManager":75,"./assets/scripts/util/NickNameLabel":74,"./assets/scripts/util/Tools":70,"./assets/scripts/util/AudioManager":71,"./assets/meshTools/MeshTools":72,"./assets/resources/i18n/zh_HK":8,"./assets/resources/i18n/en":78,"./assets/resources/i18n/zh_CN":76},"path":"preview-scripts/__qc_index__.js"},{"deps":{},"path":"preview-scripts/joui18n-script/LocalizedSprite.js"},{"deps":{},"path":"preview-scripts/joui18n-script/LocalizedLabel.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/tools/MeshSdk.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/GameBean.js"},{"deps":{},"path":"preview-scripts/assets/scripts/game/Chess/GridController.js"},{"deps":{"./ScrollViewHelper":51},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectController.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/Singleton.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/zh_HK.js"},{"deps":{"../../meshTools/tools/MeshSdkApi":22,"./EventCenter":58,"./GameData":23,"./GameTools":20,"./MineConsole":25},"path":"preview-scripts/assets/scripts/common/GameMgr.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/GameServerUrl.js"},{"deps":{"../GlobalManagerController":24,"../hall/LeaveDialogController":48,"../util/Config":68,"../util/Tools":70,"../net/MessageId":63,"../net/WebSocketManager":55,"../common/EventCenter":58,"../common/GameMgr":9,"../game/Chess/SingleChessBoardController":30,"../game/Chess/HexSingleChessBoardController":29},"path":"preview-scripts/assets/scripts/level/LevelPageController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/pfb/InfoItemOneController.js"},{"deps":{"../common/GameMgr":9,"./StartUpCenterController":65},"path":"preview-scripts/assets/scripts/start_up/StartUpPageController.js"},{"deps":{"./Config":68,"./Dictionary":77},"path":"preview-scripts/assets/scripts/util/AudioMgr.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/BaseSDK.js"},{"deps":{"../Singleton":7},"path":"preview-scripts/assets/meshTools/tools/Publish.js"},{"deps":{"./util/Config":68,"./util/Tools":70},"path":"preview-scripts/assets/scripts/TipsDialogController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/ToastController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/LanguageType.js"},{"deps":{"../../meshTools/Singleton":7},"path":"preview-scripts/assets/scripts/common/GameTools.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/EnumBean.js"},{"deps":{"../MeshTools":72,"../BaseSDK":15,"../../scripts/net/MessageBaseBean":54,"../../scripts/common/GameMgr":9,"../../scripts/common/EventCenter":58,"MeshSdk":3},"path":"preview-scripts/assets/meshTools/tools/MeshSdkApi.js"},{"deps":{"../../meshTools/MeshTools":72,"../../meshTools/Singleton":7,"../net/GameServerUrl":10},"path":"preview-scripts/assets/scripts/common/GameData.js"},{"deps":{"./TipsDialogController":17,"./ToastController":18,"../meshTools/MeshTools":72,"../meshTools/tools/Publish":16,"./bean/GlobalBean":27,"./bean/LanguageType":19,"./bean/EnumBean":21,"./common/GameMgr":9,"./common/EventCenter":58,"./game/GamePageController":33,"./hall/TopUpDialogController":46,"./hall/HallPageController":35,"./level/LevelPageController":11,"./net/GameServerUrl":10,"./net/MessageBaseBean":54,"./net/MessageId":63,"./net/WebSocketManager":55,"./net/WebSocketTool":57,"./net/ErrorCode":67,"./start_up/StartUpPageController":13,"./util/Config":68,"./util/AudioMgr":14},"path":"preview-scripts/assets/scripts/GlobalManagerController.js"},{"deps":{"../../meshTools/Singleton":7},"path":"preview-scripts/assets/scripts/common/MineConsole.js"},{"deps":{"../util/AudioManager":71,"../util/Config":68,"../util/LocalStorageManager":75},"path":"preview-scripts/assets/scripts/game/BtnController.js"},{"deps":{"../../meshTools/Singleton":7,"../hall/HallAutoController":45},"path":"preview-scripts/assets/scripts/bean/GlobalBean.js"},{"deps":{"../bean/GlobalBean":27,"../common/EventCenter":58,"../common/GameMgr":9,"../net/MessageBaseBean":54,"../pfb/CongratsItemController":64,"../util/Config":68,"../util/Tools":70},"path":"preview-scripts/assets/scripts/game/CongratsDialogController.js"},{"deps":{"../../net/MessageId":63,"../../net/WebSocketManager":55},"path":"preview-scripts/assets/scripts/game/Chess/HexSingleChessBoardController.js"},{"deps":{"../../net/MessageId":63,"../../net/WebSocketManager":55},"path":"preview-scripts/assets/scripts/game/Chess/SingleChessBoardController.js"},{"deps":{"../net/WebSocketManager":55,"../net/MessageId":63,"../util/Config":68},"path":"preview-scripts/assets/scripts/game/AIManagedDialogController.js"},{"deps":{"../bean/GlobalBean":27,"../pfb/SeatItemController":66,"../util/Config":68,"../util/Tools":70},"path":"preview-scripts/assets/scripts/hall/HallCreateRoomController.js"},{"deps":{"./CongratsDialogController":28,"./GameScoreController":42,"./AIManagedDialogController":31,"../bean/GlobalBean":27,"../hall/LeaveDialogController":48,"../util/Config":68,"../util/Tools":70,"../util/AudioManager":71,"./Chess/HexChessBoardController":40,"./Chess/ChessBoardController":41,"../pfb/PlayerGameController ":60,"../net/MessageId":63,"../net/WebSocketManager":55},"path":"preview-scripts/assets/scripts/game/GamePageController.js"},{"deps":{"../util/Tools":70},"path":"preview-scripts/assets/scripts/hall/HallJoinRoomController.js"},{"deps":{"../ToastController":18,"./HallParentController":36,"./InfoDialogController":37,"./KickOutDialogController":38,"./LeaveDialogController":48,"./MatchParentController":69,"./SettingDialogController":44,"../bean/GlobalBean":27,"../common/GameMgr":9,"../net/WebSocketManager":55,"../net/WebSocketTool":57,"../net/MessageId":63,"../util/AudioManager":71,"./Level/LevelSelectPageController":49},"path":"preview-scripts/assets/scripts/hall/HallPageController.js"},{"deps":{"../../meshTools/tools/Publish":16,"../bean/GlobalBean":27,"../common/GameMgr":9,"../net/MessageId":63,"../net/WebSocketManager":55,"../ToastController":18,"../util/Config":68,"../util/Tools":70,"./HallCenterLayController":59},"path":"preview-scripts/assets/scripts/hall/HallParentController.js"},{"deps":{"../util/Config":68,"../util/Tools":70},"path":"preview-scripts/assets/scripts/hall/InfoDialogController.js"},{"deps":{"../net/MessageId":63,"../net/WebSocketManager":55,"../util/Config":68,"../util/Tools":70},"path":"preview-scripts/assets/scripts/hall/KickOutDialogController.js"},{"deps":{"./Level/LevelSelectController":6},"path":"preview-scripts/assets/scripts/hall/LevelSelectDemo.js"},{"deps":{"../../bean/GlobalBean":27,"../../pfb/PlayerGameController ":60},"path":"preview-scripts/assets/scripts/game/Chess/HexChessBoardController.js"},{"deps":{"../../bean/GlobalBean":27,"../../pfb/PlayerGameController ":60},"path":"preview-scripts/assets/scripts/game/Chess/ChessBoardController.js"},{"deps":{"../bean/GlobalBean":27,"../pfb/PlayerScoreController":62},"path":"preview-scripts/assets/scripts/game/GameScoreController.js"},{"deps":{"../bean/GlobalBean":27,"../util/Tools":70},"path":"preview-scripts/assets/scripts/hall/PlayerLayoutController.js"},{"deps":{"../../meshTools/tools/Publish":16,"../util/AudioManager":71,"../util/Config":68,"../util/LocalStorageManager":75,"../util/Tools":70},"path":"preview-scripts/assets/scripts/hall/SettingDialogController.js"},{"deps":{"../bean/GlobalBean":27,"../util/Config":68,"../util/Tools":70},"path":"preview-scripts/assets/scripts/hall/HallAutoController.js"},{"deps":{"../common/GameMgr":9,"../util/Config":68,"../util/Tools":70},"path":"preview-scripts/assets/scripts/hall/TopUpDialogController.js"},{"deps":{"./LevelSelectController":6},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectExample.js"},{"deps":{"../common/GameMgr":9,"../net/MessageId":63,"../net/WebSocketManager":55,"../util/Config":68,"../util/Tools":70},"path":"preview-scripts/assets/scripts/hall/LeaveDialogController.js"},{"deps":{"../../GlobalManagerController":24,"./LevelSelectController":6,"../../net/MessageId":63,"../../net/WebSocketManager":55},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectPageController.js"},{"deps":{"./LevelSelectController":6},"path":"preview-scripts/assets/scripts/hall/Level/LevelItemController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/hall/Level/ScrollViewHelper.js"},{"deps":{"./HttpUtils":53,"./MessageBaseBean":54,"./GameServerUrl":10,"../../meshTools/MeshTools":72,"../common/GameMgr":9,"../common/EventCenter":58},"path":"preview-scripts/assets/scripts/net/HttpManager.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/HttpUtils.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/MessageBaseBean.js"},{"deps":{"../../meshTools/Singleton":7,"../common/EventCenter":58,"../common/GameMgr":9,"./WebSocketTool":57},"path":"preview-scripts/assets/scripts/net/WebSocketManager.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/IHttpMsgBody.js"},{"deps":{"./MessageBaseBean":54,"./MessageId":63,"../util/Tools":70,"../../meshTools/Singleton":7,"../common/EventCenter":58,"../common/GameMgr":9},"path":"preview-scripts/assets/scripts/net/WebSocketTool.js"},{"deps":{"../../meshTools/Singleton":7,"./GameMgr":9},"path":"preview-scripts/assets/scripts/common/EventCenter.js"},{"deps":{"../bean/GlobalBean":27,"../net/MessageId":63,"../net/WebSocketManager":55,"../ToastController":18,"./HallAutoController":45,"./HallCreateRoomController":32,"./HallJoinRoomController":34},"path":"preview-scripts/assets/scripts/hall/HallCenterLayController.js"},{"deps":{"../util/Tools":70},"path":"preview-scripts/assets/scripts/pfb/PlayerGameController .js"},{"deps":{},"path":"preview-scripts/assets/scripts/pfb/InfoItemController.js"},{"deps":{"../bean/GlobalBean":27,"../util/NickNameLabel":74,"../util/Tools":70},"path":"preview-scripts/assets/scripts/pfb/PlayerScoreController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/MessageId.js"},{"deps":{"../../meshTools/tools/Publish":16,"../util/Config":68,"../util/NickNameLabel":74,"../util/Tools":70},"path":"preview-scripts/assets/scripts/pfb/CongratsItemController.js"},{"deps":{"../common/EventCenter":58,"../common/GameMgr":9,"../net/MessageBaseBean":54,"../util/Config":68},"path":"preview-scripts/assets/scripts/start_up/StartUpCenterController.js"},{"deps":{"../util/NickNameLabel":74,"../util/Tools":70},"path":"preview-scripts/assets/scripts/pfb/SeatItemController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/ErrorCode.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/Config.js"},{"deps":{"../../meshTools/tools/Publish":16,"../bean/GlobalBean":27,"../common/EventCenter":58,"../common/GameMgr":9,"../net/MessageBaseBean":54,"../pfb/MatchItemController":73,"../util/Config":68,"../util/Tools":70},"path":"preview-scripts/assets/scripts/hall/MatchParentController.js"},{"deps":{"./AudioManager":71,"./Config":68},"path":"preview-scripts/assets/scripts/util/Tools.js"},{"deps":{"./AudioMgr":14,"./LocalStorageManager":75},"path":"preview-scripts/assets/scripts/util/AudioManager.js"},{"deps":{"./tools/Publish":16},"path":"preview-scripts/assets/meshTools/MeshTools.js"},{"deps":{"../util/NickNameLabel":74,"../util/Tools":70},"path":"preview-scripts/assets/scripts/pfb/MatchItemController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/NickNameLabel.js"},{"deps":{"../../meshTools/Singleton":7},"path":"preview-scripts/assets/scripts/util/LocalStorageManager.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/zh_CN.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/Dictionary.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/en.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/BlockingQueue.js"}];
var entries = ["preview-scripts/__qc_index__.js"];
var bundleScript = 'preview-scripts/__qc_bundle__.js';

/**
 * Notice: This file can not use ES6 (for IE 11)
 */
var modules = {};
var name2path = {};

// Will generated by module.js plugin
// var scripts = ${scripts};
// var entries = ${entries};
// var bundleScript = ${bundleScript};

if (typeof global === 'undefined') {
    window.global = window;
}

var isJSB = typeof jsb !== 'undefined';

function getXMLHttpRequest () {
    return window.XMLHttpRequest ? new window.XMLHttpRequest() : new ActiveXObject('MSXML2.XMLHTTP');
}

function downloadText(url, callback) {
    if (isJSB) {
        var result = jsb.fileUtils.getStringFromFile(url);
        callback(null, result);
        return;
    }

    var xhr = getXMLHttpRequest(),
        errInfo = 'Load text file failed: ' + url;
    xhr.open('GET', url, true);
    if (xhr.overrideMimeType) xhr.overrideMimeType('text\/plain; charset=utf-8');
    xhr.onload = function () {
        if (xhr.readyState === 4) {
            if (xhr.status === 200 || xhr.status === 0) {
                callback(null, xhr.responseText);
            }
            else {
                callback({status:xhr.status, errorMessage:errInfo + ', status: ' + xhr.status});
            }
        }
        else {
            callback({status:xhr.status, errorMessage:errInfo + '(wrong readyState)'});
        }
    };
    xhr.onerror = function(){
        callback({status:xhr.status, errorMessage:errInfo + '(error)'});
    };
    xhr.ontimeout = function(){
        callback({status:xhr.status, errorMessage:errInfo + '(time out)'});
    };
    xhr.send(null);
};

function loadScript (src, cb) {
    if (typeof require !== 'undefined') {
        require(src);
        return cb();
    }

    // var timer = 'load ' + src;
    // console.time(timer);

    var scriptElement = document.createElement('script');

    function done() {
        // console.timeEnd(timer);
        // deallocation immediate whatever
        scriptElement.remove();
    }

    scriptElement.onload = function () {
        done();
        cb();
    };
    scriptElement.onerror = function () {
        done();
        var error = 'Failed to load ' + src;
        console.error(error);
        cb(new Error(error));
    };
    scriptElement.setAttribute('type','text/javascript');
    scriptElement.setAttribute('charset', 'utf-8');
    scriptElement.setAttribute('src', src);

    document.head.appendChild(scriptElement);
}

function loadScripts (srcs, cb) {
    var n = srcs.length;

    srcs.forEach(function (src) {
        loadScript(src, function () {
            n--;
            if (n === 0) {
                cb();
            }
        });
    })
}

function formatPath (path) {
    let destPath = window.__quick_compile_project__.destPath;
    if (destPath) {
        let prefix = 'preview-scripts';
        if (destPath[destPath.length - 1] === '/') {
            prefix += '/';
        }
        path = path.replace(prefix, destPath);
    }
    return path;
}

window.__quick_compile_project__ = {
    destPath: '',

    registerModule: function (path, module) {
        path = formatPath(path);
        modules[path].module = module;
    },

    registerModuleFunc: function (path, func) {
        path = formatPath(path);
        modules[path].func = func;

        var sections = path.split('/');
        var name = sections[sections.length - 1];
        name = name.replace(/\.(?:js|ts|json)$/i, '');
        name2path[name] = path;
    },

    require: function (request, path) {
        var m, requestScript;

        path = formatPath(path);
        if (path) {
            m = modules[path];
            if (!m) {
                console.warn('Can not find module for path : ' + path);
                return null;
            }
        }

        if (m) {
            let depIndex = m.deps[request];
            // dependence script was excluded
            if (depIndex === -1) {
                return null;
            }
            else {
                requestScript = scripts[ m.deps[request] ];
            }
        }
        
        let requestPath = '';
        if (!requestScript) {
            // search from name2path when request is a dynamic module name
            if (/^[\w- .]*$/.test(request)) {
                requestPath = name2path[request];
            }

            if (!requestPath) {
                if (CC_JSB) {
                    return require(request);
                }
                else {
                    console.warn('Can not find deps [' + request + '] for path : ' + path);
                    return null;
                }
            }
        }
        else {
            requestPath = formatPath(requestScript.path);
        }

        let requestModule = modules[requestPath];
        if (!requestModule) {
            console.warn('Can not find request module for path : ' + requestPath);
            return null;
        }

        if (!requestModule.module && requestModule.func) {
            requestModule.func();
        }

        if (!requestModule.module) {
            console.warn('Can not find requestModule.module for path : ' + path);
            return null;
        }

        return requestModule.module.exports;
    },

    run: function () {
        entries.forEach(function (entry) {
            entry = formatPath(entry);
            var module = modules[entry];
            if (!module.module) {
                module.func();
            }
        });
    },

    load: function (cb) {
        var self = this;

        var srcs = scripts.map(function (script) {
            var path = formatPath(script.path);
            modules[path] = script;

            if (script.mtime) {
                path += ("?mtime=" + script.mtime);
            }
            return path;
        });

        console.time && console.time('load __quick_compile_project__');
        // jsb can not analysis sourcemap, so keep separate files.
        if (bundleScript && !isJSB) {
            downloadText(formatPath(bundleScript), function (err, bundleSource) {
                console.timeEnd && console.timeEnd('load __quick_compile_project__');
                if (err) {
                    console.error(err);
                    return;
                }

                let evalTime = 'eval __quick_compile_project__ : ' + srcs.length + ' files';
                console.time && console.time(evalTime);
                var sources = bundleSource.split('\n//------QC-SOURCE-SPLIT------\n');
                for (var i = 0; i < sources.length; i++) {
                    if (sources[i]) {
                        window.eval(sources[i]);
                        // not sure why new Function cannot set breakpoints precisely
                        // new Function(sources[i])()
                    }
                }
                self.run();
                console.timeEnd && console.timeEnd(evalTime);
                cb();
            })
        }
        else {
            loadScripts(srcs, function () {
                self.run();
                console.timeEnd && console.timeEnd('load __quick_compile_project__');
                cb();
            });
        }
    }
};

// Polyfill for IE 11
if (!('remove' in Element.prototype)) {
    Element.prototype.remove = function () {
        if (this.parentNode) {
            this.parentNode.removeChild(this);
        }
    };
}
})();
    