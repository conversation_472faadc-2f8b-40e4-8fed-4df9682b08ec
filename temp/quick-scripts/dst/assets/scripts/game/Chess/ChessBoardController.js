
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/game/Chess/ChessBoardController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'b2da8U/JnpOW6usOBaTL1QA', 'ChessBoardController');
// scripts/game/Chess/ChessBoardController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __spreadArrays = (this && this.__spreadArrays) || function () {
    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;
    for (var r = Array(s), k = 0, i = 0; i < il; i++)
        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)
            r[k] = a[j];
    return r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalBean_1 = require("../../bean/GlobalBean");
var PlayerGameController_1 = require("../../pfb/PlayerGameController ");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var ChessBoardController = /** @class */ (function (_super) {
    __extends(ChessBoardController, _super);
    function ChessBoardController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.playerGamePrefab = null;
        _this.boomPrefab = null;
        _this.biaojiPrefab = null;
        _this.boom1Prefab = null;
        _this.boom2Prefab = null;
        _this.boom3Prefab = null;
        _this.boom4Prefab = null;
        _this.boom5Prefab = null;
        _this.boom6Prefab = null;
        _this.boom7Prefab = null;
        _this.boom8Prefab = null; // player_game_pfb 预制体
        _this.boardNode = null; // 棋盘节点
        // 棋盘配置
        _this.BOARD_SIZE = 8; // 8x8棋盘
        _this.BOARD_WIDTH = 750; // 棋盘总宽度
        _this.BOARD_HEIGHT = 750; // 棋盘总高度
        _this.GRID_SIZE = 88; // 每个格子的大小 88x88
        // 格子数据存储
        _this.gridData = []; // 二维数组存储格子数据
        _this.gridNodes = []; // 二维数组存储格子节点
        // 添加到坐标历史记录
        _this.coordinateHistory = [];
        // 自定义偏移量（如果需要调整位置）
        _this.customOffsetX = 0;
        _this.customOffsetY = -16; // 恢复原来的值，保持点击生成位置正确
        return _this;
        // update (dt) {}
    }
    ChessBoardController.prototype.onLoad = function () {
        this.initBoard();
    };
    ChessBoardController.prototype.start = function () {
        var _this = this;
        // 延迟一帧后再次尝试启用触摸事件，确保所有节点都已创建完成
        this.scheduleOnce(function () {
            _this.enableTouchForExistingGrids();
        }, 0.1);
    };
    // 初始化棋盘
    ChessBoardController.prototype.initBoard = function () {
        // 初始化数据数组
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            this.gridData[x] = [];
            this.gridNodes[x] = [];
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                this.gridData[x][y] = {
                    x: x,
                    y: y,
                    worldPos: this.getGridWorldPosition(x, y),
                    hasPlayer: false
                };
            }
        }
        this.createGridNodes();
    };
    // 启用现有格子的触摸事件
    ChessBoardController.prototype.createGridNodes = function () {
        if (!this.boardNode) {
            console.error("棋盘节点未设置！");
            return;
        }
        // 如果格子已经存在，直接启用触摸事件
        this.enableTouchForExistingGrids();
    };
    // 为现有格子启用触摸事件
    ChessBoardController.prototype.enableTouchForExistingGrids = function () {
        // 检查棋盘节点是否存在
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法启用触摸事件！");
            return;
        }
        // 遍历棋盘节点的所有子节点
        var children = this.boardNode.children;
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            // 尝试从节点名称解析坐标
            var coords = this.parseGridCoordinateFromName(child.name);
            if (coords) {
                this.setupGridTouchEvents(child, coords.x, coords.y);
                this.gridNodes[coords.x] = this.gridNodes[coords.x] || [];
                this.gridNodes[coords.x][coords.y] = child;
            }
            else {
                // 如果无法从名称解析，尝试从位置计算
                var pos = child.getPosition();
                var coords_1 = this.getGridCoordinateFromPosition(pos);
                if (coords_1) {
                    this.setupGridTouchEvents(child, coords_1.x, coords_1.y);
                    this.gridNodes[coords_1.x] = this.gridNodes[coords_1.x] || [];
                    this.gridNodes[coords_1.x][coords_1.y] = child;
                }
            }
        }
    };
    // 从节点名称解析格子坐标
    ChessBoardController.prototype.parseGridCoordinateFromName = function (nodeName) {
        // 尝试匹配 Grid_x_y 格式
        var match = nodeName.match(/Grid_(\d+)_(\d+)/);
        if (match) {
            return { x: parseInt(match[1]), y: parseInt(match[2]) };
        }
        return null;
    };
    // 从位置计算格子坐标
    ChessBoardController.prototype.getGridCoordinateFromPosition = function (pos) {
        var x = Math.floor((pos.x + this.BOARD_WIDTH / 2) / this.GRID_SIZE);
        var y = Math.floor((pos.y + this.BOARD_HEIGHT / 2) / this.GRID_SIZE);
        if (this.isValidCoordinate(x, y)) {
            return { x: x, y: y };
        }
        return null;
    };
    // 为格子节点设置触摸事件
    ChessBoardController.prototype.setupGridTouchEvents = function (gridNode, x, y) {
        var _this = this;
        // 安全检查：确保坐标有效
        if (!this.isValidCoordinate(x, y)) {
            console.error("\u274C setupGridTouchEvents: \u5C1D\u8BD5\u4E3A\u65E0\u6548\u5750\u6807(" + x + "," + y + ")\u8BBE\u7F6E\u89E6\u6478\u4E8B\u4EF6");
            return;
        }
        // 长按相关变量
        var isLongPressing = false;
        var longPressTimer = 0;
        var longPressCallback = null;
        var LONG_PRESS_TIME = 1.0; // 1秒长按时间
        // 触摸开始事件
        gridNode.on(cc.Node.EventType.TOUCH_START, function (_event) {
            isLongPressing = true;
            longPressTimer = 0;
            // 开始长按检测
            longPressCallback = function () {
                if (isLongPressing) {
                    longPressTimer += 0.1;
                    if (longPressTimer >= LONG_PRESS_TIME) {
                        _this.onGridLongPress(x, y);
                        isLongPressing = false;
                        if (longPressCallback) {
                            _this.unschedule(longPressCallback);
                        }
                    }
                }
            };
            _this.schedule(longPressCallback, 0.1);
        }, this);
        // 触摸结束事件
        gridNode.on(cc.Node.EventType.TOUCH_END, function (event) {
            // 如果不是长按，则执行点击事件
            if (isLongPressing && longPressTimer < LONG_PRESS_TIME) {
                _this.onGridClick(x, y, event);
            }
            isLongPressing = false;
            if (longPressCallback) {
                _this.unschedule(longPressCallback);
            }
        }, this);
        // 触摸取消事件
        gridNode.on(cc.Node.EventType.TOUCH_CANCEL, function (_event) {
            isLongPressing = false;
            if (longPressCallback) {
                _this.unschedule(longPressCallback);
            }
        }, this);
        // 添加Button组件以确保触摸响应
        var button = gridNode.getComponent(cc.Button);
        if (!button) {
            button = gridNode.addComponent(cc.Button);
            button.transition = cc.Button.Transition.SCALE;
            button.zoomScale = 0.95;
        }
    };
    // 计算格子的世界坐标位置（左下角为(0,0)）
    ChessBoardController.prototype.getGridWorldPosition = function (x, y) {
        // 计算格子中心点位置
        // 左下角为(0,0)，所以y坐标需要从下往上计算
        var posX = (x * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_WIDTH / 2);
        var posY = (y * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_HEIGHT / 2);
        return cc.v2(posX, posY);
    };
    // 格子点击事件 - 发送挖掘操作
    ChessBoardController.prototype.onGridClick = function (x, y, _event) {
        // 检查坐标是否有效（确保在8x8棋盘范围内）
        if (!this.isValidCoordinate(x, y)) {
            return;
        }
        // 检查该位置是否已经有玩家预制体
        if (this.gridData[x][y].hasPlayer) {
            return;
        }
        // 发送挖掘操作事件 (action = 1)
        // 只发送事件，不直接生成预制体，等待GamePageController确认后再生成
        this.node.emit('chess-board-click', {
            x: x,
            y: y,
            action: 1 // 1 = 挖掘
        });
    };
    // 格子长按事件 - 发送标记操作
    ChessBoardController.prototype.onGridLongPress = function (x, y) {
        // 检查坐标是否有效（确保在8x8棋盘范围内）
        if (!this.isValidCoordinate(x, y)) {
            return;
        }
        // 检查该位置是否已经有玩家预制体
        if (this.gridData[x][y].hasPlayer) {
            return;
        }
        // 发送标记操作事件 (action = 2)
        // 只发送事件，不直接生成预制体，等待GamePageController确认后再生成
        this.node.emit('chess-board-click', {
            x: x,
            y: y,
            action: 2 // 2 = 标记
        });
    };
    // 在格子上放置玩家预制体
    ChessBoardController.prototype.placePlayerOnGrid = function (x, y, withFlag) {
        var _this = this;
        if (withFlag === void 0) { withFlag = false; }
        // 双重检查：确保坐标有效
        if (!this.isValidCoordinate(x, y)) {
            console.error("\u274C placePlayerOnGrid: \u65E0\u6548\u5750\u6807(" + x + "," + y + ")");
            return;
        }
        // 双重检查：确保格子为空
        var gridData = this.gridData[x][y];
        if (gridData.hasPlayer) {
            console.error("\u274C placePlayerOnGrid: \u683C\u5B50(" + x + "," + y + ")\u5DF2\u6709\u73A9\u5BB6\uFF0C\u4E0D\u80FD\u91CD\u590D\u653E\u7F6E");
            return;
        }
        if (!this.playerGamePrefab) {
            console.error("❌ 玩家预制体未设置！");
            return;
        }
        if (!this.boardNode) {
            console.error("❌ 棋盘节点未设置！");
            return;
        }
        // 实例化玩家预制体
        var playerNode = cc.instantiate(this.playerGamePrefab);
        // 计算正确的位置
        var correctPosition = this.calculateCorrectPosition(x, y);
        playerNode.setPosition(correctPosition);
        // 先隐藏节点，等头像加载完成后再显示
        playerNode.active = false;
        // 处理Layout限制问题
        this.addPlayerNodeSafely(playerNode);
        // 设置头像和用户数据（异步加载）
        this.setupPlayerAvatarAsync(playerNode, x, y, withFlag, function () {
            // 头像加载完成的回调，播放生成动画（点击生成和单人格子）
            _this.playAvatarSpawnAnimation(playerNode);
        });
        // 更新格子数据
        gridData.hasPlayer = true;
        gridData.playerNode = playerNode;
    };
    // 计算正确的位置（格子中心偏移(0, -16)）
    ChessBoardController.prototype.calculateCorrectPosition = function (x, y) {
        // 使用自定义偏移量
        var offsetX = this.customOffsetX;
        var offsetY = this.customOffsetY;
        // 方法1: 如果能找到对应的格子节点，使用其位置并添加偏移
        var targetGridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (targetGridNode) {
            var gridPos = targetGridNode.getPosition();
            var finalPos = cc.v2(gridPos.x + offsetX, gridPos.y + offsetY);
            return finalPos;
        }
        // 方法2: 基于棋盘的实际位置计算
        // 计算格子中心位置
        var centerX = (x * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_WIDTH / 2);
        var centerY = (y * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_HEIGHT / 2);
        // 添加偏移
        var finalX = centerX + offsetX;
        var finalY = centerY + offsetY;
        var calculatedPos = cc.v2(finalX, finalY);
        return calculatedPos;
    };
    /**
     * 计算预制体的精确位置（根据您提供的坐标规律）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @returns 预制体应该放置的精确位置
     */
    ChessBoardController.prototype.calculatePrefabPosition = function (x, y) {
        // 根据您提供的坐标规律计算：
        // (0,0) → (-314, -310)
        // (1,0) → (-224, -310)  // x增加90
        // (0,1) → (-314, -222)  // y增加88
        // (7,7) → (310, 312)
        var startX = -314; // 起始X坐标
        var startY = -310; // 起始Y坐标
        var stepX = 90; // X方向步长
        var stepY = 88; // Y方向步长
        var finalX = startX + (x * stepX);
        var finalY = startY + (y * stepY);
        var position = cc.v2(finalX, finalY);
        return position;
    };
    /**
     * 播放头像生成动画（由大变小）
     * @param playerNode 玩家节点
     */
    ChessBoardController.prototype.playAvatarSpawnAnimation = function (playerNode) {
        if (!playerNode) {
            console.warn("播放生成动画失败：节点为空");
            return;
        }
        // 显示节点
        playerNode.active = true;
        // 设置初始缩放为1.5倍（比正常大）
        var originalScale = playerNode.scaleX;
        var startScale = originalScale * 1.5;
        playerNode.setScale(startScale);
        // 使用cc.Tween创建由大变小的缩放动画
        cc.tween(playerNode)
            .to(0.3, { scaleX: originalScale, scaleY: originalScale }, { easing: 'backOut' })
            .start();
    };
    /**
     * 播放头像调整动画（平滑移动和缩小）
     * @param playerNode 玩家节点
     * @param newPosition 新位置
     * @param newScale 新缩放
     */
    ChessBoardController.prototype.playAvatarAdjustAnimation = function (playerNode, newPosition, newScale) {
        if (!playerNode) {
            console.warn("播放调整动画失败：节点为空");
            return;
        }
        // 使用cc.Tween同时播放移动和缩放动画
        cc.tween(playerNode)
            .to(0.3, {
            x: newPosition.x,
            y: newPosition.y,
            scaleX: newScale,
            scaleY: newScale
        }, { easing: 'sineOut' })
            .start();
    };
    /**
     * 根据格子总人数计算基础位置（统一逻辑）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param totalPlayers 该格子的总人数
     * @returns 基础位置
     */
    ChessBoardController.prototype.calculateBasePositionByPlayerCount = function (x, y, totalPlayers) {
        var offsetX = this.customOffsetX;
        var offsetY;
        if (totalPlayers === 1) {
            // 一个格子里只有一个人：需要偏移
            offsetY = this.customOffsetY; // -16
        }
        else {
            // 一个格子里有两个及以上：不偏移
            offsetY = 0;
        }
        // 方法1: 如果能找到对应的格子节点，使用其位置并添加偏移
        var targetGridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (targetGridNode) {
            var gridPos = targetGridNode.getPosition();
            var finalPos = cc.v2(gridPos.x + offsetX, gridPos.y + offsetY);
            return finalPos;
        }
        // 方法2: 基于棋盘的实际位置计算
        var centerX = (x * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_WIDTH / 2);
        var centerY = (y * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_HEIGHT / 2);
        var finalX = centerX + offsetX;
        var finalY = centerY + offsetY;
        var calculatedPos = cc.v2(finalX, finalY);
        return calculatedPos;
    };
    /**
     * 计算多人情况下的基础位置（不包含往下偏移，逻辑分开）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @returns 格子中心位置（多人专用，不偏移）
     */
    ChessBoardController.prototype.calculateMultiPlayerBasePosition = function (x, y) {
        // 多人情况使用独立的偏移逻辑
        var offsetX = this.customOffsetX;
        var offsetY = 0; // 多人时不往下偏移，逻辑分开
        // 方法1: 如果能找到对应的格子节点，使用其位置并添加偏移
        var targetGridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (targetGridNode) {
            var gridPos = targetGridNode.getPosition();
            var finalPos = cc.v2(gridPos.x + offsetX, gridPos.y + offsetY);
            return finalPos;
        }
        // 方法2: 基于棋盘的实际位置计算
        // 计算格子中心位置
        var centerX = (x * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_WIDTH / 2);
        var centerY = (y * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_HEIGHT / 2);
        // 添加偏移（不包含往下偏移）
        var finalX = centerX + offsetX;
        var finalY = centerY + offsetY;
        var calculatedPos = cc.v2(finalX, finalY);
        return calculatedPos;
    };
    // 安全地添加玩家节点（处理Layout限制）
    ChessBoardController.prototype.addPlayerNodeSafely = function (playerNode) {
        // 检查棋盘节点是否存在
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法添加玩家节点！");
            return;
        }
        // 检查棋盘节点是否有Layout组件
        var layout = this.boardNode.getComponent(cc.Layout);
        if (layout) {
            // 方案1: 临时禁用Layout
            layout.enabled = false;
            // 添加节点
            this.boardNode.addChild(playerNode);
        }
        else {
            this.boardNode.addChild(playerNode);
        }
        // 方案2备选：添加到Layout外部
        // this.addToParentNode(playerNode);
    };
    // 备选方案：添加到父节点（Layout外部）
    ChessBoardController.prototype.addToParentNode = function (playerNode) {
        // 检查棋盘节点是否存在
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法添加玩家节点！");
            return;
        }
        if (this.boardNode.parent) {
            // 需要转换坐标系
            var worldPos = this.boardNode.convertToWorldSpaceAR(playerNode.getPosition());
            var localPos = this.boardNode.parent.convertToNodeSpaceAR(worldPos);
            playerNode.setPosition(localPos);
            this.boardNode.parent.addChild(playerNode);
        }
        else {
            console.error("\u274C \u68CB\u76D8\u8282\u70B9\u6CA1\u6709\u7236\u8282\u70B9");
            // 回退到直接添加
            this.boardNode.addChild(playerNode);
        }
    };
    // 异步设置玩家头像（带回调）
    ChessBoardController.prototype.setupPlayerAvatarAsync = function (playerNode, x, y, withFlag, onComplete) {
        // 查找PlayerGameController组件
        var playerController = playerNode.getComponent("PlayerGameController") ||
            playerNode.getComponent("PlayerGameController ") ||
            playerNode.getComponentInChildren("PlayerGameController");
        if (playerController) {
            // 检查avatar节点是否存在
            if (playerController.avatar) {
                // 检查avatar节点是否有Sprite组件
                var avatarSprite = playerController.avatar.getComponent(cc.Sprite);
                if (!avatarSprite) {
                    avatarSprite = playerController.avatar.addComponent(cc.Sprite);
                }
                // 确保avatar节点可见
                playerController.avatar.active = true;
                playerController.avatar.opacity = 255;
            }
            else {
                console.error("❌ PlayerGameController中的avatar节点为null");
                onComplete();
                return;
            }
            // 设置旗子节点的显示状态 - 重点检查
            if (playerController.flagNode) {
                playerController.flagNode.active = withFlag;
                // 额外检查旗子节点的可见性
                if (withFlag) {
                    playerController.flagNode.opacity = 255;
                    // 确保旗子节点的父节点也是可见的
                    var parent = playerController.flagNode.parent;
                    while (parent && parent !== playerNode) {
                        parent.active = true;
                        parent = parent.parent;
                    }
                    // 延迟检查旗子是否真的显示了
                    this.scheduleOnce(function () {
                    }, 1.0);
                }
            }
            else {
                console.warn("\u26A0\uFE0F \u627E\u4E0D\u5230\u65D7\u5B50\u8282\u70B9 (" + x + "," + y + ")");
            }
            // 创建用户数据并设置头像
            var userData = {
                userId: "player_" + x + "_" + y,
                nickName: "\u73A9\u5BB6(" + x + "," + y + ")",
                avatar: this.getDefaultAvatarUrl(),
                score: 0,
                pos: 0,
                coin: 0,
                status: 0,
                rank: 0
            };
            // 使用PlayerGameController的setData方法来设置头像
            try {
                playerController.setData(userData);
                // 延迟设置旗子状态，确保在PlayerGameController初始化之后
                this.scheduleOnce(function () {
                    if (playerController.flagNode) {
                        playerController.flagNode.active = withFlag;
                    }
                    onComplete();
                }, 0.1);
            }
            catch (error) {
                console.error("设置头像数据失败:", error);
                onComplete();
            }
        }
        else {
            console.warn("⚠️ 找不到PlayerGameController组件");
            this.tryDirectAvatarSetupAsync(playerNode, x, y, onComplete);
        }
    };
    // 设置玩家头像（保留原方法用于其他地方）
    ChessBoardController.prototype.setupPlayerAvatar = function (playerNode, x, y) {
        var _this = this;
        // 查找PlayerGameController组件
        var playerController = playerNode.getComponent("PlayerGameController") ||
            playerNode.getComponent("PlayerGameController ") ||
            playerNode.getComponentInChildren("PlayerGameController");
        if (playerController) {
            // 检查avatar节点是否存在
            if (playerController.avatar) {
                // 检查avatar节点是否有Sprite组件
                var avatarSprite = playerController.avatar.getComponent(cc.Sprite);
                if (!avatarSprite) {
                    console.warn("⚠️ avatar节点缺少Sprite组件，正在添加...");
                    avatarSprite = playerController.avatar.addComponent(cc.Sprite);
                }
                // 确保avatar节点可见
                playerController.avatar.active = true;
                playerController.avatar.opacity = 255;
            }
            else {
                console.error("❌ PlayerGameController中的avatar节点为null");
                return;
            }
            // 创建用户数据
            var userData = {
                userId: "player_" + x + "_" + y,
                nickName: "\u73A9\u5BB6(" + x + "," + y + ")",
                avatar: this.getDefaultAvatarUrl(),
                score: 0,
                pos: 0,
                coin: 0,
                status: 0,
                rank: 0
            };
            // 安全地调用setData
            try {
                playerController.setData(userData);
                // 延迟检查头像是否加载成功
                this.scheduleOnce(function () {
                    _this.checkAvatarLoaded(playerController.avatar, x, y);
                }, 2.0);
            }
            catch (error) {
                console.error("❌ 设置头像时出错:", error);
            }
        }
        else {
            console.warn("⚠️ 找不到PlayerGameController组件，跳过头像设置");
            // 尝试直接在节点上查找avatar子节点
            this.tryDirectAvatarSetup(playerNode, x, y);
        }
    };
    // 检查头像是否加载成功
    ChessBoardController.prototype.checkAvatarLoaded = function (avatarNode, x, y) {
        if (!avatarNode) {
            console.error("\u274C \u4F4D\u7F6E(" + x + "," + y + ")\u7684avatar\u8282\u70B9\u4E3Anull");
            return;
        }
        var sprite = avatarNode.getComponent(cc.Sprite);
        if (!sprite) {
            console.error("\u274C \u4F4D\u7F6E(" + x + "," + y + ")\u7684avatar\u8282\u70B9\u6CA1\u6709Sprite\u7EC4\u4EF6");
            return;
        }
        if (!sprite.spriteFrame) {
            console.warn("\u26A0\uFE0F \u4F4D\u7F6E(" + x + "," + y + ")\u7684\u5934\u50CF\u53EF\u80FD\u52A0\u8F7D\u5931\u8D25\uFF0CspriteFrame\u4E3Anull");
            // 尝试设置一个默认的颜色作为备用显示
            this.setFallbackAvatar(avatarNode, x, y);
        }
        else {
        }
    };
    // 设置备用头像（纯色方块）
    ChessBoardController.prototype.setFallbackAvatar = function (avatarNode, x, y) {
        var sprite = avatarNode.getComponent(cc.Sprite);
        if (!sprite) {
            sprite = avatarNode.addComponent(cc.Sprite);
        }
        // 创建一个简单的纯色纹理
        var texture = new cc.Texture2D();
        var colors = [
            [255, 107, 107, 255],
            [78, 205, 196, 255],
            [69, 183, 209, 255],
            [150, 206, 180, 255],
            [255, 234, 167, 255] // 黄色
        ];
        var colorIndex = (x + y) % colors.length;
        var color = colors[colorIndex];
        texture.initWithData(new Uint8Array(color), cc.Texture2D.PixelFormat.RGBA8888, 1, 1);
        sprite.spriteFrame = new cc.SpriteFrame(texture);
        // 设置大小
        avatarNode.setContentSize(80, 80);
        avatarNode.active = true;
    };
    // 尝试直接设置头像（当找不到PlayerGameController时）
    ChessBoardController.prototype.tryDirectAvatarSetup = function (playerNode, x, y) {
        // 查找名为"avatar"的子节点
        var avatarNode = playerNode.getChildByName("avatar");
        if (avatarNode) {
            this.setFallbackAvatar(avatarNode, x, y);
        }
        else {
            console.warn("⚠️ 未找到avatar子节点");
            // 列出所有子节点名称
        }
    };
    // 获取默认头像URL
    ChessBoardController.prototype.getDefaultAvatarUrl = function () {
        // 使用真实的头像URL
        return "https://static.gooplay.com/online/user-avatar/1732786296530322669.jpg";
    };
    // 保存格子坐标（用于后续发送给后端）
    ChessBoardController.prototype.saveGridCoordinate = function (x, y) {
        // 这里可以将坐标保存到数组或发送给后端
        // 示例：可以调用网络管理器发送坐标
        this.sendCoordinateToServer(x, y);
        // 或者保存到本地数组以备后用
        this.addToCoordinateHistory(x, y);
    };
    // 发送坐标到服务器
    ChessBoardController.prototype.sendCoordinateToServer = function (x, y) {
        // 构造发送数据
        var moveData = {
            x: x,
            y: y,
            timestamp: Date.now(),
            playerId: this.getCurrentPlayerId()
        };
        // 暂时只是打印，避免未使用变量警告
        return moveData;
    };
    ChessBoardController.prototype.addToCoordinateHistory = function (x, y) {
        this.coordinateHistory.push({
            x: x,
            y: y,
            timestamp: Date.now()
        });
    };
    // 获取当前玩家ID（示例）
    ChessBoardController.prototype.getCurrentPlayerId = function () {
        // 这里应该从全局状态或用户数据中获取
        return "player_001"; // 示例ID
    };
    // 获取指定坐标的格子数据
    ChessBoardController.prototype.getGridData = function (x, y) {
        if (x < 0 || x >= this.BOARD_SIZE || y < 0 || y >= this.BOARD_SIZE) {
            return null;
        }
        return this.gridData[x][y];
    };
    // 清除指定格子的玩家
    ChessBoardController.prototype.clearGridPlayer = function (x, y) {
        var gridData = this.getGridData(x, y);
        if (!gridData || !gridData.hasPlayer) {
            return false;
        }
        // 移除玩家节点
        if (gridData.playerNode) {
            gridData.playerNode.removeFromParent();
            gridData.playerNode = null;
        }
        // 更新数据
        gridData.hasPlayer = false;
        return true;
    };
    // 清除所有玩家
    ChessBoardController.prototype.clearAllPlayers = function () {
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                this.clearGridPlayer(x, y);
            }
        }
    };
    // 获取所有已放置玩家的坐标
    ChessBoardController.prototype.getAllPlayerCoordinates = function () {
        var coordinates = [];
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                if (this.gridData[x][y].hasPlayer) {
                    coordinates.push({ x: x, y: y });
                }
            }
        }
        return coordinates;
    };
    // 检查坐标是否有效
    ChessBoardController.prototype.isValidCoordinate = function (x, y) {
        return x >= 0 && x < this.BOARD_SIZE && y >= 0 && y < this.BOARD_SIZE;
    };
    // 检查格子是否为空
    ChessBoardController.prototype.isGridEmpty = function (x, y) {
        if (!this.isValidCoordinate(x, y)) {
            return false;
        }
        return !this.gridData[x][y].hasPlayer;
    };
    // 获取坐标历史记录
    ChessBoardController.prototype.getCoordinateHistory = function () {
        return __spreadArrays(this.coordinateHistory); // 返回副本
    };
    // 清除坐标历史记录
    ChessBoardController.prototype.clearCoordinateHistory = function () {
        this.coordinateHistory = [];
    };
    // 根据世界坐标获取格子坐标
    ChessBoardController.prototype.getGridCoordinateFromWorldPos = function (worldPos) {
        // 检查棋盘节点是否存在
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法获取格子坐标！");
            return null;
        }
        // 将世界坐标转换为相对于棋盘的坐标
        var localPos = this.boardNode.convertToNodeSpaceAR(worldPos);
        // 计算格子坐标
        var x = Math.floor((localPos.x + this.BOARD_WIDTH / 2) / this.GRID_SIZE);
        var y = Math.floor((localPos.y + this.BOARD_HEIGHT / 2) / this.GRID_SIZE);
        if (this.isValidCoordinate(x, y)) {
            return { x: x, y: y };
        }
        return null;
    };
    // 高亮显示格子（可选功能）
    ChessBoardController.prototype.highlightGrid = function (x, y, highlight) {
        if (highlight === void 0) { highlight = true; }
        if (!this.isValidCoordinate(x, y)) {
            return;
        }
        var gridNode = this.gridNodes[x][y];
        if (gridNode) {
            // 这里可以添加高亮效果，比如改变颜色或添加边框
            if (highlight) {
                gridNode.color = cc.Color.YELLOW;
            }
            else {
                gridNode.color = cc.Color.WHITE;
            }
        }
    };
    // 批量放置玩家（用于从服务器同步数据）
    ChessBoardController.prototype.batchPlacePlayers = function (coordinates) {
        var _this = this;
        coordinates.forEach(function (coord) {
            if (_this.isValidCoordinate(coord.x, coord.y) && _this.isGridEmpty(coord.x, coord.y)) {
                _this.placePlayerOnGrid(coord.x, coord.y);
            }
        });
    };
    // 手动启用触摸事件（调试用）
    ChessBoardController.prototype.manualEnableTouch = function () {
        this.enableTouchForExistingGrids();
    };
    // 测试点击功能（调试用）
    ChessBoardController.prototype.testClick = function (x, y) {
        this.onGridClick(x, y);
    };
    // 获取棋盘状态信息（调试用）
    ChessBoardController.prototype.getBoardInfo = function () {
        var info = {
            boardSize: this.BOARD_SIZE,
            gridSize: this.GRID_SIZE,
            boardWidth: this.BOARD_WIDTH,
            boardHeight: this.BOARD_HEIGHT,
            totalGrids: this.BOARD_SIZE * this.BOARD_SIZE,
            boardNodeChildren: this.boardNode ? this.boardNode.children.length : 0,
            playerCount: this.getAllPlayerCoordinates().length,
            hasPlayerGamePrefab: !!this.playerGamePrefab,
            hasBoardNode: !!this.boardNode
        };
        return info;
    };
    // 简单测试方法 - 只测试位置，不加载头像
    ChessBoardController.prototype.simpleTest = function (x, y) {
        if (!this.boardNode) {
            console.error("❌ 棋盘节点未设置");
            return;
        }
        // 创建一个简单的彩色方块
        var testNode = new cc.Node("Test_" + x + "_" + y);
        // 添加一个彩色方块
        var sprite = testNode.addComponent(cc.Sprite);
        var texture = new cc.Texture2D();
        var color = [Math.random() * 255, Math.random() * 255, Math.random() * 255, 255];
        texture.initWithData(new Uint8Array(color), cc.Texture2D.PixelFormat.RGBA8888, 1, 1);
        sprite.spriteFrame = new cc.SpriteFrame(texture);
        // 设置大小
        testNode.setContentSize(60, 60);
        // 计算位置
        var pos = this.calculateCorrectPosition(x, y);
        testNode.setPosition(pos);
        // 添加坐标标签
        var labelNode = new cc.Node("Label");
        var label = labelNode.addComponent(cc.Label);
        label.string = "(" + x + "," + y + ")";
        label.fontSize = 16;
        label.node.color = cc.Color.WHITE;
        labelNode.setPosition(0, 0);
        testNode.addChild(labelNode);
        // 添加到棋盘（处理Layout问题）
        this.addPlayerNodeSafely(testNode);
    };
    // 清除所有测试节点
    ChessBoardController.prototype.clearTestNodes = function () {
        if (this.boardNode) {
            var children = this.boardNode.children.slice();
            children.forEach(function (child) {
                if (child.name.startsWith("Test_")) {
                    child.removeFromParent();
                }
            });
        }
    };
    // 切换到父节点添加模式（如果Layout问题仍然存在）
    ChessBoardController.prototype.useParentNodeMode = function () {
        // 重新定义添加方法
        this.addPlayerNodeSafely = this.addToParentNode;
    };
    // 重新启用Layout（如果需要）
    ChessBoardController.prototype.reEnableLayout = function () {
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法重新启用Layout！");
            return;
        }
        var layout = this.boardNode.getComponent(cc.Layout);
        if (layout) {
            layout.enabled = true;
        }
    };
    // 永久禁用Layout
    ChessBoardController.prototype.disableLayout = function () {
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法禁用Layout！");
            return;
        }
        var layout = this.boardNode.getComponent(cc.Layout);
        if (layout) {
            layout.enabled = false;
        }
    };
    // 设置自定义偏移量
    ChessBoardController.prototype.setCustomOffset = function (offsetX, offsetY) {
        this.customOffsetX = offsetX;
        this.customOffsetY = offsetY;
    };
    // 获取当前偏移量
    ChessBoardController.prototype.getCurrentOffset = function () {
        return { x: this.customOffsetX, y: this.customOffsetY };
    };
    // 测试不同偏移量
    ChessBoardController.prototype.testWithOffset = function (x, y, offsetX, offsetY) {
        // 临时保存当前偏移
        var originalOffsetX = this.customOffsetX;
        var originalOffsetY = this.customOffsetY;
        // 设置测试偏移
        this.setCustomOffset(offsetX, offsetY);
        // 执行测试
        this.simpleTest(x, y);
        // 恢复原偏移
        this.setCustomOffset(originalOffsetX, originalOffsetY);
    };
    // 测试头像显示功能
    ChessBoardController.prototype.testAvatarDisplay = function (x, y) {
        var _this = this;
        if (!this.isValidCoordinate(x, y)) {
            console.error("❌ 无效坐标");
            return;
        }
        if (this.gridData[x][y].hasPlayer) {
            console.warn("⚠️ 该位置已有玩家");
            return;
        }
        // 直接调用放置玩家方法
        this.placePlayerOnGrid(x, y);
        // 延迟检查结果
        this.scheduleOnce(function () {
            var gridData = _this.gridData[x][y];
            if (gridData.playerNode) {
                // 检查PlayerGameController
                var controller = gridData.playerNode.getComponent("PlayerGameController");
                if (controller && controller.avatar) {
                    var sprite = controller.avatar.getComponent(cc.Sprite);
                    if (sprite && sprite.spriteFrame) {
                    }
                    else {
                        console.warn("⚠️ 头像SpriteFrame不存在");
                    }
                }
                else {
                    console.warn("⚠️ PlayerGameController或avatar节点不存在");
                }
            }
            else {
                console.error("❌ 玩家节点创建失败");
            }
        }, 3.0);
    };
    // 调试预制体结构
    ChessBoardController.prototype.debugPrefabStructure = function () {
        if (!this.playerGamePrefab) {
            console.error("❌ playerGamePrefab为null");
            return;
        }
        // 实例化一个临时节点来检查结构
        var tempNode = cc.instantiate(this.playerGamePrefab);
        // 检查组件
        var controller = tempNode.getComponent("PlayerGameController");
        if (controller) {
            if (controller.avatar) {
                var sprite = controller.avatar.getComponent(cc.Sprite);
            }
            else {
                console.error("❌ avatar节点不存在");
            }
        }
        else {
            console.error("❌ 找不到PlayerGameController组件");
        }
        // 列出所有子节点
        this.logNodeHierarchy(tempNode, 0);
        // 清理临时节点
        tempNode.destroy();
    };
    // 递归打印节点层级
    ChessBoardController.prototype.logNodeHierarchy = function (node, depth) {
        var indent = "  ".repeat(depth);
        for (var _i = 0, _a = node.children; _i < _a.length; _i++) {
            var child = _a[_i];
            this.logNodeHierarchy(child, depth + 1);
        }
    };
    // 异步加载头像
    ChessBoardController.prototype.loadAvatarAsync = function (avatarNode, url, onComplete) {
        var _this = this;
        if (!avatarNode) {
            console.error("❌ avatar节点为null");
            onComplete();
            return;
        }
        var avatarSprite = avatarNode.getComponent(cc.Sprite);
        if (!avatarSprite) {
            console.warn("⚠️ avatar节点没有Sprite组件，正在添加...");
            avatarSprite = avatarNode.addComponent(cc.Sprite);
        }
        if (!url || url === '') {
            console.warn("⚠️ URL为空，设置备用头像");
            this.setFallbackAvatar(avatarNode, 0, 0);
            onComplete();
            return;
        }
        // 根据URL判断文件扩展名
        var ext = '.png';
        if (url.toLowerCase().includes('.jpg') || url.toLowerCase().includes('.jpeg')) {
            ext = '.jpg';
        }
        else if (url.toLowerCase().includes('.png')) {
            ext = '.png';
        }
        cc.assetManager.loadRemote(url, { ext: ext }, function (err, texture) {
            if (err) {
                console.error("\u274C \u5934\u50CF\u52A0\u8F7D\u5931\u8D25: " + (err.message || err));
                console.error("\u274C \u5931\u8D25\u7684URL: " + url);
                // 设置备用头像
                _this.setFallbackAvatar(avatarNode, 0, 0);
                onComplete();
                return;
            }
            texture.setPremultiplyAlpha(true);
            texture.packable = false;
            avatarSprite.spriteFrame = new cc.SpriteFrame(texture);
            // 确保节点可见
            avatarNode.active = true;
            avatarNode.opacity = 255;
            onComplete();
        });
    };
    // 异步直接设置头像（当找不到PlayerGameController时）
    ChessBoardController.prototype.tryDirectAvatarSetupAsync = function (playerNode, x, y, onComplete) {
        // 查找名为"avatar"的子节点
        var avatarNode = playerNode.getChildByName("avatar");
        if (avatarNode) {
            this.setFallbackAvatar(avatarNode, x, y);
            onComplete();
        }
        else {
            console.warn("⚠️ 未找到avatar子节点");
            // 列出所有子节点名称
            onComplete();
        }
    };
    /**
     * 显示玩家游戏加减分效果
     * @param userId 用户ID
     * @param score 分数变化（正数为加分，负数为减分）
     */
    ChessBoardController.prototype.showPlayerGameScore = function (userId, score) {
        var currentUserId = this.getCurrentUserId();
        var foundPlayer = false;
        // 1. 如果是当前用户，查找自己的玩家节点（存储在gridData中）
        if (userId === currentUserId) {
            foundPlayer = this.showScoreForCurrentUser(score);
        }
        else {
            // 2. 如果是其他用户，查找对应的玩家头像节点
            foundPlayer = this.showScoreForOtherUser(userId, score);
        }
        if (!foundPlayer) {
            console.warn("\u672A\u627E\u5230\u7528\u6237 " + userId + " \u7684\u5934\u50CF\u8282\u70B9\u6765\u663E\u793A\u5206\u6570\u6548\u679C");
        }
    };
    /**
     * 获取当前用户ID
     */
    ChessBoardController.prototype.getCurrentUserId = function () {
        var _a, _b;
        return ((_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId) || "";
    };
    /**
     * 为当前用户显示分数效果
     */
    ChessBoardController.prototype.showScoreForCurrentUser = function (score) {
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                var gridData = this.gridData[x][y];
                if (gridData.hasPlayer && gridData.playerNode) {
                    var playerController = gridData.playerNode.getComponent("PlayerGameController") ||
                        gridData.playerNode.getComponent("PlayerGameController ");
                    if (playerController) {
                        this.showScoreOnPlayerController(playerController, score);
                        return true;
                    }
                }
            }
        }
        return false;
    };
    /**
     * 为其他用户显示分数效果
     */
    ChessBoardController.prototype.showScoreForOtherUser = function (userId, score) {
        if (!this.boardNode) {
            return false;
        }
        // 遍历棋盘上的所有玩家头像节点
        // 由于目前没有在节点上存储userId，我们需要通过其他方式匹配
        // 临时方案：根据最近的操作位置来匹配
        return this.findPlayerNodeByRecentAction(userId, score);
    };
    /**
     * 根据userId查找对应的玩家节点
     */
    ChessBoardController.prototype.findPlayerNodeByRecentAction = function (userId, score) {
        if (!this.boardNode) {
            console.warn("\u68CB\u76D8\u8282\u70B9\u4E0D\u5B58\u5728\uFF0C\u65E0\u6CD5\u67E5\u627E\u7528\u6237 " + userId + " \u7684\u5934\u50CF");
            return false;
        }
        // 遍历棋盘上的所有玩家头像节点，根据存储的userId精确匹配
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            // 尝试多种方式获取PlayerGameController组件
            var playerController = child.getComponent("PlayerGameController");
            if (!playerController) {
                playerController = child.getComponent("PlayerGameController "); // 注意末尾有空格
            }
            if (!playerController) {
                // 尝试通过类名获取
                var components = child.getComponents(cc.Component);
                playerController = components.find(function (comp) {
                    return comp.constructor.name === 'PlayerGameController' ||
                        comp.constructor.name === 'PlayerGameController ';
                });
            }
            var storedUserId = child['userId'];
            // 先输出组件列表，帮助诊断问题
            if (storedUserId && (storedUserId === userId || i < 5)) { // 为前5个节点或匹配的节点输出组件列表
                var allComponents = child.getComponents(cc.Component);
            }
            if (storedUserId === userId) {
                if (playerController) {
                    // 找到匹配的用户ID和组件，显示分数效果
                    this.showScoreOnPlayerController(playerController, score);
                    return true;
                }
                else {
                    // 找到匹配的用户ID但没有组件
                    console.warn("\u26A0\uFE0F \u627E\u5230\u7528\u6237 " + userId + " \u7684\u8282\u70B9\u4F46\u6CA1\u6709PlayerGameController\u7EC4\u4EF6");
                    return false; // 找到节点但没有组件，返回false
                }
            }
        }
        console.warn("\u274C \u672A\u627E\u5230\u7528\u6237 " + userId + " \u7684\u5934\u50CF\u8282\u70B9");
        return false;
    };
    /**
     * 在PlayerController上显示分数效果
     */
    ChessBoardController.prototype.showScoreOnPlayerController = function (playerController, score) {
        // 临时提升节点层级，避免被其他头像遮挡
        var playerNode = playerController.node;
        var originalSiblingIndex = playerNode.getSiblingIndex();
        // 将节点移到最上层
        playerNode.setSiblingIndex(-1);
        // 同时确保加分/减分节点的层级更高
        this.ensureScoreNodeTopLevel(playerController);
        if (score > 0) {
            playerController.showAddScore(score);
        }
        else if (score < 0) {
            playerController.showSubScore(Math.abs(score));
        }
        // 延迟恢复原始层级（等分数动画播放完成）
        this.scheduleOnce(function () {
            if (playerNode && playerNode.isValid) {
                playerNode.setSiblingIndex(originalSiblingIndex);
            }
        }, 2.5); // 增加到2.5秒，确保动画完全结束
    };
    /**
     * 确保加分/减分节点在最高层级
     */
    ChessBoardController.prototype.ensureScoreNodeTopLevel = function (playerController) {
        // 设置加分节点的最高层级
        if (playerController.addScoreNode) {
            playerController.addScoreNode.zIndex = cc.macro.MAX_ZINDEX - 1;
        }
        // 设置减分节点的最高层级
        if (playerController.subScoreNode) {
            playerController.subScoreNode.zIndex = cc.macro.MAX_ZINDEX - 1;
        }
    };
    /**
     * 显示玩家游戏减分效果
     * @param userId 用户ID
     * @param subScore 减分数值
     */
    ChessBoardController.prototype.showPlayerGameSubScore = function (userId, subScore) {
        var foundPlayer = false;
        // 遍历所有格子，查找玩家节点
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                var gridData = this.gridData[x][y];
                if (gridData.hasPlayer && gridData.playerNode) {
                    var playerController = gridData.playerNode.getComponent("PlayerGameController") ||
                        gridData.playerNode.getComponent("PlayerGameController ");
                    if (playerController) {
                        playerController.showSubScore(subScore);
                        foundPlayer = true;
                        break;
                    }
                }
            }
            if (foundPlayer)
                break;
        }
        if (!foundPlayer) {
            console.warn("\u672A\u627E\u5230\u73A9\u5BB6\u8282\u70B9\u6765\u663E\u793A\u51CF\u5206\u6548\u679C: userId=" + userId);
        }
    };
    /**
     * 重置游戏场景（游戏开始时调用）
     * 清除数字、炸弹、标记预制体，重新显示所有小格子
     */
    ChessBoardController.prototype.resetGameScene = function () {
        if (!this.boardNode) {
            console.error("❌ 棋盘节点不存在，无法重置");
            return;
        }
        // 列出所有子节点
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
        }
        // 清除所有游戏元素（数字、炸弹、标记等）
        this.clearAllGameElements();
        // 显示所有小格子
        this.showAllGrids();
        // 重新初始化棋盘数据
        this.reinitializeBoardData();
        // 列出所有子节点
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
        }
    };
    /**
     * 测试重置功能（可以在浏览器控制台手动调用）
     */
    ChessBoardController.prototype.testReset = function () {
        this.resetGameScene();
    };
    /**
     * 恢复联机模式地图状态（断线重连时使用）
     * @param mapData 地图数据
     */
    ChessBoardController.prototype.restoreOnlineMapState = function (mapData) {
        console.log("ChessBoardController: 恢复联机模式地图状态", mapData);
        // 检查数据格式
        if (Array.isArray(mapData) && mapData.length > 0 && Array.isArray(mapData[0])) {
            // 二维数组格式：mapData[x][y] 表示每个格子的状态
            console.log("检测到二维数组格式的mapData，尺寸:", mapData.length, "x", mapData[0].length);
            this.restoreFromGridArray(mapData);
        }
        else if (mapData.revealedBlocks || mapData.markedBlocks) {
            // 对象格式：{revealedBlocks: [], markedBlocks: []}
            console.log("检测到对象格式的mapData");
            this.restoreFromBlockLists(mapData);
        }
        else {
            console.warn("未知的mapData格式:", mapData);
        }
    };
    /**
     * 从二维数组格式恢复地图状态
     * @param gridArray 二维数组，gridArray[x][y] 表示格子状态
     */
    ChessBoardController.prototype.restoreFromGridArray = function (gridArray) {
        var _this = this;
        console.log("从二维数组恢复地图状态");
        var _loop_1 = function (x) {
            var _loop_2 = function (y) {
                var cellData = gridArray[x][y];
                if (!this_1.isValidCoordinate(x, y)) {
                    return "continue";
                }
                // 根据cellData的值判断格子状态
                if (cellData !== null && cellData !== undefined && cellData !== 0) {
                    console.log("\u6062\u590D\u683C\u5B50 (" + x + ", " + y + "), \u72B6\u6001:", cellData);
                    // 立即隐藏格子（不播放动画）
                    this_1.hideGridAt(x, y, true);
                    // 根据cellData的值显示相应内容
                    if (typeof cellData === 'number' && cellData > 0 && cellData <= 8) {
                        // 数字：表示周围地雷数
                        this_1.scheduleOnce(function () {
                            _this.createNumberPrefab(x, y, cellData);
                        }, 0.1);
                    }
                    else if (cellData === -1 || cellData === 'mine' || cellData === 'bomb') {
                        // 地雷
                        this_1.scheduleOnce(function () {
                            _this.createBoomPrefab(x, y);
                        }, 0.1);
                    }
                    else if (cellData === 'flag' || cellData === 'marked') {
                        // 标记
                        this_1.createBiaojiPrefab(x, y);
                    }
                    // 标记格子已被处理
                    this_1.gridData[x][y].hasPlayer = true;
                }
            };
            for (var y = 0; y < gridArray[x].length; y++) {
                _loop_2(y);
            }
        };
        var this_1 = this;
        for (var x = 0; x < gridArray.length; x++) {
            _loop_1(x);
        }
    };
    /**
     * 从对象格式恢复地图状态
     * @param mapData 包含revealedBlocks和markedBlocks的对象
     */
    ChessBoardController.prototype.restoreFromBlockLists = function (mapData) {
        var _this = this;
        console.log("从对象格式恢复地图状态");
        // 恢复已挖掘的方块
        if (mapData.revealedBlocks && Array.isArray(mapData.revealedBlocks)) {
            console.log("恢复已挖掘的方块数量:", mapData.revealedBlocks.length);
            mapData.revealedBlocks.forEach(function (block) {
                var x = block.x;
                var y = block.y;
                var neighborMines = block.neighborMines;
                if (_this.isValidCoordinate(x, y)) {
                    console.log("\u6062\u590D\u5DF2\u6316\u6398\u65B9\u5757: (" + x + ", " + y + "), \u5468\u56F4\u5730\u96F7\u6570: " + neighborMines);
                    // 立即隐藏格子（不播放动画）
                    _this.hideGridAt(x, y, true);
                    // 显示挖掘结果
                    if (neighborMines > 0) {
                        // 延迟创建数字预制体，确保格子先隐藏
                        _this.scheduleOnce(function () {
                            _this.createNumberPrefab(x, y, neighborMines);
                        }, 0.1);
                    }
                    // 标记格子已被处理
                    _this.gridData[x][y].hasPlayer = true;
                }
            });
        }
        // 恢复已标记的方块
        if (mapData.markedBlocks && Array.isArray(mapData.markedBlocks)) {
            console.log("恢复已标记的方块数量:", mapData.markedBlocks.length);
            mapData.markedBlocks.forEach(function (block) {
                var x = block.x;
                var y = block.y;
                if (_this.isValidCoordinate(x, y)) {
                    console.log("\u6062\u590D\u5DF2\u6807\u8BB0\u65B9\u5757: (" + x + ", " + y + ")");
                    // 创建标记预制体
                    _this.createBiaojiPrefab(x, y);
                }
            });
        }
    };
    /**
     * 清除所有游戏元素（数字、炸弹、标记、玩家头像等），但保留小格子
     */
    ChessBoardController.prototype.clearAllGameElements = function () {
        if (!this.boardNode) {
            return;
        }
        var childrenToRemove = [];
        var totalChildren = this.boardNode.children.length;
        // 遍历棋盘的所有子节点
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            var nodeName = child.name;
            // 检查是否是需要清除的游戏元素（不包括小格子）
            if (this.isGameElement(child, nodeName)) {
                childrenToRemove.push(child);
            }
            else {
            }
        }
        // 移除找到的游戏元素
        childrenToRemove.forEach(function (child, index) {
            child.removeFromParent();
        });
        // 暂时禁用强制清理，避免误删小格子
        // this.forceCleanNonGridNodes();
    };
    /**
     * 强制清理所有游戏预制体（除了Grid_开头的节点和分数控制器）
     */
    ChessBoardController.prototype.forceCleanNonGridNodes = function () {
        if (!this.boardNode) {
            return;
        }
        var childrenToRemove = [];
        // 再次遍历，强制清除所有游戏预制体
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            var nodeName = child.name;
            // 保留条件：
            // 1. Grid_开头的节点（小格子）
            // 2. 包含Score的节点（分数控制器）
            // 3. UI相关节点
            var shouldKeep = nodeName.startsWith("Grid_") ||
                nodeName.includes("Score") ||
                nodeName.includes("score") ||
                nodeName.includes("UI") ||
                nodeName.includes("ui") ||
                nodeName.includes("Canvas") ||
                nodeName.includes("Background");
            if (!shouldKeep) {
                childrenToRemove.push(child);
            }
            // 移除找到的节点
            childrenToRemove.forEach(function (child) {
                child.removeFromParent();
            });
        }
    };
    /**
     * 判断节点是否是游戏元素（需要清除的），小格子和分数控制器不会被清除
     */
    ChessBoardController.prototype.isGameElement = function (node, nodeName) {
        //  绝对不清除的节点（小格子）
        if (nodeName.startsWith("Grid_") || nodeName === "block") {
            return false;
        }
        //  分数控制器不清除
        if (nodeName.includes("Score") || nodeName.includes("score")) {
            return false;
        }
        //  UI相关节点不清除
        if (nodeName.includes("UI") || nodeName.includes("ui")) {
            return false;
        }
        // 🗑️明确需要清除的游戏预制体
        // 炸弹预制体
        if (nodeName === "Boom") {
            return true;
        }
        // 数字预制体（Boom1, Boom2, Boom3 等）
        if (nodeName.match(/^Boom\d+$/)) {
            return true;
        }
        // 临时数字节点（NeighborMines_1, NeighborMines_2 等）
        if (nodeName.match(/^NeighborMines_\d+$/)) {
            return true;
        }
        // 测试节点（Test_x_y 格式）
        if (nodeName.match(/^Test_\d+_\d+$/)) {
            return true;
        }
        // 玩家预制体（通过组件判断）
        if (node.getComponent("PlayerGameController")) {
            return true;
        }
        // 标记预制体
        if (nodeName.includes("Flag") || nodeName.includes("Mark") || nodeName.includes("flag") ||
            nodeName === "Biaoji" || nodeName.includes("Biaoji")) {
            return true;
        }
        // 玩家头像预制体
        if (nodeName.includes("Player") || nodeName.includes("Avatar")) {
            return true;
        }
        //  默认保留未知节点（保守策略）
        return false;
    };
    /**
     * 显示所有小格子（第二把游戏开始时恢复被隐藏的小格子）
     */
    ChessBoardController.prototype.showAllGrids = function () {
        if (!this.boardNode) {
            return;
        }
        var totalGrids = 0;
        var restoredGrids = 0;
        var alreadyVisibleGrids = 0;
        // 遍历棋盘的所有子节点，找到小格子并显示
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            // 如果是小格子节点
            if (child.name.startsWith("Grid_") || child.name === "block") {
                totalGrids++;
                // 记录恢复前的状态
                var wasHidden = !child.active || child.opacity < 255 || child.scaleX < 1 || child.scaleY < 1;
                if (wasHidden) {
                    restoredGrids++;
                }
                else {
                    alreadyVisibleGrids++;
                }
                // 停止所有可能正在进行的动画
                child.stopAllActions();
                // 恢复显示状态
                child.active = true;
                child.opacity = 255;
                child.scaleX = 1;
                child.scaleY = 1;
                child.angle = 0; // 重置旋转角度
                // 恢复原始位置（如果有保存的话）
                if (child['originalPosition']) {
                    child.setPosition(child['originalPosition']);
                }
                // 确保格子可以交互
                var button = child.getComponent(cc.Button);
                if (button) {
                    button.enabled = true;
                }
            }
        }
    };
    /**
     * 隐藏指定位置的小格子（点击时调用）
     * @param x x坐标
     * @param y y坐标
     * @param immediate 是否立即隐藏（不播放动画）
     */
    ChessBoardController.prototype.hideGridAt = function (x, y, immediate) {
        if (immediate === void 0) { immediate = false; }
        if (!this.isValidCoordinate(x, y)) {
            console.warn("\u9690\u85CF\u683C\u5B50\u5931\u8D25\uFF1A\u5750\u6807(" + x + ", " + y + ")\u65E0\u6548");
            return;
        }
        // 获取格子节点
        var gridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (gridNode) {
            if (immediate) {
                // 立即隐藏格子（断线重连时使用）
                gridNode.opacity = 0;
                gridNode.scaleX = 0;
                gridNode.scaleY = 0;
                gridNode.active = false;
            }
            else {
                // 使用动画隐藏格子（正常游戏时使用）
                cc.tween(gridNode)
                    .to(0.3, { opacity: 0, scaleX: 0, scaleY: 0 }, { easing: 'sineIn' })
                    .call(function () {
                    gridNode.active = false;
                })
                    .start();
            }
        }
    };
    /**
     * 重新初始化棋盘数据
     */
    ChessBoardController.prototype.reinitializeBoardData = function () {
        // 重置gridData中的玩家状态
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                if (this.gridData[x][y]) {
                    this.gridData[x][y].hasPlayer = false;
                    this.gridData[x][y].playerNode = null;
                }
            }
        }
        // 清除坐标历史记录
        this.clearCoordinateHistory();
    };
    /**
     * 清理所有玩家预制体（新回合开始时调用）
     * 包括自己的头像和其他玩家的头像
     */
    ChessBoardController.prototype.clearAllPlayerNodes = function () {
        if (!this.boardNode) {
            console.warn("棋盘节点不存在，无法清理");
            return;
        }
        var totalCleared = 0;
        // 方法1: 清理存储在gridData中的玩家节点（自己的头像）
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                if (this.gridData[x][y].hasPlayer && this.gridData[x][y].playerNode) {
                    // 移除玩家节点
                    this.gridData[x][y].playerNode.removeFromParent();
                    this.gridData[x][y].playerNode = null;
                    this.gridData[x][y].hasPlayer = false;
                    totalCleared++;
                }
            }
        }
        // 方法2: 清理棋盘上所有的玩家预制体节点（包括其他玩家的头像）
        var childrenToRemove = [];
        // 遍历棋盘的所有子节点
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            // 检查是否是玩家预制体（通过组件判断）
            var playerController = child.getComponent(PlayerGameController_1.default);
            if (playerController) {
                childrenToRemove.push(child);
                totalCleared++;
            }
        }
        // 移除找到的玩家预制体
        childrenToRemove.forEach(function (child) {
            child.removeFromParent();
        });
    };
    /**
     * 在指定位置显示其他玩家的操作（参考自己头像的生成逻辑）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param actions 该位置的其他玩家操作列表
     */
    ChessBoardController.prototype.displayOtherPlayersAtPosition = function (x, y, actions) {
        if (!this.isValidCoordinate(x, y) || !actions || actions.length === 0) {
            console.warn("\u65E0\u6548\u53C2\u6570: (" + x + ", " + y + "), actions: " + ((actions === null || actions === void 0 ? void 0 : actions.length) || 0));
            return;
        }
        // 检查该位置是否已经有自己的头像
        if (this.gridData[x][y].hasPlayer) {
            // 只有当真的有其他玩家时，才调整自己的头像位置
            if (actions.length > 0) {
                // 如果已有自己的头像且有其他玩家，需要使用多人布局策略
                this.addOtherPlayersToExistingGrid(x, y, actions);
            }
            else {
            }
        }
        else {
            // 如果没有自己的头像，直接添加其他玩家头像
            this.addOtherPlayersToEmptyGrid(x, y, actions);
        }
    };
    /**
     * 在已有自己头像的格子上添加其他玩家头像，并调整自己的头像位置和缩放
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param actions 其他玩家操作列表
     */
    ChessBoardController.prototype.addOtherPlayersToExistingGrid = function (x, y, actions) {
        // 总玩家数 = 自己(1) + 其他玩家数量
        var totalPlayers = 1 + actions.length;
        var positions = this.getPlayerPositions(totalPlayers);
        // 第一步：调整自己的头像位置和缩放
        // 注意：如果自己的头像是通过点击生成的，位置是正确的，应该调整
        // 如果是通过后端消息生成的，也应该参与多人布局
        var myPosition = positions[0]; // 第一个位置是自己的
        this.adjustMyAvatarPosition(x, y, myPosition, actions);
        // 第二步：从第二个位置开始放置其他玩家
        for (var i = 0; i < actions.length; i++) {
            var action = actions[i];
            var position = positions[i + 1]; // 跳过第一个位置（自己的位置）
            // 使用棋盘坐标系创建其他玩家头像
            this.createOtherPlayerAtBoardPosition(x, y, action, position, totalPlayers);
        }
    };
    /**
     * 调整自己的头像位置和缩放（当多人在同一格子时）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param position 新的位置和缩放信息
     * @param actions 其他玩家操作列表
     */
    ChessBoardController.prototype.adjustMyAvatarPosition = function (x, y, position, actions) {
        // 查找自己的头像节点
        if (!this.gridData[x][y].hasPlayer || !this.gridData[x][y].playerNode) {
            console.warn("\u5728\u4F4D\u7F6E(" + x + ", " + y + ")\u627E\u4E0D\u5230\u81EA\u5DF1\u7684\u5934\u50CF\u8282\u70B9");
            return;
        }
        var myPlayerNode = this.gridData[x][y].playerNode;
        // 计算该格子的总人数（自己 + 其他玩家）
        var totalPlayers = 1 + (actions ? actions.length : 0);
        // 根据总人数计算基础位置
        var basePosition = this.calculateBasePositionByPlayerCount(x, y, totalPlayers);
        // 计算新的最终位置
        var newPosition = cc.v2(basePosition.x + position.x, basePosition.y + position.y);
        // 播放平滑移动和缩小动画（多人格子情况）
        this.playAvatarAdjustAnimation(myPlayerNode, newPosition, position.scale);
    };
    /**
     * 在空格子上添加其他玩家头像
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param actions 其他玩家操作列表
     */
    ChessBoardController.prototype.addOtherPlayersToEmptyGrid = function (x, y, actions) {
        var totalPlayers = actions.length; // 空格子上只有其他玩家
        var positions = this.getPlayerPositions(totalPlayers);
        for (var i = 0; i < actions.length; i++) {
            var action = actions[i];
            var position = positions[i];
            // 使用棋盘坐标系创建其他玩家头像
            this.createOtherPlayerAtBoardPosition(x, y, action, position, totalPlayers);
        }
    };
    /**
     * 在棋盘坐标系中创建其他玩家头像（参考自己头像的生成逻辑）
     * @param gridX 格子x坐标
     * @param gridY 格子y坐标
     * @param action 玩家操作数据
     * @param relativePosition 相对于格子中心的位置和缩放
     * @param totalPlayers 该格子的总人数
     */
    ChessBoardController.prototype.createOtherPlayerAtBoardPosition = function (gridX, gridY, action, relativePosition, totalPlayers) {
        var _this = this;
        if (!this.playerGamePrefab) {
            console.error("playerGamePrefab 预制体未设置");
            return;
        }
        if (!this.boardNode) {
            console.error("棋盘节点未设置");
            return;
        }
        // 创建玩家预制体实例
        var playerNode = cc.instantiate(this.playerGamePrefab);
        // 根据总人数计算基础位置（统一逻辑）
        var basePosition = this.calculateBasePositionByPlayerCount(gridX, gridY, totalPlayers);
        // 添加相对偏移
        var finalPosition = cc.v2(basePosition.x + relativePosition.x, basePosition.y + relativePosition.y);
        playerNode.setPosition(finalPosition);
        playerNode.setScale(relativePosition.scale);
        // 先隐藏节点，等头像加载完成后再显示
        playerNode.active = false;
        // 安全地添加到棋盘节点（参考自己头像的添加逻辑）
        this.addPlayerNodeSafely(playerNode);
        // 设置其他玩家的头像和数据
        this.setupOtherPlayerData(playerNode, action, function () {
            // 头像加载完成的回调
            if (totalPlayers === 1) {
                // 单人格子：播放生成动画
                _this.playAvatarSpawnAnimation(playerNode);
            }
            else {
                // 多人格子：直接显示（其他人是新生成的，不需要动画）
                playerNode.active = true;
            }
        });
    };
    /**
     * 设置其他玩家的数据（参考自己头像的设置逻辑）
     * @param playerNode 玩家节点
     * @param action 玩家操作数据
     * @param onComplete 完成回调
     */
    ChessBoardController.prototype.setupOtherPlayerData = function (playerNode, action, onComplete) {
        try {
            var playerController_1 = playerNode.getComponent(PlayerGameController_1.default);
            if (!playerController_1) {
                console.error("❌ 找不到PlayerGameController组件");
                return;
            }
            // 从GlobalBean中获取真实的玩家数据
            var realUserData_1 = this.getRealUserData(action.userId);
            if (!realUserData_1) {
                console.warn("\u627E\u4E0D\u5230\u7528\u6237 " + action.userId + " \u7684\u771F\u5B9E\u6570\u636E");
                return;
            }
            // 在节点上存储userId信息，用于后续分数显示匹配
            playerNode['userId'] = action.userId;
            // 使用延迟设置，参考自己头像的设置逻辑
            this.scheduleOnce(function () {
                if (typeof playerController_1.setData === 'function') {
                    playerController_1.setData(realUserData_1);
                }
                // 根据操作类型设置旗子显示
                var withFlag = (action.action === 2); // action=2表示标记操作，显示旗子
                if (playerController_1.flagNode) {
                    playerController_1.flagNode.active = withFlag;
                }
                // 调用完成回调
                if (onComplete) {
                    onComplete();
                }
            }, 0.1);
        }
        catch (error) {
            console.warn("\u8BBE\u7F6E\u5176\u4ED6\u73A9\u5BB6\u6570\u636E\u65F6\u51FA\u9519: " + error);
        }
    };
    /**
     * 根据玩家数量获取布局位置
     * @param playerCount 玩家数量
     * @returns 位置数组 {x: number, y: number, scale: number}[]
     */
    ChessBoardController.prototype.getPlayerPositions = function (playerCount) {
        switch (playerCount) {
            case 1:
                // 单个玩家，居中显示，正常大小
                return [{ x: 0, y: 0, scale: 1.0 }];
            case 2:
                // 两个玩家，左右分布，缩放0.5
                return [
                    { x: -22, y: -8, scale: 0.5 },
                    { x: 22, y: -8, scale: 0.5 } // 右
                ];
            case 3:
                // 三个玩家，上中下分布，缩放0.5
                return [
                    { x: 0, y: 12, scale: 0.5 },
                    { x: -23, y: -27, scale: 0.5 },
                    { x: 23, y: -27, scale: 0.5 } // 右下
                ];
            case 4:
                // 四个玩家，四角分布，缩放0.5
                return [
                    { x: -22, y: 12, scale: 0.5 },
                    { x: 22, y: 12, scale: 0.5 },
                    { x: -22, y: -30, scale: 0.5 },
                    { x: 22, y: -30, scale: 0.5 } // 右下
                ];
            default:
                // 超过4个玩家，只显示前4个
                console.warn("\u73A9\u5BB6\u6570\u91CF\u8FC7\u591A: " + playerCount + "\uFF0C\u53EA\u663E\u793A\u524D4\u4E2A");
                return this.getPlayerPositions(4);
        }
    };
    /**
     * 获取指定位置的格子节点
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @returns 格子节点或null
     */
    ChessBoardController.prototype.getGridNode = function (x, y) {
        if (!this.boardNode || !this.isValidCoordinate(x, y)) {
            return null;
        }
        // 计算在棋盘子节点中的索引 (8x8棋盘，从左到右，从上到下)
        var index = y * this.BOARD_SIZE + x;
        if (index >= 0 && index < this.boardNode.children.length) {
            return this.boardNode.children[index];
        }
        return null;
    };
    /**
     * 在指定位置创建玩家预制体节点
     * @param gridNode 格子节点
     * @param action 玩家操作数据
     * @param position 相对位置和缩放
     */
    ChessBoardController.prototype.createPlayerNodeAtPosition = function (gridNode, action, position) {
        if (!this.playerGamePrefab) {
            console.error("playerGamePrefab 预制体未设置");
            return;
        }
        // 创建玩家预制体实例
        var playerNode = cc.instantiate(this.playerGamePrefab);
        // 检查预制体上的组件
        var components = playerNode.getComponents(cc.Component);
        components.forEach(function (comp, index) {
        });
        // 设置位置和缩放
        playerNode.setPosition(position.x, position.y);
        playerNode.setScale(position.scale);
        // 添加到格子节点
        gridNode.addChild(playerNode);
        // 设置玩家数据
        this.setupPlayerNodeData(playerNode, action);
    };
    /**
     * 设置玩家节点数据
     * @param playerNode 玩家节点
     * @param action 玩家操作数据
     */
    ChessBoardController.prototype.setupPlayerNodeData = function (playerNode, action) {
        try {
            var playerController = playerNode.getComponent(PlayerGameController_1.default);
            if (!playerController) {
                console.error("❌ 找不到PlayerGameController组件");
                var allComponents = playerNode.getComponents(cc.Component);
                allComponents.forEach(function (comp, index) {
                });
                return;
            }
            // 从GlobalBean中获取真实的玩家数据
            var realUserData = this.getRealUserData(action.userId);
            if (!realUserData) {
                console.warn("\u627E\u4E0D\u5230\u7528\u6237 " + action.userId + " \u7684\u771F\u5B9E\u6570\u636E");
                return;
            }
            if (typeof playerController.setData === 'function') {
                playerController.setData(realUserData);
            }
            // 根据操作类型设置旗子显示
            var withFlag = (action.action === 2); // action=2表示标记操作，显示旗子
            if (playerController.flagNode) {
                playerController.flagNode.active = withFlag;
            }
            else {
                console.warn("找不到flagNode节点");
            }
        }
        catch (error) {
            console.warn("\u8BBE\u7F6E\u73A9\u5BB6\u8282\u70B9\u6570\u636E\u65F6\u51FA\u9519: " + error);
        }
    };
    /**
     * 从GlobalBean中获取真实的用户数据
     * @param userId 用户ID
     * @returns RoomUser 或 null
     */
    ChessBoardController.prototype.getRealUserData = function (userId) {
        try {
            if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
                console.warn("没有游戏数据，无法获取用户信息");
                return null;
            }
            var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
            var user = users.find(function (u) { return u.userId === userId; });
            if (user) {
                return user;
            }
            else {
                console.warn("\u672A\u627E\u5230\u7528\u6237 " + userId + " \u7684\u6570\u636E");
                return null;
            }
        }
        catch (error) {
            console.error("\u83B7\u53D6\u7528\u6237\u6570\u636E\u65F6\u51FA\u9519: " + error);
            return null;
        }
    };
    /**
     * 在指定位置的玩家节点上显示分数
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param score 分数
     * @param showPlusOne 是否显示+1（先手奖励）
     */
    ChessBoardController.prototype.showScoreOnPlayerNode = function (x, y, score, showPlusOne) {
        var _this = this;
        if (!this.isValidCoordinate(x, y)) {
            console.warn("\u65E0\u6548\u5750\u6807: (" + x + ", " + y + ")");
            return;
        }
        // 查找该位置的玩家节点
        var playerNode = this.findPlayerNodeAtPosition(x, y);
        if (!playerNode) {
            console.warn("\u5728\u4F4D\u7F6E(" + x + ", " + y + ")\u627E\u4E0D\u5230\u73A9\u5BB6\u8282\u70B9");
            return;
        }
        // 获取PlayerGameController组件
        var playerController = playerNode.getComponent(PlayerGameController_1.default);
        if (!playerController) {
            console.warn("找不到PlayerGameController组件");
            return;
        }
        // 显示分数动画
        if (showPlusOne) {
            // 先显示+1，再显示本回合得分
            this.showScoreAnimationOnNode(playerController, 1, function () {
                _this.scheduleOnce(function () {
                    _this.showScoreAnimationOnNode(playerController, score, null);
                }, 1.0);
            });
        }
        else {
            // 只显示本回合得分
            this.showScoreAnimationOnNode(playerController, score, null);
        }
    };
    /**
     * 查找指定位置的玩家节点
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @returns 玩家节点或null
     */
    ChessBoardController.prototype.findPlayerNodeAtPosition = function (x, y) {
        // 方法1: 从gridData中查找（自己的头像）
        if (this.gridData[x][y].hasPlayer && this.gridData[x][y].playerNode) {
            return this.gridData[x][y].playerNode;
        }
        // 方法2: 在棋盘上查找其他玩家的头像
        if (!this.boardNode) {
            return null;
        }
        // 计算该位置的世界坐标
        var targetPosition = this.calculateCorrectPosition(x, y);
        // 遍历棋盘上的所有玩家节点，找到最接近目标位置的
        var closestNode = null;
        var minDistance = Number.MAX_VALUE;
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            var playerController = child.getComponent(PlayerGameController_1.default);
            if (playerController) {
                var distance = cc.Vec2.distance(child.getPosition(), targetPosition);
                if (distance < minDistance && distance < 50) { // 50像素的容差
                    minDistance = distance;
                    closestNode = child;
                }
            }
        }
        return closestNode;
    };
    /**
     * 在节点上显示分数动画
     * @param playerController 玩家控制器
     * @param score 分数
     * @param onComplete 完成回调
     */
    ChessBoardController.prototype.showScoreAnimationOnNode = function (playerController, score, onComplete) {
        // TODO: 实现在player_game_pfb上显示分数动画的逻辑
        // 这里需要根据PlayerGameController的具体实现来显示分数
        if (onComplete) {
            this.scheduleOnce(onComplete, 1.0);
        }
    };
    /**
     * 让指定位置的所有头像消失（参考回合结束时的清理逻辑）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param onComplete 完成回调
     */
    ChessBoardController.prototype.hideAvatarsAtPosition = function (x, y, onComplete) {
        var _this = this;
        if (!this.boardNode) {
            console.warn("棋盘节点不存在，无法清理头像");
            onComplete();
            return;
        }
        // 收集所有头像节点（参考clearAllPlayerNodes的逻辑）
        var avatarNodes = [];
        // 方法1: 收集存储在gridData中的玩家节点（自己的头像）
        for (var gx = 0; gx < this.BOARD_SIZE; gx++) {
            for (var gy = 0; gy < this.BOARD_SIZE; gy++) {
                if (this.gridData[gx][gy].hasPlayer && this.gridData[gx][gy].playerNode) {
                    avatarNodes.push(this.gridData[gx][gy].playerNode);
                }
            }
        }
        // 方法2: 收集棋盘上所有的玩家预制体节点（包括其他玩家的头像）
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            // 检查是否是玩家预制体（通过组件判断，参考clearAllPlayerNodes）
            var playerController = child.getComponent(PlayerGameController_1.default);
            if (playerController) {
                // 避免重复添加（可能已经在方法1中添加过）
                if (!avatarNodes.includes(child)) {
                    avatarNodes.push(child);
                }
            }
        }
        if (avatarNodes.length === 0) {
            // 没有头像需要消失
            onComplete();
            return;
        }
        var completedCount = 0;
        var totalCount = avatarNodes.length;
        // 为每个头像播放消失动画
        avatarNodes.forEach(function (avatarNode, index) {
            // 使用cc.Tween播放消失动画
            cc.tween(avatarNode)
                .to(0.3, { opacity: 0, scaleX: 0.5, scaleY: 0.5 }, { easing: 'sineIn' })
                .call(function () {
                // 动画完成后移除节点
                avatarNode.removeFromParent();
                completedCount++;
                // 所有头像都消失完成后，执行回调
                if (completedCount >= totalCount) {
                    // 清除所有自己头像的引用（参考clearAllPlayerNodes）
                    _this.clearAllMyAvatarReferences();
                    onComplete();
                }
            })
                .start();
        });
    };
    /**
     * 清除所有自己头像的引用（参考clearAllPlayerNodes的逻辑）
     */
    ChessBoardController.prototype.clearAllMyAvatarReferences = function () {
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                if (this.gridData[x][y].hasPlayer) {
                    this.gridData[x][y].hasPlayer = false;
                    this.gridData[x][y].playerNode = null;
                }
            }
        }
    };
    /**
     * 隐藏指定位置的格子（不销毁，以便重置时可以重新显示）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param immediate 是否立即隐藏（不播放动画）
     */
    ChessBoardController.prototype.removeGridAt = function (x, y, immediate) {
        if (immediate === void 0) { immediate = false; }
        if (!this.isValidCoordinate(x, y)) {
            console.warn("\u9690\u85CF\u683C\u5B50\u5931\u8D25\uFF1A\u5750\u6807(" + x + ", " + y + ")\u65E0\u6548");
            return;
        }
        // 获取格子节点
        var gridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (gridNode) {
            if (immediate) {
                // 立即隐藏，不播放动画
                gridNode.active = false;
            }
            else {
                // 播放四边形格子消失动画
                this.playGridFallAnimation(gridNode);
            }
        }
    };
    /**
     * 播放四边形格子消失动画
     * 效果：格子持续旋转，给一个随机向上的力，然后旋转着自由落体
     * @param gridNode 格子节点
     */
    ChessBoardController.prototype.playGridFallAnimation = function (gridNode) {
        if (!gridNode)
            return;
        // 停止该格子上所有正在进行的动画（包括震动动画）
        gridNode.stopAllActions();
        // 保存格子的原始位置（用于重置时恢复）
        if (!gridNode['originalPosition']) {
            gridNode['originalPosition'] = gridNode.getPosition();
        }
        // 随机选择向上的力的方向：0=向上，1=右上15度，2=左上15度
        var forceDirection = Math.floor(Math.random() * 3);
        var moveX = 0;
        var moveY = 200; // 向上的基础距离（增加高度）
        switch (forceDirection) {
            case 0: // 向上
                moveX = 0;
                break;
            case 1: // 右上20度
                moveX = Math.sin(20 * Math.PI / 180) * moveY;
                break;
            case 2: // 左上20度
                moveX = -Math.sin(20 * Math.PI / 180) * moveY;
                break;
        }
        // 随机旋转速度
        var rotationSpeed = (Math.random() * 1440 + 720) * (Math.random() > 0.5 ? 1 : -1); // 720-2160度/秒，随机方向
        // 动画参数
        var upTime = 0.15; // 向上运动时间
        var fallTime = 0.3; // 下落时间
        var initialPosition = gridNode.getPosition();
        // 创建持续旋转的动画
        var rotationTween = cc.tween(gridNode)
            .repeatForever(cc.tween().by(0.1, { angle: rotationSpeed * 0.1 }));
        // 创建分阶段的运动动画
        var movementTween = cc.tween(gridNode)
            // 第一阶段：向上抛出
            .to(upTime, {
            x: initialPosition.x + moveX,
            y: initialPosition.y + moveY
        }, { easing: 'quadOut' })
            // 第二阶段：自由落体
            .to(fallTime, {
            x: initialPosition.x + moveX + (Math.random() - 0.5) * 100,
            y: initialPosition.y - 500 // 下落到屏幕下方更远处
        }, { easing: 'quadIn' })
            .call(function () {
            // 动画结束后隐藏格子
            gridNode.active = false;
            // 停止旋转动画
            gridNode.stopAllActions();
        });
        // 同时开始旋转和移动动画
        rotationTween.start();
        movementTween.start();
    };
    /**
     * 在指定位置创建boom预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param isCurrentUser 是否是当前用户点到的雷（可选，默认为true以保持向后兼容）
     */
    ChessBoardController.prototype.createBoomPrefab = function (x, y, isCurrentUser) {
        var _this = this;
        if (isCurrentUser === void 0) { isCurrentUser = true; }
        if (!this.boomPrefab) {
            console.error("boomPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }
        // 实例化您的boom预制体
        var boomNode = cc.instantiate(this.boomPrefab);
        boomNode.name = "Boom";
        // 设置位置（使用新的精确位置计算）
        var position = this.calculatePrefabPosition(x, y);
        boomNode.setPosition(position);
        // 添加到棋盘
        this.addPlayerNodeSafely(boomNode);
        // 播放出现动画
        boomNode.setScale(0);
        cc.tween(boomNode)
            .to(0.3, { scaleX: 1.2, scaleY: 1.2 }, { easing: 'backOut' })
            .to(0.1, { scaleX: 1.0, scaleY: 1.0 })
            .start();
        // 只有当前用户点到雷时才播放棋盘震动效果
        // 延迟0.45秒，等格子下落动画完成后再播放震动
        if (isCurrentUser) {
            this.scheduleOnce(function () {
                _this.playBoardShakeAnimation();
            }, 0.45);
        }
    };
    /**
     * 在指定位置创建biaoji预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     */
    ChessBoardController.prototype.createBiaojiPrefab = function (x, y) {
        if (!this.biaojiPrefab) {
            console.error("biaojiPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }
        // 实例化您的biaoji预制体
        var biaojiNode = cc.instantiate(this.biaojiPrefab);
        biaojiNode.name = "Biaoji";
        // 设置位置（使用新的精确位置计算）
        var position = this.calculatePrefabPosition(x, y);
        biaojiNode.setPosition(position);
        // 添加到棋盘
        this.addPlayerNodeSafely(biaojiNode);
        // 播放出现动画
        biaojiNode.setScale(0);
        cc.tween(biaojiNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    };
    /**
     * 更新指定位置的neighborMines显示（使用boom数字预制体）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param neighborMines 周围地雷数量
     */
    ChessBoardController.prototype.updateNeighborMinesDisplay = function (x, y, neighborMines) {
        // 0不需要显示数字
        if (neighborMines === 0) {
            return;
        }
        // 直接使用boom数字预制体
        this.createNumberPrefab(x, y, neighborMines);
    };
    /**
     * 创建数字预制体（boom1, boom2, ...）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param number 数字
     */
    ChessBoardController.prototype.createNumberPrefab = function (x, y, number) {
        // 根据数字选择对应的预制体
        var prefab = null;
        switch (number) {
            case 1:
                prefab = this.boom1Prefab;
                break;
            case 2:
                prefab = this.boom2Prefab;
                break;
            case 3:
                prefab = this.boom3Prefab;
                break;
            case 4:
                prefab = this.boom4Prefab;
                break;
            case 5:
                prefab = this.boom5Prefab;
                break;
            case 6:
                prefab = this.boom6Prefab;
                break;
            case 7:
                prefab = this.boom7Prefab;
                break;
            case 8:
                prefab = this.boom8Prefab;
                break;
            default:
                console.error("\u4E0D\u652F\u6301\u7684\u6570\u5B57: " + number);
                return;
        }
        if (!prefab) {
            console.error("boom" + number + "Prefab \u9884\u5236\u4F53\u672A\u8BBE\u7F6E\uFF0C\u8BF7\u5728\u7F16\u8F91\u5668\u4E2D\u6302\u8F7D");
            return;
        }
        // 实例化数字预制体
        var numberNode = cc.instantiate(prefab);
        numberNode.name = "Boom" + number;
        // 设置位置（使用新的精确位置计算）
        var position = this.calculatePrefabPosition(x, y);
        numberNode.setPosition(position);
        // 添加到棋盘
        this.addPlayerNodeSafely(numberNode);
        // 播放出现动画
        numberNode.setScale(0);
        cc.tween(numberNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    };
    /**
     * 加载数字预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param number 数字
     */
    ChessBoardController.prototype.loadNumberPrefab = function (x, y, number) {
        var prefabName = number + "boom";
        this.createTemporaryNumberNode(x, y, number);
    };
    /**
     * 创建临时的数字节点（在预制体加载失败时使用）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param number 数字
     */
    ChessBoardController.prototype.createTemporaryNumberNode = function (x, y, number) {
        // 创建数字显示节点
        var numberNode = new cc.Node("NeighborMines_" + number);
        var label = numberNode.addComponent(cc.Label);
        // 设置数字显示 - 更大的字体和居中对齐
        label.string = number.toString();
        label.fontSize = 48; // 增大字体
        label.node.color = this.getNumberColor(number);
        label.horizontalAlign = cc.Label.HorizontalAlign.CENTER;
        label.verticalAlign = cc.Label.VerticalAlign.CENTER;
        // 设置节点大小，确保居中
        numberNode.setContentSize(this.GRID_SIZE, this.GRID_SIZE);
        // 设置位置 - 使用格子中心位置
        var position = this.calculateCorrectPosition(x, y);
        numberNode.setPosition(position);
        // 添加到棋盘
        this.addPlayerNodeSafely(numberNode);
        // 播放出现动画
        this.playNumberAppearAnimation(numberNode, number);
    };
    /**
     * 设置数字节点（用于预制体）
     * @param numberNode 数字节点
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param number 数字
     */
    ChessBoardController.prototype.setupNumberNode = function (numberNode, x, y, number) {
        // 设置位置 - 使用格子中心位置
        var position = this.calculateCorrectPosition(x, y);
        numberNode.setPosition(position);
        // 添加到棋盘
        this.addPlayerNodeSafely(numberNode);
        // 播放出现动画
        this.playNumberAppearAnimation(numberNode, number);
    };
    /**
     * 播放数字出现动画
     * @param numberNode 数字节点
     * @param number 数字
     */
    ChessBoardController.prototype.playNumberAppearAnimation = function (numberNode, number) {
        // 使用cc.Tween播放数字出现动画
        numberNode.setScale(0);
        cc.tween(numberNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    };
    /**
     * 播放格子消失动画（连锁效果）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param neighborMines 周围地雷数量
     */
    ChessBoardController.prototype.playGridDisappearAnimation = function (x, y, neighborMines) {
        var _this = this;
        // 先删除格子
        this.removeGridAt(x, y);
        // 延迟0.3秒后显示数字（等格子消失动画完成）
        this.scheduleOnce(function () {
            _this.updateNeighborMinesDisplay(x, y, neighborMines);
        }, 0.3);
    };
    /**
     * 根据数字获取颜色
     * @param number 数字
     * @returns 颜色
     */
    ChessBoardController.prototype.getNumberColor = function (number) {
        switch (number) {
            case 1: return cc.Color.BLUE;
            case 2: return cc.Color.GREEN;
            case 3: return cc.Color.RED;
            case 4: return cc.Color.MAGENTA;
            case 5: return cc.Color.YELLOW;
            case 6: return cc.Color.CYAN;
            case 7: return cc.Color.BLACK;
            case 8: return cc.Color.GRAY;
            default: return cc.Color.BLACK;
        }
    };
    /**
     * 播放棋盘震动动画（包括所有小格子）
     */
    ChessBoardController.prototype.playBoardShakeAnimation = function () {
        if (!this.boardNode) {
            console.warn("boardNode 未设置，无法播放震动效果");
            return;
        }
        // 震动参数 - 增强震动效果
        var shakeIntensity = 30; // 震动强度
        var shakeDuration = 1.0; // 震动持续时间
        var shakeFrequency = 40; // 震动频率
        // 震动棋盘
        this.shakeBoardNode(shakeIntensity, shakeDuration, shakeFrequency);
        // 震动所有小格子
        this.shakeAllGrids(shakeIntensity * 0.6, shakeDuration, shakeFrequency);
    };
    /**
     * 震动棋盘节点
     */
    ChessBoardController.prototype.shakeBoardNode = function (intensity, duration, frequency) {
        // 保存原始位置
        var originalPosition = this.boardNode.position.clone();
        // 创建震动动画，使用递减强度
        var currentIntensity = intensity;
        var intensityDecay = 0.92; // 强度衰减系数
        var createShakeStep = function (shakeIntensity) {
            return cc.tween()
                .to(0.025, {
                x: originalPosition.x + (Math.random() - 0.5) * shakeIntensity * 2,
                y: originalPosition.y + (Math.random() - 0.5) * shakeIntensity * 2
            });
        };
        // 创建震动序列，强度逐渐衰减
        var shakeTween = cc.tween(this.boardNode);
        var totalSteps = Math.floor(duration * frequency);
        for (var i = 0; i < totalSteps; i++) {
            shakeTween = shakeTween.then(createShakeStep(currentIntensity));
            currentIntensity *= intensityDecay; // 逐渐减弱震动强度
        }
        // 最后恢复到原位置
        shakeTween.to(0.2, {
            x: originalPosition.x,
            y: originalPosition.y
        }, { easing: 'backOut' })
            .start();
    };
    /**
     * 震动所有小格子
     */
    ChessBoardController.prototype.shakeAllGrids = function (intensity, duration, frequency) {
        if (!this.gridNodes)
            return;
        // 遍历所有格子节点
        for (var x = 0; x < this.gridNodes.length; x++) {
            if (!this.gridNodes[x])
                continue;
            for (var y = 0; y < this.gridNodes[x].length; y++) {
                var gridNode = this.gridNodes[x][y];
                if (!gridNode || !gridNode.active)
                    continue;
                // 为每个格子创建独立的震动动画
                this.shakeGridNode(gridNode, intensity, duration, frequency);
            }
        }
    };
    /**
     * 震动单个格子节点
     */
    ChessBoardController.prototype.shakeGridNode = function (gridNode, intensity, duration, frequency) {
        // 保存原始位置
        var originalPosition = gridNode.position.clone();
        // 为每个格子添加随机延迟，创造波浪效果
        var randomDelay = Math.random() * 0.1;
        this.scheduleOnce(function () {
            // 创建震动动画，使用递减强度
            var currentIntensity = intensity;
            var intensityDecay = 0.94; // 格子震动衰减稍慢一些
            var createGridShakeStep = function (shakeIntensity) {
                return cc.tween()
                    .to(0.02, {
                    x: originalPosition.x + (Math.random() - 0.5) * shakeIntensity * 2,
                    y: originalPosition.y + (Math.random() - 0.5) * shakeIntensity * 2
                });
            };
            // 创建震动序列
            var shakeTween = cc.tween(gridNode);
            var totalSteps = Math.floor(duration * frequency * 0.8); // 格子震动时间稍短
            for (var i = 0; i < totalSteps; i++) {
                shakeTween = shakeTween.then(createGridShakeStep(currentIntensity));
                currentIntensity *= intensityDecay;
            }
            // 最后恢复到原位置
            shakeTween.to(0.15, {
                x: originalPosition.x,
                y: originalPosition.y
            }, { easing: 'backOut' })
                .start();
        }, randomDelay);
    };
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "playerGamePrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boomPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "biaojiPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom1Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom2Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom3Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom4Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom5Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom6Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom7Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom8Prefab", void 0);
    __decorate([
        property(cc.Node)
    ], ChessBoardController.prototype, "boardNode", void 0);
    ChessBoardController = __decorate([
        ccclass
    ], ChessBoardController);
    return ChessBoardController;
}(cc.Component));
exports.default = ChessBoardController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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