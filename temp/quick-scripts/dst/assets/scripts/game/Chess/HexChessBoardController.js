
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/game/Chess/HexChessBoardController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'd9f2eirVKNNorhz2LDmkG2T', 'HexChessBoardController');
// scripts/game/Chess/HexChessBoardController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalBean_1 = require("../../bean/GlobalBean");
var PlayerGameController_1 = require("../../pfb/PlayerGameController ");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var HexChessBoardController = /** @class */ (function (_super) {
    __extends(HexChessBoardController, _super);
    function HexChessBoardController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.playerGamePrefab = null;
        _this.boomPrefab = null;
        _this.biaojiPrefab = null;
        _this.boom1Prefab = null;
        _this.boom2Prefab = null;
        _this.boom3Prefab = null;
        _this.boom4Prefab = null;
        _this.boom5Prefab = null;
        _this.boom6Prefab = null;
        _this.boom7Prefab = null;
        _this.boom8Prefab = null;
        _this.boardNode = null; // 棋盘节点
        // 六边形棋盘配置
        _this.HEX_SIZE = 44; // 六边形半径
        _this.HEX_WIDTH = _this.HEX_SIZE * 2; // 六边形宽度
        _this.HEX_HEIGHT = _this.HEX_SIZE * Math.sqrt(3); // 六边形高度
        // 格子数据存储 - 使用Map存储六边形坐标
        _this.hexGridData = new Map(); // 存储六边形格子数据
        _this.hexGridNodes = new Map(); // 存储六边形格子节点
        _this.validHexCoords = []; // 有效的六边形坐标列表
        return _this;
    }
    HexChessBoardController.prototype.onLoad = function () {
    };
    HexChessBoardController.prototype.start = function () {
        var _this = this;
        // 延迟一帧后再次尝试启用触摸事件，确保所有节点都已创建完成
        this.scheduleOnce(function () {
            _this.setValidHexCoords([]); // 传入空数组，但会被忽略
            // 测试预制体位置计算
            _this.testHexPositionCalculation();
            _this.enableTouchForExistingGrids();
        }, 0.1);
    };
    /**
     * 设置有效的六边形坐标列表（忽略服务器数据，使用前端节点坐标）
     * @param _coords 服务器发送的坐标列表（将被忽略）
     */
    HexChessBoardController.prototype.setValidHexCoords = function (_coords) {
        // 忽略服务器传入的坐标，始终从节点名称自动生成
        this.generateCoordsFromNodeNames();
        this.initHexBoard();
    };
    /**
     * 从节点名称自动生成有效坐标列表
     */
    HexChessBoardController.prototype.generateCoordsFromNodeNames = function () {
        if (!this.boardNode) {
            console.error("❌ 棋盘节点不存在，无法生成坐标列表");
            return;
        }
        var foundCoords = [];
        var children = this.boardNode.children;
        var _loop_1 = function (i) {
            var child = children[i];
            var nodeName = child.name;
            // 跳过游戏元素节点，只处理六边形格子节点
            if (this_1.isGameElement(child, nodeName)) {
                return "continue";
            }
            var coords = this_1.parseHexCoordinateFromName(nodeName);
            if (coords) {
                // 检查是否已经存在相同的坐标
                var exists = foundCoords.some(function (c) { return c.q === coords.q && c.r === coords.r; });
                if (!exists) {
                    foundCoords.push({ q: coords.q, r: coords.r });
                }
            }
        };
        var this_1 = this;
        for (var i = 0; i < children.length; i++) {
            _loop_1(i);
        }
        this.validHexCoords = foundCoords;
    };
    // 初始化六边形棋盘
    HexChessBoardController.prototype.initHexBoard = function () {
        // 清空现有数据
        this.hexGridData.clear();
        this.hexGridNodes.clear();
        // 初始化有效坐标的数据
        for (var _i = 0, _a = this.validHexCoords; _i < _a.length; _i++) {
            var coord = _a[_i];
            var key = this.getHexKey(coord.q, coord.r);
            this.hexGridData.set(key, {
                q: coord.q,
                r: coord.r,
                worldPos: this.getHexWorldPosition(coord.q, coord.r),
                hasPlayer: false
            });
        }
        this.createHexGridNodes();
    };
    // 生成六边形坐标的唯一键
    HexChessBoardController.prototype.getHexKey = function (q, r) {
        return q + "," + r;
    };
    // 启用现有格子的触摸事件
    HexChessBoardController.prototype.createHexGridNodes = function () {
        if (!this.boardNode) {
            console.error("棋盘节点未设置！");
            return;
        }
        // 如果格子已经存在，直接启用触摸事件
        this.enableTouchForExistingGrids();
    };
    // 为现有格子启用触摸事件
    HexChessBoardController.prototype.enableTouchForExistingGrids = function () {
        // 检查棋盘节点是否存在
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法启用触摸事件！");
            return;
        }
        // 遍历棋盘节点的所有子节点
        var children = this.boardNode.children;
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            var nodeName = child.name;
            // 跳过游戏元素节点，只处理六边形格子节点
            if (this.isGameElement(child, nodeName)) {
                continue;
            }
            // 尝试从节点名称解析六边形坐标
            var coords = this.parseHexCoordinateFromName(nodeName);
            if (coords) {
                this.setupHexGridTouchEvents(child, coords.q, coords.r);
                var key = this.getHexKey(coords.q, coords.r);
                this.hexGridNodes.set(key, child);
            }
            else {
                // 如果无法从名称解析，尝试从位置计算
                var pos = child.getPosition();
                var coords_1 = this.getHexCoordinateFromPosition(pos);
                if (coords_1) {
                    this.setupHexGridTouchEvents(child, coords_1.q, coords_1.r);
                    var key = this.getHexKey(coords_1.q, coords_1.r);
                    this.hexGridNodes.set(key, child);
                }
            }
        }
    };
    // 从节点名称解析六边形坐标
    HexChessBoardController.prototype.parseHexCoordinateFromName = function (nodeName) {
        var patterns = [
            /^sixblock_(-?\d+)_(-?\d+)$/,
        ];
        for (var _i = 0, patterns_1 = patterns; _i < patterns_1.length; _i++) {
            var pattern = patterns_1[_i];
            var match = nodeName.match(pattern);
            if (match) {
                var coords = { q: parseInt(match[1]), r: parseInt(match[2]) };
                return coords;
            }
        }
        console.warn("\u274C \u65E0\u6CD5\u89E3\u6790\u8282\u70B9\u540D\u79F0: " + nodeName);
        return null;
    };
    // 从位置计算六边形坐标（近似）
    HexChessBoardController.prototype.getHexCoordinateFromPosition = function (pos) {
        // 六边形坐标转换（从像素坐标到六边形坐标）
        var x = pos.x;
        var y = pos.y;
        // 使用六边形坐标转换公式
        var q = Math.round((Math.sqrt(3) / 3 * x - 1 / 3 * y) / this.HEX_SIZE);
        var r = Math.round((2 / 3 * y) / this.HEX_SIZE);
        // 检查是否为有效坐标
        if (this.isValidHexCoordinate(q, r)) {
            return { q: q, r: r };
        }
        return null;
    };
    // 计算六边形预制体的生成位置（直接使用您提供的格子中心坐标）
    HexChessBoardController.prototype.getHexWorldPosition = function (q, r, isPlayerAvatar) {
        if (isPlayerAvatar === void 0) { isPlayerAvatar = false; }
        // 您提供的精确格子中心坐标
        var exactCoords = new Map();
        // 更新后的基准点坐标（与rowData保持一致）
        exactCoords.set("0,0", cc.v2(-300, -258)); // r=0行基准点
        exactCoords.set("1,-1", cc.v2(-258, -184)); // r=-1行基准点
        exactCoords.set("1,-2", cc.v2(-300, -108)); // r=-2行基准点
        exactCoords.set("2,-3", cc.v2(-258, -36)); // r=-3行基准点
        exactCoords.set("2,-4", cc.v2(-300, 37)); // r=-4行基准点
        exactCoords.set("3,-5", cc.v2(-258, 110)); // r=-5行基准点
        exactCoords.set("3,-6", cc.v2(-300, 185)); // r=-6行基准点
        exactCoords.set("4,-7", cc.v2(-258, 260)); // r=-7行基准点
        // 首先检查是否有精确坐标
        var key = q + "," + r;
        if (exactCoords.has(key)) {
            var pos = exactCoords.get(key);
            // 如果是单人头像预制体，往左上偏移一点点
            if (isPlayerAvatar) {
                return cc.v2(pos.x, pos.y - 12); // 改为往左偏移10像素
            }
            return pos;
        }
        // 对于其他坐标，使用基于您提供的精确坐标数据进行计算
        // 定义每一行的数据：使用统一步长86，保证美观整齐
        var UNIFORM_STEP_X = 86; // 统一的x方向步长
        var rowData = new Map();
        // 基于您提供的更新数据，使用统一步长86
        rowData.set(0, { baseQ: 0, baseX: -300, y: -258 }); // r=0行：基准点(0,0) → (-300, -258)
        rowData.set(-1, { baseQ: 1, baseX: -258, y: -184 }); // r=-1行：基准点(1,-1) → (-258, -184)
        rowData.set(-2, { baseQ: 1, baseX: -300, y: -108 }); // r=-2行：基准点(1,-2) → (-300, -108)
        rowData.set(-3, { baseQ: 2, baseX: -258, y: -36 }); // r=-3行：基准点(2,-3) → (-258, -36)
        rowData.set(-4, { baseQ: 2, baseX: -300, y: 37 }); // r=-4行：基准点(2,-4) → (-300, 37)
        rowData.set(-5, { baseQ: 3, baseX: -258, y: 110 }); // r=-5行：基准点(3,-5) → (-258, 110)
        rowData.set(-6, { baseQ: 3, baseX: -300, y: 185 }); // r=-6行：基准点(3,-6) → (-300, 185)
        rowData.set(-7, { baseQ: 4, baseX: -258, y: 260 }); // r=-7行：基准点(4,-7) → (-258, 260)
        // 计算基础位置
        var x, y;
        // 如果有该行的数据，使用统一步长计算
        if (rowData.has(r)) {
            var data = rowData.get(r);
            x = data.baseX + (q - data.baseQ) * UNIFORM_STEP_X;
            y = data.y;
        }
        else {
            // 对于其他行，使用通用的六边形轴线坐标系公式（也使用统一步长）
            var baseX = -300; // 更新为新的基准点
            var baseY = -258;
            var stepXR = -43;
            var stepYR = 74;
            x = baseX + q * UNIFORM_STEP_X + r * stepXR;
            y = baseY - r * stepYR;
        }
        // 如果是单人头像预制体，往左上偏移一点点
        if (isPlayerAvatar) {
            y -= 12; // 往下偏移12像素（相比之前的-20，现在是-12，相当于往上调了8像素）
        }
        return cc.v2(x, y);
    };
    // 为六边形格子节点设置触摸事件
    HexChessBoardController.prototype.setupHexGridTouchEvents = function (gridNode, q, r) {
        var _this = this;
        // 安全检查：确保坐标有效
        if (!this.isValidHexCoordinate(q, r)) {
            console.error("\u274C setupHexGridTouchEvents: \u5C1D\u8BD5\u4E3A\u65E0\u6548\u5750\u6807(" + q + "," + r + ")\u8BBE\u7F6E\u89E6\u6478\u4E8B\u4EF6");
            return;
        }
        // 长按相关变量
        var isLongPressing = false;
        var longPressTimer = 0;
        var longPressCallback = null;
        var LONG_PRESS_TIME = 1.0; // 1秒长按时间
        // 触摸开始事件
        gridNode.on(cc.Node.EventType.TOUCH_START, function (_event) {
            isLongPressing = true;
            longPressTimer = 0;
            // 开始长按检测
            longPressCallback = function () {
                if (isLongPressing) {
                    longPressTimer += 0.1;
                    if (longPressTimer >= LONG_PRESS_TIME) {
                        _this.onHexGridLongPress(q, r);
                        isLongPressing = false;
                        if (longPressCallback) {
                            _this.unschedule(longPressCallback);
                        }
                    }
                }
            };
            _this.schedule(longPressCallback, 0.1);
        }, this);
        // 触摸结束事件
        gridNode.on(cc.Node.EventType.TOUCH_END, function (event) {
            // 如果不是长按，则执行点击事件
            if (isLongPressing && longPressTimer < LONG_PRESS_TIME) {
                _this.onHexGridClick(q, r, event);
            }
            isLongPressing = false;
            if (longPressCallback) {
                _this.unschedule(longPressCallback);
            }
        }, this);
        // 触摸取消事件
        gridNode.on(cc.Node.EventType.TOUCH_CANCEL, function (_event) {
            isLongPressing = false;
            if (longPressCallback) {
                _this.unschedule(longPressCallback);
            }
        }, this);
        // 添加Button组件以确保触摸响应
        var button = gridNode.getComponent(cc.Button);
        if (!button) {
            button = gridNode.addComponent(cc.Button);
            button.transition = cc.Button.Transition.SCALE;
            button.zoomScale = 0.95;
        }
    };
    // 六边形格子点击事件 - 发送挖掘操作
    HexChessBoardController.prototype.onHexGridClick = function (q, r, _event) {
        // 检查坐标是否有效
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn("\u274C \u65E0\u6548\u7684\u516D\u8FB9\u5F62\u5750\u6807: (" + q + ", " + r + ")");
            return;
        }
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        // 检查该位置是否已经有玩家预制体
        if (gridData && gridData.hasPlayer) {
            console.warn("\u26A0\uFE0F \u683C\u5B50(" + q + ", " + r + ")\u5DF2\u6709\u73A9\u5BB6");
            return;
        }
        // 发送挖掘操作事件 (action = 1)
        this.node.emit('hex-chess-board-click', {
            q: q,
            r: r,
            action: 1 // 1 = 挖掘
        });
    };
    // 六边形格子长按事件 - 发送标记操作
    HexChessBoardController.prototype.onHexGridLongPress = function (q, r) {
        // 检查坐标是否有效
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn("\u274C \u65E0\u6548\u7684\u516D\u8FB9\u5F62\u5750\u6807: (" + q + ", " + r + ")");
            return;
        }
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        // 检查该位置是否已经有玩家预制体
        if (gridData && gridData.hasPlayer) {
            console.warn("\u26A0\uFE0F \u683C\u5B50(" + q + ", " + r + ")\u5DF2\u6709\u73A9\u5BB6");
            return;
        }
        // 发送标记操作事件 (action = 2)
        this.node.emit('hex-chess-board-click', {
            q: q,
            r: r,
            action: 2 // 2 = 标记
        });
    };
    // 检查六边形坐标是否有效
    HexChessBoardController.prototype.isValidHexCoordinate = function (q, r) {
        var key = this.getHexKey(q, r);
        return this.hexGridData.has(key);
    };
    // 在六边形格子上放置玩家预制体
    HexChessBoardController.prototype.placePlayerOnHexGrid = function (q, r, withFlag) {
        var _this = this;
        if (withFlag === void 0) { withFlag = false; }
        // 双重检查：确保坐标有效
        if (!this.isValidHexCoordinate(q, r)) {
            console.error("\u274C placePlayerOnHexGrid: \u65E0\u6548\u5750\u6807(" + q + "," + r + ")");
            return;
        }
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        // 双重检查：确保格子为空
        if (!gridData || gridData.hasPlayer) {
            console.error("\u274C placePlayerOnHexGrid: \u683C\u5B50(" + q + "," + r + ")\u5DF2\u6709\u73A9\u5BB6\uFF0C\u4E0D\u80FD\u91CD\u590D\u653E\u7F6E");
            return;
        }
        if (!this.playerGamePrefab) {
            console.error("❌ 玩家预制体未设置！");
            return;
        }
        if (!this.boardNode) {
            console.error("❌ 棋盘节点未设置！");
            return;
        }
        // 实例化玩家预制体
        var playerNode = cc.instantiate(this.playerGamePrefab);
        // 计算正确的位置（单人头像预制体，y轴+20）
        var correctPosition = this.getHexWorldPosition(q, r, true);
        playerNode.setPosition(correctPosition);
        // 设置单人放置的缩放为0.8
        playerNode.setScale(0.8);
        // 先隐藏节点，等头像加载完成后再显示
        playerNode.active = false;
        // 处理Layout限制问题
        this.addPlayerNodeSafely(playerNode);
        // 设置头像和用户数据（异步加载）
        this.setupPlayerAvatarAsync(playerNode, q, r, withFlag, function () {
            // 头像加载完成的回调，播放生成动画
            _this.playAvatarSpawnAnimation(playerNode);
        });
        // 更新格子数据
        gridData.hasPlayer = true;
        gridData.playerNode = playerNode;
    };
    // 安全地添加玩家节点（处理Layout限制）
    HexChessBoardController.prototype.addPlayerNodeSafely = function (playerNode) {
        // 检查棋盘节点是否存在
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法添加玩家节点！");
            return;
        }
        // 检查棋盘节点是否有Layout组件
        var layout = this.boardNode.getComponent(cc.Layout);
        if (layout) {
            // 临时禁用Layout
            layout.enabled = false;
            // 添加节点
            this.boardNode.addChild(playerNode);
        }
        else {
            this.boardNode.addChild(playerNode);
        }
    };
    // 异步设置玩家头像（带回调）
    HexChessBoardController.prototype.setupPlayerAvatarAsync = function (playerNode, q, r, withFlag, onComplete) {
        var _a, _b;
        // 查找PlayerGameController组件（使用类引用）
        var playerController = playerNode.getComponent(PlayerGameController_1.default);
        if (playerController) {
            // 检查avatar节点是否存在
            if (playerController.avatar) {
                // 检查avatar节点是否有Sprite组件
                var avatarSprite = playerController.avatar.getComponent(cc.Sprite);
                if (!avatarSprite) {
                    avatarSprite = playerController.avatar.addComponent(cc.Sprite);
                }
                // 确保avatar节点可见
                playerController.avatar.active = true;
                playerController.avatar.opacity = 255;
            }
            else {
                console.error("❌ PlayerGameController中的avatar节点为null");
                onComplete();
                return;
            }
            // 设置旗子节点的显示状态
            if (playerController.flagNode) {
                playerController.flagNode.active = withFlag;
                // 额外检查旗子节点的可见性
                if (withFlag) {
                    playerController.flagNode.opacity = 255;
                    // 确保旗子节点的父节点也是可见的
                    var parent = playerController.flagNode.parent;
                    while (parent && parent !== playerNode) {
                        parent.active = true;
                        parent = parent.parent;
                    }
                }
            }
            else {
                console.warn("\u26A0\uFE0F \u627E\u4E0D\u5230\u65D7\u5B50\u8282\u70B9 (" + q + "," + r + ")");
            }
            // 获取当前用户ID
            var currentUserId = ((_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId) || "hex_player_" + q + "_" + r;
            // 在节点上存储userId，用于后续查找
            playerNode['userId'] = currentUserId;
            // 创建用户数据并设置头像
            var userData = {
                userId: currentUserId,
                nickName: "\u73A9\u5BB6(" + q + "," + r + ")",
                avatar: this.getDefaultAvatarUrl(),
                score: 0,
                pos: 0,
                coin: 0,
                status: 0,
                rank: 0
            };
            // 使用PlayerGameController的setData方法来设置头像
            try {
                playerController.setData(userData);
                // 延迟设置旗子状态，确保在PlayerGameController初始化之后
                this.scheduleOnce(function () {
                    if (playerController.flagNode) {
                        playerController.flagNode.active = withFlag;
                    }
                    onComplete();
                }, 0.1);
            }
            catch (error) {
                console.error("设置头像数据失败:", error);
                onComplete();
            }
        }
        else {
            console.warn("⚠️ 找不到PlayerGameController组件");
            onComplete();
        }
    };
    // 获取默认头像URL
    HexChessBoardController.prototype.getDefaultAvatarUrl = function () {
        // 使用真实的头像URL
        return "https://static.gooplay.com/online/user-avatar/1732786296530322669.jpg";
    };
    /**
     * 播放头像生成动画（由大变小，完全复制四边形棋盘控制器的逻辑）
     * @param playerNode 玩家节点
     */
    HexChessBoardController.prototype.playAvatarSpawnAnimation = function (playerNode) {
        if (!playerNode) {
            console.warn("播放生成动画失败：节点为空");
            return;
        }
        // 显示节点
        playerNode.active = true;
        // 设置初始缩放为1.5倍（比正常大）
        var originalScale = playerNode.scaleX;
        var startScale = originalScale * 1.5;
        playerNode.setScale(startScale);
        // 使用cc.Tween创建由大变小的缩放动画
        cc.tween(playerNode)
            .to(0.3, { scaleX: originalScale, scaleY: originalScale }, { easing: 'backOut' })
            .start();
    };
    // 清除指定六边形格子的玩家
    HexChessBoardController.prototype.clearHexGridPlayer = function (q, r) {
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (!gridData || !gridData.hasPlayer) {
            return false;
        }
        // 移除玩家节点
        if (gridData.playerNode) {
            gridData.playerNode.removeFromParent();
            gridData.playerNode = null;
        }
        // 更新数据
        gridData.hasPlayer = false;
        return true;
    };
    // 清除所有玩家
    HexChessBoardController.prototype.clearAllPlayers = function () {
        var clearedCount = 0;
        // 1. 清理存储在hexGridData中的玩家节点（自己的头像）
        this.hexGridData.forEach(function (gridData) {
            if (gridData.hasPlayer && gridData.playerNode) {
                gridData.playerNode.removeFromParent();
                gridData.playerNode = null;
                gridData.hasPlayer = false;
                clearedCount++;
            }
        });
        // 2. 清理棋盘上的其他玩家头像节点
        if (this.boardNode) {
            var children = this.boardNode.children.slice(); // 创建副本避免遍历时修改数组
            for (var i = 0; i < children.length; i++) {
                var child = children[i];
                if (child.name === "player_game_pfb") {
                    // 检查是否有PlayerGameController组件
                    var playerController = child.getComponent(PlayerGameController_1.default);
                    if (playerController) {
                        child.removeFromParent();
                        clearedCount++;
                    }
                }
            }
        }
    };
    /**
     * 清理所有玩家预制体（新回合开始时调用）
     * 包括自己的头像和其他玩家的头像
     * 为了与四边形棋盘控制器保持一致的接口
     */
    HexChessBoardController.prototype.clearAllPlayerNodes = function () {
        this.clearAllPlayers();
    };
    // 获取所有已放置玩家的六边形坐标
    HexChessBoardController.prototype.getAllPlayerHexCoordinates = function () {
        var coordinates = [];
        this.hexGridData.forEach(function (gridData) {
            if (gridData.hasPlayer) {
                coordinates.push({ q: gridData.q, r: gridData.r });
            }
        });
        return coordinates;
    };
    // 检查六边形格子是否为空
    HexChessBoardController.prototype.isHexGridEmpty = function (q, r) {
        if (!this.isValidHexCoordinate(q, r)) {
            return false;
        }
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        return gridData ? !gridData.hasPlayer : false;
    };
    /**
     * 重置游戏场景（游戏开始时调用）
     * 清除数字、炸弹、标记预制体，重新显示所有小格子
     */
    HexChessBoardController.prototype.resetGameScene = function () {
        if (!this.boardNode) {
            console.error("❌ 棋盘节点不存在，无法重置");
            return;
        }
        // 清除所有游戏元素（数字、炸弹、标记等）
        this.clearAllGameElements();
        // 显示所有小格子
        this.showAllHexGrids();
        // 重新初始化棋盘数据
        this.reinitializeHexBoardData();
    };
    /**
     * 清除所有游戏元素（数字、炸弹、标记、玩家头像等），但保留小格子
     */
    HexChessBoardController.prototype.clearAllGameElements = function () {
        if (!this.boardNode) {
            console.error("❌ 棋盘节点不存在，无法清除游戏元素");
            return;
        }
        var childrenToRemove = [];
        // 遍历棋盘的所有子节点
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            var nodeName = child.name;
            // 检查是否是需要清除的游戏元素（不包括小格子）
            if (this.isGameElement(child, nodeName)) {
                childrenToRemove.push(child);
            }
        }
        // 移除找到的游戏元素
        childrenToRemove.forEach(function (child) {
            child.removeFromParent();
        });
    };
    /**
     * 判断节点是否是游戏元素（需要清除的），小格子不会被清除
     */
    HexChessBoardController.prototype.isGameElement = function (node, nodeName) {
        // 绝对不清除的节点（六边形小格子）
        if (nodeName.startsWith("sixblock_") || nodeName === "hexblock") {
            return false;
        }
        // 分数控制器不清除
        if (nodeName.includes("Score") || nodeName.includes("score")) {
            return false;
        }
        // UI相关节点不清除
        if (nodeName.includes("UI") || nodeName.includes("ui")) {
            return false;
        }
        // 明确需要清除的游戏预制体
        // 炸弹预制体
        if (nodeName === "Boom") {
            return true;
        }
        // 数字预制体（Boom1, Boom2, Boom3 等，以及 HexBoom1, HexBoom2 等）
        if (nodeName.match(/^(Hex)?Boom\d+$/)) {
            return true;
        }
        // 临时数字节点（NeighborMines_1, NeighborMines_2 等）
        if (nodeName.match(/^NeighborMines_\d+$/)) {
            return true;
        }
        // 测试节点（Test_q_r 格式）
        if (nodeName.match(/^Test_-?\d+_-?\d+$/)) {
            return true;
        }
        // 玩家预制体（通过组件判断）
        if (node.getComponent(PlayerGameController_1.default)) {
            return true;
        }
        // 标记预制体
        if (nodeName.includes("Flag") || nodeName.includes("Mark") || nodeName.includes("flag") ||
            nodeName === "Biaoji" || nodeName.includes("Biaoji")) {
            return true;
        }
        // 玩家头像预制体
        if (nodeName.includes("Player") || nodeName.includes("Avatar")) {
            return true;
        }
        // 默认保留未知节点（保守策略）
        return false;
    };
    /**
     * 显示所有六边形小格子（第二把游戏开始时恢复被隐藏的小格子）
     */
    HexChessBoardController.prototype.showAllHexGrids = function () {
        if (!this.boardNode) {
            console.error("❌ 棋盘节点不存在，无法显示六边形格子");
            return;
        }
        var totalGrids = 0;
        var restoredGrids = 0;
        var alreadyVisibleGrids = 0;
        // 遍历棋盘的所有子节点，找到小格子并显示
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            // 如果是六边形小格子节点（sixblock_q_r 格式）
            if (child.name.startsWith("sixblock_") || child.name === "hexblock") {
                totalGrids++;
                // 记录恢复前的状态
                var wasHidden = !child.active || child.opacity < 255 || child.scaleX < 1 || child.scaleY < 1;
                if (wasHidden) {
                    restoredGrids++;
                }
                else {
                    alreadyVisibleGrids++;
                }
                // 停止所有可能正在进行的动画
                child.stopAllActions();
                // 恢复显示状态
                child.active = true;
                child.opacity = 255;
                child.scaleX = 1;
                child.scaleY = 1;
                child.angle = 0; // 重置旋转角度
                // 恢复原始位置（如果有保存的话）
                if (child['originalPosition']) {
                    child.setPosition(child['originalPosition']);
                }
                // 确保格子可以交互
                var button = child.getComponent(cc.Button);
                if (button) {
                    button.enabled = true;
                }
            }
        }
        if (totalGrids === 0) {
            console.warn("⚠️ 没有找到任何六边形格子节点！请检查节点命名是否正确");
            // 列出所有子节点名称以便调试
        }
    };
    /**
     * 隐藏指定位置的六边形小格子（点击时调用）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param immediate 是否立即隐藏（不播放动画）
     */
    HexChessBoardController.prototype.hideHexGridAt = function (q, r, immediate) {
        if (immediate === void 0) { immediate = false; }
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn("\u9690\u85CF\u683C\u5B50\u5931\u8D25\uFF1A\u5750\u6807(" + q + ", " + r + ")\u65E0\u6548");
            return;
        }
        // 获取格子节点
        var key = this.getHexKey(q, r);
        var gridNode = this.hexGridNodes.get(key);
        if (gridNode) {
            if (immediate) {
                // 立即隐藏，不播放动画
                gridNode.active = false;
            }
            else {
                // 播放六边形格子消失动画
                this.playHexGridFallAnimation(gridNode);
            }
        }
    };
    /**
     * 播放六边形格子消失动画
     * 效果：格子持续旋转，给一个随机向上的力，然后旋转着自由落体
     * @param gridNode 格子节点
     */
    HexChessBoardController.prototype.playHexGridFallAnimation = function (gridNode) {
        if (!gridNode)
            return;
        // 停止该格子上所有正在进行的动画（包括震动动画）
        gridNode.stopAllActions();
        // 保存格子的原始位置（用于重置时恢复）
        if (!gridNode['originalPosition']) {
            gridNode['originalPosition'] = gridNode.getPosition();
        }
        // 随机选择向上的力的方向：0=向上，1=右上15度，2=左上15度
        var forceDirection = Math.floor(Math.random() * 3);
        var moveX = 0;
        var moveY = 200; // 向上的基础距离（增加高度）
        switch (forceDirection) {
            case 0: // 向上
                moveX = 0;
                break;
            case 1: // 右上20度
                moveX = Math.sin(20 * Math.PI / 180) * moveY;
                break;
            case 2: // 左上20度
                moveX = -Math.sin(20 * Math.PI / 180) * moveY;
                break;
        }
        // 随机旋转速度
        var rotationSpeed = (Math.random() * 1440 + 720) * (Math.random() > 0.5 ? 1 : -1); // 720-2160度/秒，随机方向
        // 动画参数
        var upTime = 0.15; // 向上运动时间
        var fallTime = 0.3; // 下落时间
        var initialPosition = gridNode.getPosition();
        // 创建持续旋转的动画
        var rotationTween = cc.tween(gridNode)
            .repeatForever(cc.tween().by(0.1, { angle: rotationSpeed * 0.1 }));
        // 创建分阶段的运动动画
        var movementTween = cc.tween(gridNode)
            // 第一阶段：向上抛出
            .to(upTime, {
            x: initialPosition.x + moveX,
            y: initialPosition.y + moveY
        }, { easing: 'quadOut' })
            // 第二阶段：自由落体
            .to(fallTime, {
            x: initialPosition.x + moveX + (Math.random() - 0.5) * 100,
            y: initialPosition.y - 500 // 下落到屏幕下方更远处
        }, { easing: 'quadIn' })
            .call(function () {
            // 动画结束后隐藏格子
            gridNode.active = false;
            // 停止旋转动画
            gridNode.stopAllActions();
        });
        // 同时开始旋转和移动动画
        rotationTween.start();
        movementTween.start();
    };
    /**
     * 重新初始化六边形棋盘数据
     */
    HexChessBoardController.prototype.reinitializeHexBoardData = function () {
        // 重置hexGridData中的玩家状态
        this.hexGridData.forEach(function (gridData) {
            gridData.hasPlayer = false;
            gridData.playerNode = null;
        });
    };
    /**
     * 获取六边形格子数据
     */
    HexChessBoardController.prototype.getHexGridData = function (q, r) {
        var key = this.getHexKey(q, r);
        return this.hexGridData.get(key) || null;
    };
    /**
     * 批量放置玩家（用于从服务器同步数据）
     */
    HexChessBoardController.prototype.batchPlaceHexPlayers = function (coordinates) {
        var _this = this;
        coordinates.forEach(function (coord) {
            if (_this.isValidHexCoordinate(coord.q, coord.r) && _this.isHexGridEmpty(coord.q, coord.r)) {
                _this.placePlayerOnHexGrid(coord.q, coord.r);
            }
        });
    };
    /**
     * 测试点击功能（调试用）
     */
    HexChessBoardController.prototype.testHexClick = function (q, r) {
        this.onHexGridClick(q, r);
    };
    /**
     * 获取棋盘状态信息（调试用）
     */
    HexChessBoardController.prototype.getHexBoardInfo = function () {
        var info = {
            validHexCoordsCount: this.validHexCoords.length,
            boardNodeChildren: this.boardNode ? this.boardNode.children.length : 0,
            playerCount: this.getAllPlayerHexCoordinates().length,
            hasPlayerGamePrefab: !!this.playerGamePrefab,
            hasBoardNode: !!this.boardNode,
            hexGridDataSize: this.hexGridData.size,
            hexGridNodesSize: this.hexGridNodes.size
        };
        return info;
    };
    /**
     * 获取前端节点的总数量（用于计算炸弹数量）
     */
    HexChessBoardController.prototype.getHexGridCount = function () {
        return this.validHexCoords.length;
    };
    /**
     * 根据前端节点数量计算推荐的炸弹数量
     */
    HexChessBoardController.prototype.getRecommendedMineCount = function () {
        var gridCount = this.getHexGridCount();
        if (gridCount === 0) {
            return 13; // 默认值
        }
        // 约15%的格子是炸弹
        var mineCount = Math.floor(gridCount * 0.15);
        return Math.max(mineCount, 5); // 至少5个炸弹
    };
    /**
     * 测试六边形预制体位置计算是否正确
     */
    HexChessBoardController.prototype.testHexPositionCalculation = function () {
        var _this = this;
        // 测试更新后的基准点坐标
        var testPoints = [
            { q: 0, r: 0, expected: { x: -300, y: -258 }, desc: "r=0行基准点(0,0)" },
            { q: 1, r: -1, expected: { x: -258, y: -184 }, desc: "r=-1行基准点(1,-1)" },
            { q: 1, r: -2, expected: { x: -300, y: -108 }, desc: "r=-2行基准点(1,-2)" },
            { q: 2, r: -3, expected: { x: -258, y: -36 }, desc: "r=-3行基准点(2,-3)" },
            { q: 2, r: -4, expected: { x: -300, y: 37 }, desc: "r=-4行基准点(2,-4)" },
            { q: 3, r: -5, expected: { x: -258, y: 110 }, desc: "r=-5行基准点(3,-5)" },
            { q: 3, r: -6, expected: { x: -300, y: 185 }, desc: "r=-6行基准点(3,-6)" },
            { q: 4, r: -7, expected: { x: -258, y: 260 }, desc: "r=-7行基准点(4,-7)" }
        ];
        var correctCount = 0;
        testPoints.forEach(function (point) {
            var calculated = _this.getHexWorldPosition(point.q, point.r);
            var errorX = Math.abs(calculated.x - point.expected.x);
            var errorY = Math.abs(calculated.y - point.expected.y);
            var isCorrect = errorX < 2 && errorY < 2; // 允许2像素误差
            if (isCorrect)
                correctCount++;
        });
        // 测试一些中间坐标
        var intermediatePoints = [
            // r=0行测试
            { q: 2, r: 0 }, { q: 3, r: 0 }, { q: 4, r: 0 }, { q: 5, r: 0 }, { q: 6, r: 0 },
            // r=-1行测试
            { q: 3, r: -1 }, { q: 4, r: -1 }, { q: 5, r: -1 }, { q: 6, r: -1 },
            // r=-2行测试
            { q: 3, r: -2 }, { q: 4, r: -2 }, { q: 5, r: -2 }, { q: 6, r: -2 }, { q: 7, r: -2 },
            // r=-3行测试
            { q: 4, r: -3 }, { q: 5, r: -3 }, { q: 6, r: -3 }, { q: 7, r: -3 }
        ];
        intermediatePoints.forEach(function (point) {
            var calculated = _this.getHexWorldPosition(point.q, point.r);
        });
        // 暴露到全局以便调试
        window.testHexPositions = function () { return _this.testHexPositionCalculation(); };
    };
    // ==================== NoticeActionDisplay 相关方法 ====================
    // 以下方法与第一张地图（方形地图）的逻辑完全一样，用于处理加分和掀开地图
    /**
     * 在指定六边形位置创建boom预制体
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param isCurrentUser 是否是当前用户点到的雷
     */
    HexChessBoardController.prototype.createHexBoomPrefab = function (q, r, isCurrentUser) {
        var _this = this;
        if (isCurrentUser === void 0) { isCurrentUser = true; }
        if (!this.boomPrefab) {
            console.error("boomPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }
        // 实例化boom预制体
        var boomNode = cc.instantiate(this.boomPrefab);
        boomNode.name = "HexBoom";
        // 设置位置（使用六边形坐标计算，不是单人头像所以不偏移）
        var position = this.getHexWorldPosition(q, r, false);
        boomNode.setPosition(position);
        // 添加到棋盘
        this.boardNode.addChild(boomNode);
        // 播放出现动画
        boomNode.setScale(0);
        cc.tween(boomNode)
            .to(0.3, { scaleX: 1.2, scaleY: 1.2 }, { easing: 'backOut' })
            .to(0.1, { scaleX: 1.0, scaleY: 1.0 })
            .start();
        // 只有当前用户点到雷时才播放棋盘震动效果
        // 延迟0.45秒，等格子下落动画完成后再播放震动
        if (isCurrentUser) {
            this.scheduleOnce(function () {
                _this.playBoardShakeAnimation();
            }, 0.45);
        }
    };
    /**
     * 在指定六边形位置创建biaoji预制体
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     */
    HexChessBoardController.prototype.createHexBiaojiPrefab = function (q, r) {
        if (!this.biaojiPrefab) {
            console.error("biaojiPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }
        // 实例化biaoji预制体
        var biaojiNode = cc.instantiate(this.biaojiPrefab);
        biaojiNode.name = "HexBiaoji";
        // 设置位置（使用六边形坐标计算，不是单人头像所以不偏移）
        var position = this.getHexWorldPosition(q, r, false);
        biaojiNode.setPosition(position);
        // 添加到棋盘
        this.boardNode.addChild(biaojiNode);
        // 播放出现动画
        biaojiNode.setScale(0);
        cc.tween(biaojiNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    };
    /**
     * 更新指定六边形位置的neighborMines显示
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param neighborMines 周围地雷数量
     */
    HexChessBoardController.prototype.updateHexNeighborMinesDisplay = function (q, r, neighborMines) {
        // 0不需要显示数字
        if (neighborMines === 0) {
            return;
        }
        // 直接使用boom数字预制体
        this.createHexNumberPrefab(q, r, neighborMines);
    };
    /**
     * 创建六边形数字预制体（boom1, boom2, ...）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param number 数字
     */
    HexChessBoardController.prototype.createHexNumberPrefab = function (q, r, number) {
        // 根据数字选择对应的预制体
        var prefab = null;
        switch (number) {
            case 1:
                prefab = this.boom1Prefab;
                break;
            case 2:
                prefab = this.boom2Prefab;
                break;
            case 3:
                prefab = this.boom3Prefab;
                break;
            case 4:
                prefab = this.boom4Prefab;
                break;
            case 5:
                prefab = this.boom5Prefab;
                break;
            case 6:
                prefab = this.boom6Prefab;
                break;
            case 7:
                prefab = this.boom7Prefab;
                break;
            case 8:
                prefab = this.boom8Prefab;
                break;
            default:
                console.error("\u4E0D\u652F\u6301\u7684\u6570\u5B57: " + number);
                return;
        }
        if (!prefab) {
            console.error("boom" + number + "Prefab \u9884\u5236\u4F53\u672A\u8BBE\u7F6E\uFF0C\u8BF7\u5728\u7F16\u8F91\u5668\u4E2D\u6302\u8F7D");
            return;
        }
        // 实例化数字预制体
        var numberNode = cc.instantiate(prefab);
        numberNode.name = "HexBoom" + number;
        // 设置位置（使用六边形坐标计算，不是单人头像所以不偏移）
        var position = this.getHexWorldPosition(q, r, false);
        numberNode.setPosition(position);
        // 添加到棋盘
        this.boardNode.addChild(numberNode);
        // 播放出现动画
        numberNode.setScale(0);
        cc.tween(numberNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    };
    /**
     * 播放棋盘震动动画（包括所有小格子）
     */
    HexChessBoardController.prototype.playBoardShakeAnimation = function () {
        if (!this.boardNode) {
            return;
        }
        // 震动参数 - 增强震动效果
        var shakeIntensity = 30; // 震动强度
        var shakeDuration = 1.0; // 震动持续时间
        var shakeFrequency = 40; // 震动频率
        // 震动棋盘
        this.shakeBoardNode(shakeIntensity, shakeDuration, shakeFrequency);
        // 震动所有六边形格子
        this.shakeAllHexGrids(shakeIntensity * 0.6, shakeDuration, shakeFrequency);
    };
    /**
     * 震动棋盘节点
     */
    HexChessBoardController.prototype.shakeBoardNode = function (intensity, duration, frequency) {
        // 保存原始位置
        var originalPosition = this.boardNode.position.clone();
        // 创建震动动画，使用递减强度
        var currentIntensity = intensity;
        var intensityDecay = 0.92; // 强度衰减系数
        var createShakeStep = function (shakeIntensity) {
            return cc.tween()
                .to(0.025, {
                x: originalPosition.x + (Math.random() - 0.5) * shakeIntensity * 2,
                y: originalPosition.y + (Math.random() - 0.5) * shakeIntensity * 2
            });
        };
        // 创建震动序列，强度逐渐衰减
        var shakeTween = cc.tween(this.boardNode);
        var totalSteps = Math.floor(duration * frequency);
        for (var i = 0; i < totalSteps; i++) {
            shakeTween = shakeTween.then(createShakeStep(currentIntensity));
            currentIntensity *= intensityDecay; // 逐渐减弱震动强度
        }
        // 最后恢复到原位置
        shakeTween.to(0.2, {
            x: originalPosition.x,
            y: originalPosition.y
        }, { easing: 'backOut' })
            .start();
    };
    /**
     * 震动所有六边形格子
     */
    HexChessBoardController.prototype.shakeAllHexGrids = function (intensity, duration, frequency) {
        var _this = this;
        if (!this.hexGridNodes)
            return;
        // 遍历所有六边形格子节点
        this.hexGridNodes.forEach(function (gridNode, key) {
            if (!gridNode || !gridNode.active)
                return;
            // 为每个格子创建独立的震动动画
            _this.shakeHexGridNode(gridNode, intensity, duration, frequency);
        });
    };
    /**
     * 震动单个六边形格子节点
     */
    HexChessBoardController.prototype.shakeHexGridNode = function (gridNode, intensity, duration, frequency) {
        // 保存原始位置
        var originalPosition = gridNode.position.clone();
        // 为每个格子添加随机延迟，创造波浪效果
        var randomDelay = Math.random() * 0.1;
        this.scheduleOnce(function () {
            // 创建震动动画，使用递减强度
            var currentIntensity = intensity;
            var intensityDecay = 0.94; // 格子震动衰减稍慢一些
            var createGridShakeStep = function (shakeIntensity) {
                return cc.tween()
                    .to(0.02, {
                    x: originalPosition.x + (Math.random() - 0.5) * shakeIntensity * 2,
                    y: originalPosition.y + (Math.random() - 0.5) * shakeIntensity * 2
                });
            };
            // 创建震动序列
            var shakeTween = cc.tween(gridNode);
            var totalSteps = Math.floor(duration * frequency * 0.8); // 格子震动时间稍短
            for (var i = 0; i < totalSteps; i++) {
                shakeTween = shakeTween.then(createGridShakeStep(currentIntensity));
                currentIntensity *= intensityDecay;
            }
            // 最后恢复到原位置
            shakeTween.to(0.15, {
                x: originalPosition.x,
                y: originalPosition.y
            }, { easing: 'backOut' })
                .start();
        }, randomDelay);
    };
    // ==================== 头像生命周期管理 ====================
    // 以下方法与第一张地图的逻辑完全一样，用于管理头像预制体的生命周期
    /**
     * 让所有六边形头像消失（和第一张地图的hideAvatarsAtPosition逻辑一样）
     * @param onComplete 完成回调
     */
    HexChessBoardController.prototype.hideAllHexAvatars = function (onComplete) {
        var _this = this;
        if (!this.boardNode) {
            console.warn("棋盘节点不存在，无法清理六边形头像");
            onComplete();
            return;
        }
        // 收集所有头像节点（参考第一张地图的逻辑）
        var avatarNodes = [];
        // 方法1: 收集存储在hexGridData中的玩家节点（自己的头像）
        this.hexGridData.forEach(function (gridData) {
            if (gridData.hasPlayer && gridData.playerNode) {
                avatarNodes.push(gridData.playerNode);
            }
        });
        // 方法2: 收集棋盘上所有的玩家预制体节点（包括其他玩家的头像）
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            // 检查是否是玩家预制体（通过组件判断）
            var playerController = child.getComponent(PlayerGameController_1.default);
            if (playerController) {
                // 避免重复添加（可能已经在方法1中添加过）
                if (!avatarNodes.includes(child)) {
                    avatarNodes.push(child);
                }
            }
        }
        // 如果没有头像，直接执行回调
        if (avatarNodes.length === 0) {
            this.clearAllMyHexAvatarReferences();
            onComplete();
            return;
        }
        var completedCount = 0;
        var totalCount = avatarNodes.length;
        // 为每个头像播放消失动画（和第一张地图完全一样）
        avatarNodes.forEach(function (avatarNode) {
            // 使用cc.Tween播放消失动画
            cc.tween(avatarNode)
                .to(0.3, { opacity: 0, scaleX: 0.5, scaleY: 0.5 }, { easing: 'sineIn' })
                .call(function () {
                // 动画完成后移除节点
                avatarNode.removeFromParent();
                completedCount++;
                // 所有头像都消失完成后，执行回调
                if (completedCount >= totalCount) {
                    // 清除所有自己头像的引用
                    _this.clearAllMyHexAvatarReferences();
                    onComplete();
                }
            })
                .start();
        });
    };
    /**
     * 清除所有自己六边形头像的引用（和第一张地图的clearAllMyAvatarReferences逻辑一样）
     */
    HexChessBoardController.prototype.clearAllMyHexAvatarReferences = function () {
        this.hexGridData.forEach(function (gridData) {
            if (gridData.hasPlayer) {
                gridData.hasPlayer = false;
                gridData.playerNode = null;
            }
        });
    };
    // ==================== 加分逻辑相关方法 ====================
    // 以下方法与第一张地图的加分逻辑完全一样
    /**
     * 在指定六边形位置的玩家节点上显示分数
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param score 分数
     * @param showPlusOne 是否显示+1（先手奖励）
     */
    HexChessBoardController.prototype.showScoreOnHexPlayerNode = function (q, r, score, showPlusOne) {
        var _this = this;
        // 查找该位置的玩家节点
        var playerNode = this.findHexPlayerNodeAtPosition(q, r);
        if (!playerNode) {
            // 在NoticeActionDisplay流程中，头像会被清理，找不到节点是正常的
            return;
        }
        // 获取PlayerGameController组件
        var playerController = playerNode.getComponent(PlayerGameController_1.default);
        if (!playerController) {
            console.warn("找不到PlayerGameController组件");
            return;
        }
        // 显示分数动画
        if (showPlusOne) {
            // 先显示+1，再显示本回合得分
            this.showScoreAnimationOnHexNode(playerController, 1, function () {
                _this.scheduleOnce(function () {
                    _this.showScoreAnimationOnHexNode(playerController, score, null);
                }, 1.0);
            });
        }
        else {
            // 只显示本回合得分
            this.showScoreAnimationOnHexNode(playerController, score, null);
        }
    };
    /**
     * 查找指定六边形位置的玩家节点
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @returns 玩家节点或null
     */
    HexChessBoardController.prototype.findHexPlayerNodeAtPosition = function (q, r) {
        // 方法1: 从hexGridData中查找（自己的头像）
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (gridData && gridData.hasPlayer && gridData.playerNode) {
            return gridData.playerNode;
        }
        // 方法2: 在棋盘上查找其他玩家的头像
        if (!this.boardNode) {
            return null;
        }
        // 遍历棋盘上的所有子节点，查找player_game_pfb
        var children = this.boardNode.children;
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            if (child.name === "player_game_pfb") {
                // 检查位置是否匹配（允许一定的误差）
                var expectedPos = this.getHexWorldPosition(q, r, true);
                var actualPos = child.getPosition();
                var distance = expectedPos.sub(actualPos).mag();
                if (distance < 10) { // 10像素误差范围内
                    return child;
                }
            }
        }
        return null;
    };
    /**
     * 在六边形节点上显示分数动画
     * @param playerController 玩家控制器
     * @param score 分数
     * @param onComplete 完成回调
     */
    HexChessBoardController.prototype.showScoreAnimationOnHexNode = function (playerController, score, onComplete) {
        // 调用PlayerGameController的showAddScore方法
        if (playerController && typeof playerController.showAddScore === 'function') {
            playerController.showAddScore(score);
        }
        if (onComplete) {
            this.scheduleOnce(onComplete, 1.0);
        }
    };
    /**
     * 显示玩家游戏加减分效果（完全复制四边形棋盘控制器的逻辑）
     * @param userId 用户ID
     * @param score 分数变化（正数为加分，负数为减分）
     */
    HexChessBoardController.prototype.showHexPlayerGameScore = function (userId, score) {
        var currentUserId = this.getCurrentHexUserId();
        var foundPlayer = false;
        // 1. 如果是当前用户，查找自己的玩家节点（存储在hexGridData中）
        if (userId === currentUserId) {
            foundPlayer = this.showScoreForCurrentHexUser(score);
        }
        else {
            // 2. 如果是其他用户，查找对应的玩家头像节点
            foundPlayer = this.showScoreForOtherHexUser(userId, score);
        }
        if (!foundPlayer) {
            console.warn("\u672A\u627E\u5230\u7528\u6237 " + userId + " \u7684\u516D\u8FB9\u5F62\u5934\u50CF\u8282\u70B9\u6765\u663E\u793A\u5206\u6570\u6548\u679C");
        }
    };
    /**
     * 获取当前用户ID（复制四边形棋盘控制器的方法）
     */
    HexChessBoardController.prototype.getCurrentHexUserId = function () {
        var _a, _b;
        return ((_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId) || "";
    };
    /**
     * 为当前用户显示分数效果（复制四边形棋盘控制器的逻辑）
     */
    HexChessBoardController.prototype.showScoreForCurrentHexUser = function (score) {
        var _this = this;
        var foundPlayer = false;
        this.hexGridData.forEach(function (gridData) {
            // 如果已经找到了，就不再继续查找
            if (foundPlayer) {
                return;
            }
            if (gridData.hasPlayer && gridData.playerNode) {
                var playerController = gridData.playerNode.getComponent("PlayerGameController") ||
                    gridData.playerNode.getComponent("PlayerGameController ");
                if (playerController) {
                    _this.showScoreOnHexPlayerController(playerController, score);
                    foundPlayer = true;
                }
            }
        });
        if (!foundPlayer) {
            console.warn("❌ 未找到当前用户的六边形头像节点");
        }
        return foundPlayer;
    };
    /**
     * 为其他用户显示分数效果（复制四边形棋盘控制器的逻辑）
     */
    HexChessBoardController.prototype.showScoreForOtherHexUser = function (userId, score) {
        if (!this.boardNode) {
            return false;
        }
        // 遍历棋盘上的所有玩家头像节点
        return this.findHexPlayerNodeByUserId(userId, score);
    };
    /**
     * 根据userId查找对应的玩家节点（复制四边形棋盘控制器的逻辑）
     */
    HexChessBoardController.prototype.findHexPlayerNodeByUserId = function (userId, score) {
        if (!this.boardNode) {
            console.warn("\u68CB\u76D8\u8282\u70B9\u4E0D\u5B58\u5728\uFF0C\u65E0\u6CD5\u67E5\u627E\u7528\u6237 " + userId + " \u7684\u5934\u50CF");
            return false;
        }
        // 遍历棋盘上的所有玩家头像节点，根据存储的userId精确匹配
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            // 尝试多种方式获取PlayerGameController组件
            var playerController = child.getComponent("PlayerGameController");
            if (!playerController) {
                playerController = child.getComponent("PlayerGameController "); // 注意末尾有空格
            }
            if (!playerController) {
                // 尝试通过类名获取
                var components = child.getComponents(cc.Component);
                playerController = components.find(function (comp) {
                    return comp.constructor.name === 'PlayerGameController' ||
                        comp.constructor.name === 'PlayerGameController ';
                });
            }
            var storedUserId = child['userId'];
            if (storedUserId === userId) {
                if (playerController) {
                    // 找到匹配的用户ID和组件，显示分数效果
                    this.showScoreOnHexPlayerController(playerController, score);
                    return true;
                }
                else {
                    // 找到匹配的用户ID但没有组件
                    console.warn("\u26A0\uFE0F \u627E\u5230\u7528\u6237 " + userId + " \u7684\u8282\u70B9\u4F46\u6CA1\u6709PlayerGameController\u7EC4\u4EF6");
                    return false; // 找到节点但没有组件，返回false
                }
            }
        }
        console.warn("\u274C \u672A\u627E\u5230\u7528\u6237 " + userId + " \u7684\u516D\u8FB9\u5F62\u5934\u50CF\u8282\u70B9");
        return false;
    };
    /**
     * 在PlayerController上显示分数效果（复制四边形棋盘控制器的逻辑）
     */
    HexChessBoardController.prototype.showScoreOnHexPlayerController = function (playerController, score) {
        // 临时提升节点层级，避免被其他头像遮挡
        var playerNode = playerController.node;
        var originalSiblingIndex = playerNode.getSiblingIndex();
        // 将节点移到最上层
        playerNode.setSiblingIndex(-1);
        // 同时确保加分/减分节点的层级更高
        this.ensureHexScoreNodeTopLevel(playerController);
        if (score > 0) {
            playerController.showAddScore(score);
        }
        else if (score < 0) {
            playerController.showSubScore(Math.abs(score));
        }
        // 延迟恢复原始层级（等分数动画播放完成）
        this.scheduleOnce(function () {
            if (playerNode && playerNode.isValid) {
                playerNode.setSiblingIndex(originalSiblingIndex);
            }
        }, 2.5); // 增加到2.5秒，确保动画完全结束
    };
    /**
     * 确保六边形加分/减分节点在最高层级
     */
    HexChessBoardController.prototype.ensureHexScoreNodeTopLevel = function (playerController) {
        // 设置加分节点的最高层级
        if (playerController.addScoreNode) {
            playerController.addScoreNode.zIndex = cc.macro.MAX_ZINDEX - 1;
        }
        // 设置减分节点的最高层级
        if (playerController.subScoreNode) {
            playerController.subScoreNode.zIndex = cc.macro.MAX_ZINDEX - 1;
        }
    };
    /**
     * 查找指定用户ID的所有六边形头像节点
     * @param userId 用户ID
     * @returns 头像节点数组
     */
    HexChessBoardController.prototype.findAllHexPlayerNodesByUserId = function (userId) {
        var playerNodes = [];
        if (!this.boardNode) {
            return playerNodes;
        }
        // 遍历棋盘上的所有子节点
        var children = this.boardNode.children;
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            // 检查是否是玩家预制体（通过组件判断）
            var playerController = child.getComponent(PlayerGameController_1.default);
            if (playerController) {
                // 检查是否是指定用户的头像（使用存储在节点上的userId）
                var storedUserId = child['userId'];
                if (storedUserId === userId) {
                    playerNodes.push(child);
                }
            }
        }
        // 也检查存储在hexGridData中的玩家节点
        this.hexGridData.forEach(function (gridData) {
            if (gridData.hasPlayer && gridData.playerNode) {
                var playerController = gridData.playerNode.getComponent(PlayerGameController_1.default);
                var storedUserId = gridData.playerNode['userId'];
                if (playerController && storedUserId === userId) {
                    // 避免重复添加
                    if (!playerNodes.includes(gridData.playerNode)) {
                        playerNodes.push(gridData.playerNode);
                    }
                }
            }
        });
        return playerNodes;
    };
    // ==================== 其他玩家头像生成 ====================
    // 以下方法与第一张地图的逻辑完全一样，用于生成其他玩家的头像
    /**
     * 在指定六边形位置显示其他玩家的操作（完全复制四边形棋盘控制器的逻辑）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param actions 该位置的其他玩家操作列表
     */
    HexChessBoardController.prototype.displayOtherPlayersAtHexPosition = function (q, r, actions) {
        if (!this.isValidHexCoordinate(q, r) || !actions || actions.length === 0) {
            console.warn("\u65E0\u6548\u53C2\u6570: (" + q + ", " + r + "), actions: " + ((actions === null || actions === void 0 ? void 0 : actions.length) || 0));
            return;
        }
        // 检查该位置是否已经有自己的头像
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (gridData && gridData.hasPlayer) {
            // 只有当真的有其他玩家时，才调整自己的头像位置
            if (actions.length > 0) {
                // 如果已有自己的头像且有其他玩家，需要使用多人布局策略
                this.addOtherPlayersToExistingHexGrid(q, r, actions);
            }
        }
        else {
            // 如果没有自己的头像，直接添加其他玩家头像
            this.addOtherPlayersToEmptyHexGrid(q, r, actions);
        }
    };
    /**
     * 在已有自己头像的六边形格子上添加其他玩家头像，并调整自己的头像位置和缩放
     * （完全复制四边形棋盘控制器的逻辑）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param actions 其他玩家操作列表
     */
    HexChessBoardController.prototype.addOtherPlayersToExistingHexGrid = function (q, r, actions) {
        // 总玩家数 = 自己(1) + 其他玩家数量
        var totalPlayers = 1 + actions.length;
        var positions = this.getHexPlayerPositions(totalPlayers);
        // 第一步：调整自己的头像位置和缩放
        var myPosition = positions[0]; // 第一个位置是自己的
        this.adjustMyHexAvatarPosition(q, r, myPosition, actions);
        // 第二步：从第二个位置开始放置其他玩家
        for (var i = 0; i < actions.length; i++) {
            var action = actions[i];
            var position = positions[i + 1]; // 跳过第一个位置（自己的位置）
            // 使用六边形坐标系创建其他玩家头像
            this.createOtherPlayerAtHexPosition(q, r, action, position, totalPlayers);
        }
    };
    /**
     * 在空六边形格子上添加其他玩家头像
     * （完全复制四边形棋盘控制器的逻辑）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param actions 其他玩家操作列表
     */
    HexChessBoardController.prototype.addOtherPlayersToEmptyHexGrid = function (q, r, actions) {
        var totalPlayers = actions.length; // 空格子上只有其他玩家
        var positions = this.getHexPlayerPositions(totalPlayers);
        for (var i = 0; i < actions.length; i++) {
            var action = actions[i];
            var position = positions[i];
            // 使用六边形坐标系创建其他玩家头像
            this.createOtherPlayerAtHexPosition(q, r, action, position, totalPlayers);
        }
    };
    /**
     * 根据玩家数量获取六边形布局位置（完全复制四边形棋盘控制器的逻辑）
     * @param playerCount 玩家数量
     * @returns 位置数组 {x: number, y: number, scale: number}[]
     */
    HexChessBoardController.prototype.getHexPlayerPositions = function (playerCount) {
        switch (playerCount) {
            case 1:
                // 单个玩家，居中显示，正常大小
                return [{ x: 0, y: 0, scale: 1.0 }];
            case 2:
                // 两个玩家，左右分布，缩放0.5
                return [
                    { x: -22, y: -8, scale: 0.5 },
                    { x: 22, y: -8, scale: 0.5 } // 右
                ];
            case 3:
                // 三个玩家，上中下分布，缩放0.5
                return [
                    { x: 0, y: 12, scale: 0.5 },
                    { x: -23, y: -27, scale: 0.5 },
                    { x: 23, y: -27, scale: 0.5 } // 右下
                ];
            case 4:
                // 四个玩家，四角分布，缩放0.5
                return [
                    { x: -22, y: 12, scale: 0.5 },
                    { x: 22, y: 12, scale: 0.5 },
                    { x: -22, y: -30, scale: 0.5 },
                    { x: 22, y: -30, scale: 0.5 } // 右下
                ];
            default:
                // 超过4个玩家，只显示前4个
                console.warn("\u73A9\u5BB6\u6570\u91CF\u8FC7\u591A: " + playerCount + "\uFF0C\u53EA\u663E\u793A\u524D4\u4E2A");
                return this.getHexPlayerPositions(4);
        }
    };
    /**
     * 调整自己的六边形头像位置和缩放（当多人在同一格子时）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param position 新的位置和缩放信息
     * @param actions 其他玩家操作列表
     */
    HexChessBoardController.prototype.adjustMyHexAvatarPosition = function (q, r, position, actions) {
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        // 查找自己的头像节点
        if (!gridData || !gridData.hasPlayer || !gridData.playerNode) {
            console.warn("\u5728\u516D\u8FB9\u5F62\u4F4D\u7F6E(" + q + ", " + r + ")\u627E\u4E0D\u5230\u81EA\u5DF1\u7684\u5934\u50CF\u8282\u70B9");
            return;
        }
        var myPlayerNode = gridData.playerNode;
        // 计算该格子的总人数（自己 + 其他玩家）
        var totalPlayers = 1 + (actions ? actions.length : 0);
        // 计算基础位置（根据总人数决定是否偏移）
        var basePosition = this.calculateHexBasePositionByPlayerCount(q, r, totalPlayers);
        // 计算新的最终位置
        var newPosition = cc.v2(basePosition.x + position.x, basePosition.y + position.y);
        // 播放平滑移动和缩放动画
        this.playHexAvatarAdjustAnimation(myPlayerNode, newPosition, position.scale);
    };
    /**
     * 根据六边形格子总人数计算基础位置（完全复制四边形棋盘控制器的逻辑）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param totalPlayers 该格子的总人数
     * @returns 基础位置
     */
    HexChessBoardController.prototype.calculateHexBasePositionByPlayerCount = function (q, r, totalPlayers) {
        if (totalPlayers === 1) {
            // 一个格子里只有一个人：使用正常的偏移（单人头像预制体，y轴+20）
            return this.getHexWorldPosition(q, r, true);
        }
        else {
            // 一个格子里有两个及以上：不偏移（多人头像预制体，不偏移）
            return this.getHexWorldPosition(q, r, false);
        }
    };
    /**
     * 播放六边形头像调整动画（完全复制四边形棋盘控制器的逻辑）
     * @param playerNode 玩家节点
     * @param newPosition 新位置
     * @param newScale 新缩放
     */
    HexChessBoardController.prototype.playHexAvatarAdjustAnimation = function (playerNode, newPosition, newScale) {
        if (!playerNode || !playerNode.isValid) {
            return;
        }
        // 停止之前的动画
        playerNode.stopAllActions();
        // 使用cc.tween播放位置和缩放动画
        cc.tween(playerNode)
            .to(0.3, {
            x: newPosition.x,
            y: newPosition.y,
            scaleX: newScale,
            scaleY: newScale
        }, { easing: 'sineOut' })
            .start();
    };
    /**
     * 创建其他玩家在六边形位置的头像
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param action 玩家操作数据
     * @param position 位置和缩放信息
     * @param totalPlayers 总玩家数
     */
    HexChessBoardController.prototype.createOtherPlayerAtHexPosition = function (q, r, action, position, totalPlayers) {
        var _this = this;
        if (!this.playerGamePrefab || !this.boardNode) {
            console.error("❌ 预制体或棋盘节点未设置！");
            return;
        }
        // 实例化玩家预制体
        var playerNode = cc.instantiate(this.playerGamePrefab);
        // 计算基础位置（根据总人数决定是否偏移）
        var basePosition = this.calculateHexBasePositionByPlayerCount(q, r, totalPlayers);
        // 计算最终位置
        var finalPosition = cc.v2(basePosition.x + position.x, basePosition.y + position.y);
        playerNode.setPosition(finalPosition);
        // 根据总人数设置缩放：单人0.8，多人使用position.scale
        if (totalPlayers === 1) {
            playerNode.setScale(0.8);
        }
        else {
            playerNode.setScale(position.scale);
        }
        // 先隐藏节点，等头像加载完成后再显示
        playerNode.active = false;
        // 添加到棋盘
        this.addPlayerNodeSafely(playerNode);
        // 设置其他玩家的头像和数据
        this.setupOtherPlayerHexAvatar(playerNode, action, function () {
            // 头像加载完成的回调，播放生成动画
            _this.playAvatarSpawnAnimation(playerNode);
        });
    };
    /**
     * 设置其他玩家的六边形头像和数据
     * @param playerNode 玩家节点
     * @param action 玩家操作数据
     * @param onComplete 完成回调
     */
    HexChessBoardController.prototype.setupOtherPlayerHexAvatar = function (playerNode, action, onComplete) {
        // 查找PlayerGameController组件
        var playerController = playerNode.getComponent(PlayerGameController_1.default);
        if (playerController) {
            // 在节点上存储userId，用于后续查找
            playerNode['userId'] = action.userId;
            // 设置旗子节点的显示状态
            var withFlag_1 = (action.action === 2); // action=2表示标记操作，显示旗子
            if (playerController.flagNode) {
                playerController.flagNode.active = withFlag_1;
            }
            // 获取真实的用户数据（和第一张地图逻辑一样）
            var realUserData = this.getRealUserData(action.userId);
            if (!realUserData) {
                console.warn("\u627E\u4E0D\u5230\u7528\u6237 " + action.userId + " \u7684\u771F\u5B9E\u6570\u636E\uFF0C\u4F7F\u7528\u9ED8\u8BA4\u6570\u636E");
                // 使用默认数据作为备选
                realUserData = {
                    userId: action.userId,
                    nickName: "\u73A9\u5BB6" + action.userId,
                    avatar: this.getDefaultAvatarUrl(),
                    score: 0,
                    pos: 0,
                    coin: 0,
                    status: 0,
                    rank: 0
                };
            }
            // 使用PlayerGameController的setData方法来设置头像
            try {
                playerController.setData(realUserData);
                // 延迟设置旗子状态，确保在PlayerGameController初始化之后
                this.scheduleOnce(function () {
                    if (playerController.flagNode) {
                        playerController.flagNode.active = withFlag_1;
                    }
                    onComplete();
                }, 0.1);
            }
            catch (error) {
                console.error("设置其他玩家头像数据失败:", error);
                onComplete();
            }
        }
        else {
            console.warn("⚠️ 找不到PlayerGameController组件");
            onComplete();
        }
    };
    /**
     * 获取其他玩家的头像URL
     * @param userId 用户ID
     * @returns 头像URL
     */
    HexChessBoardController.prototype.getOtherPlayerAvatarUrl = function (userId) {
        // 这里可以根据userId获取真实的头像URL
        // 暂时使用默认头像
        return this.getDefaultAvatarUrl();
    };
    /**
     * 从GlobalBean中获取真实的用户数据（和第一张地图逻辑完全一样）
     * @param userId 用户ID
     * @returns RoomUser 或 null
     */
    HexChessBoardController.prototype.getRealUserData = function (userId) {
        try {
            if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
                console.warn("没有游戏数据，无法获取用户信息");
                return null;
            }
            var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
            var user = users.find(function (u) { return u.userId === userId; });
            if (user) {
                return user;
            }
            else {
                console.warn("\u672A\u627E\u5230\u7528\u6237 " + userId + " \u7684\u6570\u636E");
                return null;
            }
        }
        catch (error) {
            console.error("\u83B7\u53D6\u7528\u6237\u6570\u636E\u65F6\u51FA\u9519: " + error);
            return null;
        }
    };
    /**
     * 恢复联机模式地图状态（断线重连时使用）
     * @param mapData 地图数据
     */
    HexChessBoardController.prototype.restoreOnlineMapState = function (mapData) {
        var _this = this;
        console.log("HexChessBoardController: 恢复联机模式地图状态", mapData);
        // 恢复已挖掘的方块
        if (mapData.revealedBlocks && Array.isArray(mapData.revealedBlocks)) {
            console.log("恢复已挖掘的方块数量:", mapData.revealedBlocks.length);
            mapData.revealedBlocks.forEach(function (block) {
                // 六边形地图使用q和r坐标，如果没有则使用x和y
                var q = block.q !== undefined ? block.q : block.x;
                var r = block.r !== undefined ? block.r : block.y;
                var neighborMines = block.neighborMines;
                if (_this.isValidHexCoordinate(q, r)) {
                    console.log("\u6062\u590D\u5DF2\u6316\u6398\u516D\u8FB9\u5F62\u65B9\u5757: (" + q + ", " + r + "), \u5468\u56F4\u5730\u96F7\u6570: " + neighborMines);
                    // 立即隐藏格子（不播放动画）
                    _this.hideHexGridAt(q, r, true);
                    // 显示挖掘结果
                    if (neighborMines > 0) {
                        // 延迟创建数字预制体，确保格子先隐藏
                        _this.scheduleOnce(function () {
                            _this.createHexNumberPrefab(q, r, neighborMines);
                        }, 0.1);
                    }
                    // 标记格子已被处理
                    var key = _this.getHexKey(q, r);
                    var gridData = _this.hexGridData.get(key);
                    if (gridData) {
                        gridData.hasPlayer = true;
                    }
                }
            });
        }
        // 恢复已标记的方块
        if (mapData.markedBlocks && Array.isArray(mapData.markedBlocks)) {
            console.log("恢复已标记的方块数量:", mapData.markedBlocks.length);
            mapData.markedBlocks.forEach(function (block) {
                // 六边形地图使用q和r坐标，如果没有则使用x和y
                var q = block.q !== undefined ? block.q : block.x;
                var r = block.r !== undefined ? block.r : block.y;
                if (_this.isValidHexCoordinate(q, r)) {
                    console.log("\u6062\u590D\u5DF2\u6807\u8BB0\u516D\u8FB9\u5F62\u65B9\u5757: (" + q + ", " + r + ")");
                    // 创建标记预制体
                    _this.createHexBiaojiPrefab(q, r);
                }
            });
        }
    };
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "playerGamePrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boomPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "biaojiPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom1Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom2Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom3Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom4Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom5Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom6Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom7Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom8Prefab", void 0);
    __decorate([
        property(cc.Node)
    ], HexChessBoardController.prototype, "boardNode", void 0);
    HexChessBoardController = __decorate([
        ccclass
    ], HexChessBoardController);
    return HexChessBoardController;
}(cc.Component));
exports.default = HexChessBoardController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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