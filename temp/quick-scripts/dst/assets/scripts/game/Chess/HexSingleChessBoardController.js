
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/game/Chess/HexSingleChessBoardController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '147d16sE21EH4WnxrTp2v7g', 'HexSingleChessBoardController');
// scripts/game/Chess/HexSingleChessBoardController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var WebSocketManager_1 = require("../../net/WebSocketManager");
var MessageId_1 = require("../../net/MessageId");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var HexSingleChessBoardController = /** @class */ (function (_super) {
    __extends(HexSingleChessBoardController, _super);
    function HexSingleChessBoardController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boomPrefab = null; // boom预制体
        _this.biaojiPrefab = null; // biaoji预制体
        _this.boom1Prefab = null; // 数字1预制体
        _this.boom2Prefab = null; // 数字2预制体
        _this.boom3Prefab = null; // 数字3预制体
        _this.boom4Prefab = null; // 数字4预制体
        _this.boom5Prefab = null; // 数字5预制体
        _this.boom6Prefab = null; // 数字6预制体
        _this.boom7Prefab = null; // 数字7预制体
        _this.boom8Prefab = null; // 数字8预制体
        // 六个六边形棋盘节点
        _this.hexBoard1Node = null; // 六边形棋盘1节点
        _this.hexBoard2Node = null; // 六边形棋盘2节点
        _this.hexBoard3Node = null; // 六边形棋盘3节点
        _this.hexBoard4Node = null; // 六边形棋盘4节点
        _this.hexBoard5Node = null; // 六边形棋盘5节点
        _this.hexBoard6Node = null; // 六边形棋盘6节点
        // 当前使用的棋盘节点
        _this.currentBoardNode = null;
        _this.currentBoardType = "hexBoard1"; // 默认使用第一个六边形棋盘
        // 六边形棋盘配置
        _this.HEX_SIZE = 44; // 六边形半径
        _this.HEX_WIDTH = _this.HEX_SIZE * 2; // 六边形宽度
        _this.HEX_HEIGHT = _this.HEX_SIZE * Math.sqrt(3); // 六边形高度
        // 格子数据存储 - 使用Map存储六边形坐标
        _this.hexGridData = new Map(); // 存储六边形格子数据
        _this.hexGridNodes = new Map(); // 存储六边形格子节点
        _this.validHexCoords = []; // 有效的六边形坐标列表
        // 炸弹爆炸标记
        _this.hasBombExploded = false;
        // 防重复发送消息
        _this.lastClickTime = 0;
        _this.lastClickPosition = "";
        _this.CLICK_COOLDOWN = 200; // 200毫秒冷却时间
        return _this;
    }
    HexSingleChessBoardController.prototype.onLoad = function () {
        // 不进行默认初始化，等待外部调用initBoard
    };
    HexSingleChessBoardController.prototype.start = function () {
        // start方法不再自动启用触摸事件，避免与initBoard重复
        // 触摸事件的启用由initBoard方法负责
    };
    /**
     * 根据棋盘类型获取对应的棋盘节点
     * @param boardType 棋盘类型
     */
    HexSingleChessBoardController.prototype.getBoardNodeByType = function (boardType) {
        switch (boardType) {
            case "hexBoard1":
                return this.hexBoard1Node;
            case "hexBoard2":
                return this.hexBoard2Node;
            case "hexBoard3":
                return this.hexBoard3Node;
            case "hexBoard4":
                return this.hexBoard4Node;
            case "hexBoard5":
                return this.hexBoard5Node;
            case "hexBoard6":
                return this.hexBoard6Node;
            default:
                return null;
        }
    };
    /**
     * 初始化指定类型的六边形棋盘
     * @param boardType 棋盘类型 ("hexBoard1", "hexBoard2", "hexBoard3", "hexBoard4", "hexBoard5", "hexBoard6")
     */
    HexSingleChessBoardController.prototype.initBoard = function (boardType) {
        var _this = this;
        // 根据棋盘类型获取对应的节点
        this.currentBoardNode = this.getBoardNodeByType(boardType);
        if (!this.currentBoardNode) {
            console.error("\u516D\u8FB9\u5F62\u68CB\u76D8\u8282\u70B9\u672A\u8BBE\u7F6E\uFF01\u68CB\u76D8\u7C7B\u578B: " + boardType);
            return;
        }
        this.currentBoardType = boardType;
        // 清空现有数据
        this.hexGridData.clear();
        this.hexGridNodes.clear();
        this.validHexCoords = [];
        // 重置炸弹爆炸标记
        this.hasBombExploded = false;
        // 延迟一帧后再次尝试启用触摸事件，确保所有节点都已创建完成
        this.scheduleOnce(function () {
            _this.setValidHexCoords([]); // 传入空数组，但会被忽略
            // 测试预制体位置计算
            _this.testHexPositionCalculation();
            _this.enableTouchForExistingGrids();
        }, 0.1);
    };
    /**
     * 设置有效的六边形坐标列表（忽略服务器数据，使用前端节点坐标）
     * @param _coords 服务器发送的坐标列表（将被忽略）
     */
    HexSingleChessBoardController.prototype.setValidHexCoords = function (_coords) {
        // 忽略服务器传入的坐标，始终从节点名称自动生成
        this.generateCoordsFromNodeNames();
        this.initHexBoard();
    };
    /**
     * 从节点名称自动生成有效坐标列表
     */
    HexSingleChessBoardController.prototype.generateCoordsFromNodeNames = function () {
        if (!this.currentBoardNode) {
            console.error("❌ 棋盘节点不存在，无法生成坐标列表");
            return;
        }
        var foundCoords = [];
        var children = this.currentBoardNode.children;
        var _loop_1 = function (i) {
            var child = children[i];
            var nodeName = child.name;
            // 跳过游戏元素节点，只处理六边形格子节点
            if (this_1.isGameElement(child, nodeName)) {
                return "continue";
            }
            var coords = this_1.parseHexCoordinateFromName(nodeName);
            if (coords) {
                // 检查是否已经存在相同的坐标
                var exists = foundCoords.some(function (c) { return c.q === coords.q && c.r === coords.r; });
                if (!exists) {
                    foundCoords.push({ q: coords.q, r: coords.r });
                }
            }
        };
        var this_1 = this;
        for (var i = 0; i < children.length; i++) {
            _loop_1(i);
        }
        this.validHexCoords = foundCoords;
    };
    // 初始化六边形棋盘
    HexSingleChessBoardController.prototype.initHexBoard = function () {
        // 清空现有数据
        this.hexGridData.clear();
        this.hexGridNodes.clear();
        // 初始化有效坐标的数据
        for (var _i = 0, _a = this.validHexCoords; _i < _a.length; _i++) {
            var coord = _a[_i];
            var key = this.getHexKey(coord.q, coord.r);
            this.hexGridData.set(key, {
                q: coord.q,
                r: coord.r,
                worldPos: this.getHexWorldPosition(coord.q, coord.r),
                hasPlayer: false
            });
        }
        this.createHexGridNodes();
    };
    // 生成六边形坐标的唯一键
    HexSingleChessBoardController.prototype.getHexKey = function (q, r) {
        return q + "," + r;
    };
    // 启用现有格子的触摸事件
    HexSingleChessBoardController.prototype.createHexGridNodes = function () {
        if (!this.currentBoardNode) {
            console.error("棋盘节点未设置！");
            return;
        }
        // 如果格子已经存在，直接启用触摸事件
        this.enableTouchForExistingGrids();
    };
    // 为现有格子启用触摸事件
    HexSingleChessBoardController.prototype.enableTouchForExistingGrids = function () {
        // 检查棋盘节点是否存在
        if (!this.currentBoardNode) {
            console.error("棋盘节点未设置，无法启用触摸事件！");
            return;
        }
        // 遍历棋盘节点的所有子节点
        var children = this.currentBoardNode.children;
        var validGridCount = 0;
        var skippedCount = 0;
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            var nodeName = child.name;
            // 跳过游戏元素节点，只处理六边形格子节点
            if (this.isGameElement(child, nodeName)) {
                skippedCount++;
                continue;
            }
            // 尝试从节点名称解析六边形坐标
            var coords = this.parseHexCoordinateFromName(nodeName);
            if (coords) {
                this.setupHexGridTouchEvents(child, coords.q, coords.r);
                var key = this.getHexKey(coords.q, coords.r);
                this.hexGridNodes.set(key, child);
                validGridCount++;
            }
            else {
                // 如果无法从名称解析，尝试从位置计算
                var pos = child.getPosition();
                var coords_1 = this.getHexCoordinateFromPosition(pos);
                if (coords_1) {
                    this.setupHexGridTouchEvents(child, coords_1.q, coords_1.r);
                    var key = this.getHexKey(coords_1.q, coords_1.r);
                    this.hexGridNodes.set(key, child);
                    validGridCount++;
                }
                else {
                }
            }
        }
        if (validGridCount === 0) {
            console.warn("\u26A0\uFE0F \u6CA1\u6709\u627E\u5230\u4EFB\u4F55\u6709\u6548\u7684\u516D\u8FB9\u5F62\u683C\u5B50\u8282\u70B9\uFF01");
            console.warn("   \u8BF7\u68C0\u67E5\u683C\u5B50\u8282\u70B9\u547D\u540D\u662F\u5426\u4E3A sixblock_q_r \u683C\u5F0F");
            console.warn("   \u4F8B\u5982: sixblock_0_0, sixblock_1_-1, sixblock_-1_2");
        }
    };
    // 从节点名称解析六边形坐标
    HexSingleChessBoardController.prototype.parseHexCoordinateFromName = function (nodeName) {
        var patterns = [
            /^sixblock_(-?\d+)_(-?\d+)$/,
        ];
        for (var _i = 0, patterns_1 = patterns; _i < patterns_1.length; _i++) {
            var pattern = patterns_1[_i];
            var match = nodeName.match(pattern);
            if (match) {
                var coords = { q: parseInt(match[1]), r: parseInt(match[2]) };
                return coords;
            }
        }
        console.warn("\u274C \u65E0\u6CD5\u89E3\u6790\u8282\u70B9\u540D\u79F0: " + nodeName);
        return null;
    };
    // 判断是否为游戏元素节点（需要跳过的节点）
    HexSingleChessBoardController.prototype.isGameElement = function (child, nodeName) {
        // 跳过玩家头像预制体
        if (nodeName === "player_game_pfb" || child.name.includes("Player")) {
            return true;
        }
        // 跳过boom相关预制体
        if (nodeName === "Boom" || nodeName.includes("Boom") || nodeName.includes("boom")) {
            return true;
        }
        // 跳过biaoji相关预制体
        if (nodeName === "Biaoji" || nodeName.includes("Biaoji") || nodeName.includes("biaoji")) {
            return true;
        }
        // 跳过数字预制体
        if (nodeName.match(/^\d+$/) || nodeName.includes("Number")) {
            return true;
        }
        return false;
    };
    // 测试六边形位置计算
    HexSingleChessBoardController.prototype.testHexPositionCalculation = function () {
        var _this = this;
        // 您提供的实际测量数据
        var testPoints = [
            { q: 0, r: 0, expected: cc.v2(-170, -165), description: "中心位置" },
            { q: 0, r: -1, expected: cc.v2(-220, -81), description: "上方" },
            { q: 1, r: -2, expected: cc.v2(-172, 2), description: "右上" },
            { q: 2, r: -3, expected: cc.v2(-122, 85), description: "右上远" },
            { q: 4, r: -4, expected: cc.v2(23, 171), description: "右上最远" }
        ];
        var correctCount = 0;
        testPoints.forEach(function (point) {
            var calculated = _this.getHexWorldPosition(point.q, point.r);
            var errorX = Math.abs(calculated.x - point.expected.x);
            var errorY = Math.abs(calculated.y - point.expected.y);
            var isCorrect = errorX < 1 && errorY < 1; // 允许1像素误差
            if (isCorrect) {
                correctCount++;
            }
            else {
            }
        });
        // 如果不是全部正确，显示详细的数据分析
        if (correctCount < testPoints.length) {
            // 分析相邻点的差值
            for (var i = 1; i < testPoints.length; i++) {
                var prev = testPoints[i - 1];
                var curr = testPoints[i];
                var deltaQ = curr.q - prev.q;
                var deltaR = curr.r - prev.r;
                var deltaX = curr.expected.x - prev.expected.x;
                var deltaY = curr.expected.y - prev.expected.y;
            }
        }
        // 测试一些关键的推算坐标
        var extraPoints = [
            { q: 1, r: 0, description: "右侧邻居" },
            { q: -1, r: 0, description: "左侧邻居" },
            { q: 0, r: 1, description: "下方邻居" },
            { q: 1, r: -1, description: "右上邻居" },
            { q: 2, r: -2, description: "应该在(1,-2)和(2,-3)之间" }
        ];
        extraPoints.forEach(function (point) {
            var calculated = _this.getHexWorldPosition(point.q, point.r);
        });
        // 验证左右间距（q方向）
        var pos1 = this.getHexWorldPosition(0, 0);
        var pos2 = this.getHexWorldPosition(1, 0);
        var actualSpacing = pos2.x - pos1.x; // 不用绝对值，看方向
    };
    // 从位置计算六边形坐标
    HexSingleChessBoardController.prototype.getHexCoordinateFromPosition = function (pos) {
        // 简化的六边形坐标转换，实际项目中可能需要更精确的算法
        var q = Math.round(pos.x / (this.HEX_SIZE * 1.5));
        var r = Math.round((pos.y - pos.x * Math.tan(Math.PI / 6)) / this.HEX_HEIGHT);
        return { q: q, r: r };
    };
    // 计算六边形世界坐标位置（参考联机版Level_S001的实现）
    HexSingleChessBoardController.prototype.getHexWorldPosition = function (q, r, isPlayerAvatar) {
        if (isPlayerAvatar === void 0) { isPlayerAvatar = false; }
        // 根据当前棋盘类型获取配置
        var config = this.getBoardConfig(this.currentBoardType);
        if (!config) {
            console.error("\u274C \u672A\u627E\u5230\u68CB\u76D8 " + this.currentBoardType + " \u7684\u914D\u7F6E");
            return cc.v2(0, 0);
        }
        // 首先检查是否有精确坐标
        var key = q + "," + r;
        if (config.exactCoords.has(key)) {
            var pos = config.exactCoords.get(key);
            // 如果是玩家头像预制体，y轴向上偏移
            if (isPlayerAvatar) {
                return cc.v2(pos.x, pos.y + 20);
            }
            return pos;
        }
        // 使用联机版的逻辑：每行有基准点，使用统一步长计算
        var x, y;
        // 如果有该行的数据，使用统一步长计算
        if (config.rowData.has(r)) {
            var data = config.rowData.get(r);
            x = data.baseX + (q - data.baseQ) * config.uniformStepX;
            y = data.y;
        }
        else {
            // 对于其他行，使用通用的六边形轴线坐标系公式
            console.warn("\u26A0\uFE0F \u6CA1\u6709r=" + r + "\u884C\u7684\u7CBE\u786E\u6570\u636E\uFF0C\u4F7F\u7528\u901A\u7528\u516C\u5F0F");
            var stepXR = -config.uniformStepX / 2;
            var stepYR = 74;
            x = config.baseX + q * config.uniformStepX + r * stepXR;
            y = config.baseY - r * stepYR;
        }
        // 如果是玩家头像预制体，y轴向上偏移
        if (isPlayerAvatar) {
            y += 20;
        }
        return cc.v2(x, y);
    };
    // 获取棋盘配置（参考联机版Level_S001的实现）
    HexSingleChessBoardController.prototype.getBoardConfig = function (boardType) {
        var configs = new Map();
        // Level_S001 (hexBoard1) - 第5关，您最开始给的数据
        configs.set("hexBoard1", {
            baseX: -170, baseY: -165,
            uniformStepX: 97,
            exactCoords: new Map([
                ["0,0", cc.v2(-170, -165)],
                ["0,-1", cc.v2(-220, -81)],
                ["1,-2", cc.v2(-172, 2)],
                ["2,-3", cc.v2(-122, 85)],
                ["4,-4", cc.v2(23, 171)]
            ]),
            rowData: new Map([
                [0, { baseQ: 0, baseX: -170, y: -165 }],
                [-1, { baseQ: 0, baseX: -220, y: -81 }],
                [-2, { baseQ: 1, baseX: -172, y: 2 }],
                [-3, { baseQ: 2, baseX: -122, y: 85 }],
                [-4, { baseQ: 4, baseX: 23, y: 171 }] // r=-4行：基准点(4,-4) → (23, 171)
            ])
        });
        // Level_S002 (hexBoard2) - 第10关
        configs.set("hexBoard2", {
            baseX: 0, baseY: -293,
            uniformStepX: 98,
            exactCoords: new Map([
                ["0,0", cc.v2(0, -293)],
                ["0,-1", cc.v2(-50, -209)],
                ["0,-2", cc.v2(-100, -125)],
                ["0,-3", cc.v2(-150, -42)],
                ["1,-4", cc.v2(-100, 44)],
                ["2,-5", cc.v2(-50, 127)],
                ["2,-6", cc.v2(-100, 210)],
                ["3,-7", cc.v2(-50, 293)]
            ]),
            rowData: new Map([
                [0, { baseQ: 0, baseX: 0, y: -293 }],
                [-1, { baseQ: 0, baseX: -50, y: -209 }],
                [-2, { baseQ: 0, baseX: -100, y: -125 }],
                [-3, { baseQ: 0, baseX: -150, y: -42 }],
                [-4, { baseQ: 1, baseX: -100, y: 44 }],
                [-5, { baseQ: 2, baseX: -50, y: 127 }],
                [-6, { baseQ: 2, baseX: -100, y: 210 }],
                [-7, { baseQ: 3, baseX: -50, y: 293 }] // r=-7行：基准点(3,-7) → (-50, 293)
            ])
        });
        // Level_S003 (hexBoard3) - 第15关
        configs.set("hexBoard3", {
            baseX: -146, baseY: -250,
            uniformStepX: 98,
            exactCoords: new Map([
                ["0,0", cc.v2(-146, -250)],
                ["1,1", cc.v2(0, -336)],
                ["0,-1", cc.v2(-196, -168)],
                ["1,-2", cc.v2(-146, -85)],
                ["2,-3", cc.v2(-99, -1)],
                ["1,-4", cc.v2(-246, 84)],
                ["1,-5", cc.v2(-293, 167)],
                ["2,-6", cc.v2(-246, 251)],
                ["3,-7", cc.v2(-196, 336)]
            ]),
            rowData: new Map([
                [1, { baseQ: 1, baseX: 0, y: -336 }],
                [0, { baseQ: 0, baseX: -146, y: -250 }],
                [-1, { baseQ: 0, baseX: -196, y: -168 }],
                [-2, { baseQ: 1, baseX: -146, y: -85 }],
                [-3, { baseQ: 2, baseX: -99, y: -1 }],
                [-4, { baseQ: 1, baseX: -246, y: 84 }],
                [-5, { baseQ: 1, baseX: -293, y: 167 }],
                [-6, { baseQ: 2, baseX: -246, y: 251 }],
                [-7, { baseQ: 3, baseX: -196, y: 336 }] // r=-7行：基准点(3,-7) → (-196, 336)
            ])
        });
        // Level_S004 (hexBoard4) - 第20关，同联机版
        configs.set("hexBoard4", {
            baseX: -300, baseY: -258,
            uniformStepX: 86,
            exactCoords: new Map([
                ["0,0", cc.v2(-300, -258)],
                ["1,-1", cc.v2(-258, -184)],
                ["1,-2", cc.v2(-300, -108)],
                ["2,-3", cc.v2(-258, -36)],
                ["2,-4", cc.v2(-300, 37)],
                ["3,-5", cc.v2(-258, 110)],
                ["3,-6", cc.v2(-300, 185)],
                ["4,-7", cc.v2(-258, 260)]
            ]),
            rowData: new Map([
                [0, { baseQ: 0, baseX: -300, y: -258 }],
                [-1, { baseQ: 1, baseX: -258, y: -184 }],
                [-2, { baseQ: 1, baseX: -300, y: -108 }],
                [-3, { baseQ: 2, baseX: -258, y: -36 }],
                [-4, { baseQ: 2, baseX: -300, y: 37 }],
                [-5, { baseQ: 3, baseX: -258, y: 110 }],
                [-6, { baseQ: 3, baseX: -300, y: 185 }],
                [-7, { baseQ: 4, baseX: -258, y: 260 }]
            ])
        });
        // Level_S005 (hexBoard5) - 第25关，预制体scale改为0.8
        configs.set("hexBoard5", {
            baseX: -257, baseY: -293,
            uniformStepX: 85.5,
            scale: 0.8,
            exactCoords: new Map([
                ["0,0", cc.v2(-257, -293)],
                ["0,-1", cc.v2(-300, -219)],
                ["1,-2", cc.v2(-257, -146)],
                ["1,-3", cc.v2(-300, -74)],
                ["2,-4", cc.v2(-257, 0)],
                ["2,-5", cc.v2(-300, 74)],
                ["3,-6", cc.v2(-257, 146)],
                ["3,-7", cc.v2(-300, 219)],
                ["4,-8", cc.v2(-257, 293)]
            ]),
            rowData: new Map([
                [0, { baseQ: 0, baseX: -257, y: -293 }],
                [-1, { baseQ: 0, baseX: -300, y: -219 }],
                [-2, { baseQ: 1, baseX: -257, y: -146 }],
                [-3, { baseQ: 1, baseX: -300, y: -74 }],
                [-4, { baseQ: 2, baseX: -257, y: 0 }],
                [-5, { baseQ: 2, baseX: -300, y: 74 }],
                [-6, { baseQ: 3, baseX: -257, y: 146 }],
                [-7, { baseQ: 3, baseX: -300, y: 219 }],
                [-8, { baseQ: 4, baseX: -257, y: 293 }]
            ])
        });
        // Level_S006 (hexBoard6) - 第30关，预制体scale改为0.8
        configs.set("hexBoard6", {
            baseX: -313, baseY: -298,
            uniformStepX: 78,
            scale: 0.8,
            exactCoords: new Map([
                ["0,0", cc.v2(-313, -298)],
                ["1,-1", cc.v2(-274, -233)],
                ["1,-2", cc.v2(-313, -165)],
                ["2,-3", cc.v2(-274, -99)],
                ["2,-4", cc.v2(-313, -34)],
                ["3,-5", cc.v2(-274, 34)],
                ["3,-6", cc.v2(-313, 96)],
                ["4,-7", cc.v2(-274, 165)],
                ["4,-8", cc.v2(-313, 226)],
                ["5,-9", cc.v2(-274, 300)]
            ]),
            rowData: new Map([
                [0, { baseQ: 0, baseX: -313, y: -298 }],
                [-1, { baseQ: 1, baseX: -274, y: -233 }],
                [-2, { baseQ: 1, baseX: -313, y: -165 }],
                [-3, { baseQ: 2, baseX: -274, y: -99 }],
                [-4, { baseQ: 2, baseX: -313, y: -34 }],
                [-5, { baseQ: 3, baseX: -274, y: 34 }],
                [-6, { baseQ: 3, baseX: -313, y: 96 }],
                [-7, { baseQ: 4, baseX: -274, y: 165 }],
                [-8, { baseQ: 4, baseX: -313, y: 226 }],
                [-9, { baseQ: 5, baseX: -274, y: 300 }]
            ])
        });
        return configs.get(boardType);
    };
    // 为六边形格子设置触摸事件
    HexSingleChessBoardController.prototype.setupHexGridTouchEvents = function (gridNode, q, r) {
        var _this = this;
        // 确保节点可以接收触摸事件
        if (!gridNode.getComponent(cc.Button)) {
            // 如果没有Button组件，添加一个
            var button = gridNode.addComponent(cc.Button);
            button.transition = cc.Button.Transition.NONE; // 不需要视觉反馈
        }
        // 移除现有的触摸事件监听器
        gridNode.off(cc.Node.EventType.TOUCH_END);
        gridNode.off(cc.Node.EventType.TOUCH_START);
        gridNode.off(cc.Node.EventType.TOUCH_CANCEL);
        // 添加点击事件监听器
        gridNode.on(cc.Node.EventType.TOUCH_END, function (event) {
            _this.onHexGridClick(q, r, event);
        }, this);
        // 添加长按事件监听器
        this.setupLongPressEvent(gridNode, q, r);
    };
    // 设置长按事件
    HexSingleChessBoardController.prototype.setupLongPressEvent = function (gridNode, q, r) {
        var _this = this;
        var touchStartTime = 0;
        var longPressTriggered = false;
        var LONG_PRESS_DURATION = 500; // 500毫秒长按
        gridNode.on(cc.Node.EventType.TOUCH_START, function () {
            touchStartTime = Date.now();
            longPressTriggered = false;
            // 设置长按定时器
            _this.scheduleOnce(function () {
                if (!longPressTriggered && (Date.now() - touchStartTime) >= LONG_PRESS_DURATION) {
                    longPressTriggered = true;
                    _this.onHexGridLongPress(q, r);
                }
            }, LONG_PRESS_DURATION / 1000);
        }, this);
        gridNode.on(cc.Node.EventType.TOUCH_END, function () {
            longPressTriggered = true; // 防止长按事件触发
        }, this);
        gridNode.on(cc.Node.EventType.TOUCH_CANCEL, function () {
            longPressTriggered = true; // 防止长按事件触发
        }, this);
    };
    // 六边形格子点击事件 - 发送挖掘操作
    HexSingleChessBoardController.prototype.onHexGridClick = function (q, r, _event) {
        // 检查坐标是否有效
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn("\u274C \u65E0\u6548\u7684\u516D\u8FB9\u5F62\u5750\u6807: (" + q + ", " + r + ")");
            return;
        }
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        // 检查该位置是否已经有预制体
        if (gridData && gridData.hasPlayer) {
            console.warn("\u26A0\uFE0F \u683C\u5B50(" + q + ", " + r + ")\u5DF2\u6709\u9884\u5236\u4F53");
            return;
        }
        // 防重复点击检查
        var currentTime = Date.now();
        var positionKey = q + "," + r;
        if (currentTime - this.lastClickTime < this.CLICK_COOLDOWN &&
            this.lastClickPosition === positionKey) {
            console.warn("点击过于频繁，忽略本次点击");
            return;
        }
        this.lastClickTime = currentTime;
        this.lastClickPosition = positionKey;
        // 发送LevelClickBlock消息 (action = 1 表示挖掘)
        this.sendLevelClickBlock(q, r, 1);
    };
    // 六边形格子长按事件 - 发送标记操作
    HexSingleChessBoardController.prototype.onHexGridLongPress = function (q, r) {
        // 检查坐标是否有效
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn("\u274C \u65E0\u6548\u7684\u516D\u8FB9\u5F62\u5750\u6807: (" + q + ", " + r + ")");
            return;
        }
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        // 检查该位置是否已经有biaoji预制体
        if (this.hasBiaojiAt(q, r)) {
            // 如果已经有biaoji，则删除它（本地立即处理）
            this.removeBiaojiAt(q, r);
            // 发送取消标记消息
            this.sendLevelClickBlock(q, r, 2);
        }
        else if (!gridData || !gridData.hasPlayer) {
            // 如果没有任何预制体，则生成biaoji（本地立即处理）
            this.createBiaojiPrefab(q, r);
            // 发送标记消息
            this.sendLevelClickBlock(q, r, 2);
        }
        else {
            // 如果有其他类型的预制体（如数字、boom），则不处理
            console.warn("\u683C\u5B50(" + q + ", " + r + ")\u5DF2\u6709\u5176\u4ED6\u9884\u5236\u4F53\uFF0C\u65E0\u6CD5\u6807\u8BB0");
        }
    };
    // 检查六边形坐标是否有效
    HexSingleChessBoardController.prototype.isValidHexCoordinate = function (q, r) {
        var key = this.getHexKey(q, r);
        return this.hexGridData.has(key);
    };
    // 发送LevelClickBlock消息（参考四边形单机控制器）
    HexSingleChessBoardController.prototype.sendLevelClickBlock = function (q, r, action) {
        var message = {
            q: q,
            r: r,
            action: action // 1 = 挖掘, 2 = 标记/取消标记
        };
        // 发送WebSocket消息
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeLevelClickBlock, message);
    };
    // 检查指定位置是否有biaoji预制体
    HexSingleChessBoardController.prototype.hasBiaojiAt = function (q, r) {
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (!gridData || !gridData.hasPlayer || !gridData.playerNode) {
            return false;
        }
        // 检查节点名称是否为Biaoji
        return gridData.playerNode.name === "HexBiaoji";
    };
    // 移除指定位置的biaoji预制体
    HexSingleChessBoardController.prototype.removeBiaojiAt = function (q, r) {
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (gridData && gridData.hasPlayer && gridData.playerNode &&
            gridData.playerNode.name === "HexBiaoji") {
            // 播放消失动画
            var biaojiNode_1 = gridData.playerNode;
            cc.tween(biaojiNode_1)
                .to(0.2, { scaleX: 0, scaleY: 0, opacity: 0 })
                .call(function () {
                biaojiNode_1.removeFromParent();
            })
                .start();
            // 更新格子数据
            gridData.hasPlayer = false;
            gridData.playerNode = null;
        }
    };
    // 创建biaoji预制体
    HexSingleChessBoardController.prototype.createBiaojiPrefab = function (q, r) {
        if (!this.biaojiPrefab) {
            console.error("biaojiPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }
        // 实例化biaoji预制体
        var biaojiNode = cc.instantiate(this.biaojiPrefab);
        biaojiNode.name = "HexBiaoji";
        // 设置位置
        var position = this.getHexWorldPosition(q, r, false);
        biaojiNode.setPosition(position);
        // 添加到棋盘
        this.currentBoardNode.addChild(biaojiNode);
        // 获取当前棋盘的缩放配置
        var config = this.getBoardConfig(this.currentBoardType);
        var targetScale = (config === null || config === void 0 ? void 0 : config.scale) || 1.0;
        // 播放出现动画
        biaojiNode.setScale(0);
        cc.tween(biaojiNode)
            .to(0.2, { scaleX: targetScale, scaleY: targetScale }, { easing: 'backOut' })
            .start();
        // 更新格子数据
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (gridData) {
            gridData.hasPlayer = true;
            gridData.playerNode = biaojiNode;
        }
    };
    // 创建boom预制体
    HexSingleChessBoardController.prototype.createBoomPrefab = function (q, r, isCurrentUser) {
        if (isCurrentUser === void 0) { isCurrentUser = true; }
        if (!this.boomPrefab) {
            console.error("boomPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }
        // 实例化boom预制体
        var boomNode = cc.instantiate(this.boomPrefab);
        boomNode.name = "HexBoom";
        // 设置位置
        var position = this.getHexWorldPosition(q, r, false);
        boomNode.setPosition(position);
        // 添加到棋盘
        this.currentBoardNode.addChild(boomNode);
        // 获取当前棋盘的缩放配置
        var config = this.getBoardConfig(this.currentBoardType);
        var targetScale = (config === null || config === void 0 ? void 0 : config.scale) || 1.0;
        // 播放出现动画
        var bounceScale = targetScale * 1.2; // 弹跳效果，基于目标缩放
        boomNode.setScale(0);
        cc.tween(boomNode)
            .to(0.3, { scaleX: bounceScale, scaleY: bounceScale }, { easing: 'backOut' })
            .to(0.1, { scaleX: targetScale, scaleY: targetScale })
            .start();
        // 只有当前用户点到雷时才播放棋盘震动效果
        if (isCurrentUser) {
            this.playBoardShakeAnimation();
        }
        // 更新格子数据
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (gridData) {
            gridData.hasPlayer = true;
            gridData.playerNode = boomNode;
        }
        // 设置标记，表示点到了炸弹
        this.hasBombExploded = true;
    };
    // 创建数字预制体
    HexSingleChessBoardController.prototype.createNumberPrefab = function (q, r, number) {
        // 根据数字选择对应的预制体
        var prefab = null;
        switch (number) {
            case 1:
                prefab = this.boom1Prefab;
                break;
            case 2:
                prefab = this.boom2Prefab;
                break;
            case 3:
                prefab = this.boom3Prefab;
                break;
            case 4:
                prefab = this.boom4Prefab;
                break;
            case 5:
                prefab = this.boom5Prefab;
                break;
            case 6:
                prefab = this.boom6Prefab;
                break;
            case 7:
                prefab = this.boom7Prefab;
                break;
            case 8:
                prefab = this.boom8Prefab;
                break;
            default:
                console.error("\u4E0D\u652F\u6301\u7684\u6570\u5B57: " + number);
                return;
        }
        if (!prefab) {
            console.error("boom" + number + "Prefab \u9884\u5236\u4F53\u672A\u8BBE\u7F6E\uFF0C\u8BF7\u5728\u7F16\u8F91\u5668\u4E2D\u6302\u8F7D");
            return;
        }
        // 实例化数字预制体
        var numberNode = cc.instantiate(prefab);
        numberNode.name = "HexBoom" + number;
        // 设置位置
        var position = this.getHexWorldPosition(q, r, false);
        numberNode.setPosition(position);
        // 添加到棋盘
        this.currentBoardNode.addChild(numberNode);
        // 获取当前棋盘的缩放配置
        var config = this.getBoardConfig(this.currentBoardType);
        var targetScale = (config === null || config === void 0 ? void 0 : config.scale) || 1.0;
        // 播放出现动画
        numberNode.setScale(0);
        cc.tween(numberNode)
            .to(0.2, { scaleX: targetScale, scaleY: targetScale }, { easing: 'backOut' })
            .start();
        // 更新格子数据
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (gridData) {
            gridData.hasPlayer = true;
            gridData.playerNode = numberNode;
        }
    };
    // 播放棋盘震动动画
    HexSingleChessBoardController.prototype.playBoardShakeAnimation = function () {
        if (!this.currentBoardNode) {
            return;
        }
        var originalPosition = this.currentBoardNode.getPosition();
        var shakeIntensity = 10;
        cc.tween(this.currentBoardNode)
            .to(0.05, { x: originalPosition.x + shakeIntensity, y: originalPosition.y })
            .to(0.05, { x: originalPosition.x - shakeIntensity, y: originalPosition.y })
            .to(0.05, { x: originalPosition.x, y: originalPosition.y + shakeIntensity })
            .to(0.05, { x: originalPosition.x, y: originalPosition.y - shakeIntensity })
            .to(0.05, { x: originalPosition.x, y: originalPosition.y })
            .start();
    };
    // 隐藏指定位置的六边形小格子（点击时调用）
    HexSingleChessBoardController.prototype.hideHexGridAt = function (q, r, immediate) {
        if (immediate === void 0) { immediate = false; }
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn("\u274C \u9690\u85CF\u683C\u5B50\u5931\u8D25\uFF1A\u5750\u6807(" + q + ", " + r + ")\u65E0\u6548");
            console.warn("   \u5750\u6807\u7C7B\u578B: q=" + typeof q + ", r=" + typeof r);
            console.warn("   \u6709\u6548\u5750\u6807\u5217\u8868: " + this.validHexCoords.map(function (c) { return "(" + c.q + "," + c.r + ")"; }).join(', '));
            return;
        }
        // 获取格子节点
        var key = this.getHexKey(q, r);
        var gridNode = this.hexGridNodes.get(key);
        if (gridNode) {
            if (immediate) {
                // 立即隐藏，不播放动画
                gridNode.active = false;
            }
            else {
                // 播放六边形格子消失动画
                this.playHexGridFallAnimation(gridNode);
            }
        }
    };
    // 播放六边形格子掉落动画
    HexSingleChessBoardController.prototype.playHexGridFallAnimation = function (gridNode) {
        // 保存原始位置
        var originalPos = gridNode.getPosition();
        gridNode['originalPosition'] = originalPos;
        // 播放掉落动画
        cc.tween(gridNode)
            .parallel(cc.tween().to(0.5, { y: originalPos.y - 200 }, { easing: 'sineIn' }), cc.tween().to(0.3, { opacity: 0 }), cc.tween().to(0.5, { angle: 180 }))
            .call(function () {
            gridNode.active = false;
        })
            .start();
    };
    // 显示所有隐藏的格子（游戏结束时调用）
    HexSingleChessBoardController.prototype.showAllHiddenGrids = function () {
        if (!this.currentBoardNode) {
            console.warn("⚠️ 棋盘节点不存在，无法显示隐藏格子");
            return;
        }
        var totalGrids = 0;
        var restoredGrids = 0;
        var alreadyVisibleGrids = 0;
        // 遍历棋盘的所有子节点
        var children = this.currentBoardNode.children;
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            // 如果是六边形小格子节点（sixblock_q_r 格式）
            if (child.name.startsWith("sixblock_") || child.name === "hexblock") {
                totalGrids++;
                // 记录恢复前的状态
                var wasHidden = !child.active || child.opacity < 255 || child.scaleX < 1 || child.scaleY < 1;
                if (wasHidden) {
                    restoredGrids++;
                }
                else {
                    alreadyVisibleGrids++;
                }
                // 停止所有可能正在进行的动画
                child.stopAllActions();
                // 恢复显示状态
                child.active = true;
                child.opacity = 255;
                child.scaleX = 1;
                child.scaleY = 1;
                child.angle = 0; // 重置旋转角度
                // 恢复原始位置（如果有保存的话）
                if (child['originalPosition']) {
                    child.setPosition(child['originalPosition']);
                }
                // 确保格子可以交互
                var button = child.getComponent(cc.Button);
                if (button) {
                    button.enabled = true;
                }
            }
        }
        if (totalGrids === 0) {
            console.warn("⚠️ 没有找到任何六边形格子节点！请检查节点命名是否正确");
        }
    };
    // 清除所有预制体（游戏结束时调用）
    HexSingleChessBoardController.prototype.clearAllPrefabs = function () {
        if (!this.currentBoardNode) {
            console.warn("⚠️ 棋盘节点不存在，无法清除预制体");
            return;
        }
        var clearedCount = 0;
        var children = this.currentBoardNode.children.slice(); // 创建副本避免遍历时修改数组
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            var nodeName = child.name;
            // 检查是否是需要清除的游戏预制体
            if (this.isGamePrefab(nodeName)) {
                child.removeFromParent();
                clearedCount++;
            }
        }
        // 重置格子数据中的预制体状态
        this.hexGridData.forEach(function (gridData) {
            if (gridData.hasPlayer) {
                gridData.hasPlayer = false;
                gridData.playerNode = null;
            }
        });
        // 重置炸弹爆炸标记
        this.hasBombExploded = false;
    };
    // 判断是否为游戏预制体（需要清除的预制体）
    HexSingleChessBoardController.prototype.isGamePrefab = function (nodeName) {
        // 跳过六边形格子节点
        if (nodeName.startsWith("sixblock_") || nodeName === "hexblock") {
            return false;
        }
        // 炸弹预制体
        if (nodeName === "HexBoom") {
            return true;
        }
        // 数字预制体（HexBoom1, HexBoom2, HexBoom3 等）
        if (nodeName.match(/^HexBoom\d+$/)) {
            return true;
        }
        // 标记预制体
        if (nodeName === "HexBiaoji") {
            return true;
        }
        return false;
    };
    /**
     * 处理点击响应，根据服务器返回的结果更新棋盘
     * @param q 格子q坐标
     * @param r 格子r坐标
     * @param result 点击结果 ("boom" | "safe" | number)
     */
    HexSingleChessBoardController.prototype.handleClickResponse = function (q, r, result) {
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn("\u5904\u7406\u70B9\u51FB\u54CD\u5E94\u5931\u8D25\uFF1A\u5750\u6807(" + q + ", " + r + ")\u65E0\u6548");
            return;
        }
        // 如果格子上有biaoji预制体，先移除它
        if (this.hasBiaojiAt(q, r)) {
            // 直接移除，不播放动画
            var gridData_1 = this.hexGridData.get(this.getHexKey(q, r));
            if (gridData_1 && gridData_1.playerNode) {
                gridData_1.playerNode.removeFromParent();
                gridData_1.playerNode = null;
            }
        }
        // 标记格子已被处理，防止重复点击
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (gridData) {
            gridData.hasPlayer = true;
        }
        // 使用连锁动画的方式处理单个格子，保持一致性
        this.playGridDisappearAnimation(q, r, result);
    };
    /**
     * 批量处理连锁反应的格子
     * @param revealedGrids 被揭开的格子列表，支持 {q,r} 或 {x,y} 格式
     */
    HexSingleChessBoardController.prototype.handleChainReaction = function (revealedGrids) {
        var _this = this;
        if (!revealedGrids || revealedGrids.length === 0) {
            return;
        }
        // 同时播放所有连锁格子的消失动画，不使用延迟
        revealedGrids.forEach(function (block, index) {
            // 处理坐标映射：服务器可能返回 x,y 格式
            var coordQ, coordR, neighborMines;
            if (block.q !== undefined && block.r !== undefined) {
                // 标准六边形坐标格式
                coordQ = block.q;
                coordR = block.r;
                neighborMines = block.neighborMines !== undefined ? block.neighborMines : block.result;
            }
            else if (block.x !== undefined && block.y !== undefined) {
                // 服务器返回x,y格式，映射为六边形坐标
                coordQ = block.x; // x 就是 q
                coordR = block.y; // y 就是 r
                neighborMines = block.neighborMines !== undefined ? block.neighborMines : block.result;
            }
            else {
                console.error("   \u274C \u683C\u5B50" + (index + 1) + ": \u65E0\u6548\u7684\u5750\u6807\u6570\u636E:", block);
                console.error("      \u53EF\u7528\u5B57\u6BB5: " + Object.keys(block).join(', '));
                return;
            }
            // 验证坐标是否为有效数字
            if (typeof coordQ !== 'number' || typeof coordR !== 'number' ||
                isNaN(coordQ) || isNaN(coordR)) {
                console.error("   \u274C \u683C\u5B50" + (index + 1) + ": \u5750\u6807\u4E0D\u662F\u6709\u6548\u6570\u5B57: q=" + coordQ + ", r=" + coordR);
                return;
            }
            // 立即播放动画，不延迟
            _this.playGridDisappearAnimation(coordQ, coordR, neighborMines);
        });
    };
    // 播放格子消失动画并更新显示
    HexSingleChessBoardController.prototype.playGridDisappearAnimation = function (q, r, result) {
        var _this = this;
        // 先隐藏格子
        this.hideHexGridAt(q, r, false);
        // 延迟显示结果，让格子消失动画先播放
        this.scheduleOnce(function () {
            _this.updateNeighborMinesDisplay(q, r, result);
        }, 0.3);
    };
    /**
     * 更新指定位置的neighborMines显示（使用boom数字预制体）
     * @param q 格子q坐标
     * @param r 格子r坐标
     * @param neighborMines 周围地雷数量或结果类型
     */
    HexSingleChessBoardController.prototype.updateNeighborMinesDisplay = function (q, r, neighborMines) {
        if (neighborMines === "boom" || neighborMines === "mine") {
            // 踩到地雷，生成boom预制体并触发震动
            this.createBoomPrefab(q, r, true); // true表示是当前用户踩到的雷，需要震动
            // 设置标记，表示点到了炸弹
            this.hasBombExploded = true;
        }
        else if (typeof neighborMines === "number" && neighborMines > 0) {
            // 显示数字
            this.createNumberPrefab(q, r, neighborMines);
        }
        else {
            // 如果是0、"safe"或其他，则不显示任何预制体
        }
    };
    /**
     * 处理ExtendLevelInfo消息（游戏开始时调用）
     */
    HexSingleChessBoardController.prototype.onExtendLevelInfo = function () {
        this.showAllHiddenGrids();
        this.clearAllPrefabs();
    };
    /**
     * 处理ExtendLevelInfo断线重连（恢复游戏状态）
     * @param levelInfo 关卡信息响应数据
     */
    HexSingleChessBoardController.prototype.onExtendLevelInfoReconnect = function (levelInfo) {
        console.log("HexSingleChessBoardController: 处理断线重连，恢复游戏状态");
        // 清理当前状态
        this.clearAllPrefabs();
        this.showAllHiddenGrids();
        // 如果有地图状态信息，恢复棋盘状态
        if (levelInfo.mineMap) {
            this.restoreBoardState(levelInfo.mineMap);
        }
    };
    /**
     * 恢复棋盘状态（断线重连时使用）
     * @param mineMap 地图状态信息
     */
    HexSingleChessBoardController.prototype.restoreBoardState = function (mineMap) {
        var _this = this;
        var _a;
        console.log("HexSingleChessBoardController: 恢复棋盘状态", mineMap);
        var restoredCount = 0;
        var skippedCount = 0;
        // 恢复已挖掘的方块
        if (mineMap.revealedBlocks && Array.isArray(mineMap.revealedBlocks)) {
            console.log("恢复已挖掘的方块数量:", mineMap.revealedBlocks.length);
            mineMap.revealedBlocks.forEach(function (block) {
                // 六边形地图使用q和r坐标，如果没有则使用x和y
                var q = block.q !== undefined ? block.q : block.x;
                var r = block.r !== undefined ? block.r : block.y;
                var neighborMines = block.neighborMines;
                if (!_this.isValidHexCoordinate(q, r)) {
                    return;
                }
                // 获取六边形格子节点，确保它存在
                var key = _this.getHexKey(q, r);
                var gridNode = _this.hexGridNodes.get(key);
                if (!gridNode) {
                    console.warn("\u516D\u8FB9\u5F62\u683C\u5B50\u8282\u70B9\u4E0D\u5B58\u5728: (" + q + ", " + r + ")");
                    return;
                }
                // 检查格子是否已经被处理过（已经隐藏）
                if (!gridNode.active || gridNode.opacity === 0) {
                    console.log("\u8DF3\u8FC7\u5DF2\u5904\u7406\u7684\u516D\u8FB9\u5F62\u683C\u5B50: (" + q + ", " + r + "), active=" + gridNode.active + ", opacity=" + gridNode.opacity);
                    skippedCount++;
                    return;
                }
                console.log("\u6062\u590D\u5DF2\u6316\u6398\u516D\u8FB9\u5F62\u65B9\u5757: (" + q + ", " + r + "), \u5468\u56F4\u5730\u96F7\u6570: " + neighborMines);
                console.log("\u516D\u8FB9\u5F62\u683C\u5B50 (" + q + ", " + r + ") \u5F53\u524D\u72B6\u6001: active=" + gridNode.active + ", opacity=" + gridNode.opacity);
                restoredCount++;
                // 立即隐藏格子（不播放动画）
                gridNode.active = false;
                gridNode.opacity = 0;
                gridNode.scaleX = 0;
                gridNode.scaleY = 0;
                console.log("\u516D\u8FB9\u5F62\u683C\u5B50 (" + q + ", " + r + ") \u9690\u85CF\u540E\u72B6\u6001: active=" + gridNode.active + ", opacity=" + gridNode.opacity);
                // 显示挖掘结果
                if (neighborMines > 0) {
                    console.log("\u521B\u5EFA\u516D\u8FB9\u5F62\u6570\u5B57\u9884\u5236\u4F53 (" + q + ", " + r + "), \u6570\u5B57:", neighborMines);
                    _this.scheduleOnce(function () {
                        _this.createNumberPrefab(q, r, neighborMines);
                    }, 0.1);
                }
                else {
                    console.log("\u516D\u8FB9\u5F62\u683C\u5B50 (" + q + ", " + r + ") \u5468\u56F4\u65E0\u5730\u96F7\uFF0C\u4E0D\u521B\u5EFA\u6570\u5B57\u9884\u5236\u4F53");
                }
                // 标记格子已被处理
                var gridData = _this.hexGridData.get(key);
                if (gridData) {
                    gridData.hasPlayer = true;
                }
            });
        }
        // 恢复已标记的方块
        if (mineMap.markedBlocks && Array.isArray(mineMap.markedBlocks)) {
            console.log("恢复已标记的方块数量:", mineMap.markedBlocks.length);
            mineMap.markedBlocks.forEach(function (block) {
                // 六边形地图使用q和r坐标，如果没有则使用x和y
                var q = block.q !== undefined ? block.q : block.x;
                var r = block.r !== undefined ? block.r : block.y;
                if (_this.isValidHexCoordinate(q, r)) {
                    console.log("\u6062\u590D\u5DF2\u6807\u8BB0\u516D\u8FB9\u5F62\u65B9\u5757: (" + q + ", " + r + ")");
                    // 创建标记预制体
                    _this.createBiaojiPrefab(q, r);
                }
            });
        }
        console.log("\u516D\u8FB9\u5F62\u65AD\u7EBF\u91CD\u8FDE\u6062\u590D\u7EDF\u8BA1: \u65B0\u6062\u590D=" + restoredCount + "\u4E2A, \u8DF3\u8FC7\u5DF2\u5904\u7406=" + skippedCount + "\u4E2A, \u603B\u6570\u636E=" + (((_a = mineMap.revealedBlocks) === null || _a === void 0 ? void 0 : _a.length) || 0) + "\u4E2A");
    };
    /**
     * 处理LevelGameEnd消息（游戏结束时调用）
     * 注意：不清理任何数据，保持玩家的游玩痕迹
     */
    HexSingleChessBoardController.prototype.onLevelGameEnd = function () {
        // 不显示隐藏的格子，保持当前状态
        // 不清理预制体，不重置格子状态，保持游戏结果显示
        // 让玩家可以看到自己的标记（biaoji）、挖掘结果（数字、boom）等
    };
    /**
     * 重新初始化棋盘数据（仅在开始新游戏时调用）
     */
    HexSingleChessBoardController.prototype.reinitializeBoardData = function () {
        // 重置hexGridData中的预制体状态
        this.hexGridData.forEach(function (gridData) {
            gridData.hasPlayer = false;
            gridData.playerNode = null;
        });
        // 重置炸弹爆炸标记
        this.hasBombExploded = false;
    };
    /**
     * 禁用所有格子的触摸事件（游戏结束时调用）
     */
    HexSingleChessBoardController.prototype.disableAllGridTouch = function () {
        this.hexGridNodes.forEach(function (gridNode, key) {
            if (gridNode && cc.isValid(gridNode)) {
                // 移除所有触摸事件监听器
                gridNode.off(cc.Node.EventType.TOUCH_END);
                gridNode.off(cc.Node.EventType.TOUCH_START);
                gridNode.off(cc.Node.EventType.TOUCH_CANCEL);
                // 禁用Button组件（如果有的话）
                var button = gridNode.getComponent(cc.Button);
                if (button) {
                    button.enabled = false;
                }
            }
        });
    };
    /**
     * 检查是否点到了炸弹
     * @returns 是否点到了炸弹
     */
    HexSingleChessBoardController.prototype.hasBombExplodedInThisGame = function () {
        return this.hasBombExploded;
    };
    /**
     * 重置棋盘状态（清理所有预制体和格子状态）
     */
    HexSingleChessBoardController.prototype.resetBoard = function () {
        // 显示所有隐藏的格子
        this.showAllHiddenGrids();
        // 清除所有预制体
        this.clearAllPrefabs();
        // 重新初始化棋盘数据
        this.reinitializeBoardData();
    };
    /**
     * 启用所有格子的触摸事件
     */
    HexSingleChessBoardController.prototype.enableAllGridTouch = function () {
        this.enableTouchForExistingGrids();
    };
    /**
     * 创建自定义预制体（用于调试等特殊用途）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param prefab 预制体
     * @param nodeName 节点名称
     */
    HexSingleChessBoardController.prototype.createCustomPrefab = function (q, r, prefab, nodeName) {
        if (!prefab) {
            console.error("\u81EA\u5B9A\u4E49\u9884\u5236\u4F53\u672A\u8BBE\u7F6E: " + nodeName);
            return null;
        }
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn("\u65E0\u6548\u7684\u516D\u8FB9\u5F62\u5750\u6807: (" + q + ", " + r + ")");
            return null;
        }
        // 实例化预制体
        var customNode = cc.instantiate(prefab);
        customNode.name = nodeName;
        // 设置位置
        var position = this.getHexWorldPosition(q, r, false);
        customNode.setPosition(position);
        // 添加到棋盘
        this.currentBoardNode.addChild(customNode);
        return customNode;
    };
    /**
     * 重置炸弹爆炸状态（开始新游戏时调用）
     */
    HexSingleChessBoardController.prototype.resetBombExplodedStatus = function () {
        this.hasBombExploded = false;
    };
    /**
     * 获取当前棋盘类型
     */
    HexSingleChessBoardController.prototype.getCurrentBoardType = function () {
        return this.currentBoardType;
    };
    /**
     * 获取当前棋盘配置（六边形版本返回简化信息）
     */
    HexSingleChessBoardController.prototype.getCurrentBoardConfig = function () {
        return {
            boardType: this.currentBoardType,
            gridCount: this.getHexGridCount(),
            hasBombExploded: this.hasBombExploded
        };
    };
    /**
     * 获取棋盘状态信息（调试用）
     */
    HexSingleChessBoardController.prototype.getHexBoardInfo = function () {
        var info = {
            validHexCoordsCount: this.validHexCoords.length,
            boardNodeChildren: this.currentBoardNode ? this.currentBoardNode.children.length : 0,
            hasPlayerGamePrefab: false,
            hasBoardNode: !!this.currentBoardNode,
            currentBoardType: this.currentBoardType,
            hexGridDataSize: this.hexGridData.size,
            hexGridNodesSize: this.hexGridNodes.size,
            hasBombExploded: this.hasBombExploded
        };
        return info;
    };
    /**
     * 获取前端节点的总数量（用于计算炸弹数量）
     */
    HexSingleChessBoardController.prototype.getHexGridCount = function () {
        return this.validHexCoords.length;
    };
    /**
     * 开始新游戏时的重置方法
     */
    HexSingleChessBoardController.prototype.resetForNewGame = function () {
        // 显示所有隐藏的格子
        this.showAllHiddenGrids();
        // 清除所有预制体
        this.clearAllPrefabs();
        // 重新初始化棋盘数据
        this.reinitializeBoardData();
    };
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boomPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "biaojiPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boom1Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boom2Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boom3Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boom4Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boom5Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boom6Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boom7Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boom8Prefab", void 0);
    __decorate([
        property(cc.Node)
    ], HexSingleChessBoardController.prototype, "hexBoard1Node", void 0);
    __decorate([
        property(cc.Node)
    ], HexSingleChessBoardController.prototype, "hexBoard2Node", void 0);
    __decorate([
        property(cc.Node)
    ], HexSingleChessBoardController.prototype, "hexBoard3Node", void 0);
    __decorate([
        property(cc.Node)
    ], HexSingleChessBoardController.prototype, "hexBoard4Node", void 0);
    __decorate([
        property(cc.Node)
    ], HexSingleChessBoardController.prototype, "hexBoard5Node", void 0);
    __decorate([
        property(cc.Node)
    ], HexSingleChessBoardController.prototype, "hexBoard6Node", void 0);
    HexSingleChessBoardController = __decorate([
        ccclass
    ], HexSingleChessBoardController);
    return HexSingleChessBoardController;
}(cc.Component));
exports.default = HexSingleChessBoardController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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