
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/game/Chess/HexSingleChessBoardController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '147d16sE21EH4WnxrTp2v7g', 'HexSingleChessBoardController');
// scripts/game/Chess/HexSingleChessBoardController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var WebSocketManager_1 = require("../../net/WebSocketManager");
var MessageId_1 = require("../../net/MessageId");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var HexSingleChessBoardController = /** @class */ (function (_super) {
    __extends(HexSingleChessBoardController, _super);
    function HexSingleChessBoardController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boomPrefab = null; // boom预制体
        _this.biaojiPrefab = null; // biaoji预制体
        _this.boom1Prefab = null; // 数字1预制体
        _this.boom2Prefab = null; // 数字2预制体
        _this.boom3Prefab = null; // 数字3预制体
        _this.boom4Prefab = null; // 数字4预制体
        _this.boom5Prefab = null; // 数字5预制体
        _this.boom6Prefab = null; // 数字6预制体
        _this.boom7Prefab = null; // 数字7预制体
        _this.boom8Prefab = null; // 数字8预制体
        // 六个六边形棋盘节点
        _this.hexBoard1Node = null; // 六边形棋盘1节点
        _this.hexBoard2Node = null; // 六边形棋盘2节点
        _this.hexBoard3Node = null; // 六边形棋盘3节点
        _this.hexBoard4Node = null; // 六边形棋盘4节点
        _this.hexBoard5Node = null; // 六边形棋盘5节点
        _this.hexBoard6Node = null; // 六边形棋盘6节点
        // 当前使用的棋盘节点
        _this.currentBoardNode = null;
        _this.currentBoardType = "hexBoard1"; // 默认使用第一个六边形棋盘
        // 六边形棋盘配置
        _this.HEX_SIZE = 44; // 六边形半径
        _this.HEX_WIDTH = _this.HEX_SIZE * 2; // 六边形宽度
        _this.HEX_HEIGHT = _this.HEX_SIZE * Math.sqrt(3); // 六边形高度
        // 格子数据存储 - 使用Map存储六边形坐标
        _this.hexGridData = new Map(); // 存储六边形格子数据
        _this.hexGridNodes = new Map(); // 存储六边形格子节点
        _this.validHexCoords = []; // 有效的六边形坐标列表
        // 炸弹爆炸标记
        _this.hasBombExploded = false;
        // 防重复发送消息
        _this.lastClickTime = 0;
        _this.lastClickPosition = "";
        _this.CLICK_COOLDOWN = 200; // 200毫秒冷却时间
        return _this;
    }
    HexSingleChessBoardController.prototype.onLoad = function () {
        // 不进行默认初始化，等待外部调用initBoard
    };
    HexSingleChessBoardController.prototype.start = function () {
        // start方法不再自动启用触摸事件，避免与initBoard重复
        // 触摸事件的启用由initBoard方法负责
    };
    /**
     * 根据棋盘类型获取对应的棋盘节点
     * @param boardType 棋盘类型
     */
    HexSingleChessBoardController.prototype.getBoardNodeByType = function (boardType) {
        switch (boardType) {
            case "hexBoard1":
                return this.hexBoard1Node;
            case "hexBoard2":
                return this.hexBoard2Node;
            case "hexBoard3":
                return this.hexBoard3Node;
            case "hexBoard4":
                return this.hexBoard4Node;
            case "hexBoard5":
                return this.hexBoard5Node;
            case "hexBoard6":
                return this.hexBoard6Node;
            default:
                return null;
        }
    };
    /**
     * 初始化指定类型的六边形棋盘
     * @param boardType 棋盘类型 ("hexBoard1", "hexBoard2", "hexBoard3", "hexBoard4", "hexBoard5", "hexBoard6")
     */
    HexSingleChessBoardController.prototype.initBoard = function (boardType) {
        var _this = this;
        // 根据棋盘类型获取对应的节点
        this.currentBoardNode = this.getBoardNodeByType(boardType);
        if (!this.currentBoardNode) {
            console.error("\u516D\u8FB9\u5F62\u68CB\u76D8\u8282\u70B9\u672A\u8BBE\u7F6E\uFF01\u68CB\u76D8\u7C7B\u578B: " + boardType);
            return;
        }
        this.currentBoardType = boardType;
        // 清空现有数据
        this.hexGridData.clear();
        this.hexGridNodes.clear();
        this.validHexCoords = [];
        // 重置炸弹爆炸标记
        this.hasBombExploded = false;
        // 延迟一帧后再次尝试启用触摸事件，确保所有节点都已创建完成
        this.scheduleOnce(function () {
            _this.setValidHexCoords([]); // 传入空数组，但会被忽略
            // 测试预制体位置计算
            _this.testHexPositionCalculation();
            _this.enableTouchForExistingGrids();
        }, 0.1);
        console.log("\u516D\u8FB9\u5F62\u68CB\u76D8\u521D\u59CB\u5316\u5B8C\u6210: " + boardType);
    };
    /**
     * 设置有效的六边形坐标列表（忽略服务器数据，使用前端节点坐标）
     * @param _coords 服务器发送的坐标列表（将被忽略）
     */
    HexSingleChessBoardController.prototype.setValidHexCoords = function (_coords) {
        // 忽略服务器传入的坐标，始终从节点名称自动生成
        this.generateCoordsFromNodeNames();
        this.initHexBoard();
    };
    /**
     * 从节点名称自动生成有效坐标列表
     */
    HexSingleChessBoardController.prototype.generateCoordsFromNodeNames = function () {
        if (!this.currentBoardNode) {
            console.error("❌ 棋盘节点不存在，无法生成坐标列表");
            return;
        }
        var foundCoords = [];
        var children = this.currentBoardNode.children;
        var _loop_1 = function (i) {
            var child = children[i];
            var nodeName = child.name;
            // 跳过游戏元素节点，只处理六边形格子节点
            if (this_1.isGameElement(child, nodeName)) {
                return "continue";
            }
            var coords = this_1.parseHexCoordinateFromName(nodeName);
            if (coords) {
                // 检查是否已经存在相同的坐标
                var exists = foundCoords.some(function (c) { return c.q === coords.q && c.r === coords.r; });
                if (!exists) {
                    foundCoords.push({ q: coords.q, r: coords.r });
                }
            }
        };
        var this_1 = this;
        for (var i = 0; i < children.length; i++) {
            _loop_1(i);
        }
        this.validHexCoords = foundCoords;
    };
    // 初始化六边形棋盘
    HexSingleChessBoardController.prototype.initHexBoard = function () {
        // 清空现有数据
        this.hexGridData.clear();
        this.hexGridNodes.clear();
        // 初始化有效坐标的数据
        for (var _i = 0, _a = this.validHexCoords; _i < _a.length; _i++) {
            var coord = _a[_i];
            var key = this.getHexKey(coord.q, coord.r);
            this.hexGridData.set(key, {
                q: coord.q,
                r: coord.r,
                worldPos: this.getHexWorldPosition(coord.q, coord.r),
                hasPlayer: false
            });
        }
        this.createHexGridNodes();
    };
    // 生成六边形坐标的唯一键
    HexSingleChessBoardController.prototype.getHexKey = function (q, r) {
        return q + "," + r;
    };
    // 启用现有格子的触摸事件
    HexSingleChessBoardController.prototype.createHexGridNodes = function () {
        if (!this.currentBoardNode) {
            console.error("棋盘节点未设置！");
            return;
        }
        // 如果格子已经存在，直接启用触摸事件
        this.enableTouchForExistingGrids();
    };
    // 为现有格子启用触摸事件
    HexSingleChessBoardController.prototype.enableTouchForExistingGrids = function () {
        // 检查棋盘节点是否存在
        if (!this.currentBoardNode) {
            console.error("棋盘节点未设置，无法启用触摸事件！");
            return;
        }
        console.log("\uD83D\uDD0D \u5F00\u59CB\u4E3A\u68CB\u76D8 " + this.currentBoardType + " \u542F\u7528\u89E6\u6478\u4E8B\u4EF6");
        console.log("\uD83D\uDCCB \u68CB\u76D8\u8282\u70B9\u5B50\u8282\u70B9\u6570\u91CF: " + this.currentBoardNode.children.length);
        // 遍历棋盘节点的所有子节点
        var children = this.currentBoardNode.children;
        var validGridCount = 0;
        var skippedCount = 0;
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            var nodeName = child.name;
            console.log("\uD83D\uDD0D \u68C0\u67E5\u5B50\u8282\u70B9 [" + i + "]: " + nodeName);
            // 跳过游戏元素节点，只处理六边形格子节点
            if (this.isGameElement(child, nodeName)) {
                console.log("\u23ED\uFE0F \u8DF3\u8FC7\u6E38\u620F\u5143\u7D20\u8282\u70B9: " + nodeName);
                skippedCount++;
                continue;
            }
            // 尝试从节点名称解析六边形坐标
            var coords = this.parseHexCoordinateFromName(nodeName);
            if (coords) {
                console.log("\u2705 \u6210\u529F\u89E3\u6790\u5750\u6807 " + nodeName + " -> (" + coords.q + ", " + coords.r + ")");
                this.setupHexGridTouchEvents(child, coords.q, coords.r);
                var key = this.getHexKey(coords.q, coords.r);
                this.hexGridNodes.set(key, child);
                validGridCount++;
            }
            else {
                console.log("\u274C \u65E0\u6CD5\u4ECE\u540D\u79F0\u89E3\u6790\u5750\u6807: " + nodeName + "\uFF0C\u5C1D\u8BD5\u4ECE\u4F4D\u7F6E\u8BA1\u7B97");
                // 如果无法从名称解析，尝试从位置计算
                var pos = child.getPosition();
                var coords_1 = this.getHexCoordinateFromPosition(pos);
                if (coords_1) {
                    console.log("\u2705 \u4ECE\u4F4D\u7F6E\u8BA1\u7B97\u5F97\u5230\u5750\u6807 " + nodeName + " -> (" + coords_1.q + ", " + coords_1.r + ")");
                    this.setupHexGridTouchEvents(child, coords_1.q, coords_1.r);
                    var key = this.getHexKey(coords_1.q, coords_1.r);
                    this.hexGridNodes.set(key, child);
                    validGridCount++;
                }
                else {
                    console.log("\u274C \u65E0\u6CD5\u4ECE\u4F4D\u7F6E\u8BA1\u7B97\u5750\u6807: " + nodeName);
                }
            }
        }
        console.log("\uD83D\uDCCA \u89E6\u6478\u4E8B\u4EF6\u542F\u7528\u5B8C\u6210:");
        console.log("   - \u6709\u6548\u683C\u5B50\u6570\u91CF: " + validGridCount);
        console.log("   - \u8DF3\u8FC7\u8282\u70B9\u6570\u91CF: " + skippedCount);
        console.log("   - \u603B\u5B50\u8282\u70B9\u6570\u91CF: " + children.length);
        if (validGridCount === 0) {
            console.warn("\u26A0\uFE0F \u6CA1\u6709\u627E\u5230\u4EFB\u4F55\u6709\u6548\u7684\u516D\u8FB9\u5F62\u683C\u5B50\u8282\u70B9\uFF01");
            console.warn("   \u8BF7\u68C0\u67E5\u683C\u5B50\u8282\u70B9\u547D\u540D\u662F\u5426\u4E3A sixblock_q_r \u683C\u5F0F");
            console.warn("   \u4F8B\u5982: sixblock_0_0, sixblock_1_-1, sixblock_-1_2");
        }
    };
    // 从节点名称解析六边形坐标
    HexSingleChessBoardController.prototype.parseHexCoordinateFromName = function (nodeName) {
        var patterns = [
            /^sixblock_(-?\d+)_(-?\d+)$/,
        ];
        for (var _i = 0, patterns_1 = patterns; _i < patterns_1.length; _i++) {
            var pattern = patterns_1[_i];
            var match = nodeName.match(pattern);
            if (match) {
                var coords = { q: parseInt(match[1]), r: parseInt(match[2]) };
                return coords;
            }
        }
        console.warn("\u274C \u65E0\u6CD5\u89E3\u6790\u8282\u70B9\u540D\u79F0: " + nodeName);
        return null;
    };
    // 判断是否为游戏元素节点（需要跳过的节点）
    HexSingleChessBoardController.prototype.isGameElement = function (child, nodeName) {
        // 跳过玩家头像预制体
        if (nodeName === "player_game_pfb" || child.name.includes("Player")) {
            return true;
        }
        // 跳过boom相关预制体
        if (nodeName === "Boom" || nodeName.includes("Boom") || nodeName.includes("boom")) {
            return true;
        }
        // 跳过biaoji相关预制体
        if (nodeName === "Biaoji" || nodeName.includes("Biaoji") || nodeName.includes("biaoji")) {
            return true;
        }
        // 跳过数字预制体
        if (nodeName.match(/^\d+$/) || nodeName.includes("Number")) {
            return true;
        }
        return false;
    };
    // 测试六边形位置计算
    HexSingleChessBoardController.prototype.testHexPositionCalculation = function () {
        var _this = this;
        console.log("🧪 重新测试六边形位置计算算法（基于您的精确数据）...");
        // 您提供的实际测量数据
        var testPoints = [
            { q: 0, r: 0, expected: cc.v2(-170, -165), description: "中心位置" },
            { q: 0, r: -1, expected: cc.v2(-220, -81), description: "上方" },
            { q: 1, r: -2, expected: cc.v2(-172, 2), description: "右上" },
            { q: 2, r: -3, expected: cc.v2(-122, 85), description: "右上远" },
            { q: 4, r: -4, expected: cc.v2(23, 171), description: "右上最远" }
        ];
        console.log("📍 验证您提供的精确坐标:");
        var correctCount = 0;
        testPoints.forEach(function (point) {
            var calculated = _this.getHexWorldPosition(point.q, point.r);
            var errorX = Math.abs(calculated.x - point.expected.x);
            var errorY = Math.abs(calculated.y - point.expected.y);
            var isCorrect = errorX < 1 && errorY < 1; // 允许1像素误差
            if (isCorrect) {
                correctCount++;
                console.log("\u2705 (" + point.q + ", " + point.r + ") " + point.description + ": \u8BA1\u7B97=(" + calculated.x.toFixed(1) + ", " + calculated.y.toFixed(1) + ") | \u671F\u671B=(" + point.expected.x + ", " + point.expected.y + ")");
            }
            else {
                console.log("\u274C (" + point.q + ", " + point.r + ") " + point.description + ": \u8BA1\u7B97=(" + calculated.x.toFixed(1) + ", " + calculated.y.toFixed(1) + ") | \u671F\u671B=(" + point.expected.x + ", " + point.expected.y + ") | \u8BEF\u5DEE=(" + errorX.toFixed(1) + ", " + errorY.toFixed(1) + ")");
            }
        });
        console.log("\uD83D\uDCCA \u7CBE\u786E\u5750\u6807\u9A8C\u8BC1\u7ED3\u679C: " + correctCount + "/" + testPoints.length + " \u6B63\u786E");
        // 如果不是全部正确，显示详细的数据分析
        if (correctCount < testPoints.length) {
            console.log("\n🔍 详细数据分析:");
            console.log("您的数据规律分析:");
            // 分析相邻点的差值
            for (var i = 1; i < testPoints.length; i++) {
                var prev = testPoints[i - 1];
                var curr = testPoints[i];
                var deltaQ = curr.q - prev.q;
                var deltaR = curr.r - prev.r;
                var deltaX = curr.expected.x - prev.expected.x;
                var deltaY = curr.expected.y - prev.expected.y;
                console.log("\u4ECE(" + prev.q + "," + prev.r + ")\u5230(" + curr.q + "," + curr.r + "): \u0394q=" + deltaQ + ", \u0394r=" + deltaR + ", \u0394x=" + deltaX + ", \u0394y=" + deltaY);
            }
        }
        // 测试一些关键的推算坐标
        console.log("\n📍 测试关键推算坐标:");
        var extraPoints = [
            { q: 1, r: 0, description: "右侧邻居" },
            { q: -1, r: 0, description: "左侧邻居" },
            { q: 0, r: 1, description: "下方邻居" },
            { q: 1, r: -1, description: "右上邻居" },
            { q: 2, r: -2, description: "应该在(1,-2)和(2,-3)之间" }
        ];
        extraPoints.forEach(function (point) {
            var calculated = _this.getHexWorldPosition(point.q, point.r);
            console.log("\uD83D\uDCD0 (" + point.q + ", " + point.r + ") " + point.description + ": (" + calculated.x.toFixed(1) + ", " + calculated.y.toFixed(1) + ")");
        });
        // 验证左右间距（q方向）
        console.log("\n📏 验证左右间距:");
        var pos1 = this.getHexWorldPosition(0, 0);
        var pos2 = this.getHexWorldPosition(1, 0);
        var actualSpacing = pos2.x - pos1.x; // 不用绝对值，看方向
        console.log("\u4ECE(0,0)\u5230(1,0)\u7684x\u95F4\u8DDD: " + actualSpacing.toFixed(1) + " (\u671F\u671B: 97)");
        if (Math.abs(actualSpacing - 97) < 2) {
            console.log("✅ 左右间距正确");
        }
        else {
            console.log("\u274C \u5DE6\u53F3\u95F4\u8DDD\u4E0D\u6B63\u786E\uFF0C\u5DEE\u503C: " + (actualSpacing - 97).toFixed(1));
        }
    };
    // 从位置计算六边形坐标
    HexSingleChessBoardController.prototype.getHexCoordinateFromPosition = function (pos) {
        // 简化的六边形坐标转换，实际项目中可能需要更精确的算法
        var q = Math.round(pos.x / (this.HEX_SIZE * 1.5));
        var r = Math.round((pos.y - pos.x * Math.tan(Math.PI / 6)) / this.HEX_HEIGHT);
        return { q: q, r: r };
    };
    // 计算六边形世界坐标位置（参考联机版Level_S001的实现）
    HexSingleChessBoardController.prototype.getHexWorldPosition = function (q, r, isPlayerAvatar) {
        if (isPlayerAvatar === void 0) { isPlayerAvatar = false; }
        console.log("\uD83D\uDD0D \u8BA1\u7B97\u5750\u6807: (" + q + ", " + r + ")\uFF0C\u5F53\u524D\u68CB\u76D8: " + this.currentBoardType);
        // 根据当前棋盘类型获取配置
        var config = this.getBoardConfig(this.currentBoardType);
        if (!config) {
            console.error("\u274C \u672A\u627E\u5230\u68CB\u76D8 " + this.currentBoardType + " \u7684\u914D\u7F6E");
            return cc.v2(0, 0);
        }
        // 首先检查是否有精确坐标
        var key = q + "," + r;
        if (config.exactCoords.has(key)) {
            var pos = config.exactCoords.get(key);
            console.log("\u2705 \u4F7F\u7528\u7CBE\u786E\u5750\u6807: (" + q + ", " + r + ") \u2192 (" + pos.x + ", " + pos.y + ")");
            // 如果是玩家头像预制体，y轴向上偏移
            if (isPlayerAvatar) {
                return cc.v2(pos.x, pos.y + 20);
            }
            return pos;
        }
        // 使用联机版的逻辑：每行有基准点，使用统一步长计算
        var x, y;
        // 如果有该行的数据，使用统一步长计算
        if (config.rowData.has(r)) {
            var data = config.rowData.get(r);
            x = data.baseX + (q - data.baseQ) * config.uniformStepX;
            y = data.y;
            console.log("\uD83D\uDCCD \u4F7F\u7528\u884C\u6570\u636E: r=" + r + "\u884C\uFF0C\u57FA\u51C6\u70B9(" + data.baseQ + ", " + data.baseX + ", " + data.y + ")");
            console.log("   \u8BA1\u7B97: x = " + data.baseX + " + (" + q + " - " + data.baseQ + ") * " + config.uniformStepX + " = " + x);
        }
        else {
            // 对于其他行，使用通用的六边形轴线坐标系公式
            console.warn("\u26A0\uFE0F \u6CA1\u6709r=" + r + "\u884C\u7684\u7CBE\u786E\u6570\u636E\uFF0C\u4F7F\u7528\u901A\u7528\u516C\u5F0F");
            var stepXR = -config.uniformStepX / 2;
            var stepYR = 74;
            x = config.baseX + q * config.uniformStepX + r * stepXR;
            y = config.baseY - r * stepYR;
        }
        console.log("\uD83D\uDCD0 \u6700\u7EC8\u5750\u6807: (" + q + ", " + r + ") \u2192 (" + x.toFixed(1) + ", " + y.toFixed(1) + ")");
        // 如果是玩家头像预制体，y轴向上偏移
        if (isPlayerAvatar) {
            y += 20;
        }
        return cc.v2(x, y);
    };
    // 获取棋盘配置（参考联机版Level_S001的实现）
    HexSingleChessBoardController.prototype.getBoardConfig = function (boardType) {
        var configs = new Map();
        // Level_S001 (hexBoard1) - 第5关，您最开始给的数据
        configs.set("hexBoard1", {
            baseX: -170, baseY: -165,
            uniformStepX: 97,
            exactCoords: new Map([
                ["0,0", cc.v2(-170, -165)],
                ["0,-1", cc.v2(-220, -81)],
                ["1,-2", cc.v2(-172, 2)],
                ["2,-3", cc.v2(-122, 85)],
                ["4,-4", cc.v2(23, 171)]
            ]),
            rowData: new Map([
                [0, { baseQ: 0, baseX: -170, y: -165 }],
                [-1, { baseQ: 0, baseX: -220, y: -81 }],
                [-2, { baseQ: 1, baseX: -172, y: 2 }],
                [-3, { baseQ: 2, baseX: -122, y: 85 }],
                [-4, { baseQ: 4, baseX: 23, y: 171 }] // r=-4行：基准点(4,-4) → (23, 171)
            ])
        });
        // Level_S002 (hexBoard2) - 第10关
        configs.set("hexBoard2", {
            baseX: 0, baseY: -293,
            uniformStepX: 98,
            exactCoords: new Map([
                ["0,0", cc.v2(0, -293)],
                ["0,-1", cc.v2(-50, -209)],
                ["0,-2", cc.v2(-100, -125)],
                ["0,-3", cc.v2(-150, -42)],
                ["1,-4", cc.v2(-100, 44)],
                ["2,-5", cc.v2(-50, 127)],
                ["2,-6", cc.v2(-100, 210)],
                ["3,-7", cc.v2(-50, 293)]
            ]),
            rowData: new Map([
                [0, { baseQ: 0, baseX: 0, y: -293 }],
                [-1, { baseQ: 0, baseX: -50, y: -209 }],
                [-2, { baseQ: 0, baseX: -100, y: -125 }],
                [-3, { baseQ: 0, baseX: -150, y: -42 }],
                [-4, { baseQ: 1, baseX: -100, y: 44 }],
                [-5, { baseQ: 2, baseX: -50, y: 127 }],
                [-6, { baseQ: 2, baseX: -100, y: 210 }],
                [-7, { baseQ: 3, baseX: -50, y: 293 }] // r=-7行：基准点(3,-7) → (-50, 293)
            ])
        });
        // Level_S003 (hexBoard3) - 第15关
        configs.set("hexBoard3", {
            baseX: -146, baseY: -250,
            uniformStepX: 98,
            exactCoords: new Map([
                ["0,0", cc.v2(-146, -250)],
                ["1,1", cc.v2(0, -336)],
                ["0,-1", cc.v2(-196, -168)],
                ["1,-2", cc.v2(-146, -85)],
                ["2,-3", cc.v2(-99, -1)],
                ["1,-4", cc.v2(-246, 84)],
                ["1,-5", cc.v2(-293, 167)],
                ["2,-6", cc.v2(-246, 251)],
                ["3,-7", cc.v2(-196, 336)]
            ]),
            rowData: new Map([
                [1, { baseQ: 1, baseX: 0, y: -336 }],
                [0, { baseQ: 0, baseX: -146, y: -250 }],
                [-1, { baseQ: 0, baseX: -196, y: -168 }],
                [-2, { baseQ: 1, baseX: -146, y: -85 }],
                [-3, { baseQ: 2, baseX: -99, y: -1 }],
                [-4, { baseQ: 1, baseX: -246, y: 84 }],
                [-5, { baseQ: 1, baseX: -293, y: 167 }],
                [-6, { baseQ: 2, baseX: -246, y: 251 }],
                [-7, { baseQ: 3, baseX: -196, y: 336 }] // r=-7行：基准点(3,-7) → (-196, 336)
            ])
        });
        // Level_S004 (hexBoard4) - 第20关，同联机版
        configs.set("hexBoard4", {
            baseX: -300, baseY: -258,
            uniformStepX: 86,
            exactCoords: new Map([
                ["0,0", cc.v2(-300, -258)],
                ["1,-1", cc.v2(-258, -184)],
                ["1,-2", cc.v2(-300, -108)],
                ["2,-3", cc.v2(-258, -36)],
                ["2,-4", cc.v2(-300, 37)],
                ["3,-5", cc.v2(-258, 110)],
                ["3,-6", cc.v2(-300, 185)],
                ["4,-7", cc.v2(-258, 260)]
            ]),
            rowData: new Map([
                [0, { baseQ: 0, baseX: -300, y: -258 }],
                [-1, { baseQ: 1, baseX: -258, y: -184 }],
                [-2, { baseQ: 1, baseX: -300, y: -108 }],
                [-3, { baseQ: 2, baseX: -258, y: -36 }],
                [-4, { baseQ: 2, baseX: -300, y: 37 }],
                [-5, { baseQ: 3, baseX: -258, y: 110 }],
                [-6, { baseQ: 3, baseX: -300, y: 185 }],
                [-7, { baseQ: 4, baseX: -258, y: 260 }]
            ])
        });
        // Level_S005 (hexBoard5) - 第25关，预制体scale改为0.8
        configs.set("hexBoard5", {
            baseX: -257, baseY: -293,
            uniformStepX: 85.5,
            scale: 0.8,
            exactCoords: new Map([
                ["0,0", cc.v2(-257, -293)],
                ["0,-1", cc.v2(-300, -219)],
                ["1,-2", cc.v2(-257, -146)],
                ["1,-3", cc.v2(-300, -74)],
                ["2,-4", cc.v2(-257, 0)],
                ["2,-5", cc.v2(-300, 74)],
                ["3,-6", cc.v2(-257, 146)],
                ["3,-7", cc.v2(-300, 219)],
                ["4,-8", cc.v2(-257, 293)]
            ]),
            rowData: new Map([
                [0, { baseQ: 0, baseX: -257, y: -293 }],
                [-1, { baseQ: 0, baseX: -300, y: -219 }],
                [-2, { baseQ: 1, baseX: -257, y: -146 }],
                [-3, { baseQ: 1, baseX: -300, y: -74 }],
                [-4, { baseQ: 2, baseX: -257, y: 0 }],
                [-5, { baseQ: 2, baseX: -300, y: 74 }],
                [-6, { baseQ: 3, baseX: -257, y: 146 }],
                [-7, { baseQ: 3, baseX: -300, y: 219 }],
                [-8, { baseQ: 4, baseX: -257, y: 293 }]
            ])
        });
        // Level_S006 (hexBoard6) - 第30关，预制体scale改为0.8
        configs.set("hexBoard6", {
            baseX: -313, baseY: -298,
            uniformStepX: 78,
            scale: 0.8,
            exactCoords: new Map([
                ["0,0", cc.v2(-313, -298)],
                ["1,-1", cc.v2(-274, -233)],
                ["1,-2", cc.v2(-313, -165)],
                ["2,-3", cc.v2(-274, -99)],
                ["2,-4", cc.v2(-313, -34)],
                ["3,-5", cc.v2(-274, 34)],
                ["3,-6", cc.v2(-313, 96)],
                ["4,-7", cc.v2(-274, 165)],
                ["4,-8", cc.v2(-313, 226)],
                ["5,-9", cc.v2(-274, 300)]
            ]),
            rowData: new Map([
                [0, { baseQ: 0, baseX: -313, y: -298 }],
                [-1, { baseQ: 1, baseX: -274, y: -233 }],
                [-2, { baseQ: 1, baseX: -313, y: -165 }],
                [-3, { baseQ: 2, baseX: -274, y: -99 }],
                [-4, { baseQ: 2, baseX: -313, y: -34 }],
                [-5, { baseQ: 3, baseX: -274, y: 34 }],
                [-6, { baseQ: 3, baseX: -313, y: 96 }],
                [-7, { baseQ: 4, baseX: -274, y: 165 }],
                [-8, { baseQ: 4, baseX: -313, y: 226 }],
                [-9, { baseQ: 5, baseX: -274, y: 300 }]
            ])
        });
        return configs.get(boardType);
    };
    // 为六边形格子设置触摸事件
    HexSingleChessBoardController.prototype.setupHexGridTouchEvents = function (gridNode, q, r) {
        var _this = this;
        console.log("\uD83C\uDFAF \u4E3A\u683C\u5B50 (" + q + ", " + r + ") \u8BBE\u7F6E\u89E6\u6478\u4E8B\u4EF6\uFF0C\u8282\u70B9\u540D\u79F0: " + gridNode.name);
        // 确保节点可以接收触摸事件
        if (!gridNode.getComponent(cc.Button)) {
            // 如果没有Button组件，添加一个
            var button = gridNode.addComponent(cc.Button);
            button.transition = cc.Button.Transition.NONE; // 不需要视觉反馈
            console.log("\u2795 \u4E3A\u8282\u70B9 " + gridNode.name + " \u6DFB\u52A0\u4E86Button\u7EC4\u4EF6");
        }
        // 移除现有的触摸事件监听器
        gridNode.off(cc.Node.EventType.TOUCH_END);
        gridNode.off(cc.Node.EventType.TOUCH_START);
        gridNode.off(cc.Node.EventType.TOUCH_CANCEL);
        // 添加点击事件监听器
        gridNode.on(cc.Node.EventType.TOUCH_END, function (event) {
            console.log("\uD83D\uDC46 \u683C\u5B50 (" + q + ", " + r + ") \u88AB\u70B9\u51FB");
            _this.onHexGridClick(q, r, event);
        }, this);
        // 添加长按事件监听器
        this.setupLongPressEvent(gridNode, q, r);
        console.log("\u2705 \u683C\u5B50 (" + q + ", " + r + ") \u89E6\u6478\u4E8B\u4EF6\u8BBE\u7F6E\u5B8C\u6210");
    };
    // 设置长按事件
    HexSingleChessBoardController.prototype.setupLongPressEvent = function (gridNode, q, r) {
        var _this = this;
        var touchStartTime = 0;
        var longPressTriggered = false;
        var LONG_PRESS_DURATION = 500; // 500毫秒长按
        gridNode.on(cc.Node.EventType.TOUCH_START, function () {
            touchStartTime = Date.now();
            longPressTriggered = false;
            // 设置长按定时器
            _this.scheduleOnce(function () {
                if (!longPressTriggered && (Date.now() - touchStartTime) >= LONG_PRESS_DURATION) {
                    longPressTriggered = true;
                    _this.onHexGridLongPress(q, r);
                }
            }, LONG_PRESS_DURATION / 1000);
        }, this);
        gridNode.on(cc.Node.EventType.TOUCH_END, function () {
            longPressTriggered = true; // 防止长按事件触发
        }, this);
        gridNode.on(cc.Node.EventType.TOUCH_CANCEL, function () {
            longPressTriggered = true; // 防止长按事件触发
        }, this);
    };
    // 六边形格子点击事件 - 发送挖掘操作
    HexSingleChessBoardController.prototype.onHexGridClick = function (q, r, _event) {
        console.log("\uD83C\uDFAF \u516D\u8FB9\u5F62\u683C\u5B50\u70B9\u51FB\u4E8B\u4EF6\u89E6\u53D1: (" + q + ", " + r + ")");
        // 检查坐标是否有效
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn("\u274C \u65E0\u6548\u7684\u516D\u8FB9\u5F62\u5750\u6807: (" + q + ", " + r + ")");
            return;
        }
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        // 检查该位置是否已经有预制体
        if (gridData && gridData.hasPlayer) {
            console.warn("\u26A0\uFE0F \u683C\u5B50(" + q + ", " + r + ")\u5DF2\u6709\u9884\u5236\u4F53");
            return;
        }
        // 防重复点击检查
        var currentTime = Date.now();
        var positionKey = q + "," + r;
        if (currentTime - this.lastClickTime < this.CLICK_COOLDOWN &&
            this.lastClickPosition === positionKey) {
            console.warn("点击过于频繁，忽略本次点击");
            return;
        }
        this.lastClickTime = currentTime;
        this.lastClickPosition = positionKey;
        console.log("\uD83D\uDCE4 \u53D1\u9001LevelClickBlock\u6D88\u606F: (" + q + ", " + r + "), action=1");
        // 发送LevelClickBlock消息 (action = 1 表示挖掘)
        this.sendLevelClickBlock(q, r, 1);
    };
    // 六边形格子长按事件 - 发送标记操作
    HexSingleChessBoardController.prototype.onHexGridLongPress = function (q, r) {
        // 检查坐标是否有效
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn("\u274C \u65E0\u6548\u7684\u516D\u8FB9\u5F62\u5750\u6807: (" + q + ", " + r + ")");
            return;
        }
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        // 检查该位置是否已经有biaoji预制体
        if (this.hasBiaojiAt(q, r)) {
            // 如果已经有biaoji，则删除它（本地立即处理）
            this.removeBiaojiAt(q, r);
            // 发送取消标记消息
            this.sendLevelClickBlock(q, r, 2);
        }
        else if (!gridData || !gridData.hasPlayer) {
            // 如果没有任何预制体，则生成biaoji（本地立即处理）
            this.createBiaojiPrefab(q, r);
            // 发送标记消息
            this.sendLevelClickBlock(q, r, 2);
        }
        else {
            // 如果有其他类型的预制体（如数字、boom），则不处理
            console.warn("\u683C\u5B50(" + q + ", " + r + ")\u5DF2\u6709\u5176\u4ED6\u9884\u5236\u4F53\uFF0C\u65E0\u6CD5\u6807\u8BB0");
        }
    };
    // 检查六边形坐标是否有效
    HexSingleChessBoardController.prototype.isValidHexCoordinate = function (q, r) {
        var key = this.getHexKey(q, r);
        return this.hexGridData.has(key);
    };
    // 发送LevelClickBlock消息（参考四边形单机控制器）
    HexSingleChessBoardController.prototype.sendLevelClickBlock = function (q, r, action) {
        var message = {
            q: q,
            r: r,
            action: action // 1 = 挖掘, 2 = 标记/取消标记
        };
        // 发送WebSocket消息
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeLevelClickBlock, message);
    };
    // 检查指定位置是否有biaoji预制体
    HexSingleChessBoardController.prototype.hasBiaojiAt = function (q, r) {
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (!gridData || !gridData.hasPlayer || !gridData.playerNode) {
            return false;
        }
        // 检查节点名称是否为Biaoji
        return gridData.playerNode.name === "HexBiaoji";
    };
    // 移除指定位置的biaoji预制体
    HexSingleChessBoardController.prototype.removeBiaojiAt = function (q, r) {
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (gridData && gridData.hasPlayer && gridData.playerNode &&
            gridData.playerNode.name === "HexBiaoji") {
            // 播放消失动画
            var biaojiNode_1 = gridData.playerNode;
            cc.tween(biaojiNode_1)
                .to(0.2, { scaleX: 0, scaleY: 0, opacity: 0 })
                .call(function () {
                biaojiNode_1.removeFromParent();
            })
                .start();
            // 更新格子数据
            gridData.hasPlayer = false;
            gridData.playerNode = null;
        }
    };
    // 创建biaoji预制体
    HexSingleChessBoardController.prototype.createBiaojiPrefab = function (q, r) {
        if (!this.biaojiPrefab) {
            console.error("biaojiPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }
        // 实例化biaoji预制体
        var biaojiNode = cc.instantiate(this.biaojiPrefab);
        biaojiNode.name = "HexBiaoji";
        // 设置位置
        var position = this.getHexWorldPosition(q, r, false);
        biaojiNode.setPosition(position);
        // 添加到棋盘
        this.currentBoardNode.addChild(biaojiNode);
        // 获取当前棋盘的缩放配置
        var config = this.getBoardConfig(this.currentBoardType);
        var targetScale = (config === null || config === void 0 ? void 0 : config.scale) || 1.0;
        console.log("\uD83C\uDFAF \u521B\u5EFABiaoji\u9884\u5236\u4F53: \u68CB\u76D8=" + this.currentBoardType + ", \u76EE\u6807\u7F29\u653E=" + targetScale);
        // 播放出现动画
        biaojiNode.setScale(0);
        cc.tween(biaojiNode)
            .to(0.2, { scaleX: targetScale, scaleY: targetScale }, { easing: 'backOut' })
            .start();
        // 更新格子数据
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (gridData) {
            gridData.hasPlayer = true;
            gridData.playerNode = biaojiNode;
        }
    };
    // 创建boom预制体
    HexSingleChessBoardController.prototype.createBoomPrefab = function (q, r, isCurrentUser) {
        if (isCurrentUser === void 0) { isCurrentUser = true; }
        if (!this.boomPrefab) {
            console.error("boomPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }
        // 实例化boom预制体
        var boomNode = cc.instantiate(this.boomPrefab);
        boomNode.name = "HexBoom";
        // 设置位置
        var position = this.getHexWorldPosition(q, r, false);
        boomNode.setPosition(position);
        // 添加到棋盘
        this.currentBoardNode.addChild(boomNode);
        // 获取当前棋盘的缩放配置
        var config = this.getBoardConfig(this.currentBoardType);
        var targetScale = (config === null || config === void 0 ? void 0 : config.scale) || 1.0;
        console.log("\uD83D\uDCA5 \u521B\u5EFABoom\u9884\u5236\u4F53: \u68CB\u76D8=" + this.currentBoardType + ", \u76EE\u6807\u7F29\u653E=" + targetScale);
        // 播放出现动画
        var bounceScale = targetScale * 1.2; // 弹跳效果，基于目标缩放
        boomNode.setScale(0);
        cc.tween(boomNode)
            .to(0.3, { scaleX: bounceScale, scaleY: bounceScale }, { easing: 'backOut' })
            .to(0.1, { scaleX: targetScale, scaleY: targetScale })
            .start();
        // 只有当前用户点到雷时才播放棋盘震动效果
        if (isCurrentUser) {
            this.playBoardShakeAnimation();
        }
        // 更新格子数据
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (gridData) {
            gridData.hasPlayer = true;
            gridData.playerNode = boomNode;
        }
        // 设置标记，表示点到了炸弹
        this.hasBombExploded = true;
    };
    // 创建数字预制体
    HexSingleChessBoardController.prototype.createNumberPrefab = function (q, r, number) {
        // 根据数字选择对应的预制体
        var prefab = null;
        switch (number) {
            case 1:
                prefab = this.boom1Prefab;
                break;
            case 2:
                prefab = this.boom2Prefab;
                break;
            case 3:
                prefab = this.boom3Prefab;
                break;
            case 4:
                prefab = this.boom4Prefab;
                break;
            case 5:
                prefab = this.boom5Prefab;
                break;
            case 6:
                prefab = this.boom6Prefab;
                break;
            case 7:
                prefab = this.boom7Prefab;
                break;
            case 8:
                prefab = this.boom8Prefab;
                break;
            default:
                console.error("\u4E0D\u652F\u6301\u7684\u6570\u5B57: " + number);
                return;
        }
        if (!prefab) {
            console.error("boom" + number + "Prefab \u9884\u5236\u4F53\u672A\u8BBE\u7F6E\uFF0C\u8BF7\u5728\u7F16\u8F91\u5668\u4E2D\u6302\u8F7D");
            return;
        }
        // 实例化数字预制体
        var numberNode = cc.instantiate(prefab);
        numberNode.name = "HexBoom" + number;
        // 设置位置
        var position = this.getHexWorldPosition(q, r, false);
        numberNode.setPosition(position);
        // 添加到棋盘
        this.currentBoardNode.addChild(numberNode);
        // 获取当前棋盘的缩放配置
        var config = this.getBoardConfig(this.currentBoardType);
        var targetScale = (config === null || config === void 0 ? void 0 : config.scale) || 1.0;
        console.log("\uD83D\uDD22 \u521B\u5EFA\u6570\u5B57" + number + "\u9884\u5236\u4F53: \u68CB\u76D8=" + this.currentBoardType + ", \u76EE\u6807\u7F29\u653E=" + targetScale);
        // 播放出现动画
        numberNode.setScale(0);
        cc.tween(numberNode)
            .to(0.2, { scaleX: targetScale, scaleY: targetScale }, { easing: 'backOut' })
            .start();
        // 更新格子数据
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (gridData) {
            gridData.hasPlayer = true;
            gridData.playerNode = numberNode;
        }
    };
    // 播放棋盘震动动画
    HexSingleChessBoardController.prototype.playBoardShakeAnimation = function () {
        if (!this.currentBoardNode) {
            return;
        }
        var originalPosition = this.currentBoardNode.getPosition();
        var shakeIntensity = 10;
        cc.tween(this.currentBoardNode)
            .to(0.05, { x: originalPosition.x + shakeIntensity, y: originalPosition.y })
            .to(0.05, { x: originalPosition.x - shakeIntensity, y: originalPosition.y })
            .to(0.05, { x: originalPosition.x, y: originalPosition.y + shakeIntensity })
            .to(0.05, { x: originalPosition.x, y: originalPosition.y - shakeIntensity })
            .to(0.05, { x: originalPosition.x, y: originalPosition.y })
            .start();
    };
    // 隐藏指定位置的六边形小格子（点击时调用）
    HexSingleChessBoardController.prototype.hideHexGridAt = function (q, r, immediate) {
        if (immediate === void 0) { immediate = false; }
        console.log("\uD83D\uDD0D \u5C1D\u8BD5\u9690\u85CF\u683C\u5B50: q=" + q + " (" + typeof q + "), r=" + r + " (" + typeof r + "), immediate=" + immediate);
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn("\u274C \u9690\u85CF\u683C\u5B50\u5931\u8D25\uFF1A\u5750\u6807(" + q + ", " + r + ")\u65E0\u6548");
            console.warn("   \u5750\u6807\u7C7B\u578B: q=" + typeof q + ", r=" + typeof r);
            console.warn("   \u6709\u6548\u5750\u6807\u5217\u8868: " + this.validHexCoords.map(function (c) { return "(" + c.q + "," + c.r + ")"; }).join(', '));
            return;
        }
        // 获取格子节点
        var key = this.getHexKey(q, r);
        var gridNode = this.hexGridNodes.get(key);
        if (gridNode) {
            if (immediate) {
                // 立即隐藏，不播放动画
                gridNode.active = false;
            }
            else {
                // 播放六边形格子消失动画
                this.playHexGridFallAnimation(gridNode);
            }
        }
    };
    // 播放六边形格子掉落动画
    HexSingleChessBoardController.prototype.playHexGridFallAnimation = function (gridNode) {
        // 保存原始位置
        var originalPos = gridNode.getPosition();
        gridNode['originalPosition'] = originalPos;
        // 播放掉落动画
        cc.tween(gridNode)
            .parallel(cc.tween().to(0.5, { y: originalPos.y - 200 }, { easing: 'sineIn' }), cc.tween().to(0.3, { opacity: 0 }), cc.tween().to(0.5, { angle: 180 }))
            .call(function () {
            gridNode.active = false;
        })
            .start();
    };
    // 显示所有隐藏的格子（游戏结束时调用）
    HexSingleChessBoardController.prototype.showAllHiddenGrids = function () {
        if (!this.currentBoardNode) {
            console.warn("⚠️ 棋盘节点不存在，无法显示隐藏格子");
            return;
        }
        var totalGrids = 0;
        var restoredGrids = 0;
        var alreadyVisibleGrids = 0;
        // 遍历棋盘的所有子节点
        var children = this.currentBoardNode.children;
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            // 如果是六边形小格子节点（sixblock_q_r 格式）
            if (child.name.startsWith("sixblock_") || child.name === "hexblock") {
                totalGrids++;
                // 记录恢复前的状态
                var wasHidden = !child.active || child.opacity < 255 || child.scaleX < 1 || child.scaleY < 1;
                if (wasHidden) {
                    restoredGrids++;
                }
                else {
                    alreadyVisibleGrids++;
                }
                // 停止所有可能正在进行的动画
                child.stopAllActions();
                // 恢复显示状态
                child.active = true;
                child.opacity = 255;
                child.scaleX = 1;
                child.scaleY = 1;
                child.angle = 0; // 重置旋转角度
                // 恢复原始位置（如果有保存的话）
                if (child['originalPosition']) {
                    child.setPosition(child['originalPosition']);
                }
                // 确保格子可以交互
                var button = child.getComponent(cc.Button);
                if (button) {
                    button.enabled = true;
                }
            }
        }
        if (totalGrids === 0) {
            console.warn("⚠️ 没有找到任何六边形格子节点！请检查节点命名是否正确");
        }
    };
    // 清除所有预制体（游戏结束时调用）
    HexSingleChessBoardController.prototype.clearAllPrefabs = function () {
        if (!this.currentBoardNode) {
            console.warn("⚠️ 棋盘节点不存在，无法清除预制体");
            return;
        }
        var clearedCount = 0;
        var children = this.currentBoardNode.children.slice(); // 创建副本避免遍历时修改数组
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            var nodeName = child.name;
            // 检查是否是需要清除的游戏预制体
            if (this.isGamePrefab(nodeName)) {
                child.removeFromParent();
                clearedCount++;
            }
        }
        // 重置格子数据中的预制体状态
        this.hexGridData.forEach(function (gridData) {
            if (gridData.hasPlayer) {
                gridData.hasPlayer = false;
                gridData.playerNode = null;
            }
        });
        // 重置炸弹爆炸标记
        this.hasBombExploded = false;
    };
    // 判断是否为游戏预制体（需要清除的预制体）
    HexSingleChessBoardController.prototype.isGamePrefab = function (nodeName) {
        // 跳过六边形格子节点
        if (nodeName.startsWith("sixblock_") || nodeName === "hexblock") {
            return false;
        }
        // 炸弹预制体
        if (nodeName === "HexBoom") {
            return true;
        }
        // 数字预制体（HexBoom1, HexBoom2, HexBoom3 等）
        if (nodeName.match(/^HexBoom\d+$/)) {
            return true;
        }
        // 标记预制体
        if (nodeName === "HexBiaoji") {
            return true;
        }
        return false;
    };
    /**
     * 处理点击响应，根据服务器返回的结果更新棋盘
     * @param q 格子q坐标
     * @param r 格子r坐标
     * @param result 点击结果 ("boom" | "safe" | number)
     */
    HexSingleChessBoardController.prototype.handleClickResponse = function (q, r, result) {
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn("\u5904\u7406\u70B9\u51FB\u54CD\u5E94\u5931\u8D25\uFF1A\u5750\u6807(" + q + ", " + r + ")\u65E0\u6548");
            return;
        }
        // 如果格子上有biaoji预制体，先移除它
        if (this.hasBiaojiAt(q, r)) {
            // 直接移除，不播放动画
            var gridData_1 = this.hexGridData.get(this.getHexKey(q, r));
            if (gridData_1 && gridData_1.playerNode) {
                gridData_1.playerNode.removeFromParent();
                gridData_1.playerNode = null;
            }
        }
        // 标记格子已被处理，防止重复点击
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (gridData) {
            gridData.hasPlayer = true;
        }
        // 使用连锁动画的方式处理单个格子，保持一致性
        this.playGridDisappearAnimation(q, r, result);
    };
    /**
     * 批量处理连锁反应的格子
     * @param revealedGrids 被揭开的格子列表，支持 {q,r} 或 {x,y} 格式
     */
    HexSingleChessBoardController.prototype.handleChainReaction = function (revealedGrids) {
        var _this = this;
        if (!revealedGrids || revealedGrids.length === 0) {
            return;
        }
        console.log("🔗 处理连锁反应数据:", revealedGrids);
        // 同时播放所有连锁格子的消失动画，不使用延迟
        revealedGrids.forEach(function (block, index) {
            console.log("   \uD83D\uDD0D \u683C\u5B50" + (index + 1) + "\u539F\u59CB\u6570\u636E:", JSON.stringify(block));
            // 处理坐标映射：服务器可能返回 x,y 格式
            var coordQ, coordR, neighborMines;
            if (block.q !== undefined && block.r !== undefined) {
                // 标准六边形坐标格式
                coordQ = block.q;
                coordR = block.r;
                neighborMines = block.neighborMines !== undefined ? block.neighborMines : block.result;
                console.log("   \u2705 \u683C\u5B50" + (index + 1) + ": \u4F7F\u7528\u6807\u51C6\u5750\u6807 (" + coordQ + ", " + coordR + "), \u5730\u96F7\u6570: " + neighborMines);
            }
            else if (block.x !== undefined && block.y !== undefined) {
                // 服务器返回x,y格式，映射为六边形坐标
                coordQ = block.x; // x 就是 q
                coordR = block.y; // y 就是 r
                neighborMines = block.neighborMines !== undefined ? block.neighborMines : block.result;
                console.log("   \u2705 \u683C\u5B50" + (index + 1) + ": \u6620\u5C04\u5750\u6807 (x=" + block.x + ", y=" + block.y + ") -> (q=" + coordQ + ", r=" + coordR + "), \u5730\u96F7\u6570: " + neighborMines);
            }
            else {
                console.error("   \u274C \u683C\u5B50" + (index + 1) + ": \u65E0\u6548\u7684\u5750\u6807\u6570\u636E:", block);
                console.error("      \u53EF\u7528\u5B57\u6BB5: " + Object.keys(block).join(', '));
                return;
            }
            // 验证坐标是否为有效数字
            if (typeof coordQ !== 'number' || typeof coordR !== 'number' ||
                isNaN(coordQ) || isNaN(coordR)) {
                console.error("   \u274C \u683C\u5B50" + (index + 1) + ": \u5750\u6807\u4E0D\u662F\u6709\u6548\u6570\u5B57: q=" + coordQ + ", r=" + coordR);
                return;
            }
            // 立即播放动画，不延迟
            console.log("   \uD83C\uDFAC \u64AD\u653E\u683C\u5B50\u52A8\u753B: (" + coordQ + ", " + coordR + "), \u7ED3\u679C: " + neighborMines);
            _this.playGridDisappearAnimation(coordQ, coordR, neighborMines);
        });
    };
    // 播放格子消失动画并更新显示
    HexSingleChessBoardController.prototype.playGridDisappearAnimation = function (q, r, result) {
        var _this = this;
        // 先隐藏格子
        this.hideHexGridAt(q, r, false);
        // 延迟显示结果，让格子消失动画先播放
        this.scheduleOnce(function () {
            _this.updateNeighborMinesDisplay(q, r, result);
        }, 0.3);
    };
    /**
     * 更新指定位置的neighborMines显示（使用boom数字预制体）
     * @param q 格子q坐标
     * @param r 格子r坐标
     * @param neighborMines 周围地雷数量或结果类型
     */
    HexSingleChessBoardController.prototype.updateNeighborMinesDisplay = function (q, r, neighborMines) {
        if (neighborMines === "boom" || neighborMines === "mine") {
            // 踩到地雷，生成boom预制体并触发震动
            this.createBoomPrefab(q, r, true); // true表示是当前用户踩到的雷，需要震动
            // 设置标记，表示点到了炸弹
            this.hasBombExploded = true;
        }
        else if (typeof neighborMines === "number" && neighborMines > 0) {
            // 显示数字
            this.createNumberPrefab(q, r, neighborMines);
        }
        else {
            // 如果是0、"safe"或其他，则不显示任何预制体
        }
    };
    /**
     * 处理ExtendLevelInfo消息（游戏开始时调用）
     */
    HexSingleChessBoardController.prototype.onExtendLevelInfo = function () {
        this.showAllHiddenGrids();
        this.clearAllPrefabs();
    };
    /**
     * 处理LevelGameEnd消息（游戏结束时调用）
     * 注意：不清理任何数据，保持玩家的游玩痕迹
     */
    HexSingleChessBoardController.prototype.onLevelGameEnd = function () {
        // 不显示隐藏的格子，保持当前状态
        // 不清理预制体，不重置格子状态，保持游戏结果显示
        // 让玩家可以看到自己的标记（biaoji）、挖掘结果（数字、boom）等
    };
    /**
     * 重新初始化棋盘数据（仅在开始新游戏时调用）
     */
    HexSingleChessBoardController.prototype.reinitializeBoardData = function () {
        // 重置hexGridData中的预制体状态
        this.hexGridData.forEach(function (gridData) {
            gridData.hasPlayer = false;
            gridData.playerNode = null;
        });
        // 重置炸弹爆炸标记
        this.hasBombExploded = false;
    };
    /**
     * 禁用所有格子的触摸事件（游戏结束时调用）
     */
    HexSingleChessBoardController.prototype.disableAllGridTouch = function () {
        console.log("🚫 禁用所有六边形格子的触摸事件");
        this.hexGridNodes.forEach(function (gridNode, key) {
            if (gridNode && cc.isValid(gridNode)) {
                // 移除所有触摸事件监听器
                gridNode.off(cc.Node.EventType.TOUCH_END);
                gridNode.off(cc.Node.EventType.TOUCH_START);
                gridNode.off(cc.Node.EventType.TOUCH_CANCEL);
                // 禁用Button组件（如果有的话）
                var button = gridNode.getComponent(cc.Button);
                if (button) {
                    button.enabled = false;
                }
            }
        });
        console.log("\u2705 \u5DF2\u7981\u7528 " + this.hexGridNodes.size + " \u4E2A\u516D\u8FB9\u5F62\u683C\u5B50\u7684\u89E6\u6478\u4E8B\u4EF6");
    };
    /**
     * 检查是否点到了炸弹
     * @returns 是否点到了炸弹
     */
    HexSingleChessBoardController.prototype.hasBombExplodedInThisGame = function () {
        return this.hasBombExploded;
    };
    /**
     * 重置棋盘状态（清理所有预制体和格子状态）
     */
    HexSingleChessBoardController.prototype.resetBoard = function () {
        console.log("🔄 重置六边形棋盘状态");
        // 显示所有隐藏的格子
        this.showAllHiddenGrids();
        // 清除所有预制体
        this.clearAllPrefabs();
        // 重新初始化棋盘数据
        this.reinitializeBoardData();
        console.log("✅ 六边形棋盘重置完成");
    };
    /**
     * 启用所有格子的触摸事件
     */
    HexSingleChessBoardController.prototype.enableAllGridTouch = function () {
        console.log("✅ 启用所有六边形格子的触摸事件");
        this.enableTouchForExistingGrids();
    };
    /**
     * 创建自定义预制体（用于调试等特殊用途）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param prefab 预制体
     * @param nodeName 节点名称
     */
    HexSingleChessBoardController.prototype.createCustomPrefab = function (q, r, prefab, nodeName) {
        if (!prefab) {
            console.error("\u81EA\u5B9A\u4E49\u9884\u5236\u4F53\u672A\u8BBE\u7F6E: " + nodeName);
            return null;
        }
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn("\u65E0\u6548\u7684\u516D\u8FB9\u5F62\u5750\u6807: (" + q + ", " + r + ")");
            return null;
        }
        // 实例化预制体
        var customNode = cc.instantiate(prefab);
        customNode.name = nodeName;
        // 设置位置
        var position = this.getHexWorldPosition(q, r, false);
        customNode.setPosition(position);
        // 添加到棋盘
        this.currentBoardNode.addChild(customNode);
        console.log("\u2705 \u521B\u5EFA\u81EA\u5B9A\u4E49\u9884\u5236\u4F53: " + nodeName + " at (" + q + ", " + r + ")");
        return customNode;
    };
    /**
     * 重置炸弹爆炸状态（开始新游戏时调用）
     */
    HexSingleChessBoardController.prototype.resetBombExplodedStatus = function () {
        this.hasBombExploded = false;
        console.log("🔄 重置炸弹爆炸状态");
    };
    /**
     * 获取当前棋盘类型
     */
    HexSingleChessBoardController.prototype.getCurrentBoardType = function () {
        return this.currentBoardType;
    };
    /**
     * 获取当前棋盘配置（六边形版本返回简化信息）
     */
    HexSingleChessBoardController.prototype.getCurrentBoardConfig = function () {
        return {
            boardType: this.currentBoardType,
            gridCount: this.getHexGridCount(),
            hasBombExploded: this.hasBombExploded
        };
    };
    /**
     * 获取棋盘状态信息（调试用）
     */
    HexSingleChessBoardController.prototype.getHexBoardInfo = function () {
        var info = {
            validHexCoordsCount: this.validHexCoords.length,
            boardNodeChildren: this.currentBoardNode ? this.currentBoardNode.children.length : 0,
            hasPlayerGamePrefab: false,
            hasBoardNode: !!this.currentBoardNode,
            currentBoardType: this.currentBoardType,
            hexGridDataSize: this.hexGridData.size,
            hexGridNodesSize: this.hexGridNodes.size,
            hasBombExploded: this.hasBombExploded
        };
        return info;
    };
    /**
     * 获取前端节点的总数量（用于计算炸弹数量）
     */
    HexSingleChessBoardController.prototype.getHexGridCount = function () {
        return this.validHexCoords.length;
    };
    /**
     * 开始新游戏时的重置方法
     */
    HexSingleChessBoardController.prototype.resetForNewGame = function () {
        // 显示所有隐藏的格子
        this.showAllHiddenGrids();
        // 清除所有预制体
        this.clearAllPrefabs();
        // 重新初始化棋盘数据
        this.reinitializeBoardData();
    };
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boomPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "biaojiPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boom1Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boom2Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boom3Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boom4Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boom5Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boom6Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boom7Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boom8Prefab", void 0);
    __decorate([
        property(cc.Node)
    ], HexSingleChessBoardController.prototype, "hexBoard1Node", void 0);
    __decorate([
        property(cc.Node)
    ], HexSingleChessBoardController.prototype, "hexBoard2Node", void 0);
    __decorate([
        property(cc.Node)
    ], HexSingleChessBoardController.prototype, "hexBoard3Node", void 0);
    __decorate([
        property(cc.Node)
    ], HexSingleChessBoardController.prototype, "hexBoard4Node", void 0);
    __decorate([
        property(cc.Node)
    ], HexSingleChessBoardController.prototype, "hexBoard5Node", void 0);
    __decorate([
        property(cc.Node)
    ], HexSingleChessBoardController.prototype, "hexBoard6Node", void 0);
    HexSingleChessBoardController = __decorate([
        ccclass
    ], HexSingleChessBoardController);
    return HexSingleChessBoardController;
}(cc.Component));
exports.default = HexSingleChessBoardController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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