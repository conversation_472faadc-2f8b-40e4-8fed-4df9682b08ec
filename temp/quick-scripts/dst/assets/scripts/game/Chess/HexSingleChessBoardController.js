
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/game/Chess/HexSingleChessBoardController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '147d16sE21EH4WnxrTp2v7g', 'HexSingleChessBoardController');
// scripts/game/Chess/HexSingleChessBoardController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var WebSocketManager_1 = require("../../net/WebSocketManager");
var MessageId_1 = require("../../net/MessageId");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var HexSingleChessBoardController = /** @class */ (function (_super) {
    __extends(HexSingleChessBoardController, _super);
    function HexSingleChessBoardController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boomPrefab = null; // boom预制体
        _this.biaojiPrefab = null; // biaoji预制体
        _this.boom1Prefab = null; // 数字1预制体
        _this.boom2Prefab = null; // 数字2预制体
        _this.boom3Prefab = null; // 数字3预制体
        _this.boom4Prefab = null; // 数字4预制体
        _this.boom5Prefab = null; // 数字5预制体
        _this.boom6Prefab = null; // 数字6预制体
        _this.boom7Prefab = null; // 数字7预制体
        _this.boom8Prefab = null; // 数字8预制体
        // 六个六边形棋盘节点
        _this.hexBoard1Node = null; // 六边形棋盘1节点
        _this.hexBoard2Node = null; // 六边形棋盘2节点
        _this.hexBoard3Node = null; // 六边形棋盘3节点
        _this.hexBoard4Node = null; // 六边形棋盘4节点
        _this.hexBoard5Node = null; // 六边形棋盘5节点
        _this.hexBoard6Node = null; // 六边形棋盘6节点
        // 当前使用的棋盘节点
        _this.currentBoardNode = null;
        _this.currentBoardType = "hexBoard1"; // 默认使用第一个六边形棋盘
        // 六边形棋盘配置
        _this.HEX_SIZE = 44; // 六边形半径
        _this.HEX_WIDTH = _this.HEX_SIZE * 2; // 六边形宽度
        _this.HEX_HEIGHT = _this.HEX_SIZE * Math.sqrt(3); // 六边形高度
        // 格子数据存储 - 使用Map存储六边形坐标
        _this.hexGridData = new Map(); // 存储六边形格子数据
        _this.hexGridNodes = new Map(); // 存储六边形格子节点
        _this.validHexCoords = []; // 有效的六边形坐标列表
        // 炸弹爆炸标记
        _this.hasBombExploded = false;
        // 防重复发送消息
        _this.lastClickTime = 0;
        _this.lastClickPosition = "";
        _this.CLICK_COOLDOWN = 200; // 200毫秒冷却时间
        return _this;
    }
    HexSingleChessBoardController.prototype.onLoad = function () {
        // 不进行默认初始化，等待外部调用initBoard
    };
    HexSingleChessBoardController.prototype.start = function () {
        // start方法不再自动启用触摸事件，避免与initBoard重复
        // 触摸事件的启用由initBoard方法负责
    };
    /**
     * 根据棋盘类型获取对应的棋盘节点
     * @param boardType 棋盘类型
     */
    HexSingleChessBoardController.prototype.getBoardNodeByType = function (boardType) {
        switch (boardType) {
            case "hexBoard1":
                return this.hexBoard1Node;
            case "hexBoard2":
                return this.hexBoard2Node;
            case "hexBoard3":
                return this.hexBoard3Node;
            case "hexBoard4":
                return this.hexBoard4Node;
            case "hexBoard5":
                return this.hexBoard5Node;
            case "hexBoard6":
                return this.hexBoard6Node;
            default:
                return null;
        }
    };
    /**
     * 初始化指定类型的六边形棋盘
     * @param boardType 棋盘类型 ("hexBoard1", "hexBoard2", "hexBoard3", "hexBoard4", "hexBoard5", "hexBoard6")
     */
    HexSingleChessBoardController.prototype.initBoard = function (boardType) {
        var _this = this;
        // 根据棋盘类型获取对应的节点
        this.currentBoardNode = this.getBoardNodeByType(boardType);
        if (!this.currentBoardNode) {
            console.error("\u516D\u8FB9\u5F62\u68CB\u76D8\u8282\u70B9\u672A\u8BBE\u7F6E\uFF01\u68CB\u76D8\u7C7B\u578B: " + boardType);
            return;
        }
        this.currentBoardType = boardType;
        // 清空现有数据
        this.hexGridData.clear();
        this.hexGridNodes.clear();
        this.validHexCoords = [];
        // 重置炸弹爆炸标记
        this.hasBombExploded = false;
        // 延迟一帧后再次尝试启用触摸事件，确保所有节点都已创建完成
        this.scheduleOnce(function () {
            _this.setValidHexCoords([]); // 传入空数组，但会被忽略
            // 测试预制体位置计算
            _this.testHexPositionCalculation();
            _this.enableTouchForExistingGrids();
        }, 0.1);
    };
    /**
     * 设置有效的六边形坐标列表（忽略服务器数据，使用前端节点坐标）
     * @param _coords 服务器发送的坐标列表（将被忽略）
     */
    HexSingleChessBoardController.prototype.setValidHexCoords = function (_coords) {
        // 忽略服务器传入的坐标，始终从节点名称自动生成
        this.generateCoordsFromNodeNames();
        this.initHexBoard();
    };
    /**
     * 从节点名称自动生成有效坐标列表
     */
    HexSingleChessBoardController.prototype.generateCoordsFromNodeNames = function () {
        if (!this.currentBoardNode) {
            console.error("❌ 棋盘节点不存在，无法生成坐标列表");
            return;
        }
        var foundCoords = [];
        var children = this.currentBoardNode.children;
        var _loop_1 = function (i) {
            var child = children[i];
            var nodeName = child.name;
            // 跳过游戏元素节点，只处理六边形格子节点
            if (this_1.isGameElement(child, nodeName)) {
                return "continue";
            }
            var coords = this_1.parseHexCoordinateFromName(nodeName);
            if (coords) {
                // 检查是否已经存在相同的坐标
                var exists = foundCoords.some(function (c) { return c.q === coords.q && c.r === coords.r; });
                if (!exists) {
                    foundCoords.push({ q: coords.q, r: coords.r });
                }
            }
        };
        var this_1 = this;
        for (var i = 0; i < children.length; i++) {
            _loop_1(i);
        }
        this.validHexCoords = foundCoords;
    };
    // 初始化六边形棋盘
    HexSingleChessBoardController.prototype.initHexBoard = function () {
        // 清空现有数据
        this.hexGridData.clear();
        this.hexGridNodes.clear();
        // 初始化有效坐标的数据
        for (var _i = 0, _a = this.validHexCoords; _i < _a.length; _i++) {
            var coord = _a[_i];
            var key = this.getHexKey(coord.q, coord.r);
            this.hexGridData.set(key, {
                q: coord.q,
                r: coord.r,
                worldPos: this.getHexWorldPosition(coord.q, coord.r),
                hasPlayer: false
            });
        }
        this.createHexGridNodes();
    };
    // 生成六边形坐标的唯一键
    HexSingleChessBoardController.prototype.getHexKey = function (q, r) {
        return q + "," + r;
    };
    // 启用现有格子的触摸事件
    HexSingleChessBoardController.prototype.createHexGridNodes = function () {
        if (!this.currentBoardNode) {
            console.error("棋盘节点未设置！");
            return;
        }
        // 如果格子已经存在，直接启用触摸事件
        this.enableTouchForExistingGrids();
    };
    // 为现有格子启用触摸事件
    HexSingleChessBoardController.prototype.enableTouchForExistingGrids = function () {
        // 检查棋盘节点是否存在
        if (!this.currentBoardNode) {
            console.error("棋盘节点未设置，无法启用触摸事件！");
            return;
        }
        // 遍历棋盘节点的所有子节点
        var children = this.currentBoardNode.children;
        var validGridCount = 0;
        var skippedCount = 0;
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            var nodeName = child.name;
            // 跳过游戏元素节点，只处理六边形格子节点
            if (this.isGameElement(child, nodeName)) {
                skippedCount++;
                continue;
            }
            // 尝试从节点名称解析六边形坐标
            var coords = this.parseHexCoordinateFromName(nodeName);
            if (coords) {
                this.setupHexGridTouchEvents(child, coords.q, coords.r);
                var key = this.getHexKey(coords.q, coords.r);
                this.hexGridNodes.set(key, child);
                validGridCount++;
            }
            else {
                // 如果无法从名称解析，尝试从位置计算
                var pos = child.getPosition();
                var coords_1 = this.getHexCoordinateFromPosition(pos);
                if (coords_1) {
                    this.setupHexGridTouchEvents(child, coords_1.q, coords_1.r);
                    var key = this.getHexKey(coords_1.q, coords_1.r);
                    this.hexGridNodes.set(key, child);
                    validGridCount++;
                }
                else {
                }
            }
        }
        if (validGridCount === 0) {
            console.warn("\u26A0\uFE0F \u6CA1\u6709\u627E\u5230\u4EFB\u4F55\u6709\u6548\u7684\u516D\u8FB9\u5F62\u683C\u5B50\u8282\u70B9\uFF01");
            console.warn("   \u8BF7\u68C0\u67E5\u683C\u5B50\u8282\u70B9\u547D\u540D\u662F\u5426\u4E3A sixblock_q_r \u683C\u5F0F");
            console.warn("   \u4F8B\u5982: sixblock_0_0, sixblock_1_-1, sixblock_-1_2");
        }
    };
    // 从节点名称解析六边形坐标
    HexSingleChessBoardController.prototype.parseHexCoordinateFromName = function (nodeName) {
        var patterns = [
            /^sixblock_(-?\d+)_(-?\d+)$/,
        ];
        for (var _i = 0, patterns_1 = patterns; _i < patterns_1.length; _i++) {
            var pattern = patterns_1[_i];
            var match = nodeName.match(pattern);
            if (match) {
                var coords = { q: parseInt(match[1]), r: parseInt(match[2]) };
                return coords;
            }
        }
        console.warn("\u274C \u65E0\u6CD5\u89E3\u6790\u8282\u70B9\u540D\u79F0: " + nodeName);
        return null;
    };
    // 判断是否为游戏元素节点（需要跳过的节点）
    HexSingleChessBoardController.prototype.isGameElement = function (child, nodeName) {
        // 跳过玩家头像预制体
        if (nodeName === "player_game_pfb" || child.name.includes("Player")) {
            return true;
        }
        // 跳过boom相关预制体
        if (nodeName === "Boom" || nodeName.includes("Boom") || nodeName.includes("boom")) {
            return true;
        }
        // 跳过biaoji相关预制体
        if (nodeName === "Biaoji" || nodeName.includes("Biaoji") || nodeName.includes("biaoji")) {
            return true;
        }
        // 跳过数字预制体
        if (nodeName.match(/^\d+$/) || nodeName.includes("Number")) {
            return true;
        }
        return false;
    };
    // 测试六边形位置计算
    HexSingleChessBoardController.prototype.testHexPositionCalculation = function () {
        var _this = this;
        // 您提供的实际测量数据
        var testPoints = [
            { q: 0, r: 0, expected: cc.v2(-170, -165), description: "中心位置" },
            { q: 0, r: -1, expected: cc.v2(-220, -81), description: "上方" },
            { q: 1, r: -2, expected: cc.v2(-172, 2), description: "右上" },
            { q: 2, r: -3, expected: cc.v2(-122, 85), description: "右上远" },
            { q: 4, r: -4, expected: cc.v2(23, 171), description: "右上最远" }
        ];
        var correctCount = 0;
        testPoints.forEach(function (point) {
            var calculated = _this.getHexWorldPosition(point.q, point.r);
            var errorX = Math.abs(calculated.x - point.expected.x);
            var errorY = Math.abs(calculated.y - point.expected.y);
            var isCorrect = errorX < 1 && errorY < 1; // 允许1像素误差
            if (isCorrect) {
                correctCount++;
            }
            else {
            }
        });
        // 如果不是全部正确，显示详细的数据分析
        if (correctCount < testPoints.length) {
            // 分析相邻点的差值
            for (var i = 1; i < testPoints.length; i++) {
                var prev = testPoints[i - 1];
                var curr = testPoints[i];
                var deltaQ = curr.q - prev.q;
                var deltaR = curr.r - prev.r;
                var deltaX = curr.expected.x - prev.expected.x;
                var deltaY = curr.expected.y - prev.expected.y;
            }
        }
        // 测试一些关键的推算坐标
        var extraPoints = [
            { q: 1, r: 0, description: "右侧邻居" },
            { q: -1, r: 0, description: "左侧邻居" },
            { q: 0, r: 1, description: "下方邻居" },
            { q: 1, r: -1, description: "右上邻居" },
            { q: 2, r: -2, description: "应该在(1,-2)和(2,-3)之间" }
        ];
        extraPoints.forEach(function (point) {
            var calculated = _this.getHexWorldPosition(point.q, point.r);
        });
        // 验证左右间距（q方向）
        var pos1 = this.getHexWorldPosition(0, 0);
        var pos2 = this.getHexWorldPosition(1, 0);
        var actualSpacing = pos2.x - pos1.x; // 不用绝对值，看方向
    };
    // 从位置计算六边形坐标
    HexSingleChessBoardController.prototype.getHexCoordinateFromPosition = function (pos) {
        // 简化的六边形坐标转换，实际项目中可能需要更精确的算法
        var q = Math.round(pos.x / (this.HEX_SIZE * 1.5));
        var r = Math.round((pos.y - pos.x * Math.tan(Math.PI / 6)) / this.HEX_HEIGHT);
        return { q: q, r: r };
    };
    // 计算六边形世界坐标位置（参考联机版Level_S001的实现）
    HexSingleChessBoardController.prototype.getHexWorldPosition = function (q, r, isPlayerAvatar) {
        if (isPlayerAvatar === void 0) { isPlayerAvatar = false; }
        // 根据当前棋盘类型获取配置
        var config = this.getBoardConfig(this.currentBoardType);
        if (!config) {
            console.error("\u274C \u672A\u627E\u5230\u68CB\u76D8 " + this.currentBoardType + " \u7684\u914D\u7F6E");
            return cc.v2(0, 0);
        }
        // 首先检查是否有精确坐标
        var key = q + "," + r;
        if (config.exactCoords.has(key)) {
            var pos = config.exactCoords.get(key);
            // 如果是玩家头像预制体，y轴向上偏移
            if (isPlayerAvatar) {
                return cc.v2(pos.x, pos.y + 20);
            }
            return pos;
        }
        // 使用联机版的逻辑：每行有基准点，使用统一步长计算
        var x, y;
        // 如果有该行的数据，使用统一步长计算
        if (config.rowData.has(r)) {
            var data = config.rowData.get(r);
            x = data.baseX + (q - data.baseQ) * config.uniformStepX;
            y = data.y;
        }
        else {
            // 对于其他行，使用通用的六边形轴线坐标系公式
            console.warn("\u26A0\uFE0F \u6CA1\u6709r=" + r + "\u884C\u7684\u7CBE\u786E\u6570\u636E\uFF0C\u4F7F\u7528\u901A\u7528\u516C\u5F0F");
            var stepXR = -config.uniformStepX / 2;
            var stepYR = 74;
            x = config.baseX + q * config.uniformStepX + r * stepXR;
            y = config.baseY - r * stepYR;
        }
        // 如果是玩家头像预制体，y轴向上偏移
        if (isPlayerAvatar) {
            y += 20;
        }
        return cc.v2(x, y);
    };
    // 获取棋盘配置（参考联机版Level_S001的实现）
    HexSingleChessBoardController.prototype.getBoardConfig = function (boardType) {
        var configs = new Map();
        // Level_S001 (hexBoard1) - 第5关，您最开始给的数据
        configs.set("hexBoard1", {
            baseX: -170, baseY: -165,
            uniformStepX: 97,
            exactCoords: new Map([
                ["0,0", cc.v2(-170, -165)],
                ["0,-1", cc.v2(-220, -81)],
                ["1,-2", cc.v2(-172, 2)],
                ["2,-3", cc.v2(-122, 85)],
                ["4,-4", cc.v2(23, 171)]
            ]),
            rowData: new Map([
                [0, { baseQ: 0, baseX: -170, y: -165 }],
                [-1, { baseQ: 0, baseX: -220, y: -81 }],
                [-2, { baseQ: 1, baseX: -172, y: 2 }],
                [-3, { baseQ: 2, baseX: -122, y: 85 }],
                [-4, { baseQ: 4, baseX: 23, y: 171 }] // r=-4行：基准点(4,-4) → (23, 171)
            ])
        });
        // Level_S002 (hexBoard2) - 第10关
        configs.set("hexBoard2", {
            baseX: 0, baseY: -293,
            uniformStepX: 98,
            exactCoords: new Map([
                ["0,0", cc.v2(0, -293)],
                ["0,-1", cc.v2(-50, -209)],
                ["0,-2", cc.v2(-100, -125)],
                ["0,-3", cc.v2(-150, -42)],
                ["1,-4", cc.v2(-100, 44)],
                ["2,-5", cc.v2(-50, 127)],
                ["2,-6", cc.v2(-100, 210)],
                ["3,-7", cc.v2(-50, 293)]
            ]),
            rowData: new Map([
                [0, { baseQ: 0, baseX: 0, y: -293 }],
                [-1, { baseQ: 0, baseX: -50, y: -209 }],
                [-2, { baseQ: 0, baseX: -100, y: -125 }],
                [-3, { baseQ: 0, baseX: -150, y: -42 }],
                [-4, { baseQ: 1, baseX: -100, y: 44 }],
                [-5, { baseQ: 2, baseX: -50, y: 127 }],
                [-6, { baseQ: 2, baseX: -100, y: 210 }],
                [-7, { baseQ: 3, baseX: -50, y: 293 }] // r=-7行：基准点(3,-7) → (-50, 293)
            ])
        });
        // Level_S003 (hexBoard3) - 第15关
        configs.set("hexBoard3", {
            baseX: -146, baseY: -250,
            uniformStepX: 98,
            exactCoords: new Map([
                ["0,0", cc.v2(-146, -250)],
                ["1,1", cc.v2(0, -336)],
                ["0,-1", cc.v2(-196, -168)],
                ["1,-2", cc.v2(-146, -85)],
                ["2,-3", cc.v2(-99, -1)],
                ["1,-4", cc.v2(-246, 84)],
                ["1,-5", cc.v2(-293, 167)],
                ["2,-6", cc.v2(-246, 251)],
                ["3,-7", cc.v2(-196, 336)]
            ]),
            rowData: new Map([
                [1, { baseQ: 1, baseX: 0, y: -336 }],
                [0, { baseQ: 0, baseX: -146, y: -250 }],
                [-1, { baseQ: 0, baseX: -196, y: -168 }],
                [-2, { baseQ: 1, baseX: -146, y: -85 }],
                [-3, { baseQ: 2, baseX: -99, y: -1 }],
                [-4, { baseQ: 1, baseX: -246, y: 84 }],
                [-5, { baseQ: 1, baseX: -293, y: 167 }],
                [-6, { baseQ: 2, baseX: -246, y: 251 }],
                [-7, { baseQ: 3, baseX: -196, y: 336 }] // r=-7行：基准点(3,-7) → (-196, 336)
            ])
        });
        // Level_S004 (hexBoard4) - 第20关，同联机版
        configs.set("hexBoard4", {
            baseX: -300, baseY: -258,
            uniformStepX: 86,
            exactCoords: new Map([
                ["0,0", cc.v2(-300, -258)],
                ["1,-1", cc.v2(-258, -184)],
                ["1,-2", cc.v2(-300, -108)],
                ["2,-3", cc.v2(-258, -36)],
                ["2,-4", cc.v2(-300, 37)],
                ["3,-5", cc.v2(-258, 110)],
                ["3,-6", cc.v2(-300, 185)],
                ["4,-7", cc.v2(-258, 260)]
            ]),
            rowData: new Map([
                [0, { baseQ: 0, baseX: -300, y: -258 }],
                [-1, { baseQ: 1, baseX: -258, y: -184 }],
                [-2, { baseQ: 1, baseX: -300, y: -108 }],
                [-3, { baseQ: 2, baseX: -258, y: -36 }],
                [-4, { baseQ: 2, baseX: -300, y: 37 }],
                [-5, { baseQ: 3, baseX: -258, y: 110 }],
                [-6, { baseQ: 3, baseX: -300, y: 185 }],
                [-7, { baseQ: 4, baseX: -258, y: 260 }]
            ])
        });
        // Level_S005 (hexBoard5) - 第25关，预制体scale改为0.8
        configs.set("hexBoard5", {
            baseX: -257, baseY: -293,
            uniformStepX: 85.5,
            scale: 0.8,
            exactCoords: new Map([
                ["0,0", cc.v2(-257, -293)],
                ["0,-1", cc.v2(-300, -219)],
                ["1,-2", cc.v2(-257, -146)],
                ["1,-3", cc.v2(-300, -74)],
                ["2,-4", cc.v2(-257, 0)],
                ["2,-5", cc.v2(-300, 74)],
                ["3,-6", cc.v2(-257, 146)],
                ["3,-7", cc.v2(-300, 219)],
                ["4,-8", cc.v2(-257, 293)]
            ]),
            rowData: new Map([
                [0, { baseQ: 0, baseX: -257, y: -293 }],
                [-1, { baseQ: 0, baseX: -300, y: -219 }],
                [-2, { baseQ: 1, baseX: -257, y: -146 }],
                [-3, { baseQ: 1, baseX: -300, y: -74 }],
                [-4, { baseQ: 2, baseX: -257, y: 0 }],
                [-5, { baseQ: 2, baseX: -300, y: 74 }],
                [-6, { baseQ: 3, baseX: -257, y: 146 }],
                [-7, { baseQ: 3, baseX: -300, y: 219 }],
                [-8, { baseQ: 4, baseX: -257, y: 293 }]
            ])
        });
        // Level_S006 (hexBoard6) - 第30关，预制体scale改为0.8
        configs.set("hexBoard6", {
            baseX: -313, baseY: -298,
            uniformStepX: 78,
            scale: 0.8,
            exactCoords: new Map([
                ["0,0", cc.v2(-313, -298)],
                ["1,-1", cc.v2(-274, -233)],
                ["1,-2", cc.v2(-313, -165)],
                ["2,-3", cc.v2(-274, -99)],
                ["2,-4", cc.v2(-313, -34)],
                ["3,-5", cc.v2(-274, 34)],
                ["3,-6", cc.v2(-313, 96)],
                ["4,-7", cc.v2(-274, 165)],
                ["4,-8", cc.v2(-313, 226)],
                ["5,-9", cc.v2(-274, 300)]
            ]),
            rowData: new Map([
                [0, { baseQ: 0, baseX: -313, y: -298 }],
                [-1, { baseQ: 1, baseX: -274, y: -233 }],
                [-2, { baseQ: 1, baseX: -313, y: -165 }],
                [-3, { baseQ: 2, baseX: -274, y: -99 }],
                [-4, { baseQ: 2, baseX: -313, y: -34 }],
                [-5, { baseQ: 3, baseX: -274, y: 34 }],
                [-6, { baseQ: 3, baseX: -313, y: 96 }],
                [-7, { baseQ: 4, baseX: -274, y: 165 }],
                [-8, { baseQ: 4, baseX: -313, y: 226 }],
                [-9, { baseQ: 5, baseX: -274, y: 300 }]
            ])
        });
        return configs.get(boardType);
    };
    // 为六边形格子设置触摸事件
    HexSingleChessBoardController.prototype.setupHexGridTouchEvents = function (gridNode, q, r) {
        var _this = this;
        // 确保节点可以接收触摸事件
        if (!gridNode.getComponent(cc.Button)) {
            // 如果没有Button组件，添加一个
            var button = gridNode.addComponent(cc.Button);
            button.transition = cc.Button.Transition.NONE; // 不需要视觉反馈
        }
        // 移除现有的触摸事件监听器
        gridNode.off(cc.Node.EventType.TOUCH_END);
        gridNode.off(cc.Node.EventType.TOUCH_START);
        gridNode.off(cc.Node.EventType.TOUCH_CANCEL);
        // 添加点击事件监听器
        gridNode.on(cc.Node.EventType.TOUCH_END, function (event) {
            _this.onHexGridClick(q, r, event);
        }, this);
        // 添加长按事件监听器
        this.setupLongPressEvent(gridNode, q, r);
    };
    // 设置长按事件
    HexSingleChessBoardController.prototype.setupLongPressEvent = function (gridNode, q, r) {
        var _this = this;
        var touchStartTime = 0;
        var longPressTriggered = false;
        var LONG_PRESS_DURATION = 500; // 500毫秒长按
        gridNode.on(cc.Node.EventType.TOUCH_START, function () {
            touchStartTime = Date.now();
            longPressTriggered = false;
            // 设置长按定时器
            _this.scheduleOnce(function () {
                if (!longPressTriggered && (Date.now() - touchStartTime) >= LONG_PRESS_DURATION) {
                    longPressTriggered = true;
                    _this.onHexGridLongPress(q, r);
                }
            }, LONG_PRESS_DURATION / 1000);
        }, this);
        gridNode.on(cc.Node.EventType.TOUCH_END, function () {
            longPressTriggered = true; // 防止长按事件触发
        }, this);
        gridNode.on(cc.Node.EventType.TOUCH_CANCEL, function () {
            longPressTriggered = true; // 防止长按事件触发
        }, this);
    };
    // 六边形格子点击事件 - 发送挖掘操作
    HexSingleChessBoardController.prototype.onHexGridClick = function (q, r, _event) {
        // 检查坐标是否有效
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn("\u274C \u65E0\u6548\u7684\u516D\u8FB9\u5F62\u5750\u6807: (" + q + ", " + r + ")");
            return;
        }
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        // 检查该位置是否已经有预制体
        if (gridData && gridData.hasPlayer) {
            console.warn("\u26A0\uFE0F \u683C\u5B50(" + q + ", " + r + ")\u5DF2\u6709\u9884\u5236\u4F53");
            return;
        }
        // 防重复点击检查
        var currentTime = Date.now();
        var positionKey = q + "," + r;
        if (currentTime - this.lastClickTime < this.CLICK_COOLDOWN &&
            this.lastClickPosition === positionKey) {
            console.warn("点击过于频繁，忽略本次点击");
            return;
        }
        this.lastClickTime = currentTime;
        this.lastClickPosition = positionKey;
        // 发送LevelClickBlock消息 (action = 1 表示挖掘)
        this.sendLevelClickBlock(q, r, 1);
    };
    // 六边形格子长按事件 - 发送标记操作
    HexSingleChessBoardController.prototype.onHexGridLongPress = function (q, r) {
        // 检查坐标是否有效
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn("\u274C \u65E0\u6548\u7684\u516D\u8FB9\u5F62\u5750\u6807: (" + q + ", " + r + ")");
            return;
        }
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        // 检查该位置是否已经有biaoji预制体
        if (this.hasBiaojiAt(q, r)) {
            // 如果已经有biaoji，则删除它（本地立即处理）
            this.removeBiaojiAt(q, r);
            // 发送取消标记消息
            this.sendLevelClickBlock(q, r, 2);
        }
        else if (!gridData || !gridData.hasPlayer) {
            // 如果没有任何预制体，则生成biaoji（本地立即处理）
            this.createBiaojiPrefab(q, r);
            // 发送标记消息
            this.sendLevelClickBlock(q, r, 2);
        }
        else {
            // 如果有其他类型的预制体（如数字、boom），则不处理
            console.warn("\u683C\u5B50(" + q + ", " + r + ")\u5DF2\u6709\u5176\u4ED6\u9884\u5236\u4F53\uFF0C\u65E0\u6CD5\u6807\u8BB0");
        }
    };
    // 检查六边形坐标是否有效
    HexSingleChessBoardController.prototype.isValidHexCoordinate = function (q, r) {
        var key = this.getHexKey(q, r);
        return this.hexGridData.has(key);
    };
    // 发送LevelClickBlock消息（参考四边形单机控制器）
    HexSingleChessBoardController.prototype.sendLevelClickBlock = function (q, r, action) {
        var message = {
            q: q,
            r: r,
            action: action // 1 = 挖掘, 2 = 标记/取消标记
        };
        // 发送WebSocket消息
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeLevelClickBlock, message);
    };
    // 检查指定位置是否有biaoji预制体
    HexSingleChessBoardController.prototype.hasBiaojiAt = function (q, r) {
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (!gridData || !gridData.hasPlayer || !gridData.playerNode) {
            return false;
        }
        // 检查节点名称是否为Biaoji
        return gridData.playerNode.name === "HexBiaoji";
    };
    // 移除指定位置的biaoji预制体
    HexSingleChessBoardController.prototype.removeBiaojiAt = function (q, r) {
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (gridData && gridData.hasPlayer && gridData.playerNode &&
            gridData.playerNode.name === "HexBiaoji") {
            // 播放消失动画
            var biaojiNode_1 = gridData.playerNode;
            cc.tween(biaojiNode_1)
                .to(0.2, { scaleX: 0, scaleY: 0, opacity: 0 })
                .call(function () {
                biaojiNode_1.removeFromParent();
            })
                .start();
            // 更新格子数据
            gridData.hasPlayer = false;
            gridData.playerNode = null;
        }
    };
    // 创建biaoji预制体
    HexSingleChessBoardController.prototype.createBiaojiPrefab = function (q, r) {
        if (!this.biaojiPrefab) {
            console.error("biaojiPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }
        // 实例化biaoji预制体
        var biaojiNode = cc.instantiate(this.biaojiPrefab);
        biaojiNode.name = "HexBiaoji";
        // 设置位置
        var position = this.getHexWorldPosition(q, r, false);
        biaojiNode.setPosition(position);
        // 添加到棋盘
        this.currentBoardNode.addChild(biaojiNode);
        // 获取当前棋盘的缩放配置
        var config = this.getBoardConfig(this.currentBoardType);
        var targetScale = (config === null || config === void 0 ? void 0 : config.scale) || 1.0;
        // 播放出现动画
        biaojiNode.setScale(0);
        cc.tween(biaojiNode)
            .to(0.2, { scaleX: targetScale, scaleY: targetScale }, { easing: 'backOut' })
            .start();
        // 更新格子数据
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (gridData) {
            gridData.hasPlayer = true;
            gridData.playerNode = biaojiNode;
        }
    };
    // 创建boom预制体
    HexSingleChessBoardController.prototype.createBoomPrefab = function (q, r, isCurrentUser) {
        if (isCurrentUser === void 0) { isCurrentUser = true; }
        if (!this.boomPrefab) {
            console.error("boomPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }
        // 实例化boom预制体
        var boomNode = cc.instantiate(this.boomPrefab);
        boomNode.name = "HexBoom";
        // 设置位置
        var position = this.getHexWorldPosition(q, r, false);
        boomNode.setPosition(position);
        // 添加到棋盘
        this.currentBoardNode.addChild(boomNode);
        // 获取当前棋盘的缩放配置
        var config = this.getBoardConfig(this.currentBoardType);
        var targetScale = (config === null || config === void 0 ? void 0 : config.scale) || 1.0;
        // 播放出现动画
        var bounceScale = targetScale * 1.2; // 弹跳效果，基于目标缩放
        boomNode.setScale(0);
        cc.tween(boomNode)
            .to(0.3, { scaleX: bounceScale, scaleY: bounceScale }, { easing: 'backOut' })
            .to(0.1, { scaleX: targetScale, scaleY: targetScale })
            .start();
        // 只有当前用户点到雷时才播放棋盘震动效果
        if (isCurrentUser) {
            this.playBoardShakeAnimation();
        }
        // 更新格子数据
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (gridData) {
            gridData.hasPlayer = true;
            gridData.playerNode = boomNode;
        }
        // 设置标记，表示点到了炸弹
        this.hasBombExploded = true;
    };
    // 创建数字预制体
    HexSingleChessBoardController.prototype.createNumberPrefab = function (q, r, number) {
        // 根据数字选择对应的预制体
        var prefab = null;
        switch (number) {
            case 1:
                prefab = this.boom1Prefab;
                break;
            case 2:
                prefab = this.boom2Prefab;
                break;
            case 3:
                prefab = this.boom3Prefab;
                break;
            case 4:
                prefab = this.boom4Prefab;
                break;
            case 5:
                prefab = this.boom5Prefab;
                break;
            case 6:
                prefab = this.boom6Prefab;
                break;
            case 7:
                prefab = this.boom7Prefab;
                break;
            case 8:
                prefab = this.boom8Prefab;
                break;
            default:
                console.error("\u4E0D\u652F\u6301\u7684\u6570\u5B57: " + number);
                return;
        }
        if (!prefab) {
            console.error("boom" + number + "Prefab \u9884\u5236\u4F53\u672A\u8BBE\u7F6E\uFF0C\u8BF7\u5728\u7F16\u8F91\u5668\u4E2D\u6302\u8F7D");
            return;
        }
        // 实例化数字预制体
        var numberNode = cc.instantiate(prefab);
        numberNode.name = "HexBoom" + number;
        // 设置位置
        var position = this.getHexWorldPosition(q, r, false);
        numberNode.setPosition(position);
        // 添加到棋盘
        this.currentBoardNode.addChild(numberNode);
        // 获取当前棋盘的缩放配置
        var config = this.getBoardConfig(this.currentBoardType);
        var targetScale = (config === null || config === void 0 ? void 0 : config.scale) || 1.0;
        // 播放出现动画
        numberNode.setScale(0);
        cc.tween(numberNode)
            .to(0.2, { scaleX: targetScale, scaleY: targetScale }, { easing: 'backOut' })
            .start();
        // 更新格子数据
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (gridData) {
            gridData.hasPlayer = true;
            gridData.playerNode = numberNode;
        }
    };
    // 播放棋盘震动动画
    HexSingleChessBoardController.prototype.playBoardShakeAnimation = function () {
        if (!this.currentBoardNode) {
            return;
        }
        var originalPosition = this.currentBoardNode.getPosition();
        var shakeIntensity = 10;
        cc.tween(this.currentBoardNode)
            .to(0.05, { x: originalPosition.x + shakeIntensity, y: originalPosition.y })
            .to(0.05, { x: originalPosition.x - shakeIntensity, y: originalPosition.y })
            .to(0.05, { x: originalPosition.x, y: originalPosition.y + shakeIntensity })
            .to(0.05, { x: originalPosition.x, y: originalPosition.y - shakeIntensity })
            .to(0.05, { x: originalPosition.x, y: originalPosition.y })
            .start();
    };
    // 隐藏指定位置的六边形小格子（点击时调用）
    HexSingleChessBoardController.prototype.hideHexGridAt = function (q, r, immediate) {
        if (immediate === void 0) { immediate = false; }
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn("\u274C \u9690\u85CF\u683C\u5B50\u5931\u8D25\uFF1A\u5750\u6807(" + q + ", " + r + ")\u65E0\u6548");
            console.warn("   \u5750\u6807\u7C7B\u578B: q=" + typeof q + ", r=" + typeof r);
            console.warn("   \u6709\u6548\u5750\u6807\u5217\u8868: " + this.validHexCoords.map(function (c) { return "(" + c.q + "," + c.r + ")"; }).join(', '));
            return;
        }
        // 获取格子节点
        var key = this.getHexKey(q, r);
        var gridNode = this.hexGridNodes.get(key);
        if (gridNode) {
            if (immediate) {
                // 立即隐藏，不播放动画
                gridNode.active = false;
            }
            else {
                // 播放六边形格子消失动画
                this.playHexGridFallAnimation(gridNode);
            }
        }
    };
    // 播放六边形格子掉落动画
    HexSingleChessBoardController.prototype.playHexGridFallAnimation = function (gridNode) {
        // 保存原始位置
        var originalPos = gridNode.getPosition();
        gridNode['originalPosition'] = originalPos;
        // 播放掉落动画
        cc.tween(gridNode)
            .parallel(cc.tween().to(0.5, { y: originalPos.y - 200 }, { easing: 'sineIn' }), cc.tween().to(0.3, { opacity: 0 }), cc.tween().to(0.5, { angle: 180 }))
            .call(function () {
            gridNode.active = false;
        })
            .start();
    };
    // 显示所有隐藏的格子（游戏结束时调用）
    HexSingleChessBoardController.prototype.showAllHiddenGrids = function () {
        if (!this.currentBoardNode) {
            console.warn("⚠️ 棋盘节点不存在，无法显示隐藏格子");
            return;
        }
        var totalGrids = 0;
        var restoredGrids = 0;
        var alreadyVisibleGrids = 0;
        // 遍历棋盘的所有子节点
        var children = this.currentBoardNode.children;
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            // 如果是六边形小格子节点（sixblock_q_r 格式）
            if (child.name.startsWith("sixblock_") || child.name === "hexblock") {
                totalGrids++;
                // 记录恢复前的状态
                var wasHidden = !child.active || child.opacity < 255 || child.scaleX < 1 || child.scaleY < 1;
                if (wasHidden) {
                    restoredGrids++;
                }
                else {
                    alreadyVisibleGrids++;
                }
                // 停止所有可能正在进行的动画
                child.stopAllActions();
                // 恢复显示状态
                child.active = true;
                child.opacity = 255;
                child.scaleX = 1;
                child.scaleY = 1;
                child.angle = 0; // 重置旋转角度
                // 恢复原始位置（如果有保存的话）
                if (child['originalPosition']) {
                    child.setPosition(child['originalPosition']);
                }
                // 确保格子可以交互
                var button = child.getComponent(cc.Button);
                if (button) {
                    button.enabled = true;
                }
            }
        }
        if (totalGrids === 0) {
            console.warn("⚠️ 没有找到任何六边形格子节点！请检查节点命名是否正确");
        }
    };
    // 清除所有预制体（游戏结束时调用）
    HexSingleChessBoardController.prototype.clearAllPrefabs = function () {
        if (!this.currentBoardNode) {
            console.warn("⚠️ 棋盘节点不存在，无法清除预制体");
            return;
        }
        var clearedCount = 0;
        var children = this.currentBoardNode.children.slice(); // 创建副本避免遍历时修改数组
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            var nodeName = child.name;
            // 检查是否是需要清除的游戏预制体
            if (this.isGamePrefab(nodeName)) {
                child.removeFromParent();
                clearedCount++;
            }
        }
        // 重置格子数据中的预制体状态
        this.hexGridData.forEach(function (gridData) {
            if (gridData.hasPlayer) {
                gridData.hasPlayer = false;
                gridData.playerNode = null;
            }
        });
        // 重置炸弹爆炸标记
        this.hasBombExploded = false;
    };
    // 判断是否为游戏预制体（需要清除的预制体）
    HexSingleChessBoardController.prototype.isGamePrefab = function (nodeName) {
        // 跳过六边形格子节点
        if (nodeName.startsWith("sixblock_") || nodeName === "hexblock") {
            return false;
        }
        // 炸弹预制体
        if (nodeName === "HexBoom") {
            return true;
        }
        // 数字预制体（HexBoom1, HexBoom2, HexBoom3 等）
        if (nodeName.match(/^HexBoom\d+$/)) {
            return true;
        }
        // 标记预制体
        if (nodeName === "HexBiaoji") {
            return true;
        }
        return false;
    };
    /**
     * 处理点击响应，根据服务器返回的结果更新棋盘
     * @param q 格子q坐标
     * @param r 格子r坐标
     * @param result 点击结果 ("boom" | "safe" | number)
     */
    HexSingleChessBoardController.prototype.handleClickResponse = function (q, r, result) {
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn("\u5904\u7406\u70B9\u51FB\u54CD\u5E94\u5931\u8D25\uFF1A\u5750\u6807(" + q + ", " + r + ")\u65E0\u6548");
            return;
        }
        // 如果格子上有biaoji预制体，先移除它
        if (this.hasBiaojiAt(q, r)) {
            // 直接移除，不播放动画
            var gridData_1 = this.hexGridData.get(this.getHexKey(q, r));
            if (gridData_1 && gridData_1.playerNode) {
                gridData_1.playerNode.removeFromParent();
                gridData_1.playerNode = null;
            }
        }
        // 标记格子已被处理，防止重复点击
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (gridData) {
            gridData.hasPlayer = true;
        }
        // 使用连锁动画的方式处理单个格子，保持一致性
        this.playGridDisappearAnimation(q, r, result);
    };
    /**
     * 批量处理连锁反应的格子
     * @param revealedGrids 被揭开的格子列表，支持 {q,r} 或 {x,y} 格式
     */
    HexSingleChessBoardController.prototype.handleChainReaction = function (revealedGrids) {
        var _this = this;
        if (!revealedGrids || revealedGrids.length === 0) {
            return;
        }
        // 同时播放所有连锁格子的消失动画，不使用延迟
        revealedGrids.forEach(function (block, index) {
            // 处理坐标映射：服务器可能返回 x,y 格式
            var coordQ, coordR, neighborMines;
            if (block.q !== undefined && block.r !== undefined) {
                // 标准六边形坐标格式
                coordQ = block.q;
                coordR = block.r;
                neighborMines = block.neighborMines !== undefined ? block.neighborMines : block.result;
            }
            else if (block.x !== undefined && block.y !== undefined) {
                // 服务器返回x,y格式，映射为六边形坐标
                coordQ = block.x; // x 就是 q
                coordR = block.y; // y 就是 r
                neighborMines = block.neighborMines !== undefined ? block.neighborMines : block.result;
            }
            else {
                console.error("   \u274C \u683C\u5B50" + (index + 1) + ": \u65E0\u6548\u7684\u5750\u6807\u6570\u636E:", block);
                console.error("      \u53EF\u7528\u5B57\u6BB5: " + Object.keys(block).join(', '));
                return;
            }
            // 验证坐标是否为有效数字
            if (typeof coordQ !== 'number' || typeof coordR !== 'number' ||
                isNaN(coordQ) || isNaN(coordR)) {
                console.error("   \u274C \u683C\u5B50" + (index + 1) + ": \u5750\u6807\u4E0D\u662F\u6709\u6548\u6570\u5B57: q=" + coordQ + ", r=" + coordR);
                return;
            }
            // 立即播放动画，不延迟
            _this.playGridDisappearAnimation(coordQ, coordR, neighborMines);
        });
    };
    // 播放格子消失动画并更新显示
    HexSingleChessBoardController.prototype.playGridDisappearAnimation = function (q, r, result) {
        var _this = this;
        // 先隐藏格子
        this.hideHexGridAt(q, r, false);
        // 延迟显示结果，让格子消失动画先播放
        this.scheduleOnce(function () {
            _this.updateNeighborMinesDisplay(q, r, result);
        }, 0.3);
    };
    /**
     * 更新指定位置的neighborMines显示（使用boom数字预制体）
     * @param q 格子q坐标
     * @param r 格子r坐标
     * @param neighborMines 周围地雷数量或结果类型
     */
    HexSingleChessBoardController.prototype.updateNeighborMinesDisplay = function (q, r, neighborMines) {
        if (neighborMines === "boom" || neighborMines === "mine") {
            // 踩到地雷，生成boom预制体并触发震动
            this.createBoomPrefab(q, r, true); // true表示是当前用户踩到的雷，需要震动
            // 设置标记，表示点到了炸弹
            this.hasBombExploded = true;
        }
        else if (typeof neighborMines === "number" && neighborMines > 0) {
            // 显示数字
            this.createNumberPrefab(q, r, neighborMines);
        }
        else {
            // 如果是0、"safe"或其他，则不显示任何预制体
        }
    };
    /**
     * 处理ExtendLevelInfo消息（游戏开始时调用）
     */
    HexSingleChessBoardController.prototype.onExtendLevelInfo = function () {
        this.showAllHiddenGrids();
        this.clearAllPrefabs();
    };
    /**
     * 处理ExtendLevelInfo断线重连（恢复游戏状态）
     * @param levelInfo 关卡信息响应数据
     */
    HexSingleChessBoardController.prototype.onExtendLevelInfoReconnect = function (levelInfo) {
        console.log("HexSingleChessBoardController: 处理断线重连，恢复游戏状态");
        // 清理当前状态
        this.clearAllPrefabs();
        this.showAllHiddenGrids();
        // 如果有地图状态信息，恢复棋盘状态
        if (levelInfo.mineMap) {
            this.restoreBoardState(levelInfo.mineMap);
        }
    };
    /**
     * 恢复棋盘状态（断线重连时使用）
     * @param mineMap 地图状态信息
     */
    HexSingleChessBoardController.prototype.restoreBoardState = function (mineMap) {
        var _this = this;
        console.log("HexSingleChessBoardController: 恢复棋盘状态", mineMap);
        var restoredCount = 0;
        // 恢复已挖掘的方块
        if (mineMap.revealedBlocks && Array.isArray(mineMap.revealedBlocks)) {
            console.log("恢复已挖掘的方块数量:", mineMap.revealedBlocks.length);
            mineMap.revealedBlocks.forEach(function (block) {
                // 六边形地图使用q和r坐标，如果没有则使用x和y
                var q = block.q !== undefined ? block.q : block.x;
                var r = block.r !== undefined ? block.r : block.y;
                var neighborMines = block.neighborMines;
                if (_this.isValidHexCoordinate(q, r)) {
                    console.log("\u6062\u590D\u5DF2\u6316\u6398\u516D\u8FB9\u5F62\u65B9\u5757: (" + q + ", " + r + "), \u5468\u56F4\u5730\u96F7\u6570: " + neighborMines);
                    restoredCount++;
                    // 获取六边形格子节点，确保它存在
                    var key = _this.getHexKey(q, r);
                    var gridNode = _this.hexGridNodes.get(key);
                    if (!gridNode) {
                        console.warn("\u516D\u8FB9\u5F62\u683C\u5B50\u8282\u70B9\u4E0D\u5B58\u5728: (" + q + ", " + r + ")");
                        return;
                    }
                    console.log("\u516D\u8FB9\u5F62\u683C\u5B50 (" + q + ", " + r + ") \u5F53\u524D\u72B6\u6001: active=" + gridNode.active + ", opacity=" + gridNode.opacity);
                    // 立即隐藏格子（不播放动画）
                    gridNode.active = false;
                    gridNode.opacity = 0;
                    gridNode.scaleX = 0;
                    gridNode.scaleY = 0;
                    console.log("\u516D\u8FB9\u5F62\u683C\u5B50 (" + q + ", " + r + ") \u9690\u85CF\u540E\u72B6\u6001: active=" + gridNode.active + ", opacity=" + gridNode.opacity);
                    // 显示挖掘结果
                    if (neighborMines > 0) {
                        console.log("\u521B\u5EFA\u516D\u8FB9\u5F62\u6570\u5B57\u9884\u5236\u4F53 (" + q + ", " + r + "), \u6570\u5B57:", neighborMines);
                        _this.scheduleOnce(function () {
                            _this.createNumberPrefab(q, r, neighborMines);
                        }, 0.1);
                    }
                    else {
                        console.log("\u516D\u8FB9\u5F62\u683C\u5B50 (" + q + ", " + r + ") \u5468\u56F4\u65E0\u5730\u96F7\uFF0C\u4E0D\u521B\u5EFA\u6570\u5B57\u9884\u5236\u4F53");
                    }
                    // 标记格子已被处理
                    var gridData = _this.hexGridData.get(key);
                    if (gridData) {
                        gridData.hasPlayer = true;
                    }
                }
            });
        }
        // 恢复已标记的方块
        if (mineMap.markedBlocks && Array.isArray(mineMap.markedBlocks)) {
            console.log("恢复已标记的方块数量:", mineMap.markedBlocks.length);
            mineMap.markedBlocks.forEach(function (block) {
                // 六边形地图使用q和r坐标，如果没有则使用x和y
                var q = block.q !== undefined ? block.q : block.x;
                var r = block.r !== undefined ? block.r : block.y;
                if (_this.isValidHexCoordinate(q, r)) {
                    console.log("\u6062\u590D\u5DF2\u6807\u8BB0\u516D\u8FB9\u5F62\u65B9\u5757: (" + q + ", " + r + ")");
                    // 创建标记预制体
                    _this.createBiaojiPrefab(q, r);
                }
            });
        }
        console.log("\u603B\u5171\u6062\u590D\u4E86 " + restoredCount + " \u4E2A\u516D\u8FB9\u5F62\u683C\u5B50\u7684\u72B6\u6001");
    };
    /**
     * 处理LevelGameEnd消息（游戏结束时调用）
     * 注意：不清理任何数据，保持玩家的游玩痕迹
     */
    HexSingleChessBoardController.prototype.onLevelGameEnd = function () {
        // 不显示隐藏的格子，保持当前状态
        // 不清理预制体，不重置格子状态，保持游戏结果显示
        // 让玩家可以看到自己的标记（biaoji）、挖掘结果（数字、boom）等
    };
    /**
     * 重新初始化棋盘数据（仅在开始新游戏时调用）
     */
    HexSingleChessBoardController.prototype.reinitializeBoardData = function () {
        // 重置hexGridData中的预制体状态
        this.hexGridData.forEach(function (gridData) {
            gridData.hasPlayer = false;
            gridData.playerNode = null;
        });
        // 重置炸弹爆炸标记
        this.hasBombExploded = false;
    };
    /**
     * 禁用所有格子的触摸事件（游戏结束时调用）
     */
    HexSingleChessBoardController.prototype.disableAllGridTouch = function () {
        this.hexGridNodes.forEach(function (gridNode, key) {
            if (gridNode && cc.isValid(gridNode)) {
                // 移除所有触摸事件监听器
                gridNode.off(cc.Node.EventType.TOUCH_END);
                gridNode.off(cc.Node.EventType.TOUCH_START);
                gridNode.off(cc.Node.EventType.TOUCH_CANCEL);
                // 禁用Button组件（如果有的话）
                var button = gridNode.getComponent(cc.Button);
                if (button) {
                    button.enabled = false;
                }
            }
        });
    };
    /**
     * 检查是否点到了炸弹
     * @returns 是否点到了炸弹
     */
    HexSingleChessBoardController.prototype.hasBombExplodedInThisGame = function () {
        return this.hasBombExploded;
    };
    /**
     * 重置棋盘状态（清理所有预制体和格子状态）
     */
    HexSingleChessBoardController.prototype.resetBoard = function () {
        // 显示所有隐藏的格子
        this.showAllHiddenGrids();
        // 清除所有预制体
        this.clearAllPrefabs();
        // 重新初始化棋盘数据
        this.reinitializeBoardData();
    };
    /**
     * 启用所有格子的触摸事件
     */
    HexSingleChessBoardController.prototype.enableAllGridTouch = function () {
        this.enableTouchForExistingGrids();
    };
    /**
     * 创建自定义预制体（用于调试等特殊用途）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param prefab 预制体
     * @param nodeName 节点名称
     */
    HexSingleChessBoardController.prototype.createCustomPrefab = function (q, r, prefab, nodeName) {
        if (!prefab) {
            console.error("\u81EA\u5B9A\u4E49\u9884\u5236\u4F53\u672A\u8BBE\u7F6E: " + nodeName);
            return null;
        }
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn("\u65E0\u6548\u7684\u516D\u8FB9\u5F62\u5750\u6807: (" + q + ", " + r + ")");
            return null;
        }
        // 实例化预制体
        var customNode = cc.instantiate(prefab);
        customNode.name = nodeName;
        // 设置位置
        var position = this.getHexWorldPosition(q, r, false);
        customNode.setPosition(position);
        // 添加到棋盘
        this.currentBoardNode.addChild(customNode);
        return customNode;
    };
    /**
     * 重置炸弹爆炸状态（开始新游戏时调用）
     */
    HexSingleChessBoardController.prototype.resetBombExplodedStatus = function () {
        this.hasBombExploded = false;
    };
    /**
     * 获取当前棋盘类型
     */
    HexSingleChessBoardController.prototype.getCurrentBoardType = function () {
        return this.currentBoardType;
    };
    /**
     * 获取当前棋盘配置（六边形版本返回简化信息）
     */
    HexSingleChessBoardController.prototype.getCurrentBoardConfig = function () {
        return {
            boardType: this.currentBoardType,
            gridCount: this.getHexGridCount(),
            hasBombExploded: this.hasBombExploded
        };
    };
    /**
     * 获取棋盘状态信息（调试用）
     */
    HexSingleChessBoardController.prototype.getHexBoardInfo = function () {
        var info = {
            validHexCoordsCount: this.validHexCoords.length,
            boardNodeChildren: this.currentBoardNode ? this.currentBoardNode.children.length : 0,
            hasPlayerGamePrefab: false,
            hasBoardNode: !!this.currentBoardNode,
            currentBoardType: this.currentBoardType,
            hexGridDataSize: this.hexGridData.size,
            hexGridNodesSize: this.hexGridNodes.size,
            hasBombExploded: this.hasBombExploded
        };
        return info;
    };
    /**
     * 获取前端节点的总数量（用于计算炸弹数量）
     */
    HexSingleChessBoardController.prototype.getHexGridCount = function () {
        return this.validHexCoords.length;
    };
    /**
     * 开始新游戏时的重置方法
     */
    HexSingleChessBoardController.prototype.resetForNewGame = function () {
        // 显示所有隐藏的格子
        this.showAllHiddenGrids();
        // 清除所有预制体
        this.clearAllPrefabs();
        // 重新初始化棋盘数据
        this.reinitializeBoardData();
    };
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boomPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "biaojiPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boom1Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boom2Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boom3Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boom4Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boom5Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boom6Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boom7Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexSingleChessBoardController.prototype, "boom8Prefab", void 0);
    __decorate([
        property(cc.Node)
    ], HexSingleChessBoardController.prototype, "hexBoard1Node", void 0);
    __decorate([
        property(cc.Node)
    ], HexSingleChessBoardController.prototype, "hexBoard2Node", void 0);
    __decorate([
        property(cc.Node)
    ], HexSingleChessBoardController.prototype, "hexBoard3Node", void 0);
    __decorate([
        property(cc.Node)
    ], HexSingleChessBoardController.prototype, "hexBoard4Node", void 0);
    __decorate([
        property(cc.Node)
    ], HexSingleChessBoardController.prototype, "hexBoard5Node", void 0);
    __decorate([
        property(cc.Node)
    ], HexSingleChessBoardController.prototype, "hexBoard6Node", void 0);
    HexSingleChessBoardController = __decorate([
        ccclass
    ], HexSingleChessBoardController);
    return HexSingleChessBoardController;
}(cc.Component));
exports.default = HexSingleChessBoardController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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