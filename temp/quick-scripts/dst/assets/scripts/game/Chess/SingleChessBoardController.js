
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/game/Chess/SingleChessBoardController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '04af7LUaqhJFrIS/DTERlBy', 'SingleChessBoardController');
// scripts/game/Chess/SingleChessBoardController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var WebSocketManager_1 = require("../../net/WebSocketManager");
var MessageId_1 = require("../../net/MessageId");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
// 五种棋盘配置
var BOARD_CONFIGS = {
    "8x8": {
        width: 752,
        height: 752,
        rows: 8,
        cols: 8,
        gridWidth: 88,
        gridHeight: 88
    },
    "8x9": {
        width: 752,
        height: 845,
        rows: 9,
        cols: 8,
        gridWidth: 88,
        gridHeight: 88
    },
    "9x9": {
        width: 752,
        height: 747,
        rows: 9,
        cols: 9,
        gridWidth: 76,
        gridHeight: 76
    },
    "9x10": {
        width: 752,
        height: 830,
        rows: 10,
        cols: 9,
        gridWidth: 78,
        gridHeight: 78
    },
    "10x10": {
        width: 752,
        height: 745,
        rows: 10,
        cols: 10,
        gridWidth: 69,
        gridHeight: 69
    }
};
var SingleChessBoardController = /** @class */ (function (_super) {
    __extends(SingleChessBoardController, _super);
    function SingleChessBoardController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boomPrefab = null; // boom预制体
        _this.biaojiPrefab = null; // biaoji预制体
        _this.boom1Prefab = null; // 数字1预制体
        _this.boom2Prefab = null; // 数字2预制体
        _this.boom3Prefab = null; // 数字3预制体
        _this.boom4Prefab = null; // 数字4预制体
        _this.boom5Prefab = null; // 数字5预制体
        _this.boom6Prefab = null; // 数字6预制体
        _this.boom7Prefab = null; // 数字7预制体
        _this.boom8Prefab = null; // 数字8预制体
        // 五个棋盘节点
        _this.qipan8x8Node = null; // 8x8棋盘节点
        _this.qipan8x9Node = null; // 8x9棋盘节点
        _this.qipan9x9Node = null; // 9x9棋盘节点
        _this.qipan9x10Node = null; // 9x10棋盘节点
        _this.qipan10x10Node = null; // 10x10棋盘节点
        // 当前使用的棋盘节点
        _this.currentBoardNode = null;
        // 当前棋盘配置
        _this.currentBoardConfig = null;
        _this.currentBoardType = "8x8"; // 默认8x8棋盘
        // 炸弹爆炸标记
        _this.hasBombExploded = false;
        // 格子数据存储
        _this.gridData = []; // 二维数组存储格子数据
        _this.gridNodes = []; // 二维数组存储格子节点
        // 防重复发送消息
        _this.lastClickTime = 0;
        _this.lastClickPosition = "";
        _this.CLICK_COOLDOWN = 200; // 200毫秒冷却时间
        return _this;
    }
    SingleChessBoardController.prototype.onLoad = function () {
        // 不进行默认初始化，等待外部调用initBoard
    };
    SingleChessBoardController.prototype.start = function () {
        // start方法不再自动启用触摸事件，避免与initBoard重复
        // 触摸事件的启用由initBoard方法负责
    };
    /**
     * 根据棋盘类型获取对应的棋盘节点
     * @param boardType 棋盘类型
     */
    SingleChessBoardController.prototype.getBoardNodeByType = function (boardType) {
        switch (boardType) {
            case "8x8":
                return this.qipan8x8Node;
            case "8x9":
                return this.qipan8x9Node;
            case "9x9":
                return this.qipan9x9Node;
            case "9x10":
                return this.qipan9x10Node;
            case "10x10":
                return this.qipan10x10Node;
            default:
                return null;
        }
    };
    /**
     * 初始化指定类型的棋盘
     * @param boardType 棋盘类型 ("8x8", "8x9", "9x9", "9x10", "10x10")
     */
    SingleChessBoardController.prototype.initBoard = function (boardType) {
        if (!BOARD_CONFIGS[boardType]) {
            console.error("\u4E0D\u652F\u6301\u7684\u68CB\u76D8\u7C7B\u578B: " + boardType);
            return;
        }
        // 根据棋盘类型获取对应的节点
        this.currentBoardNode = this.getBoardNodeByType(boardType);
        if (!this.currentBoardNode) {
            console.error("\u68CB\u76D8\u8282\u70B9\u672A\u8BBE\u7F6E\uFF01\u68CB\u76D8\u7C7B\u578B: " + boardType);
            return;
        }
        this.currentBoardType = boardType;
        this.currentBoardConfig = BOARD_CONFIGS[boardType];
        // 清空现有数据
        this.gridData = [];
        this.gridNodes = [];
        // 初始化数据数组
        for (var x = 0; x < this.currentBoardConfig.cols; x++) {
            this.gridData[x] = [];
            this.gridNodes[x] = [];
            for (var y = 0; y < this.currentBoardConfig.rows; y++) {
                this.gridData[x][y] = {
                    x: x,
                    y: y,
                    worldPos: this.getGridWorldPosition(x, y),
                    hasPlayer: false
                };
            }
        }
        this.createGridNodes();
    };
    // 启用现有格子的触摸事件
    SingleChessBoardController.prototype.createGridNodes = function () {
        if (!this.currentBoardNode) {
            console.error("棋盘节点未设置！");
            return;
        }
        // 如果格子已经存在，直接启用触摸事件
        this.enableTouchForExistingGrids();
    };
    // 为现有格子启用触摸事件
    SingleChessBoardController.prototype.enableTouchForExistingGrids = function () {
        // 检查棋盘节点是否存在
        if (!this.currentBoardNode) {
            console.error("棋盘节点未设置，无法启用触摸事件！");
            return;
        }
        // 遍历棋盘节点的所有子节点
        var children = this.currentBoardNode.children;
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            // 尝试从节点名称解析坐标
            var coords = this.parseGridCoordinateFromName(child.name);
            if (coords) {
                this.setupGridTouchEvents(child, coords.x, coords.y);
                this.gridNodes[coords.x] = this.gridNodes[coords.x] || [];
                this.gridNodes[coords.x][coords.y] = child;
            }
            else {
                // 如果无法从名称解析，尝试从位置计算
                var pos = child.getPosition();
                var coords_1 = this.getGridCoordinateFromPosition(pos);
                if (coords_1) {
                    this.setupGridTouchEvents(child, coords_1.x, coords_1.y);
                    this.gridNodes[coords_1.x] = this.gridNodes[coords_1.x] || [];
                    this.gridNodes[coords_1.x][coords_1.y] = child;
                }
            }
        }
    };
    // 从节点名称解析格子坐标
    SingleChessBoardController.prototype.parseGridCoordinateFromName = function (nodeName) {
        // 尝试匹配 Grid_x_y 格式
        var match = nodeName.match(/Grid_(\d+)_(\d+)/);
        if (match) {
            return { x: parseInt(match[1]), y: parseInt(match[2]) };
        }
        return null;
    };
    // 从位置计算格子坐标（需要考虑不同棋盘类型的边距）
    SingleChessBoardController.prototype.getGridCoordinateFromPosition = function (pos) {
        if (!this.currentBoardConfig)
            return null;
        // 根据不同棋盘类型使用不同的计算方式
        switch (this.currentBoardType) {
            case "8x9":
                return this.getGridCoordinateFromPositionFor8x9(pos);
            case "9x9":
                return this.getGridCoordinateFromPositionFor9x9(pos);
            case "9x10":
                return this.getGridCoordinateFromPositionFor9x10(pos);
            case "10x10":
                return this.getGridCoordinateFromPositionFor10x10(pos);
            default:
                // 默认计算方式（适用于其他棋盘类型）
                var x = Math.floor((pos.x + this.currentBoardConfig.width / 2) / this.currentBoardConfig.gridWidth);
                var y = Math.floor((pos.y + this.currentBoardConfig.height / 2) / this.currentBoardConfig.gridHeight);
                if (this.isValidCoordinate(x, y)) {
                    return { x: x, y: y };
                }
                return null;
        }
    };
    // 8x9棋盘的位置到坐标转换（基于精确坐标，左下角为(0,0)）
    SingleChessBoardController.prototype.getGridCoordinateFromPositionFor8x9 = function (pos) {
        // 使用与预制体位置计算相同的精确参数
        var startX = -321; // 左下角X坐标
        var startY = -364; // 左下角Y坐标
        var stepX = 91.14; // X方向精确步长
        var stepY = 91.125; // Y方向精确步长
        // 从位置反推坐标
        var x = Math.round((pos.x - startX) / stepX);
        var y = Math.round((pos.y - startY) / stepY);
        if (this.isValidCoordinate(x, y)) {
            return { x: x, y: y };
        }
        return null;
    };
    // 9x9棋盘的位置到坐标转换（基于精确坐标，左下角为(0,0)）
    SingleChessBoardController.prototype.getGridCoordinateFromPositionFor9x9 = function (pos) {
        // 使用与预制体位置计算相同的精确参数
        var startX = -322; // 左下角X坐标
        var startY = -320; // 左下角Y坐标
        var stepX = 80.25; // X方向精确步长
        var stepY = 80.375; // Y方向精确步长
        // 从位置反推坐标
        var x = Math.round((pos.x - startX) / stepX);
        var y = Math.round((pos.y - startY) / stepY);
        if (this.isValidCoordinate(x, y)) {
            return { x: x, y: y };
        }
        return null;
    };
    // 9x10棋盘的位置到坐标转换（基于精确坐标，左下角为(0,0)）
    SingleChessBoardController.prototype.getGridCoordinateFromPositionFor9x10 = function (pos) {
        // 使用与预制体位置计算相同的精确参数
        var startX = -320; // 左下角X坐标
        var startY = -361; // 左下角Y坐标
        var stepX = 80; // X方向精确步长
        var stepY = 80.33; // Y方向精确步长
        // 从位置反推坐标
        var x = Math.round((pos.x - startX) / stepX);
        var y = Math.round((pos.y - startY) / stepY);
        if (this.isValidCoordinate(x, y)) {
            return { x: x, y: y };
        }
        return null;
    };
    // 10x10棋盘的位置到坐标转换（基于精确坐标，左下角为(0,0)）
    SingleChessBoardController.prototype.getGridCoordinateFromPositionFor10x10 = function (pos) {
        // 使用与预制体位置计算相同的精确参数
        var startX = -328; // 左下角X坐标
        var startY = -322; // 左下角Y坐标
        var stepX = 72.56; // X方向精确步长
        var stepY = 72; // Y方向精确步长
        // 从位置反推坐标
        var x = Math.round((pos.x - startX) / stepX);
        var y = Math.round((pos.y - startY) / stepY);
        if (this.isValidCoordinate(x, y)) {
            return { x: x, y: y };
        }
        else {
            return null;
        }
    };
    // 为格子设置触摸事件
    SingleChessBoardController.prototype.setupGridTouchEvents = function (gridNode, x, y) {
        var _this = this;
        // 先清除已有的触摸事件，防止重复绑定
        gridNode.off(cc.Node.EventType.TOUCH_START);
        gridNode.off(cc.Node.EventType.TOUCH_END);
        gridNode.off(cc.Node.EventType.TOUCH_CANCEL);
        // 长按相关变量
        var isLongPressing = false;
        var longPressTimer = 0;
        var longPressCallback = null;
        var hasTriggeredLongPress = false; // 标记是否已触发长按
        var LONG_PRESS_TIME = 1.0; // 1秒长按时间
        // 触摸开始事件
        gridNode.on(cc.Node.EventType.TOUCH_START, function (_event) {
            isLongPressing = true;
            longPressTimer = 0;
            hasTriggeredLongPress = false; // 重置长按标记
            // 开始长按检测
            longPressCallback = function () {
                if (isLongPressing) {
                    longPressTimer += 0.1;
                    if (longPressTimer >= LONG_PRESS_TIME && !hasTriggeredLongPress) {
                        hasTriggeredLongPress = true; // 标记已触发长按
                        isLongPressing = false; // 立即停止长按状态，防止触摸结束时执行点击
                        // 执行长按事件
                        _this.onGridLongPress(x, y);
                        // 停止长按检测
                        if (longPressCallback) {
                            _this.unschedule(longPressCallback);
                            longPressCallback = null;
                        }
                    }
                }
            };
            _this.schedule(longPressCallback, 0.1);
        }, this);
        // 触摸结束事件
        gridNode.on(cc.Node.EventType.TOUCH_END, function (event) {
            // 停止长按检测
            if (longPressCallback) {
                _this.unschedule(longPressCallback);
                longPressCallback = null;
            }
            // 严格检查：只有在所有条件都满足的情况下才执行点击事件
            var shouldExecuteClick = isLongPressing &&
                longPressTimer < LONG_PRESS_TIME &&
                !hasTriggeredLongPress;
            if (shouldExecuteClick) {
                _this.onGridClick(x, y, event);
            }
            else {
            }
            // 清理长按检测
            isLongPressing = false;
            hasTriggeredLongPress = false;
        }, this);
        // 触摸取消事件
        gridNode.on(cc.Node.EventType.TOUCH_CANCEL, function (_event) {
            // 清理长按检测
            isLongPressing = false;
            hasTriggeredLongPress = false;
            if (longPressCallback) {
                _this.unschedule(longPressCallback);
                longPressCallback = null;
            }
        }, this);
    };
    // 计算格子的世界坐标位置（左下角为(0,0)）
    SingleChessBoardController.prototype.getGridWorldPosition = function (x, y) {
        if (!this.currentBoardConfig)
            return cc.v2(0, 0);
        // 计算格子中心点位置
        // 左下角为(0,0)，所以y坐标需要从下往上计算
        var posX = (x * this.currentBoardConfig.gridWidth) + (this.currentBoardConfig.gridWidth / 2) - (this.currentBoardConfig.width / 2);
        var posY = (y * this.currentBoardConfig.gridHeight) + (this.currentBoardConfig.gridHeight / 2) - (this.currentBoardConfig.height / 2);
        return cc.v2(posX, posY);
    };
    // 格子点击事件 - 发送挖掘操作
    SingleChessBoardController.prototype.onGridClick = function (x, y, _event) {
        // 检查坐标是否有效
        if (!this.isValidCoordinate(x, y)) {
            return;
        }
        // 检查该位置是否已经有任何预制体（包括biaoji）
        if (this.gridData[x][y].hasPlayer) {
            return;
        }
        // 发送LevelClickBlock消息
        this.sendLevelClickBlock(x, y, 1);
    };
    // 格子长按事件 - 标记/取消标记操作（参考联机版逻辑）
    SingleChessBoardController.prototype.onGridLongPress = function (x, y) {
        // 检查坐标是否有效
        if (!this.isValidCoordinate(x, y)) {
            return;
        }
        // 检查该位置是否已经有biaoji预制体
        if (this.hasBiaojiAt(x, y)) {
            // 如果已经有biaoji，则删除它（本地立即处理）
            this.removeBiaojiAt(x, y);
            // 发送取消标记消息（但不等待响应，因为已经本地处理了）
            this.sendLevelClickBlock(x, y, 2);
        }
        else if (!this.gridData[x][y].hasPlayer) {
            // 如果没有任何预制体，则生成biaoji（本地立即处理）
            this.createBiaojiPrefab(x, y);
            // 发送标记消息（但不等待响应，因为已经本地处理了）
            this.sendLevelClickBlock(x, y, 2);
        }
        else {
            // 如果有其他类型的预制体（如数字、boom），则不处理
        }
    };
    // 发送LevelClickBlock消息
    SingleChessBoardController.prototype.sendLevelClickBlock = function (x, y, action) {
        // 防重复发送检查
        var currentTime = Date.now();
        var positionKey = x + "," + y + "," + action;
        if (currentTime - this.lastClickTime < this.CLICK_COOLDOWN && this.lastClickPosition === positionKey) {
            return;
        }
        this.lastClickTime = currentTime;
        this.lastClickPosition = positionKey;
        var clickData = {
            x: x,
            y: y,
            action: action // 1=挖掘方块，2=标记/取消标记地雷
        };
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeLevelClickBlock, clickData);
    };
    // 检查坐标是否有效
    SingleChessBoardController.prototype.isValidCoordinate = function (x, y) {
        if (!this.currentBoardConfig)
            return false;
        return x >= 0 && x < this.currentBoardConfig.cols && y >= 0 && y < this.currentBoardConfig.rows;
    };
    /**
     * 在指定位置创建biaoji预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     */
    SingleChessBoardController.prototype.createBiaojiPrefab = function (x, y) {
        if (!this.biaojiPrefab) {
            console.error("biaojiPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }
        // 实例化biaoji预制体
        var biaojiNode = cc.instantiate(this.biaojiPrefab);
        biaojiNode.name = "Biaoji";
        // 设置位置
        var position = this.calculatePrefabPosition(x, y);
        biaojiNode.setPosition(position);
        // 添加到棋盘
        this.currentBoardNode.addChild(biaojiNode);
        // 播放出现动画，10x10棋盘使用0.8缩放
        var targetScale = this.currentBoardType === "10x10" ? 0.8 : 1.0;
        biaojiNode.setScale(0);
        cc.tween(biaojiNode)
            .to(0.2, { scaleX: targetScale, scaleY: targetScale }, { easing: 'backOut' })
            .start();
        // 更新格子数据
        this.gridData[x][y].hasPlayer = true;
        this.gridData[x][y].playerNode = biaojiNode;
    };
    /**
     * 在指定位置创建boom预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param isCurrentUser 是否是当前用户点到的雷（可选，默认为true以保持向后兼容）
     */
    SingleChessBoardController.prototype.createBoomPrefab = function (x, y, isCurrentUser) {
        if (isCurrentUser === void 0) { isCurrentUser = true; }
        if (!this.boomPrefab) {
            console.error("SingleChessBoardController: boomPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }
        // 实例化boom预制体
        var boomNode = cc.instantiate(this.boomPrefab);
        boomNode.name = "Boom";
        // 设置位置
        var position = this.calculatePrefabPosition(x, y);
        boomNode.setPosition(position);
        // 添加到棋盘
        this.currentBoardNode.addChild(boomNode);
        // 播放出现动画，10x10棋盘使用0.8缩放
        var targetScale = this.currentBoardType === "10x10" ? 0.8 : 1.0;
        var bounceScale = targetScale * 1.2; // 弹跳效果比目标缩放大20%
        boomNode.setScale(0);
        cc.tween(boomNode)
            .to(0.3, { scaleX: bounceScale, scaleY: bounceScale }, { easing: 'backOut' })
            .to(0.1, { scaleX: targetScale, scaleY: targetScale })
            .start();
        // 只有当前用户点到雷时才播放棋盘震动效果
        if (isCurrentUser) {
            this.playBoardShakeAnimation();
        }
        // 更新格子数据
        this.gridData[x][y].hasPlayer = true;
        this.gridData[x][y].playerNode = boomNode;
    };
    /**
     * 在指定位置创建数字预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param number 数字(1-8)
     */
    SingleChessBoardController.prototype.createNumberPrefab = function (x, y, number) {
        if (number < 1 || number > 8) {
            console.error("\u65E0\u6548\u7684\u6570\u5B57: " + number);
            return;
        }
        // 获取对应的数字预制体
        var prefab = this.getNumberPrefab(number);
        if (!prefab) {
            console.error("\u6570\u5B57" + number + "\u9884\u5236\u4F53\u672A\u8BBE\u7F6E");
            return;
        }
        // 实例化数字预制体
        var numberNode = cc.instantiate(prefab);
        numberNode.name = "Boom" + number;
        // 设置位置
        var position = this.calculatePrefabPosition(x, y);
        numberNode.setPosition(position);
        // 添加到棋盘
        this.currentBoardNode.addChild(numberNode);
        // 播放出现动画，10x10棋盘使用0.8缩放
        var targetScale = this.currentBoardType === "10x10" ? 0.8 : 1.0;
        numberNode.setScale(0);
        cc.tween(numberNode)
            .to(0.2, { scaleX: targetScale, scaleY: targetScale }, { easing: 'backOut' })
            .start();
    };
    /**
     * 在指定位置创建自定义预制体（用于测试等功能）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param prefab 要创建的预制体
     * @param name 节点名称
     */
    SingleChessBoardController.prototype.createCustomPrefab = function (x, y, prefab, name) {
        if (name === void 0) { name = "CustomPrefab"; }
        if (!prefab) {
            console.error("预制体未设置");
            return null;
        }
        if (!this.currentBoardNode) {
            console.error("currentBoardNode 未设置，无法添加预制体");
            return null;
        }
        try {
            // 实例化预制体
            var customNode = cc.instantiate(prefab);
            customNode.name = name;
            // 设置位置
            var position = this.calculatePrefabPosition(x, y);
            customNode.setPosition(position);
            // 添加到棋盘
            this.currentBoardNode.addChild(customNode);
            // 播放出现动画，10x10棋盘使用0.8缩放
            var targetScale = this.currentBoardType === "10x10" ? 0.8 : 1.0;
            customNode.setScale(0);
            cc.tween(customNode)
                .to(0.3, { scaleX: targetScale * 1.2, scaleY: targetScale * 1.2 }, { easing: 'backOut' })
                .to(0.1, { scaleX: targetScale, scaleY: targetScale })
                .start();
            return customNode;
        }
        catch (error) {
            cc.error("❌ 创建自定义预制体时发生错误:", error);
            return null;
        }
    };
    // 获取数字预制体
    SingleChessBoardController.prototype.getNumberPrefab = function (number) {
        switch (number) {
            case 1: return this.boom1Prefab;
            case 2: return this.boom2Prefab;
            case 3: return this.boom3Prefab;
            case 4: return this.boom4Prefab;
            case 5: return this.boom5Prefab;
            case 6: return this.boom6Prefab;
            case 7: return this.boom7Prefab;
            case 8: return this.boom8Prefab;
            default: return null;
        }
    };
    /**
     * 计算预制体的精确位置（参考联机版ChessBoardController）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @returns 预制体应该放置的精确位置
     */
    SingleChessBoardController.prototype.calculatePrefabPosition = function (x, y) {
        if (!this.currentBoardConfig) {
            return cc.v2(0, 0);
        }
        // 根据不同棋盘类型使用不同的计算方式
        switch (this.currentBoardType) {
            case "8x8":
                return this.calculatePrefabPositionFor8x8(x, y);
            case "8x9":
                return this.calculatePrefabPositionFor8x9(x, y);
            case "9x9":
                return this.calculatePrefabPositionFor9x9(x, y);
            case "9x10":
                return this.calculatePrefabPositionFor9x10(x, y);
            case "10x10":
                return this.calculatePrefabPositionFor10x10(x, y);
            default:
                return this.getGridWorldPosition(x, y);
        }
    };
    // 8x8棋盘的预制体位置计算（参考联机版）
    SingleChessBoardController.prototype.calculatePrefabPositionFor8x8 = function (x, y) {
        // 根据联机版的坐标规律计算：
        // (0,0) → (-314, -310)
        // (1,0) → (-224, -310)  // x增加90
        // (0,1) → (-314, -222)  // y增加88
        // (7,7) → (310, 312)
        var startX = -314; // 起始X坐标
        var startY = -310; // 起始Y坐标
        var stepX = 90; // X方向步长
        var stepY = 88; // Y方向步长
        var finalX = startX + (x * stepX);
        var finalY = startY + (y * stepY);
        return cc.v2(finalX, finalY);
    };
    // 8x9棋盘的预制体位置计算（基于精确坐标，左下角为(0,0)）
    SingleChessBoardController.prototype.calculatePrefabPositionFor8x9 = function (x, y) {
        // 根据提供的精确坐标：
        // 左下角(0,0)：(-321, -364)
        // 右下角(7,0)：(317, -364)
        // 左上角(0,8)：(-321, 365)
        // 右上角(7,8)：(317, 365)
        // 计算步长：
        // X方向步长：(317 - (-321)) / 7 = 638 / 7 ≈ 91.14
        // Y方向步长：(365 - (-364)) / 8 = 729 / 8 ≈ 91.125
        var startX = -321; // 左下角X坐标
        var startY = -364; // 左下角Y坐标
        var stepX = 91.14; // X方向精确步长
        var stepY = 91.125; // Y方向精确步长
        var finalX = startX + (x * stepX);
        var finalY = startY + (y * stepY);
        return cc.v2(finalX, finalY);
    };
    // 9x9棋盘的预制体位置计算（基于精确坐标，左下角为(0,0)）
    SingleChessBoardController.prototype.calculatePrefabPositionFor9x9 = function (x, y) {
        // 根据提供的精确坐标：
        // 左下角(0,0)：(-322, -320)
        // 右下角(8,0)：(320, -320)
        // 左上角(0,8)：(-322, 365)
        // 右上角(8,8)：(320, 323)
        // 计算步长：
        // X方向步长：(320 - (-322)) / 8 = 642 / 8 = 80.25
        // Y方向步长：(323 - (-320)) / 8 = 643 / 8 = 80.375
        var startX = -322; // 左下角X坐标
        var startY = -320; // 左下角Y坐标
        var stepX = 80.25; // X方向精确步长
        var stepY = 80.375; // Y方向精确步长
        var finalX = startX + (x * stepX);
        var finalY = startY + (y * stepY);
        return cc.v2(finalX, finalY);
    };
    // 9x10棋盘的预制体位置计算（基于精确坐标，左下角为(0,0)）
    SingleChessBoardController.prototype.calculatePrefabPositionFor9x10 = function (x, y) {
        // 根据提供的精确坐标：
        // 左下角(0,0)：(-320, -361)
        // 右下角(8,0)：(320, -361)
        // 左上角(0,9)：(-320, 362)
        // 右上角(8,9)：(320, 362)
        // 计算步长：
        // X方向步长：(320 - (-320)) / 8 = 640 / 8 = 80
        // Y方向步长：(362 - (-361)) / 9 = 723 / 9 = 80.33
        var startX = -320; // 左下角X坐标
        var startY = -361; // 左下角Y坐标
        var stepX = 80; // X方向精确步长
        var stepY = 80.33; // Y方向精确步长
        var finalX = startX + (x * stepX);
        var finalY = startY + (y * stepY);
        return cc.v2(finalX, finalY);
    };
    // 10x10棋盘的预制体位置计算（基于精确坐标，左下角为(0,0)）
    SingleChessBoardController.prototype.calculatePrefabPositionFor10x10 = function (x, y) {
        // 根据提供的精确坐标：
        // 左下角(0,0)：(-328, -322)
        // 右下角(9,0)：(325, -322)
        // 左上角(0,9)：(-328, 326)
        // 右上角(9,9)：(325, 326)
        // 计算步长：
        // X方向步长：(325 - (-328)) / 9 = 653 / 9 = 72.56
        // Y方向步长：(326 - (-322)) / 9 = 648 / 9 = 72
        var startX = -328; // 左下角X坐标
        var startY = -322; // 左下角Y坐标
        var stepX = 72.56; // X方向精确步长
        var stepY = 72; // Y方向精确步长
        var finalX = startX + (x * stepX);
        var finalY = startY + (y * stepY);
        return cc.v2(finalX, finalY);
    };
    /**
     * 播放棋盘震动动画（包括所有小格子）
     */
    SingleChessBoardController.prototype.playBoardShakeAnimation = function () {
        if (!this.currentBoardNode)
            return;
        // 震动参数 - 增强震动效果
        var shakeIntensity = 30; // 震动强度
        var shakeDuration = 1.0; // 震动持续时间
        var shakeFrequency = 40; // 震动频率
        // 震动棋盘
        this.shakeBoardNode(shakeIntensity, shakeDuration, shakeFrequency);
        // 震动所有小格子
        this.shakeAllGrids(shakeIntensity * 0.6, shakeDuration, shakeFrequency);
    };
    /**
     * 震动棋盘节点
     */
    SingleChessBoardController.prototype.shakeBoardNode = function (intensity, duration, frequency) {
        // 保存原始位置
        var originalPosition = this.currentBoardNode.position.clone();
        // 创建震动动画，使用递减强度
        var currentIntensity = intensity;
        var intensityDecay = 0.92; // 强度衰减系数
        var createShakeStep = function (shakeIntensity) {
            return cc.tween()
                .to(0.025, {
                x: originalPosition.x + (Math.random() - 0.5) * shakeIntensity * 2,
                y: originalPosition.y + (Math.random() - 0.5) * shakeIntensity * 2
            });
        };
        // 创建震动序列，强度逐渐衰减
        var shakeTween = cc.tween(this.currentBoardNode);
        var totalSteps = Math.floor(duration * frequency);
        for (var i = 0; i < totalSteps; i++) {
            shakeTween = shakeTween.then(createShakeStep(currentIntensity));
            currentIntensity *= intensityDecay; // 逐渐减弱震动强度
        }
        // 最后恢复到原位置
        shakeTween.to(0.2, {
            x: originalPosition.x,
            y: originalPosition.y
        }, { easing: 'backOut' })
            .start();
    };
    /**
     * 震动所有小格子
     */
    SingleChessBoardController.prototype.shakeAllGrids = function (intensity, duration, frequency) {
        if (!this.gridNodes)
            return;
        // 遍历所有格子节点
        for (var x = 0; x < this.gridNodes.length; x++) {
            if (!this.gridNodes[x])
                continue;
            for (var y = 0; y < this.gridNodes[x].length; y++) {
                var gridNode = this.gridNodes[x][y];
                if (!gridNode || !gridNode.active)
                    continue;
                // 为每个格子创建独立的震动动画
                this.shakeGridNode(gridNode, intensity, duration, frequency);
            }
        }
    };
    /**
     * 震动单个格子节点
     */
    SingleChessBoardController.prototype.shakeGridNode = function (gridNode, intensity, duration, frequency) {
        // 保存原始位置
        var originalPosition = gridNode.position.clone();
        // 为每个格子添加随机延迟，创造波浪效果
        var randomDelay = Math.random() * 0.1;
        this.scheduleOnce(function () {
            // 创建震动动画，使用递减强度
            var currentIntensity = intensity;
            var intensityDecay = 0.94; // 格子震动衰减稍慢一些
            var createGridShakeStep = function (shakeIntensity) {
                return cc.tween()
                    .to(0.02, {
                    x: originalPosition.x + (Math.random() - 0.5) * shakeIntensity * 2,
                    y: originalPosition.y + (Math.random() - 0.5) * shakeIntensity * 2
                });
            };
            // 创建震动序列
            var shakeTween = cc.tween(gridNode);
            var totalSteps = Math.floor(duration * frequency * 0.8); // 格子震动时间稍短
            for (var i = 0; i < totalSteps; i++) {
                shakeTween = shakeTween.then(createGridShakeStep(currentIntensity));
                currentIntensity *= intensityDecay;
            }
            // 最后恢复到原位置
            shakeTween.to(0.15, {
                x: originalPosition.x,
                y: originalPosition.y
            }, { easing: 'backOut' })
                .start();
        }, randomDelay);
    };
    /**
     * 显示所有隐藏的格子（游戏结束时调用）
     */
    SingleChessBoardController.prototype.showAllHiddenGrids = function () {
        if (!this.currentBoardNode) {
            console.error("棋盘节点未设置，无法显示隐藏格子！");
            return;
        }
        // 遍历棋盘的所有子节点，找到小格子并显示
        for (var i = 0; i < this.currentBoardNode.children.length; i++) {
            var child = this.currentBoardNode.children[i];
            // 如果是小格子节点
            if (child.name.startsWith("Grid_") || child.name === "block") {
                // 停止所有可能正在进行的动画
                child.stopAllActions();
                // 恢复显示状态
                child.active = true;
                child.opacity = 255;
                child.scaleX = 1;
                child.scaleY = 1;
                child.angle = 0; // 重置旋转角度
                // 恢复原始位置（如果有保存的话）
                if (child['originalPosition']) {
                    child.setPosition(child['originalPosition']);
                }
                // 确保格子可以交互
                var button = child.getComponent(cc.Button);
                if (button) {
                    button.enabled = true;
                }
            }
        }
    };
    /**
     * 清除所有预制体（游戏结束时调用）
     */
    SingleChessBoardController.prototype.clearAllPrefabs = function () {
        if (!this.currentBoardNode) {
            console.error("棋盘节点未设置，无法清除预制体！");
            return;
        }
        var childrenToRemove = [];
        // 遍历棋盘的所有子节点
        for (var i = 0; i < this.currentBoardNode.children.length; i++) {
            var child = this.currentBoardNode.children[i];
            // 检查是否是预制体（通过名称判断）
            if (child.name === "Biaoji" || child.name === "Boom" || child.name.startsWith("Boom")) {
                childrenToRemove.push(child);
            }
        }
        // 移除找到的预制体
        childrenToRemove.forEach(function (child) {
            child.removeFromParent();
        });
        // 重置格子数据
        this.reinitializeBoardData();
    };
    /**
     * 重新初始化棋盘数据（仅在开始新游戏时调用）
     */
    SingleChessBoardController.prototype.reinitializeBoardData = function () {
        if (!this.currentBoardConfig)
            return;
        // 重置gridData中的预制体状态
        for (var x = 0; x < this.currentBoardConfig.cols; x++) {
            for (var y = 0; y < this.currentBoardConfig.rows; y++) {
                if (this.gridData[x] && this.gridData[x][y]) {
                    this.gridData[x][y].hasPlayer = false;
                    this.gridData[x][y].playerNode = null;
                }
            }
        }
    };
    /**
     * 处理ExtendLevelInfo消息（游戏结束时调用）
     */
    SingleChessBoardController.prototype.onExtendLevelInfo = function () {
        this.showAllHiddenGrids();
        this.clearAllPrefabs();
    };
    /**
     * 处理ExtendLevelInfo断线重连（恢复游戏状态）
     * @param levelInfo 关卡信息响应数据
     */
    SingleChessBoardController.prototype.onExtendLevelInfoReconnect = function (levelInfo) {
        console.log("SingleChessBoardController: 处理断线重连，恢复游戏状态");
        // 清理当前状态
        this.clearAllPrefabs();
        this.showAllHiddenGrids();
        // 如果有地图状态信息，恢复棋盘状态
        if (levelInfo.mineMap) {
            this.restoreBoardState(levelInfo.mineMap);
        }
    };
    /**
     * 恢复棋盘状态（断线重连时使用）
     * @param mineMap 地图状态信息
     */
    SingleChessBoardController.prototype.restoreBoardState = function (mineMap) {
        var _this = this;
        console.log("SingleChessBoardController: 恢复棋盘状态", mineMap);
        // 恢复已挖掘的方块
        if (mineMap.revealedBlocks && Array.isArray(mineMap.revealedBlocks)) {
            mineMap.revealedBlocks.forEach(function (block) {
                var x = block.x;
                var y = block.y;
                var neighborMines = block.neighborMines;
                if (_this.isValidCoordinate(x, y)) {
                    // 隐藏格子
                    _this.hideGridAt(x, y);
                    // 显示挖掘结果
                    if (neighborMines > 0) {
                        _this.createNumberPrefab(x, y, neighborMines);
                    }
                    // 标记格子已被处理
                    _this.gridData[x][y].hasPlayer = true;
                }
            });
        }
        // 恢复已标记的方块
        if (mineMap.markedBlocks && Array.isArray(mineMap.markedBlocks)) {
            mineMap.markedBlocks.forEach(function (block) {
                var x = block.x;
                var y = block.y;
                if (_this.isValidCoordinate(x, y)) {
                    // 创建标记预制体
                    _this.createBiaojiPrefab(x, y);
                }
            });
        }
    };
    /**
     * 处理LevelGameEnd消息（游戏结束时调用）
     * 注意：不清理任何数据，保持玩家的游玩痕迹
     */
    SingleChessBoardController.prototype.onLevelGameEnd = function () {
        // 不显示隐藏的格子，保持当前状态
        // 不清理预制体，不重置格子状态，保持游戏结果显示
        // 让玩家可以看到自己的标记（biaoji）、挖掘结果（数字、boom）等
    };
    /**
     * 检查是否点到了炸弹
     * @returns 是否点到了炸弹
     */
    SingleChessBoardController.prototype.hasBombExplodedInThisGame = function () {
        return this.hasBombExploded;
    };
    /**
     * 重置炸弹爆炸状态（开始新游戏时调用）
     */
    SingleChessBoardController.prototype.resetBombExplodedStatus = function () {
        this.hasBombExploded = false;
    };
    /**
     * 隐藏指定位置的格子（点击时调用）
     */
    SingleChessBoardController.prototype.hideGridAt = function (x, y) {
        if (!this.isValidCoordinate(x, y)) {
            console.warn("\u9690\u85CF\u683C\u5B50\u5931\u8D25\uFF1A\u5750\u6807(" + x + ", " + y + ")\u65E0\u6548");
            return;
        }
        // 获取格子节点
        var gridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (gridNode) {
            // 使用动画隐藏格子
            cc.tween(gridNode)
                .to(0.3, { opacity: 0, scaleX: 0, scaleY: 0 }, { easing: 'sineIn' })
                .call(function () {
                gridNode.active = false;
            })
                .start();
        }
    };
    /**
     * 获取当前棋盘类型
     */
    SingleChessBoardController.prototype.getCurrentBoardType = function () {
        return this.currentBoardType;
    };
    /**
     * 获取当前棋盘配置
     */
    SingleChessBoardController.prototype.getCurrentBoardConfig = function () {
        return this.currentBoardConfig;
    };
    /**
     * 处理点击响应，根据服务器返回的结果更新棋盘 - 参考联机版的地图更新逻辑
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param result 点击结果 ("boom" | "safe" | number)
     */
    SingleChessBoardController.prototype.handleClickResponse = function (x, y, result) {
        if (!this.isValidCoordinate(x, y)) {
            console.warn("\u5904\u7406\u70B9\u51FB\u54CD\u5E94\u5931\u8D25\uFF1A\u5750\u6807(" + x + ", " + y + ")\u65E0\u6548");
            return;
        }
        // 如果格子上有biaoji预制体，先移除它
        if (this.hasBiaojiAt(x, y)) {
            // 直接移除，不播放动画
            var gridData = this.gridData[x][y];
            if (gridData.playerNode) {
                gridData.playerNode.removeFromParent();
                gridData.playerNode = null;
            }
        }
        // 标记格子已被处理，防止重复点击
        this.gridData[x][y].hasPlayer = true;
        // 使用连锁动画的方式处理单个格子，保持一致性
        this.playGridDisappearAnimation(x, y, result);
    };
    /**
     * 处理连锁展开结果
     * @param floodFillResults 连锁展开数据数组
     */
    SingleChessBoardController.prototype.handleFloodFillResults = function (floodFillResults) {
        var _this = this;
        // 同时播放所有格子的消失动画，不使用延迟
        floodFillResults.forEach(function (gridResult) {
            var x = gridResult.x, y = gridResult.y, neighborMines = gridResult.neighborMines;
            if (!_this.isValidCoordinate(x, y)) {
                console.warn("\u8FDE\u9501\u5C55\u5F00\u8DF3\u8FC7\u65E0\u6548\u5750\u6807: (" + x + ", " + y + ")");
                return;
            }
            // 立即播放动画，不延迟
            _this.playGridDisappearAnimation(x, y, neighborMines);
        });
    };
    /**
     * 批量处理连锁反应的格子（参考联机版的processFloodFillResult）
     * @param revealedGrids 被揭开的格子列表 {x: number, y: number, neighborMines: number}[]
     */
    SingleChessBoardController.prototype.handleChainReaction = function (revealedGrids) {
        var _this = this;
        if (!revealedGrids || revealedGrids.length === 0) {
            return;
        }
        // 同时播放所有连锁格子的消失动画，不使用延迟
        revealedGrids.forEach(function (block) {
            // 立即播放动画，不延迟
            _this.playGridDisappearAnimation(block.x, block.y, block.neighborMines);
        });
    };
    /**
     * 播放格子消失动画（连锁效果）- 参考联机版ChessBoardController
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param neighborMines 周围地雷数量或结果类型（可以是数字、"mine"、"boom"等）
     */
    SingleChessBoardController.prototype.playGridDisappearAnimation = function (x, y, neighborMines) {
        var _this = this;
        // 如果格子上有biaoji预制体，先移除它（连锁展开时）
        if (this.hasBiaojiAt(x, y)) {
            var gridData = this.gridData[x][y];
            if (gridData.playerNode) {
                gridData.playerNode.removeFromParent();
                gridData.playerNode = null;
            }
        }
        // 标记格子已被处理（对于连锁格子）
        if (this.isValidCoordinate(x, y)) {
            this.gridData[x][y].hasPlayer = true;
        }
        // 先删除格子
        this.removeGridAt(x, y);
        // 延迟0.3秒后显示数字（等格子消失动画完成）
        // 使用带标识的延迟任务，方便重置时清理
        var delayCallback = function () {
            _this.updateNeighborMinesDisplay(x, y, neighborMines);
        };
        this.scheduleOnce(delayCallback, 0.3);
    };
    /**
     * 隐藏指定位置的格子（不销毁，以便重置时可以重新显示）- 参考联机版
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param immediate 是否立即隐藏（不播放动画）
     */
    SingleChessBoardController.prototype.removeGridAt = function (x, y, immediate) {
        if (immediate === void 0) { immediate = false; }
        if (!this.isValidCoordinate(x, y)) {
            console.warn("\u9690\u85CF\u683C\u5B50\u5931\u8D25\uFF1A\u5750\u6807(" + x + ", " + y + ")\u65E0\u6548");
            return;
        }
        // 获取格子节点
        var gridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (gridNode) {
            if (immediate) {
                // 立即隐藏，不播放动画
                gridNode.active = false;
            }
            else {
                // 播放四边形格子消失动画
                this.playGridFallAnimation(gridNode);
            }
        }
    };
    /**
     * 播放四边形格子消失动画
     * 效果：格子持续旋转，给一个随机向上的力，然后旋转着自由落体
     * @param gridNode 格子节点
     */
    SingleChessBoardController.prototype.playGridFallAnimation = function (gridNode) {
        if (!gridNode)
            return;
        // 停止该格子上所有正在进行的动画（包括震动动画）
        gridNode.stopAllActions();
        // 保存格子的原始位置（用于重置时恢复）
        if (!gridNode['originalPosition']) {
            gridNode['originalPosition'] = gridNode.getPosition();
        }
        // 保存原始的zIndex，用于动画结束后恢复
        var originalZIndex = gridNode.zIndex;
        // 设置更高的层级，确保下落的格子在数字预制体和其他元素之上
        gridNode.zIndex = 1000;
        // 随机选择向上的力的方向：0=向上，1=右上15度，2=左上15度
        var forceDirection = Math.floor(Math.random() * 3);
        var moveX = 0;
        var moveY = 200; // 向上的基础距离（增加高度）
        switch (forceDirection) {
            case 0: // 向上
                moveX = 0;
                break;
            case 1: // 右上15度
                moveX = Math.sin(20 * Math.PI / 180) * moveY;
                break;
            case 2: // 左上15度
                moveX = -Math.sin(20 * Math.PI / 180) * moveY;
                break;
        }
        // 随机旋转速度
        var rotationSpeed = (Math.random() * 1440 + 720) * (Math.random() > 0.5 ? 1 : -1); // 360-1080度/秒，随机方向
        // 动画参数
        var upTime = 0.15; // 向上运动时间
        var fallTime = 0.3; // 下落时间
        var initialPosition = gridNode.getPosition();
        // 创建持续旋转的动画
        var rotationTween = cc.tween(gridNode)
            .repeatForever(cc.tween().by(0.1, { angle: rotationSpeed * 0.1 }));
        // 创建分阶段的运动动画
        var movementTween = cc.tween(gridNode)
            // 第一阶段：向上抛出
            .to(upTime, {
            x: initialPosition.x + moveX,
            y: initialPosition.y + moveY
        }, { easing: 'quadOut' })
            // 第二阶段：自由落体
            .to(fallTime, {
            x: initialPosition.x + moveX + (Math.random() - 0.5) * 100,
            y: initialPosition.y - 500 // 下落到屏幕下方更远处
        }, { easing: 'quadIn' })
            .call(function () {
            // 动画结束后隐藏格子
            gridNode.active = false;
            // 停止旋转动画
            gridNode.stopAllActions();
            // 恢复原始的zIndex（虽然格子已经隐藏，但保持一致性）
            gridNode.zIndex = originalZIndex;
        });
        // 同时开始旋转和移动动画
        rotationTween.start();
        movementTween.start();
    };
    /**
     * 更新指定位置的neighborMines显示（使用boom数字预制体）- 参考联机版
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param neighborMines 周围地雷数量或结果类型
     */
    SingleChessBoardController.prototype.updateNeighborMinesDisplay = function (x, y, neighborMines) {
        if (neighborMines === "boom" || neighborMines === "mine") {
            // 踩到地雷，生成boom预制体并触发震动
            this.createBoomPrefab(x, y, true); // true表示是当前用户踩到的雷，需要震动
            // 设置标记，表示点到了炸弹
            this.hasBombExploded = true;
        }
        else if (typeof neighborMines === "number" && neighborMines > 0) {
            // 显示数字
            this.createNumberPrefab(x, y, neighborMines);
        }
        else {
            // 如果是0、"safe"或其他，则不显示任何预制体
        }
    };
    /**
     * 移除指定位置的biaoji预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     */
    SingleChessBoardController.prototype.removeBiaojiAt = function (x, y) {
        if (!this.isValidCoordinate(x, y)) {
            return;
        }
        var gridData = this.gridData[x][y];
        if (gridData.hasPlayer && gridData.playerNode && gridData.playerNode.name === "Biaoji") {
            // 播放消失动画
            cc.tween(gridData.playerNode)
                .to(0.2, { scaleX: 0, scaleY: 0 }, { easing: 'sineIn' })
                .call(function () {
                gridData.playerNode.removeFromParent();
                gridData.hasPlayer = false;
                gridData.playerNode = null;
            })
                .start();
        }
    };
    /**
     * 检查指定位置是否有biaoji预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @returns 是否有biaoji预制体
     */
    SingleChessBoardController.prototype.hasBiaojiAt = function (x, y) {
        if (!this.isValidCoordinate(x, y)) {
            return false;
        }
        var gridData = this.gridData[x][y];
        return gridData.hasPlayer && gridData.playerNode && gridData.playerNode.name === "Biaoji";
    };
    /**
     * 获取所有biaoji的位置
     * @returns biaoji位置数组
     */
    SingleChessBoardController.prototype.getAllBiaojiPositions = function () {
        var positions = [];
        if (!this.currentBoardConfig)
            return positions;
        for (var x = 0; x < this.currentBoardConfig.cols; x++) {
            for (var y = 0; y < this.currentBoardConfig.rows; y++) {
                if (this.hasBiaojiAt(x, y)) {
                    positions.push({ x: x, y: y });
                }
            }
        }
        return positions;
    };
    /**
     * 重置棋盘到初始状态
     */
    SingleChessBoardController.prototype.resetBoard = function () {
        var _this = this;
        // 清理所有延迟任务（重要：防止上一局的连锁动画影响新游戏）
        this.unscheduleAllCallbacks();
        // 重置炸弹爆炸状态
        this.resetBombExplodedStatus();
        // 清除所有预制体
        this.clearAllPrefabs();
        // 重新初始化棋盘数据
        this.reinitializeBoardData();
        // 显示所有格子
        this.showAllHiddenGrids();
        // 重新启用触摸事件
        this.scheduleOnce(function () {
            _this.enableTouchForExistingGrids();
        }, 0.1);
    };
    /**
     * 禁用所有格子的触摸事件（游戏结束时调用）
     */
    SingleChessBoardController.prototype.disableAllGridTouch = function () {
        if (!this.currentBoardConfig)
            return;
        for (var x = 0; x < this.currentBoardConfig.cols; x++) {
            for (var y = 0; y < this.currentBoardConfig.rows; y++) {
                var gridNode = this.gridNodes[x] && this.gridNodes[x][y];
                if (gridNode) {
                    gridNode.off(cc.Node.EventType.TOUCH_START);
                    gridNode.off(cc.Node.EventType.TOUCH_END);
                    gridNode.off(cc.Node.EventType.TOUCH_CANCEL);
                }
            }
        }
    };
    /**
     * 启用所有格子的触摸事件
     */
    SingleChessBoardController.prototype.enableAllGridTouch = function () {
        this.enableTouchForExistingGrids();
    };
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boomPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "biaojiPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boom1Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boom2Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boom3Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boom4Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boom5Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boom6Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boom7Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boom8Prefab", void 0);
    __decorate([
        property(cc.Node)
    ], SingleChessBoardController.prototype, "qipan8x8Node", void 0);
    __decorate([
        property(cc.Node)
    ], SingleChessBoardController.prototype, "qipan8x9Node", void 0);
    __decorate([
        property(cc.Node)
    ], SingleChessBoardController.prototype, "qipan9x9Node", void 0);
    __decorate([
        property(cc.Node)
    ], SingleChessBoardController.prototype, "qipan9x10Node", void 0);
    __decorate([
        property(cc.Node)
    ], SingleChessBoardController.prototype, "qipan10x10Node", void 0);
    SingleChessBoardController = __decorate([
        ccclass
    ], SingleChessBoardController);
    return SingleChessBoardController;
}(cc.Component));
exports.default = SingleChessBoardController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0cy9zY3JpcHRzL2dhbWUvQ2hlc3MvU2luZ2xlQ2hlc3NCb2FyZENvbnRyb2xsZXIudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLG9CQUFvQjtBQUNwQiw0RUFBNEU7QUFDNUUsbUJBQW1CO0FBQ25CLHNGQUFzRjtBQUN0Riw4QkFBOEI7QUFDOUIsc0ZBQXNGOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFdEYsK0RBQThEO0FBQzlELGlEQUFnRDtBQUUxQyxJQUFBLEtBQXNCLEVBQUUsQ0FBQyxVQUFVLEVBQWxDLE9BQU8sYUFBQSxFQUFFLFFBQVEsY0FBaUIsQ0FBQztBQXFCMUMsU0FBUztBQUNULElBQU0sYUFBYSxHQUFtQztJQUNsRCxLQUFLLEVBQUU7UUFDSCxLQUFLLEVBQUUsR0FBRztRQUNWLE1BQU0sRUFBRSxHQUFHO1FBQ1gsSUFBSSxFQUFFLENBQUM7UUFDUCxJQUFJLEVBQUUsQ0FBQztRQUNQLFNBQVMsRUFBRSxFQUFFO1FBQ2IsVUFBVSxFQUFFLEVBQUU7S0FDakI7SUFDRCxLQUFLLEVBQUU7UUFDSCxLQUFLLEVBQUUsR0FBRztRQUNWLE1BQU0sRUFBRSxHQUFHO1FBQ1gsSUFBSSxFQUFFLENBQUM7UUFDUCxJQUFJLEVBQUUsQ0FBQztRQUNQLFNBQVMsRUFBRSxFQUFFO1FBQ2IsVUFBVSxFQUFFLEVBQUU7S0FDakI7SUFDRCxLQUFLLEVBQUU7UUFDSCxLQUFLLEVBQUUsR0FBRztRQUNWLE1BQU0sRUFBRSxHQUFHO1FBQ1gsSUFBSSxFQUFFLENBQUM7UUFDUCxJQUFJLEVBQUUsQ0FBQztRQUNQLFNBQVMsRUFBRSxFQUFFO1FBQ2IsVUFBVSxFQUFFLEVBQUU7S0FDakI7SUFDRCxNQUFNLEVBQUU7UUFDSixLQUFLLEVBQUUsR0FBRztRQUNWLE1BQU0sRUFBRSxHQUFHO1FBQ1gsSUFBSSxFQUFFLEVBQUU7UUFDUixJQUFJLEVBQUUsQ0FBQztRQUNQLFNBQVMsRUFBRSxFQUFFO1FBQ2IsVUFBVSxFQUFFLEVBQUU7S0FDakI7SUFDRCxPQUFPLEVBQUU7UUFDTCxLQUFLLEVBQUUsR0FBRztRQUNWLE1BQU0sRUFBRSxHQUFHO1FBQ1gsSUFBSSxFQUFFLEVBQUU7UUFDUixJQUFJLEVBQUUsRUFBRTtRQUNSLFNBQVMsRUFBRSxFQUFFO1FBQ2IsVUFBVSxFQUFFLEVBQUU7S0FDakI7Q0FDSixDQUFDO0FBR0Y7SUFBd0QsOENBQVk7SUFBcEU7UUFBQSxxRUEwOUNDO1FBdjlDRyxnQkFBVSxHQUFjLElBQUksQ0FBQyxDQUFFLFVBQVU7UUFHekMsa0JBQVksR0FBYyxJQUFJLENBQUMsQ0FBRSxZQUFZO1FBRzdDLGlCQUFXLEdBQWMsSUFBSSxDQUFDLENBQUUsU0FBUztRQUd6QyxpQkFBVyxHQUFjLElBQUksQ0FBQyxDQUFFLFNBQVM7UUFHekMsaUJBQVcsR0FBYyxJQUFJLENBQUMsQ0FBRSxTQUFTO1FBR3pDLGlCQUFXLEdBQWMsSUFBSSxDQUFDLENBQUUsU0FBUztRQUd6QyxpQkFBVyxHQUFjLElBQUksQ0FBQyxDQUFFLFNBQVM7UUFHekMsaUJBQVcsR0FBYyxJQUFJLENBQUMsQ0FBRSxTQUFTO1FBR3pDLGlCQUFXLEdBQWMsSUFBSSxDQUFDLENBQUUsU0FBUztRQUd6QyxpQkFBVyxHQUFjLElBQUksQ0FBQyxDQUFFLFNBQVM7UUFFekMsU0FBUztRQUVULGtCQUFZLEdBQVksSUFBSSxDQUFDLENBQUUsVUFBVTtRQUd6QyxrQkFBWSxHQUFZLElBQUksQ0FBQyxDQUFFLFVBQVU7UUFHekMsa0JBQVksR0FBWSxJQUFJLENBQUMsQ0FBRSxVQUFVO1FBR3pDLG1CQUFhLEdBQVksSUFBSSxDQUFDLENBQUUsV0FBVztRQUczQyxvQkFBYyxHQUFZLElBQUksQ0FBQyxDQUFFLFlBQVk7UUFFN0MsWUFBWTtRQUNKLHNCQUFnQixHQUFZLElBQUksQ0FBQztRQUV6QyxTQUFTO1FBQ0Qsd0JBQWtCLEdBQWdCLElBQUksQ0FBQztRQUN2QyxzQkFBZ0IsR0FBVyxLQUFLLENBQUMsQ0FBRSxVQUFVO1FBRXJELFNBQVM7UUFDRCxxQkFBZSxHQUFZLEtBQUssQ0FBQztRQUV6QyxTQUFTO1FBQ0QsY0FBUSxHQUF1QixFQUFFLENBQUMsQ0FBRSxhQUFhO1FBQ2pELGVBQVMsR0FBZ0IsRUFBRSxDQUFDLENBQUUsYUFBYTtRQUVuRCxVQUFVO1FBQ0YsbUJBQWEsR0FBVyxDQUFDLENBQUM7UUFDMUIsdUJBQWlCLEdBQVcsRUFBRSxDQUFDO1FBQ3RCLG9CQUFjLEdBQUcsR0FBRyxDQUFDLENBQUMsWUFBWTs7SUF5NUN2RCxDQUFDO0lBdjVDRywyQ0FBTSxHQUFOO1FBQ0ksMkJBQTJCO0lBQy9CLENBQUM7SUFFRCwwQ0FBSyxHQUFMO1FBQ0ksbUNBQW1DO1FBQ25DLHdCQUF3QjtJQUM1QixDQUFDO0lBRUQ7OztPQUdHO0lBQ0ssdURBQWtCLEdBQTFCLFVBQTJCLFNBQWlCO1FBQ3hDLFFBQVEsU0FBUyxFQUFFO1lBQ2YsS0FBSyxLQUFLO2dCQUNOLE9BQU8sSUFBSSxDQUFDLFlBQVksQ0FBQztZQUM3QixLQUFLLEtBQUs7Z0JBQ04sT0FBTyxJQUFJLENBQUMsWUFBWSxDQUFDO1lBQzdCLEtBQUssS0FBSztnQkFDTixPQUFPLElBQUksQ0FBQyxZQUFZLENBQUM7WUFDN0IsS0FBSyxNQUFNO2dCQUNQLE9BQU8sSUFBSSxDQUFDLGFBQWEsQ0FBQztZQUM5QixLQUFLLE9BQU87Z0JBQ1IsT0FBTyxJQUFJLENBQUMsY0FBYyxDQUFDO1lBQy9CO2dCQUNJLE9BQU8sSUFBSSxDQUFDO1NBQ25CO0lBQ0wsQ0FBQztJQUVEOzs7T0FHRztJQUNJLDhDQUFTLEdBQWhCLFVBQWlCLFNBQWlCO1FBQzlCLElBQUksQ0FBQyxhQUFhLENBQUMsU0FBUyxDQUFDLEVBQUU7WUFDM0IsT0FBTyxDQUFDLEtBQUssQ0FBQyx1REFBYSxTQUFXLENBQUMsQ0FBQztZQUN4QyxPQUFPO1NBQ1Y7UUFFRCxnQkFBZ0I7UUFDaEIsSUFBSSxDQUFDLGdCQUFnQixHQUFHLElBQUksQ0FBQyxrQkFBa0IsQ0FBQyxTQUFTLENBQUMsQ0FBQztRQUMzRCxJQUFJLENBQUMsSUFBSSxDQUFDLGdCQUFnQixFQUFFO1lBQ3hCLE9BQU8sQ0FBQyxLQUFLLENBQUMsK0VBQWlCLFNBQVcsQ0FBQyxDQUFDO1lBQzVDLE9BQU87U0FDVjtRQUVELElBQUksQ0FBQyxnQkFBZ0IsR0FBRyxTQUFTLENBQUM7UUFDbEMsSUFBSSxDQUFDLGtCQUFrQixHQUFHLGFBQWEsQ0FBQyxTQUFTLENBQUMsQ0FBQztRQUVuRCxTQUFTO1FBQ1QsSUFBSSxDQUFDLFFBQVEsR0FBRyxFQUFFLENBQUM7UUFDbkIsSUFBSSxDQUFDLFNBQVMsR0FBRyxFQUFFLENBQUM7UUFFcEIsVUFBVTtRQUNWLEtBQUssSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsR0FBRyxJQUFJLENBQUMsa0JBQWtCLENBQUMsSUFBSSxFQUFFLENBQUMsRUFBRSxFQUFFO1lBQ25ELElBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLEdBQUcsRUFBRSxDQUFDO1lBQ3RCLElBQUksQ0FBQyxTQUFTLENBQUMsQ0FBQyxDQUFDLEdBQUcsRUFBRSxDQUFDO1lBQ3ZCLEtBQUssSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsR0FBRyxJQUFJLENBQUMsa0JBQWtCLENBQUMsSUFBSSxFQUFFLENBQUMsRUFBRSxFQUFFO2dCQUNuRCxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHO29CQUNsQixDQUFDLEVBQUUsQ0FBQztvQkFDSixDQUFDLEVBQUUsQ0FBQztvQkFDSixRQUFRLEVBQUUsSUFBSSxDQUFDLG9CQUFvQixDQUFDLENBQUMsRUFBRSxDQUFDLENBQUM7b0JBQ3pDLFNBQVMsRUFBRSxLQUFLO2lCQUNuQixDQUFDO2FBQ0w7U0FDSjtRQUVELElBQUksQ0FBQyxlQUFlLEVBQUUsQ0FBQztJQUMzQixDQUFDO0lBRUQsY0FBYztJQUNOLG9EQUFlLEdBQXZCO1FBQ0ksSUFBSSxDQUFDLElBQUksQ0FBQyxnQkFBZ0IsRUFBRTtZQUN4QixPQUFPLENBQUMsS0FBSyxDQUFDLFVBQVUsQ0FBQyxDQUFDO1lBQzFCLE9BQU87U0FDVjtRQUVELG9CQUFvQjtRQUNwQixJQUFJLENBQUMsMkJBQTJCLEVBQUUsQ0FBQztJQUN2QyxDQUFDO0lBRUQsY0FBYztJQUNOLGdFQUEyQixHQUFuQztRQUNJLGFBQWE7UUFDYixJQUFJLENBQUMsSUFBSSxDQUFDLGdCQUFnQixFQUFFO1lBQ3hCLE9BQU8sQ0FBQyxLQUFLLENBQUMsbUJBQW1CLENBQUMsQ0FBQztZQUNuQyxPQUFPO1NBQ1Y7UUFFRCxlQUFlO1FBQ2YsSUFBSSxRQUFRLEdBQUcsSUFBSSxDQUFDLGdCQUFnQixDQUFDLFFBQVEsQ0FBQztRQUU5QyxLQUFLLElBQUksQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLEdBQUcsUUFBUSxDQUFDLE1BQU0sRUFBRSxDQUFDLEVBQUUsRUFBRTtZQUN0QyxJQUFJLEtBQUssR0FBRyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFFeEIsY0FBYztZQUNkLElBQUksTUFBTSxHQUFHLElBQUksQ0FBQywyQkFBMkIsQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLENBQUM7WUFDMUQsSUFBSSxNQUFNLEVBQUU7Z0JBQ1IsSUFBSSxDQUFDLG9CQUFvQixDQUFDLEtBQUssRUFBRSxNQUFNLENBQUMsQ0FBQyxFQUFFLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQztnQkFDckQsSUFBSSxDQUFDLFNBQVMsQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDLEdBQUcsSUFBSSxDQUFDLFNBQVMsQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDLElBQUksRUFBRSxDQUFDO2dCQUMxRCxJQUFJLENBQUMsU0FBUyxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDLEdBQUcsS0FBSyxDQUFDO2FBQzlDO2lCQUFNO2dCQUNILG9CQUFvQjtnQkFDcEIsSUFBSSxHQUFHLEdBQUcsS0FBSyxDQUFDLFdBQVcsRUFBRSxDQUFDO2dCQUM5QixJQUFJLFFBQU0sR0FBRyxJQUFJLENBQUMsNkJBQTZCLENBQUMsR0FBRyxDQUFDLENBQUM7Z0JBQ3JELElBQUksUUFBTSxFQUFFO29CQUNSLElBQUksQ0FBQyxvQkFBb0IsQ0FBQyxLQUFLLEVBQUUsUUFBTSxDQUFDLENBQUMsRUFBRSxRQUFNLENBQUMsQ0FBQyxDQUFDLENBQUM7b0JBQ3JELElBQUksQ0FBQyxTQUFTLENBQUMsUUFBTSxDQUFDLENBQUMsQ0FBQyxHQUFHLElBQUksQ0FBQyxTQUFTLENBQUMsUUFBTSxDQUFDLENBQUMsQ0FBQyxJQUFJLEVBQUUsQ0FBQztvQkFDMUQsSUFBSSxDQUFDLFNBQVMsQ0FBQyxRQUFNLENBQUMsQ0FBQyxDQUFDLENBQUMsUUFBTSxDQUFDLENBQUMsQ0FBQyxHQUFHLEtBQUssQ0FBQztpQkFDOUM7YUFDSjtTQUNKO0lBQ0wsQ0FBQztJQUVELGNBQWM7SUFDTixnRUFBMkIsR0FBbkMsVUFBb0MsUUFBZ0I7UUFDaEQsbUJBQW1CO1FBQ25CLElBQUksS0FBSyxHQUFHLFFBQVEsQ0FBQyxLQUFLLENBQUMsa0JBQWtCLENBQUMsQ0FBQztRQUMvQyxJQUFJLEtBQUssRUFBRTtZQUNQLE9BQU8sRUFBQyxDQUFDLEVBQUUsUUFBUSxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxRQUFRLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUMsQ0FBQztTQUN6RDtRQUNELE9BQU8sSUFBSSxDQUFDO0lBQ2hCLENBQUM7SUFFRCwyQkFBMkI7SUFDbkIsa0VBQTZCLEdBQXJDLFVBQXNDLEdBQVk7UUFDOUMsSUFBSSxDQUFDLElBQUksQ0FBQyxrQkFBa0I7WUFBRSxPQUFPLElBQUksQ0FBQztRQUUxQyxvQkFBb0I7UUFDcEIsUUFBUSxJQUFJLENBQUMsZ0JBQWdCLEVBQUU7WUFDM0IsS0FBSyxLQUFLO2dCQUNOLE9BQU8sSUFBSSxDQUFDLG1DQUFtQyxDQUFDLEdBQUcsQ0FBQyxDQUFDO1lBQ3pELEtBQUssS0FBSztnQkFDTixPQUFPLElBQUksQ0FBQyxtQ0FBbUMsQ0FBQyxHQUFHLENBQUMsQ0FBQztZQUN6RCxLQUFLLE1BQU07Z0JBQ1AsT0FBTyxJQUFJLENBQUMsb0NBQW9DLENBQUMsR0FBRyxDQUFDLENBQUM7WUFDMUQsS0FBSyxPQUFPO2dCQUNSLE9BQU8sSUFBSSxDQUFDLHFDQUFxQyxDQUFDLEdBQUcsQ0FBQyxDQUFDO1lBQzNEO2dCQUNJLG9CQUFvQjtnQkFDcEIsSUFBSSxDQUFDLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLEdBQUcsSUFBSSxDQUFDLGtCQUFrQixDQUFDLEtBQUssR0FBRyxDQUFDLENBQUMsR0FBRyxJQUFJLENBQUMsa0JBQWtCLENBQUMsU0FBUyxDQUFDLENBQUM7Z0JBQ3BHLElBQUksQ0FBQyxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxHQUFHLElBQUksQ0FBQyxrQkFBa0IsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxDQUFDLEdBQUcsSUFBSSxDQUFDLGtCQUFrQixDQUFDLFVBQVUsQ0FBQyxDQUFDO2dCQUV0RyxJQUFJLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUU7b0JBQzlCLE9BQU8sRUFBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUMsQ0FBQztpQkFDdkI7Z0JBQ0QsT0FBTyxJQUFJLENBQUM7U0FDbkI7SUFDTCxDQUFDO0lBRUQsa0NBQWtDO0lBQzFCLHdFQUFtQyxHQUEzQyxVQUE0QyxHQUFZO1FBQ3BELG9CQUFvQjtRQUNwQixJQUFNLE1BQU0sR0FBRyxDQUFDLEdBQUcsQ0FBQyxDQUFFLFNBQVM7UUFDL0IsSUFBTSxNQUFNLEdBQUcsQ0FBQyxHQUFHLENBQUMsQ0FBRSxTQUFTO1FBQy9CLElBQU0sS0FBSyxHQUFHLEtBQUssQ0FBQyxDQUFFLFVBQVU7UUFDaEMsSUFBTSxLQUFLLEdBQUcsTUFBTSxDQUFDLENBQUMsVUFBVTtRQUVoQyxVQUFVO1FBQ1YsSUFBSSxDQUFDLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLEdBQUcsTUFBTSxDQUFDLEdBQUcsS0FBSyxDQUFDLENBQUM7UUFDN0MsSUFBSSxDQUFDLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLEdBQUcsTUFBTSxDQUFDLEdBQUcsS0FBSyxDQUFDLENBQUM7UUFJN0MsSUFBSSxJQUFJLENBQUMsaUJBQWlCLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFO1lBQzlCLE9BQU8sRUFBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUMsQ0FBQztTQUN2QjtRQUNELE9BQU8sSUFBSSxDQUFDO0lBQ2hCLENBQUM7SUFFRCxrQ0FBa0M7SUFDMUIsd0VBQW1DLEdBQTNDLFVBQTRDLEdBQVk7UUFDcEQsb0JBQW9CO1FBQ3BCLElBQU0sTUFBTSxHQUFHLENBQUMsR0FBRyxDQUFDLENBQUUsU0FBUztRQUMvQixJQUFNLE1BQU0sR0FBRyxDQUFDLEdBQUcsQ0FBQyxDQUFFLFNBQVM7UUFDL0IsSUFBTSxLQUFLLEdBQUcsS0FBSyxDQUFDLENBQUUsVUFBVTtRQUNoQyxJQUFNLEtBQUssR0FBRyxNQUFNLENBQUMsQ0FBQyxVQUFVO1FBRWhDLFVBQVU7UUFDVixJQUFJLENBQUMsR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsR0FBRyxNQUFNLENBQUMsR0FBRyxLQUFLLENBQUMsQ0FBQztRQUM3QyxJQUFJLENBQUMsR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsR0FBRyxNQUFNLENBQUMsR0FBRyxLQUFLLENBQUMsQ0FBQztRQUk3QyxJQUFJLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUU7WUFDOUIsT0FBTyxFQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBQyxDQUFDO1NBQ3ZCO1FBQ0QsT0FBTyxJQUFJLENBQUM7SUFDaEIsQ0FBQztJQUVELG1DQUFtQztJQUMzQix5RUFBb0MsR0FBNUMsVUFBNkMsR0FBWTtRQUNyRCxvQkFBb0I7UUFDcEIsSUFBTSxNQUFNLEdBQUcsQ0FBQyxHQUFHLENBQUMsQ0FBRSxTQUFTO1FBQy9CLElBQU0sTUFBTSxHQUFHLENBQUMsR0FBRyxDQUFDLENBQUUsU0FBUztRQUMvQixJQUFNLEtBQUssR0FBRyxFQUFFLENBQUMsQ0FBSyxVQUFVO1FBQ2hDLElBQU0sS0FBSyxHQUFHLEtBQUssQ0FBQyxDQUFFLFVBQVU7UUFFaEMsVUFBVTtRQUNWLElBQUksQ0FBQyxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxHQUFHLE1BQU0sQ0FBQyxHQUFHLEtBQUssQ0FBQyxDQUFDO1FBQzdDLElBQUksQ0FBQyxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxHQUFHLE1BQU0sQ0FBQyxHQUFHLEtBQUssQ0FBQyxDQUFDO1FBSTdDLElBQUksSUFBSSxDQUFDLGlCQUFpQixDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRTtZQUM5QixPQUFPLEVBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFDLENBQUM7U0FDdkI7UUFDRCxPQUFPLElBQUksQ0FBQztJQUNoQixDQUFDO0lBRUQsb0NBQW9DO0lBQzVCLDBFQUFxQyxHQUE3QyxVQUE4QyxHQUFZO1FBQ3RELG9CQUFvQjtRQUNwQixJQUFNLE1BQU0sR0FBRyxDQUFDLEdBQUcsQ0FBQyxDQUFFLFNBQVM7UUFDL0IsSUFBTSxNQUFNLEdBQUcsQ0FBQyxHQUFHLENBQUMsQ0FBRSxTQUFTO1FBQy9CLElBQU0sS0FBSyxHQUFHLEtBQUssQ0FBQyxDQUFFLFVBQVU7UUFDaEMsSUFBTSxLQUFLLEdBQUcsRUFBRSxDQUFDLENBQUssVUFBVTtRQUVoQyxVQUFVO1FBQ1YsSUFBSSxDQUFDLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLEdBQUcsTUFBTSxDQUFDLEdBQUcsS0FBSyxDQUFDLENBQUM7UUFDN0MsSUFBSSxDQUFDLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLEdBQUcsTUFBTSxDQUFDLEdBQUcsS0FBSyxDQUFDLENBQUM7UUFJN0MsSUFBSSxJQUFJLENBQUMsaUJBQWlCLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFO1lBRTlCLE9BQU8sRUFBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUMsQ0FBQztTQUN2QjthQUFNO1lBRUgsT0FBTyxJQUFJLENBQUM7U0FDZjtJQUNMLENBQUM7SUFFRCxZQUFZO0lBQ0oseURBQW9CLEdBQTVCLFVBQTZCLFFBQWlCLEVBQUUsQ0FBUyxFQUFFLENBQVM7UUFBcEUsaUJBbUZDO1FBbEZHLG9CQUFvQjtRQUNwQixRQUFRLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLFdBQVcsQ0FBQyxDQUFDO1FBQzVDLFFBQVEsQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsU0FBUyxDQUFDLENBQUM7UUFDMUMsUUFBUSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxZQUFZLENBQUMsQ0FBQztRQUU3QyxTQUFTO1FBQ1QsSUFBSSxjQUFjLEdBQUcsS0FBSyxDQUFDO1FBQzNCLElBQUksY0FBYyxHQUFHLENBQUMsQ0FBQztRQUN2QixJQUFJLGlCQUFpQixHQUFhLElBQUksQ0FBQztRQUN2QyxJQUFJLHFCQUFxQixHQUFHLEtBQUssQ0FBQyxDQUFDLFlBQVk7UUFDL0MsSUFBTSxlQUFlLEdBQUcsR0FBRyxDQUFDLENBQUMsU0FBUztRQUV0QyxTQUFTO1FBQ1QsUUFBUSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxXQUFXLEVBQUUsVUFBQyxNQUEyQjtZQUNuRSxjQUFjLEdBQUcsSUFBSSxDQUFDO1lBQ3RCLGNBQWMsR0FBRyxDQUFDLENBQUM7WUFDbkIscUJBQXFCLEdBQUcsS0FBSyxDQUFDLENBQUMsU0FBUztZQUl4QyxTQUFTO1lBQ1QsaUJBQWlCLEdBQUc7Z0JBQ2hCLElBQUksY0FBYyxFQUFFO29CQUNoQixjQUFjLElBQUksR0FBRyxDQUFDO29CQUN0QixJQUFJLGNBQWMsSUFBSSxlQUFlLElBQUksQ0FBQyxxQkFBcUIsRUFBRTt3QkFFN0QscUJBQXFCLEdBQUcsSUFBSSxDQUFDLENBQUMsVUFBVTt3QkFDeEMsY0FBYyxHQUFHLEtBQUssQ0FBQyxDQUFDLHVCQUF1Qjt3QkFFL0MsU0FBUzt3QkFDVCxLQUFJLENBQUMsZUFBZSxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQzt3QkFFM0IsU0FBUzt3QkFDVCxJQUFJLGlCQUFpQixFQUFFOzRCQUNuQixLQUFJLENBQUMsVUFBVSxDQUFDLGlCQUFpQixDQUFDLENBQUM7NEJBQ25DLGlCQUFpQixHQUFHLElBQUksQ0FBQzt5QkFDNUI7cUJBQ0o7aUJBQ0o7WUFDTCxDQUFDLENBQUM7WUFDRixLQUFJLENBQUMsUUFBUSxDQUFDLGlCQUFpQixFQUFFLEdBQUcsQ0FBQyxDQUFDO1FBQzFDLENBQUMsRUFBRSxJQUFJLENBQUMsQ0FBQztRQUVULFNBQVM7UUFDVCxRQUFRLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLFNBQVMsRUFBRSxVQUFDLEtBQTBCO1lBR2hFLFNBQVM7WUFDVCxJQUFJLGlCQUFpQixFQUFFO2dCQUNuQixLQUFJLENBQUMsVUFBVSxDQUFDLGlCQUFpQixDQUFDLENBQUM7Z0JBQ25DLGlCQUFpQixHQUFHLElBQUksQ0FBQzthQUM1QjtZQUVELDZCQUE2QjtZQUM3QixJQUFNLGtCQUFrQixHQUFHLGNBQWM7Z0JBQ2hCLGNBQWMsR0FBRyxlQUFlO2dCQUNoQyxDQUFDLHFCQUFxQixDQUFDO1lBRWhELElBQUksa0JBQWtCLEVBQUU7Z0JBRXBCLEtBQUksQ0FBQyxXQUFXLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxLQUFLLENBQUMsQ0FBQzthQUNqQztpQkFBTTthQUVOO1lBRUQsU0FBUztZQUNULGNBQWMsR0FBRyxLQUFLLENBQUM7WUFDdkIscUJBQXFCLEdBQUcsS0FBSyxDQUFDO1FBQ2xDLENBQUMsRUFBRSxJQUFJLENBQUMsQ0FBQztRQUVULFNBQVM7UUFDVCxRQUFRLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLFlBQVksRUFBRSxVQUFDLE1BQTJCO1lBR3BFLFNBQVM7WUFDVCxjQUFjLEdBQUcsS0FBSyxDQUFDO1lBQ3ZCLHFCQUFxQixHQUFHLEtBQUssQ0FBQztZQUM5QixJQUFJLGlCQUFpQixFQUFFO2dCQUNuQixLQUFJLENBQUMsVUFBVSxDQUFDLGlCQUFpQixDQUFDLENBQUM7Z0JBQ25DLGlCQUFpQixHQUFHLElBQUksQ0FBQzthQUM1QjtRQUNMLENBQUMsRUFBRSxJQUFJLENBQUMsQ0FBQztJQUNiLENBQUM7SUFFRCx5QkFBeUI7SUFDakIseURBQW9CLEdBQTVCLFVBQTZCLENBQVMsRUFBRSxDQUFTO1FBQzdDLElBQUksQ0FBQyxJQUFJLENBQUMsa0JBQWtCO1lBQUUsT0FBTyxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQztRQUVqRCxZQUFZO1FBQ1osMEJBQTBCO1FBQzFCLElBQUksSUFBSSxHQUFHLENBQUMsQ0FBQyxHQUFHLElBQUksQ0FBQyxrQkFBa0IsQ0FBQyxTQUFTLENBQUMsR0FBRyxDQUFDLElBQUksQ0FBQyxrQkFBa0IsQ0FBQyxTQUFTLEdBQUcsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUMsa0JBQWtCLENBQUMsS0FBSyxHQUFHLENBQUMsQ0FBQyxDQUFDO1FBQ25JLElBQUksSUFBSSxHQUFHLENBQUMsQ0FBQyxHQUFHLElBQUksQ0FBQyxrQkFBa0IsQ0FBQyxVQUFVLENBQUMsR0FBRyxDQUFDLElBQUksQ0FBQyxrQkFBa0IsQ0FBQyxVQUFVLEdBQUcsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUMsa0JBQWtCLENBQUMsTUFBTSxHQUFHLENBQUMsQ0FBQyxDQUFDO1FBRXRJLE9BQU8sRUFBRSxDQUFDLEVBQUUsQ0FBQyxJQUFJLEVBQUUsSUFBSSxDQUFDLENBQUM7SUFDN0IsQ0FBQztJQUVELGtCQUFrQjtJQUNWLGdEQUFXLEdBQW5CLFVBQW9CLENBQVMsRUFBRSxDQUFTLEVBQUUsTUFBNEI7UUFDbEUsV0FBVztRQUNYLElBQUksQ0FBQyxJQUFJLENBQUMsaUJBQWlCLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFO1lBQy9CLE9BQU87U0FDVjtRQUVELDRCQUE0QjtRQUM1QixJQUFJLElBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsU0FBUyxFQUFFO1lBRS9CLE9BQU87U0FDVjtRQUVELHNCQUFzQjtRQUV0QixJQUFJLENBQUMsbUJBQW1CLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQztJQUN0QyxDQUFDO0lBRUQsOEJBQThCO0lBQ3RCLG9EQUFlLEdBQXZCLFVBQXdCLENBQVMsRUFBRSxDQUFTO1FBR3hDLFdBQVc7UUFDWCxJQUFJLENBQUMsSUFBSSxDQUFDLGlCQUFpQixDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRTtZQUUvQixPQUFPO1NBQ1Y7UUFFRCxzQkFBc0I7UUFDdEIsSUFBSSxJQUFJLENBQUMsV0FBVyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRTtZQUN4QiwyQkFBMkI7WUFFM0IsSUFBSSxDQUFDLGNBQWMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7WUFFMUIsNkJBQTZCO1lBRTdCLElBQUksQ0FBQyxtQkFBbUIsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDO1NBQ3JDO2FBQU0sSUFBSSxDQUFDLElBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsU0FBUyxFQUFFO1lBQ3ZDLDhCQUE4QjtZQUU5QixJQUFJLENBQUMsa0JBQWtCLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDO1lBRTlCLDJCQUEyQjtZQUUzQixJQUFJLENBQUMsbUJBQW1CLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQztTQUNyQzthQUFNO1lBQ0gsNkJBQTZCO1NBRWhDO0lBQ0wsQ0FBQztJQUVELHNCQUFzQjtJQUNkLHdEQUFtQixHQUEzQixVQUE0QixDQUFTLEVBQUUsQ0FBUyxFQUFFLE1BQWM7UUFDNUQsVUFBVTtRQUNWLElBQU0sV0FBVyxHQUFHLElBQUksQ0FBQyxHQUFHLEVBQUUsQ0FBQztRQUMvQixJQUFNLFdBQVcsR0FBTSxDQUFDLFNBQUksQ0FBQyxTQUFJLE1BQVEsQ0FBQztRQUUxQyxJQUFJLFdBQVcsR0FBRyxJQUFJLENBQUMsYUFBYSxHQUFHLElBQUksQ0FBQyxjQUFjLElBQUksSUFBSSxDQUFDLGlCQUFpQixLQUFLLFdBQVcsRUFBRTtZQUVsRyxPQUFPO1NBQ1Y7UUFFRCxJQUFJLENBQUMsYUFBYSxHQUFHLFdBQVcsQ0FBQztRQUNqQyxJQUFJLENBQUMsaUJBQWlCLEdBQUcsV0FBVyxDQUFDO1FBRXJDLElBQU0sU0FBUyxHQUFHO1lBQ2QsQ0FBQyxFQUFFLENBQUM7WUFDSixDQUFDLEVBQUUsQ0FBQztZQUNKLE1BQU0sRUFBRSxNQUFNLENBQUMscUJBQXFCO1NBQ3ZDLENBQUM7UUFHRixtQ0FBZ0IsQ0FBQyxXQUFXLEVBQUUsQ0FBQyxPQUFPLENBQUMscUJBQVMsQ0FBQyxzQkFBc0IsRUFBRSxTQUFTLENBQUMsQ0FBQztJQUN4RixDQUFDO0lBRUQsV0FBVztJQUNILHNEQUFpQixHQUF6QixVQUEwQixDQUFTLEVBQUUsQ0FBUztRQUMxQyxJQUFJLENBQUMsSUFBSSxDQUFDLGtCQUFrQjtZQUFFLE9BQU8sS0FBSyxDQUFDO1FBQzNDLE9BQU8sQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLEdBQUcsSUFBSSxDQUFDLGtCQUFrQixDQUFDLElBQUksSUFBSSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsR0FBRyxJQUFJLENBQUMsa0JBQWtCLENBQUMsSUFBSSxDQUFDO0lBQ3BHLENBQUM7SUFFRDs7OztPQUlHO0lBQ0ksdURBQWtCLEdBQXpCLFVBQTBCLENBQVMsRUFBRSxDQUFTO1FBQzFDLElBQUksQ0FBQyxJQUFJLENBQUMsWUFBWSxFQUFFO1lBQ3BCLE9BQU8sQ0FBQyxLQUFLLENBQUMsOEJBQThCLENBQUMsQ0FBQztZQUM5QyxPQUFPO1NBQ1Y7UUFFRCxlQUFlO1FBQ2YsSUFBTSxVQUFVLEdBQUcsRUFBRSxDQUFDLFdBQVcsQ0FBQyxJQUFJLENBQUMsWUFBWSxDQUFDLENBQUM7UUFDckQsVUFBVSxDQUFDLElBQUksR0FBRyxRQUFRLENBQUM7UUFFM0IsT0FBTztRQUNQLElBQU0sUUFBUSxHQUFHLElBQUksQ0FBQyx1QkFBdUIsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7UUFDcEQsVUFBVSxDQUFDLFdBQVcsQ0FBQyxRQUFRLENBQUMsQ0FBQztRQUVqQyxRQUFRO1FBQ1IsSUFBSSxDQUFDLGdCQUFnQixDQUFDLFFBQVEsQ0FBQyxVQUFVLENBQUMsQ0FBQztRQUUzQyx3QkFBd0I7UUFDeEIsSUFBTSxXQUFXLEdBQUcsSUFBSSxDQUFDLGdCQUFnQixLQUFLLE9BQU8sQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUM7UUFDbEUsVUFBVSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUN2QixFQUFFLENBQUMsS0FBSyxDQUFDLFVBQVUsQ0FBQzthQUNmLEVBQUUsQ0FBQyxHQUFHLEVBQUUsRUFBRSxNQUFNLEVBQUUsV0FBVyxFQUFFLE1BQU0sRUFBRSxXQUFXLEVBQUUsRUFBRSxFQUFFLE1BQU0sRUFBRSxTQUFTLEVBQUUsQ0FBQzthQUM1RSxLQUFLLEVBQUUsQ0FBQztRQUViLFNBQVM7UUFDVCxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLFNBQVMsR0FBRyxJQUFJLENBQUM7UUFDckMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxVQUFVLEdBQUcsVUFBVSxDQUFDO0lBR2hELENBQUM7SUFFRDs7Ozs7T0FLRztJQUNJLHFEQUFnQixHQUF2QixVQUF3QixDQUFTLEVBQUUsQ0FBUyxFQUFFLGFBQTZCO1FBQTdCLDhCQUFBLEVBQUEsb0JBQTZCO1FBR3ZFLElBQUksQ0FBQyxJQUFJLENBQUMsVUFBVSxFQUFFO1lBQ2xCLE9BQU8sQ0FBQyxLQUFLLENBQUMsd0RBQXdELENBQUMsQ0FBQztZQUN4RSxPQUFPO1NBQ1Y7UUFJRCxhQUFhO1FBQ2IsSUFBTSxRQUFRLEdBQUcsRUFBRSxDQUFDLFdBQVcsQ0FBQyxJQUFJLENBQUMsVUFBVSxDQUFDLENBQUM7UUFDakQsUUFBUSxDQUFDLElBQUksR0FBRyxNQUFNLENBQUM7UUFFdkIsT0FBTztRQUNQLElBQU0sUUFBUSxHQUFHLElBQUksQ0FBQyx1QkFBdUIsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7UUFDcEQsUUFBUSxDQUFDLFdBQVcsQ0FBQyxRQUFRLENBQUMsQ0FBQztRQUUvQixRQUFRO1FBQ1IsSUFBSSxDQUFDLGdCQUFnQixDQUFDLFFBQVEsQ0FBQyxRQUFRLENBQUMsQ0FBQztRQUV6Qyx3QkFBd0I7UUFDeEIsSUFBTSxXQUFXLEdBQUcsSUFBSSxDQUFDLGdCQUFnQixLQUFLLE9BQU8sQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUM7UUFDbEUsSUFBTSxXQUFXLEdBQUcsV0FBVyxHQUFHLEdBQUcsQ0FBQyxDQUFDLGdCQUFnQjtRQUN2RCxRQUFRLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQ3JCLEVBQUUsQ0FBQyxLQUFLLENBQUMsUUFBUSxDQUFDO2FBQ2IsRUFBRSxDQUFDLEdBQUcsRUFBRSxFQUFFLE1BQU0sRUFBRSxXQUFXLEVBQUUsTUFBTSxFQUFFLFdBQVcsRUFBRSxFQUFFLEVBQUUsTUFBTSxFQUFFLFNBQVMsRUFBRSxDQUFDO2FBQzVFLEVBQUUsQ0FBQyxHQUFHLEVBQUUsRUFBRSxNQUFNLEVBQUUsV0FBVyxFQUFFLE1BQU0sRUFBRSxXQUFXLEVBQUUsQ0FBQzthQUNyRCxLQUFLLEVBQUUsQ0FBQztRQUViLHNCQUFzQjtRQUN0QixJQUFJLGFBQWEsRUFBRTtZQUVmLElBQUksQ0FBQyx1QkFBdUIsRUFBRSxDQUFDO1NBQ2xDO1FBRUQsU0FBUztRQUNULElBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsU0FBUyxHQUFHLElBQUksQ0FBQztRQUNyQyxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLFVBQVUsR0FBRyxRQUFRLENBQUM7SUFDOUMsQ0FBQztJQUVEOzs7OztPQUtHO0lBQ0ksdURBQWtCLEdBQXpCLFVBQTBCLENBQVMsRUFBRSxDQUFTLEVBQUUsTUFBYztRQUMxRCxJQUFJLE1BQU0sR0FBRyxDQUFDLElBQUksTUFBTSxHQUFHLENBQUMsRUFBRTtZQUMxQixPQUFPLENBQUMsS0FBSyxDQUFDLHFDQUFVLE1BQVEsQ0FBQyxDQUFDO1lBQ2xDLE9BQU87U0FDVjtRQUVELGFBQWE7UUFDYixJQUFNLE1BQU0sR0FBRyxJQUFJLENBQUMsZUFBZSxDQUFDLE1BQU0sQ0FBQyxDQUFDO1FBQzVDLElBQUksQ0FBQyxNQUFNLEVBQUU7WUFDVCxPQUFPLENBQUMsS0FBSyxDQUFDLGlCQUFLLE1BQU0seUNBQVEsQ0FBQyxDQUFDO1lBQ25DLE9BQU87U0FDVjtRQUVELFdBQVc7UUFDWCxJQUFNLFVBQVUsR0FBRyxFQUFFLENBQUMsV0FBVyxDQUFDLE1BQU0sQ0FBQyxDQUFDO1FBQzFDLFVBQVUsQ0FBQyxJQUFJLEdBQUcsU0FBTyxNQUFRLENBQUM7UUFFbEMsT0FBTztRQUNQLElBQU0sUUFBUSxHQUFHLElBQUksQ0FBQyx1QkFBdUIsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7UUFDcEQsVUFBVSxDQUFDLFdBQVcsQ0FBQyxRQUFRLENBQUMsQ0FBQztRQUVqQyxRQUFRO1FBQ1IsSUFBSSxDQUFDLGdCQUFnQixDQUFDLFFBQVEsQ0FBQyxVQUFVLENBQUMsQ0FBQztRQUUzQyx3QkFBd0I7UUFDeEIsSUFBTSxXQUFXLEdBQUcsSUFBSSxDQUFDLGdCQUFnQixLQUFLLE9BQU8sQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUM7UUFDbEUsVUFBVSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUN2QixFQUFFLENBQUMsS0FBSyxDQUFDLFVBQVUsQ0FBQzthQUNmLEVBQUUsQ0FBQyxHQUFHLEVBQUUsRUFBRSxNQUFNLEVBQUUsV0FBVyxFQUFFLE1BQU0sRUFBRSxXQUFXLEVBQUUsRUFBRSxFQUFFLE1BQU0sRUFBRSxTQUFTLEVBQUUsQ0FBQzthQUM1RSxLQUFLLEVBQUUsQ0FBQztJQUNqQixDQUFDO0lBRUQ7Ozs7OztPQU1HO0lBQ0ksdURBQWtCLEdBQXpCLFVBQTBCLENBQVMsRUFBRSxDQUFTLEVBQUUsTUFBaUIsRUFBRSxJQUE2QjtRQUE3QixxQkFBQSxFQUFBLHFCQUE2QjtRQUU1RixJQUFJLENBQUMsTUFBTSxFQUFFO1lBQ1QsT0FBTyxDQUFDLEtBQUssQ0FBQyxRQUFRLENBQUMsQ0FBQztZQUN4QixPQUFPLElBQUksQ0FBQztTQUNmO1FBRUQsSUFBSSxDQUFDLElBQUksQ0FBQyxnQkFBZ0IsRUFBRTtZQUN4QixPQUFPLENBQUMsS0FBSyxDQUFDLDhCQUE4QixDQUFDLENBQUM7WUFDOUMsT0FBTyxJQUFJLENBQUM7U0FDZjtRQUVELElBQUk7WUFDQSxTQUFTO1lBRVQsSUFBTSxVQUFVLEdBQUcsRUFBRSxDQUFDLFdBQVcsQ0FBQyxNQUFNLENBQUMsQ0FBQztZQUMxQyxVQUFVLENBQUMsSUFBSSxHQUFHLElBQUksQ0FBQztZQUd2QixPQUFPO1lBRVAsSUFBTSxRQUFRLEdBQUcsSUFBSSxDQUFDLHVCQUF1QixDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQztZQUVwRCxVQUFVLENBQUMsV0FBVyxDQUFDLFFBQVEsQ0FBQyxDQUFDO1lBRWpDLFFBQVE7WUFFUixJQUFJLENBQUMsZ0JBQWdCLENBQUMsUUFBUSxDQUFDLFVBQVUsQ0FBQyxDQUFDO1lBRzNDLHdCQUF3QjtZQUN4QixJQUFNLFdBQVcsR0FBRyxJQUFJLENBQUMsZ0JBQWdCLEtBQUssT0FBTyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQztZQUVsRSxVQUFVLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQ3ZCLEVBQUUsQ0FBQyxLQUFLLENBQUMsVUFBVSxDQUFDO2lCQUNmLEVBQUUsQ0FBQyxHQUFHLEVBQUUsRUFBRSxNQUFNLEVBQUUsV0FBVyxHQUFHLEdBQUcsRUFBRSxNQUFNLEVBQUUsV0FBVyxHQUFHLEdBQUcsRUFBRSxFQUFFLEVBQUUsTUFBTSxFQUFFLFNBQVMsRUFBRSxDQUFDO2lCQUN4RixFQUFFLENBQUMsR0FBRyxFQUFFLEVBQUUsTUFBTSxFQUFFLFdBQVcsRUFBRSxNQUFNLEVBQUUsV0FBVyxFQUFFLENBQUM7aUJBQ3JELEtBQUssRUFBRSxDQUFDO1lBR2IsT0FBTyxVQUFVLENBQUM7U0FDckI7UUFBQyxPQUFPLEtBQUssRUFBRTtZQUNaLEVBQUUsQ0FBQyxLQUFLLENBQUMsa0JBQWtCLEVBQUUsS0FBSyxDQUFDLENBQUM7WUFDcEMsT0FBTyxJQUFJLENBQUM7U0FDZjtJQUNMLENBQUM7SUFFRCxVQUFVO0lBQ0Ysb0RBQWUsR0FBdkIsVUFBd0IsTUFBYztRQUNsQyxRQUFRLE1BQU0sRUFBRTtZQUNaLEtBQUssQ0FBQyxDQUFDLENBQUMsT0FBTyxJQUFJLENBQUMsV0FBVyxDQUFDO1lBQ2hDLEtBQUssQ0FBQyxDQUFDLENBQUMsT0FBTyxJQUFJLENBQUMsV0FBVyxDQUFDO1lBQ2hDLEtBQUssQ0FBQyxDQUFDLENBQUMsT0FBTyxJQUFJLENBQUMsV0FBVyxDQUFDO1lBQ2hDLEtBQUssQ0FBQyxDQUFDLENBQUMsT0FBTyxJQUFJLENBQUMsV0FBVyxDQUFDO1lBQ2hDLEtBQUssQ0FBQyxDQUFDLENBQUMsT0FBTyxJQUFJLENBQUMsV0FBVyxDQUFDO1lBQ2hDLEtBQUssQ0FBQyxDQUFDLENBQUMsT0FBTyxJQUFJLENBQUMsV0FBVyxDQUFDO1lBQ2hDLEtBQUssQ0FBQyxDQUFDLENBQUMsT0FBTyxJQUFJLENBQUMsV0FBVyxDQUFDO1lBQ2hDLEtBQUssQ0FBQyxDQUFDLENBQUMsT0FBTyxJQUFJLENBQUMsV0FBVyxDQUFDO1lBQ2hDLE9BQU8sQ0FBQyxDQUFDLE9BQU8sSUFBSSxDQUFDO1NBQ3hCO0lBQ0wsQ0FBQztJQUVEOzs7OztPQUtHO0lBQ0ssNERBQXVCLEdBQS9CLFVBQWdDLENBQVMsRUFBRSxDQUFTO1FBQ2hELElBQUksQ0FBQyxJQUFJLENBQUMsa0JBQWtCLEVBQUU7WUFDMUIsT0FBTyxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQztTQUN0QjtRQUVELG9CQUFvQjtRQUNwQixRQUFRLElBQUksQ0FBQyxnQkFBZ0IsRUFBRTtZQUMzQixLQUFLLEtBQUs7Z0JBQ04sT0FBTyxJQUFJLENBQUMsNkJBQTZCLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDO1lBQ3BELEtBQUssS0FBSztnQkFDTixPQUFPLElBQUksQ0FBQyw2QkFBNkIsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7WUFDcEQsS0FBSyxLQUFLO2dCQUNOLE9BQU8sSUFBSSxDQUFDLDZCQUE2QixDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQztZQUNwRCxLQUFLLE1BQU07Z0JBQ1AsT0FBTyxJQUFJLENBQUMsOEJBQThCLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDO1lBQ3JELEtBQUssT0FBTztnQkFDUixPQUFPLElBQUksQ0FBQywrQkFBK0IsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7WUFDdEQ7Z0JBQ0ksT0FBTyxJQUFJLENBQUMsb0JBQW9CLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDO1NBQzlDO0lBQ0wsQ0FBQztJQUVELHVCQUF1QjtJQUNmLGtFQUE2QixHQUFyQyxVQUFzQyxDQUFTLEVBQUUsQ0FBUztRQUN0RCxnQkFBZ0I7UUFDaEIsdUJBQXVCO1FBQ3ZCLGlDQUFpQztRQUNqQyxpQ0FBaUM7UUFDakMscUJBQXFCO1FBQ3JCLElBQU0sTUFBTSxHQUFHLENBQUMsR0FBRyxDQUFDLENBQUUsUUFBUTtRQUM5QixJQUFNLE1BQU0sR0FBRyxDQUFDLEdBQUcsQ0FBQyxDQUFFLFFBQVE7UUFDOUIsSUFBTSxLQUFLLEdBQUcsRUFBRSxDQUFDLENBQUssUUFBUTtRQUM5QixJQUFNLEtBQUssR0FBRyxFQUFFLENBQUMsQ0FBSyxRQUFRO1FBRTlCLElBQU0sTUFBTSxHQUFHLE1BQU0sR0FBRyxDQUFDLENBQUMsR0FBRyxLQUFLLENBQUMsQ0FBQztRQUNwQyxJQUFNLE1BQU0sR0FBRyxNQUFNLEdBQUcsQ0FBQyxDQUFDLEdBQUcsS0FBSyxDQUFDLENBQUM7UUFFcEMsT0FBTyxFQUFFLENBQUMsRUFBRSxDQUFDLE1BQU0sRUFBRSxNQUFNLENBQUMsQ0FBQztJQUNqQyxDQUFDO0lBRUQsa0NBQWtDO0lBQzFCLGtFQUE2QixHQUFyQyxVQUFzQyxDQUFTLEVBQUUsQ0FBUztRQUN0RCxhQUFhO1FBQ2Isd0JBQXdCO1FBQ3hCLHVCQUF1QjtRQUN2Qix1QkFBdUI7UUFDdkIsc0JBQXNCO1FBRXRCLFFBQVE7UUFDUiw2Q0FBNkM7UUFDN0MsOENBQThDO1FBRTlDLElBQU0sTUFBTSxHQUFHLENBQUMsR0FBRyxDQUFDLENBQUUsU0FBUztRQUMvQixJQUFNLE1BQU0sR0FBRyxDQUFDLEdBQUcsQ0FBQyxDQUFFLFNBQVM7UUFDL0IsSUFBTSxLQUFLLEdBQUcsS0FBSyxDQUFDLENBQUUsVUFBVTtRQUNoQyxJQUFNLEtBQUssR0FBRyxNQUFNLENBQUMsQ0FBQyxVQUFVO1FBRWhDLElBQU0sTUFBTSxHQUFHLE1BQU0sR0FBRyxDQUFDLENBQUMsR0FBRyxLQUFLLENBQUMsQ0FBQztRQUNwQyxJQUFNLE1BQU0sR0FBRyxNQUFNLEdBQUcsQ0FBQyxDQUFDLEdBQUcsS0FBSyxDQUFDLENBQUM7UUFHcEMsT0FBTyxFQUFFLENBQUMsRUFBRSxDQUFDLE1BQU0sRUFBRSxNQUFNLENBQUMsQ0FBQztJQUNqQyxDQUFDO0lBRUQsa0NBQWtDO0lBQzFCLGtFQUE2QixHQUFyQyxVQUFzQyxDQUFTLEVBQUUsQ0FBUztRQUN0RCxhQUFhO1FBQ2Isd0JBQXdCO1FBQ3hCLHVCQUF1QjtRQUN2Qix1QkFBdUI7UUFDdkIsc0JBQXNCO1FBRXRCLFFBQVE7UUFDUiw2Q0FBNkM7UUFDN0MsOENBQThDO1FBRTlDLElBQU0sTUFBTSxHQUFHLENBQUMsR0FBRyxDQUFDLENBQUUsU0FBUztRQUMvQixJQUFNLE1BQU0sR0FBRyxDQUFDLEdBQUcsQ0FBQyxDQUFFLFNBQVM7UUFDL0IsSUFBTSxLQUFLLEdBQUcsS0FBSyxDQUFDLENBQUUsVUFBVTtRQUNoQyxJQUFNLEtBQUssR0FBRyxNQUFNLENBQUMsQ0FBQyxVQUFVO1FBRWhDLElBQU0sTUFBTSxHQUFHLE1BQU0sR0FBRyxDQUFDLENBQUMsR0FBRyxLQUFLLENBQUMsQ0FBQztRQUNwQyxJQUFNLE1BQU0sR0FBRyxNQUFNLEdBQUcsQ0FBQyxDQUFDLEdBQUcsS0FBSyxDQUFDLENBQUM7UUFHcEMsT0FBTyxFQUFFLENBQUMsRUFBRSxDQUFDLE1BQU0sRUFBRSxNQUFNLENBQUMsQ0FBQztJQUNqQyxDQUFDO0lBRUQsbUNBQW1DO0lBQzNCLG1FQUE4QixHQUF0QyxVQUF1QyxDQUFTLEVBQUUsQ0FBUztRQUN2RCxhQUFhO1FBQ2Isd0JBQXdCO1FBQ3hCLHVCQUF1QjtRQUN2Qix1QkFBdUI7UUFDdkIsc0JBQXNCO1FBRXRCLFFBQVE7UUFDUiwwQ0FBMEM7UUFDMUMsNkNBQTZDO1FBRTdDLElBQU0sTUFBTSxHQUFHLENBQUMsR0FBRyxDQUFDLENBQUUsU0FBUztRQUMvQixJQUFNLE1BQU0sR0FBRyxDQUFDLEdBQUcsQ0FBQyxDQUFFLFNBQVM7UUFDL0IsSUFBTSxLQUFLLEdBQUcsRUFBRSxDQUFDLENBQUssVUFBVTtRQUNoQyxJQUFNLEtBQUssR0FBRyxLQUFLLENBQUMsQ0FBRSxVQUFVO1FBRWhDLElBQU0sTUFBTSxHQUFHLE1BQU0sR0FBRyxDQUFDLENBQUMsR0FBRyxLQUFLLENBQUMsQ0FBQztRQUNwQyxJQUFNLE1BQU0sR0FBRyxNQUFNLEdBQUcsQ0FBQyxDQUFDLEdBQUcsS0FBSyxDQUFDLENBQUM7UUFHcEMsT0FBTyxFQUFFLENBQUMsRUFBRSxDQUFDLE1BQU0sRUFBRSxNQUFNLENBQUMsQ0FBQztJQUNqQyxDQUFDO0lBRUQsb0NBQW9DO0lBQzVCLG9FQUErQixHQUF2QyxVQUF3QyxDQUFTLEVBQUUsQ0FBUztRQUN4RCxhQUFhO1FBQ2Isd0JBQXdCO1FBQ3hCLHVCQUF1QjtRQUN2Qix1QkFBdUI7UUFDdkIsc0JBQXNCO1FBRXRCLFFBQVE7UUFDUiw2Q0FBNkM7UUFDN0MsMENBQTBDO1FBRTFDLElBQU0sTUFBTSxHQUFHLENBQUMsR0FBRyxDQUFDLENBQUUsU0FBUztRQUMvQixJQUFNLE1BQU0sR0FBRyxDQUFDLEdBQUcsQ0FBQyxDQUFFLFNBQVM7UUFDL0IsSUFBTSxLQUFLLEdBQUcsS0FBSyxDQUFDLENBQUUsVUFBVTtRQUNoQyxJQUFNLEtBQUssR0FBRyxFQUFFLENBQUMsQ0FBSyxVQUFVO1FBRWhDLElBQU0sTUFBTSxHQUFHLE1BQU0sR0FBRyxDQUFDLENBQUMsR0FBRyxLQUFLLENBQUMsQ0FBQztRQUNwQyxJQUFNLE1BQU0sR0FBRyxNQUFNLEdBQUcsQ0FBQyxDQUFDLEdBQUcsS0FBSyxDQUFDLENBQUM7UUFHcEMsT0FBTyxFQUFFLENBQUMsRUFBRSxDQUFDLE1BQU0sRUFBRSxNQUFNLENBQUMsQ0FBQztJQUNqQyxDQUFDO0lBRUQ7O09BRUc7SUFDSyw0REFBdUIsR0FBL0I7UUFDSSxJQUFJLENBQUMsSUFBSSxDQUFDLGdCQUFnQjtZQUFFLE9BQU87UUFFbkMsZ0JBQWdCO1FBQ2hCLElBQU0sY0FBYyxHQUFHLEVBQUUsQ0FBQyxDQUFDLE9BQU87UUFDbEMsSUFBTSxhQUFhLEdBQUcsR0FBRyxDQUFDLENBQUMsU0FBUztRQUNwQyxJQUFNLGNBQWMsR0FBRyxFQUFFLENBQUMsQ0FBQyxPQUFPO1FBRWxDLE9BQU87UUFDUCxJQUFJLENBQUMsY0FBYyxDQUFDLGNBQWMsRUFBRSxhQUFhLEVBQUUsY0FBYyxDQUFDLENBQUM7UUFFbkUsVUFBVTtRQUNWLElBQUksQ0FBQyxhQUFhLENBQUMsY0FBYyxHQUFHLEdBQUcsRUFBRSxhQUFhLEVBQUUsY0FBYyxDQUFDLENBQUM7SUFDNUUsQ0FBQztJQUVEOztPQUVHO0lBQ0ssbURBQWMsR0FBdEIsVUFBdUIsU0FBaUIsRUFBRSxRQUFnQixFQUFFLFNBQWlCO1FBQ3pFLFNBQVM7UUFDVCxJQUFNLGdCQUFnQixHQUFHLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxRQUFRLENBQUMsS0FBSyxFQUFFLENBQUM7UUFFaEUsZ0JBQWdCO1FBQ2hCLElBQUksZ0JBQWdCLEdBQUcsU0FBUyxDQUFDO1FBQ2pDLElBQU0sY0FBYyxHQUFHLElBQUksQ0FBQyxDQUFDLFNBQVM7UUFFdEMsSUFBTSxlQUFlLEdBQUcsVUFBQyxjQUFzQjtZQUMzQyxPQUFPLEVBQUUsQ0FBQyxLQUFLLEVBQUU7aUJBQ1osRUFBRSxDQUFDLEtBQUssRUFBRTtnQkFDUCxDQUFDLEVBQUUsZ0JBQWdCLENBQUMsQ0FBQyxHQUFHLENBQUMsSUFBSSxDQUFDLE1BQU0sRUFBRSxHQUFHLEdBQUcsQ0FBQyxHQUFHLGNBQWMsR0FBRyxDQUFDO2dCQUNsRSxDQUFDLEVBQUUsZ0JBQWdCLENBQUMsQ0FBQyxHQUFHLENBQUMsSUFBSSxDQUFDLE1BQU0sRUFBRSxHQUFHLEdBQUcsQ0FBQyxHQUFHLGNBQWMsR0FBRyxDQUFDO2FBQ3JFLENBQUMsQ0FBQztRQUNYLENBQUMsQ0FBQztRQUVGLGdCQUFnQjtRQUNoQixJQUFJLFVBQVUsR0FBRyxFQUFFLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDO1FBQ2pELElBQU0sVUFBVSxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsUUFBUSxHQUFHLFNBQVMsQ0FBQyxDQUFDO1FBRXBELEtBQUssSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsR0FBRyxVQUFVLEVBQUUsQ0FBQyxFQUFFLEVBQUU7WUFDakMsVUFBVSxHQUFHLFVBQVUsQ0FBQyxJQUFJLENBQUMsZUFBZSxDQUFDLGdCQUFnQixDQUFDLENBQUMsQ0FBQztZQUNoRSxnQkFBZ0IsSUFBSSxjQUFjLENBQUMsQ0FBQyxXQUFXO1NBQ2xEO1FBRUQsV0FBVztRQUNYLFVBQVUsQ0FBQyxFQUFFLENBQUMsR0FBRyxFQUFFO1lBQ2YsQ0FBQyxFQUFFLGdCQUFnQixDQUFDLENBQUM7WUFDckIsQ0FBQyxFQUFFLGdCQUFnQixDQUFDLENBQUM7U0FDeEIsRUFBRSxFQUFFLE1BQU0sRUFBRSxTQUFTLEVBQUUsQ0FBQzthQUN4QixLQUFLLEVBQUUsQ0FBQztJQUNiLENBQUM7SUFFRDs7T0FFRztJQUNLLGtEQUFhLEdBQXJCLFVBQXNCLFNBQWlCLEVBQUUsUUFBZ0IsRUFBRSxTQUFpQjtRQUN4RSxJQUFJLENBQUMsSUFBSSxDQUFDLFNBQVM7WUFBRSxPQUFPO1FBRTVCLFdBQVc7UUFDWCxLQUFLLElBQUksQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLEdBQUcsSUFBSSxDQUFDLFNBQVMsQ0FBQyxNQUFNLEVBQUUsQ0FBQyxFQUFFLEVBQUU7WUFDNUMsSUFBSSxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsQ0FBQyxDQUFDO2dCQUFFLFNBQVM7WUFFakMsS0FBSyxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxHQUFHLElBQUksQ0FBQyxTQUFTLENBQUMsQ0FBQyxDQUFDLENBQUMsTUFBTSxFQUFFLENBQUMsRUFBRSxFQUFFO2dCQUMvQyxJQUFNLFFBQVEsR0FBRyxJQUFJLENBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO2dCQUN0QyxJQUFJLENBQUMsUUFBUSxJQUFJLENBQUMsUUFBUSxDQUFDLE1BQU07b0JBQUUsU0FBUztnQkFFNUMsaUJBQWlCO2dCQUNqQixJQUFJLENBQUMsYUFBYSxDQUFDLFFBQVEsRUFBRSxTQUFTLEVBQUUsUUFBUSxFQUFFLFNBQVMsQ0FBQyxDQUFDO2FBQ2hFO1NBQ0o7SUFDTCxDQUFDO0lBRUQ7O09BRUc7SUFDSyxrREFBYSxHQUFyQixVQUFzQixRQUFpQixFQUFFLFNBQWlCLEVBQUUsUUFBZ0IsRUFBRSxTQUFpQjtRQUMzRixTQUFTO1FBQ1QsSUFBTSxnQkFBZ0IsR0FBRyxRQUFRLENBQUMsUUFBUSxDQUFDLEtBQUssRUFBRSxDQUFDO1FBRW5ELHFCQUFxQjtRQUNyQixJQUFNLFdBQVcsR0FBRyxJQUFJLENBQUMsTUFBTSxFQUFFLEdBQUcsR0FBRyxDQUFDO1FBRXhDLElBQUksQ0FBQyxZQUFZLENBQUM7WUFDZCxnQkFBZ0I7WUFDaEIsSUFBSSxnQkFBZ0IsR0FBRyxTQUFTLENBQUM7WUFDakMsSUFBTSxjQUFjLEdBQUcsSUFBSSxDQUFDLENBQUMsYUFBYTtZQUUxQyxJQUFNLG1CQUFtQixHQUFHLFVBQUMsY0FBc0I7Z0JBQy9DLE9BQU8sRUFBRSxDQUFDLEtBQUssRUFBRTtxQkFDWixFQUFFLENBQUMsSUFBSSxFQUFFO29CQUNOLENBQUMsRUFBRSxnQkFBZ0IsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUMsTUFBTSxFQUFFLEdBQUcsR0FBRyxDQUFDLEdBQUcsY0FBYyxHQUFHLENBQUM7b0JBQ2xFLENBQUMsRUFBRSxnQkFBZ0IsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUMsTUFBTSxFQUFFLEdBQUcsR0FBRyxDQUFDLEdBQUcsY0FBYyxHQUFHLENBQUM7aUJBQ3JFLENBQUMsQ0FBQztZQUNYLENBQUMsQ0FBQztZQUVGLFNBQVM7WUFDVCxJQUFJLFVBQVUsR0FBRyxFQUFFLENBQUMsS0FBSyxDQUFDLFFBQVEsQ0FBQyxDQUFDO1lBQ3BDLElBQU0sVUFBVSxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsUUFBUSxHQUFHLFNBQVMsR0FBRyxHQUFHLENBQUMsQ0FBQyxDQUFDLFdBQVc7WUFFdEUsS0FBSyxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxHQUFHLFVBQVUsRUFBRSxDQUFDLEVBQUUsRUFBRTtnQkFDakMsVUFBVSxHQUFHLFVBQVUsQ0FBQyxJQUFJLENBQUMsbUJBQW1CLENBQUMsZ0JBQWdCLENBQUMsQ0FBQyxDQUFDO2dCQUNwRSxnQkFBZ0IsSUFBSSxjQUFjLENBQUM7YUFDdEM7WUFFRCxXQUFXO1lBQ1gsVUFBVSxDQUFDLEVBQUUsQ0FBQyxJQUFJLEVBQUU7Z0JBQ2hCLENBQUMsRUFBRSxnQkFBZ0IsQ0FBQyxDQUFDO2dCQUNyQixDQUFDLEVBQUUsZ0JBQWdCLENBQUMsQ0FBQzthQUN4QixFQUFFLEVBQUUsTUFBTSxFQUFFLFNBQVMsRUFBRSxDQUFDO2lCQUN4QixLQUFLLEVBQUUsQ0FBQztRQUNiLENBQUMsRUFBRSxXQUFXLENBQUMsQ0FBQztJQUNwQixDQUFDO0lBRUQ7O09BRUc7SUFDSSx1REFBa0IsR0FBekI7UUFDSSxJQUFJLENBQUMsSUFBSSxDQUFDLGdCQUFnQixFQUFFO1lBQ3hCLE9BQU8sQ0FBQyxLQUFLLENBQUMsbUJBQW1CLENBQUMsQ0FBQztZQUNuQyxPQUFPO1NBQ1Y7UUFFRCxzQkFBc0I7UUFDdEIsS0FBSyxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxHQUFHLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxRQUFRLENBQUMsTUFBTSxFQUFFLENBQUMsRUFBRSxFQUFFO1lBQzVELElBQU0sS0FBSyxHQUFHLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFFaEQsV0FBVztZQUNYLElBQUksS0FBSyxDQUFDLElBQUksQ0FBQyxVQUFVLENBQUMsT0FBTyxDQUFDLElBQUksS0FBSyxDQUFDLElBQUksS0FBSyxPQUFPLEVBQUU7Z0JBQzFELGdCQUFnQjtnQkFDaEIsS0FBSyxDQUFDLGNBQWMsRUFBRSxDQUFDO2dCQUV2QixTQUFTO2dCQUNULEtBQUssQ0FBQyxNQUFNLEdBQUcsSUFBSSxDQUFDO2dCQUNwQixLQUFLLENBQUMsT0FBTyxHQUFHLEdBQUcsQ0FBQztnQkFDcEIsS0FBSyxDQUFDLE1BQU0sR0FBRyxDQUFDLENBQUM7Z0JBQ2pCLEtBQUssQ0FBQyxNQUFNLEdBQUcsQ0FBQyxDQUFDO2dCQUNqQixLQUFLLENBQUMsS0FBSyxHQUFHLENBQUMsQ0FBQyxDQUFDLFNBQVM7Z0JBRTFCLGtCQUFrQjtnQkFDbEIsSUFBSSxLQUFLLENBQUMsa0JBQWtCLENBQUMsRUFBRTtvQkFDM0IsS0FBSyxDQUFDLFdBQVcsQ0FBQyxLQUFLLENBQUMsa0JBQWtCLENBQUMsQ0FBQyxDQUFDO2lCQUNoRDtnQkFFRCxXQUFXO2dCQUNYLElBQU0sTUFBTSxHQUFHLEtBQUssQ0FBQyxZQUFZLENBQUMsRUFBRSxDQUFDLE1BQU0sQ0FBQyxDQUFDO2dCQUM3QyxJQUFJLE1BQU0sRUFBRTtvQkFDUixNQUFNLENBQUMsT0FBTyxHQUFHLElBQUksQ0FBQztpQkFDekI7YUFDSjtTQUNKO0lBQ0wsQ0FBQztJQUVEOztPQUVHO0lBQ0ksb0RBQWUsR0FBdEI7UUFDSSxJQUFJLENBQUMsSUFBSSxDQUFDLGdCQUFnQixFQUFFO1lBQ3hCLE9BQU8sQ0FBQyxLQUFLLENBQUMsa0JBQWtCLENBQUMsQ0FBQztZQUNsQyxPQUFPO1NBQ1Y7UUFFRCxJQUFNLGdCQUFnQixHQUFjLEVBQUUsQ0FBQztRQUV2QyxhQUFhO1FBQ2IsS0FBSyxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxHQUFHLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxRQUFRLENBQUMsTUFBTSxFQUFFLENBQUMsRUFBRSxFQUFFO1lBQzVELElBQU0sS0FBSyxHQUFHLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFFaEQsbUJBQW1CO1lBQ25CLElBQUksS0FBSyxDQUFDLElBQUksS0FBSyxRQUFRLElBQUksS0FBSyxDQUFDLElBQUksS0FBSyxNQUFNLElBQUksS0FBSyxDQUFDLElBQUksQ0FBQyxVQUFVLENBQUMsTUFBTSxDQUFDLEVBQUU7Z0JBQ25GLGdCQUFnQixDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQzthQUNoQztTQUNKO1FBRUQsV0FBVztRQUNYLGdCQUFnQixDQUFDLE9BQU8sQ0FBQyxVQUFBLEtBQUs7WUFDMUIsS0FBSyxDQUFDLGdCQUFnQixFQUFFLENBQUM7UUFDN0IsQ0FBQyxDQUFDLENBQUM7UUFFSCxTQUFTO1FBQ1QsSUFBSSxDQUFDLHFCQUFxQixFQUFFLENBQUM7SUFDakMsQ0FBQztJQUVEOztPQUVHO0lBQ0ssMERBQXFCLEdBQTdCO1FBQ0ksSUFBSSxDQUFDLElBQUksQ0FBQyxrQkFBa0I7WUFBRSxPQUFPO1FBSXJDLG9CQUFvQjtRQUNwQixLQUFLLElBQUksQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLEdBQUcsSUFBSSxDQUFDLGtCQUFrQixDQUFDLElBQUksRUFBRSxDQUFDLEVBQUUsRUFBRTtZQUNuRCxLQUFLLElBQUksQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLEdBQUcsSUFBSSxDQUFDLGtCQUFrQixDQUFDLElBQUksRUFBRSxDQUFDLEVBQUUsRUFBRTtnQkFDbkQsSUFBSSxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxJQUFJLElBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUU7b0JBQ3pDLElBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsU0FBUyxHQUFHLEtBQUssQ0FBQztvQkFDdEMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxVQUFVLEdBQUcsSUFBSSxDQUFDO2lCQUN6QzthQUNKO1NBQ0o7SUFDTCxDQUFDO0lBRUQ7O09BRUc7SUFDSSxzREFBaUIsR0FBeEI7UUFDSSxJQUFJLENBQUMsa0JBQWtCLEVBQUUsQ0FBQztRQUMxQixJQUFJLENBQUMsZUFBZSxFQUFFLENBQUM7SUFDM0IsQ0FBQztJQUVEOzs7T0FHRztJQUNJLCtEQUEwQixHQUFqQyxVQUFrQyxTQUFjO1FBQzVDLE9BQU8sQ0FBQyxHQUFHLENBQUMsMkNBQTJDLENBQUMsQ0FBQztRQUV6RCxTQUFTO1FBQ1QsSUFBSSxDQUFDLGVBQWUsRUFBRSxDQUFDO1FBQ3ZCLElBQUksQ0FBQyxrQkFBa0IsRUFBRSxDQUFDO1FBRTFCLG1CQUFtQjtRQUNuQixJQUFJLFNBQVMsQ0FBQyxPQUFPLEVBQUU7WUFDbkIsSUFBSSxDQUFDLGlCQUFpQixDQUFDLFNBQVMsQ0FBQyxPQUFPLENBQUMsQ0FBQztTQUM3QztJQUNMLENBQUM7SUFFRDs7O09BR0c7SUFDSyxzREFBaUIsR0FBekIsVUFBMEIsT0FBWTtRQUF0QyxpQkFxQ0M7UUFwQ0csT0FBTyxDQUFDLEdBQUcsQ0FBQyxvQ0FBb0MsRUFBRSxPQUFPLENBQUMsQ0FBQztRQUUzRCxXQUFXO1FBQ1gsSUFBSSxPQUFPLENBQUMsY0FBYyxJQUFJLEtBQUssQ0FBQyxPQUFPLENBQUMsT0FBTyxDQUFDLGNBQWMsQ0FBQyxFQUFFO1lBQ2pFLE9BQU8sQ0FBQyxjQUFjLENBQUMsT0FBTyxDQUFDLFVBQUMsS0FBVTtnQkFDdEMsSUFBTSxDQUFDLEdBQUcsS0FBSyxDQUFDLENBQUMsQ0FBQztnQkFDbEIsSUFBTSxDQUFDLEdBQUcsS0FBSyxDQUFDLENBQUMsQ0FBQztnQkFDbEIsSUFBTSxhQUFhLEdBQUcsS0FBSyxDQUFDLGFBQWEsQ0FBQztnQkFFMUMsSUFBSSxLQUFJLENBQUMsaUJBQWlCLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFO29CQUM5QixPQUFPO29CQUNQLEtBQUksQ0FBQyxVQUFVLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDO29CQUV0QixTQUFTO29CQUNULElBQUksYUFBYSxHQUFHLENBQUMsRUFBRTt3QkFDbkIsS0FBSSxDQUFDLGtCQUFrQixDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsYUFBYSxDQUFDLENBQUM7cUJBQ2hEO29CQUVELFdBQVc7b0JBQ1gsS0FBSSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxTQUFTLEdBQUcsSUFBSSxDQUFDO2lCQUN4QztZQUNMLENBQUMsQ0FBQyxDQUFDO1NBQ047UUFFRCxXQUFXO1FBQ1gsSUFBSSxPQUFPLENBQUMsWUFBWSxJQUFJLEtBQUssQ0FBQyxPQUFPLENBQUMsT0FBTyxDQUFDLFlBQVksQ0FBQyxFQUFFO1lBQzdELE9BQU8sQ0FBQyxZQUFZLENBQUMsT0FBTyxDQUFDLFVBQUMsS0FBVTtnQkFDcEMsSUFBTSxDQUFDLEdBQUcsS0FBSyxDQUFDLENBQUMsQ0FBQztnQkFDbEIsSUFBTSxDQUFDLEdBQUcsS0FBSyxDQUFDLENBQUMsQ0FBQztnQkFFbEIsSUFBSSxLQUFJLENBQUMsaUJBQWlCLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFO29CQUM5QixVQUFVO29CQUNWLEtBQUksQ0FBQyxrQkFBa0IsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7aUJBQ2pDO1lBQ0wsQ0FBQyxDQUFDLENBQUM7U0FDTjtJQUNMLENBQUM7SUFFRDs7O09BR0c7SUFDSSxtREFBYyxHQUFyQjtRQUdJLGtCQUFrQjtRQUNsQiwwQkFBMEI7UUFDMUIsc0NBQXNDO0lBQzFDLENBQUM7SUFFRDs7O09BR0c7SUFDSSw4REFBeUIsR0FBaEM7UUFFSSxPQUFPLElBQUksQ0FBQyxlQUFlLENBQUM7SUFDaEMsQ0FBQztJQUVEOztPQUVHO0lBQ0ksNERBQXVCLEdBQTlCO1FBRUksSUFBSSxDQUFDLGVBQWUsR0FBRyxLQUFLLENBQUM7SUFDakMsQ0FBQztJQUVEOztPQUVHO0lBQ0ksK0NBQVUsR0FBakIsVUFBa0IsQ0FBUyxFQUFFLENBQVM7UUFDbEMsSUFBSSxDQUFDLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUU7WUFDL0IsT0FBTyxDQUFDLElBQUksQ0FBQyw0REFBYSxDQUFDLFVBQUssQ0FBQyxrQkFBSyxDQUFDLENBQUM7WUFDeEMsT0FBTztTQUNWO1FBRUQsU0FBUztRQUNULElBQU0sUUFBUSxHQUFHLElBQUksQ0FBQyxTQUFTLENBQUMsQ0FBQyxDQUFDLElBQUksSUFBSSxDQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUMzRCxJQUFJLFFBQVEsRUFBRTtZQUNWLFdBQVc7WUFDWCxFQUFFLENBQUMsS0FBSyxDQUFDLFFBQVEsQ0FBQztpQkFDYixFQUFFLENBQUMsR0FBRyxFQUFFLEVBQUUsT0FBTyxFQUFFLENBQUMsRUFBRSxNQUFNLEVBQUUsQ0FBQyxFQUFFLE1BQU0sRUFBRSxDQUFDLEVBQUUsRUFBRSxFQUFFLE1BQU0sRUFBRSxRQUFRLEVBQUUsQ0FBQztpQkFDbkUsSUFBSSxDQUFDO2dCQUNGLFFBQVEsQ0FBQyxNQUFNLEdBQUcsS0FBSyxDQUFDO1lBQzVCLENBQUMsQ0FBQztpQkFDRCxLQUFLLEVBQUUsQ0FBQztTQUNoQjtJQUNMLENBQUM7SUFFRDs7T0FFRztJQUNJLHdEQUFtQixHQUExQjtRQUNJLE9BQU8sSUFBSSxDQUFDLGdCQUFnQixDQUFDO0lBQ2pDLENBQUM7SUFFRDs7T0FFRztJQUNJLDBEQUFxQixHQUE1QjtRQUNJLE9BQU8sSUFBSSxDQUFDLGtCQUFrQixDQUFDO0lBQ25DLENBQUM7SUFFRDs7Ozs7T0FLRztJQUNJLHdEQUFtQixHQUExQixVQUEyQixDQUFTLEVBQUUsQ0FBUyxFQUFFLE1BQVc7UUFDeEQsSUFBSSxDQUFDLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUU7WUFDL0IsT0FBTyxDQUFDLElBQUksQ0FBQyx3RUFBZSxDQUFDLFVBQUssQ0FBQyxrQkFBSyxDQUFDLENBQUM7WUFDMUMsT0FBTztTQUNWO1FBSUQsdUJBQXVCO1FBQ3ZCLElBQUksSUFBSSxDQUFDLFdBQVcsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUU7WUFFeEIsYUFBYTtZQUNiLElBQU0sUUFBUSxHQUFHLElBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFDckMsSUFBSSxRQUFRLENBQUMsVUFBVSxFQUFFO2dCQUNyQixRQUFRLENBQUMsVUFBVSxDQUFDLGdCQUFnQixFQUFFLENBQUM7Z0JBQ3ZDLFFBQVEsQ0FBQyxVQUFVLEdBQUcsSUFBSSxDQUFDO2FBQzlCO1NBQ0o7UUFFRCxrQkFBa0I7UUFDbEIsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxTQUFTLEdBQUcsSUFBSSxDQUFDO1FBRXJDLHdCQUF3QjtRQUN4QixJQUFJLENBQUMsMEJBQTBCLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxNQUFNLENBQUMsQ0FBQztJQUNsRCxDQUFDO0lBRUQ7OztPQUdHO0lBQ0ksMkRBQXNCLEdBQTdCLFVBQThCLGdCQUF1QjtRQUFyRCxpQkFlQztRQVpHLHNCQUFzQjtRQUN0QixnQkFBZ0IsQ0FBQyxPQUFPLENBQUMsVUFBQyxVQUFVO1lBQ3hCLElBQUEsQ0FBQyxHQUF1QixVQUFVLEVBQWpDLEVBQUUsQ0FBQyxHQUFvQixVQUFVLEVBQTlCLEVBQUUsYUFBYSxHQUFLLFVBQVUsY0FBZixDQUFnQjtZQUUzQyxJQUFJLENBQUMsS0FBSSxDQUFDLGlCQUFpQixDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRTtnQkFDL0IsT0FBTyxDQUFDLElBQUksQ0FBQyxvRUFBZ0IsQ0FBQyxVQUFLLENBQUMsTUFBRyxDQUFDLENBQUM7Z0JBQ3pDLE9BQU87YUFDVjtZQUVELGFBQWE7WUFDYixLQUFJLENBQUMsMEJBQTBCLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxhQUFhLENBQUMsQ0FBQztRQUN6RCxDQUFDLENBQUMsQ0FBQztJQUNQLENBQUM7SUFFRDs7O09BR0c7SUFDSSx3REFBbUIsR0FBMUIsVUFBMkIsYUFBbUU7UUFBOUYsaUJBYUM7UUFWRyxJQUFJLENBQUMsYUFBYSxJQUFJLGFBQWEsQ0FBQyxNQUFNLEtBQUssQ0FBQyxFQUFFO1lBRTlDLE9BQU87U0FDVjtRQUVELHdCQUF3QjtRQUN4QixhQUFhLENBQUMsT0FBTyxDQUFDLFVBQUMsS0FBSztZQUN4QixhQUFhO1lBQ2IsS0FBSSxDQUFDLDBCQUEwQixDQUFDLEtBQUssQ0FBQyxDQUFDLEVBQUUsS0FBSyxDQUFDLENBQUMsRUFBRSxLQUFLLENBQUMsYUFBYSxDQUFDLENBQUM7UUFDM0UsQ0FBQyxDQUFDLENBQUM7SUFDUCxDQUFDO0lBRUQ7Ozs7O09BS0c7SUFDSSwrREFBMEIsR0FBakMsVUFBa0MsQ0FBUyxFQUFFLENBQVMsRUFBRSxhQUFrQjtRQUExRSxpQkE0QkM7UUF6QkcsOEJBQThCO1FBQzlCLElBQUksSUFBSSxDQUFDLFdBQVcsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUU7WUFFeEIsSUFBTSxRQUFRLEdBQUcsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUNyQyxJQUFJLFFBQVEsQ0FBQyxVQUFVLEVBQUU7Z0JBQ3JCLFFBQVEsQ0FBQyxVQUFVLENBQUMsZ0JBQWdCLEVBQUUsQ0FBQztnQkFDdkMsUUFBUSxDQUFDLFVBQVUsR0FBRyxJQUFJLENBQUM7YUFDOUI7U0FDSjtRQUVELG1CQUFtQjtRQUNuQixJQUFJLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUU7WUFDOUIsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxTQUFTLEdBQUcsSUFBSSxDQUFDO1NBQ3hDO1FBRUQsUUFBUTtRQUNSLElBQUksQ0FBQyxZQUFZLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDO1FBRXhCLHlCQUF5QjtRQUN6QixxQkFBcUI7UUFDckIsSUFBTSxhQUFhLEdBQUc7WUFFbEIsS0FBSSxDQUFDLDBCQUEwQixDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsYUFBYSxDQUFDLENBQUM7UUFDekQsQ0FBQyxDQUFDO1FBQ0YsSUFBSSxDQUFDLFlBQVksQ0FBQyxhQUFhLEVBQUUsR0FBRyxDQUFDLENBQUM7SUFDMUMsQ0FBQztJQUVEOzs7OztPQUtHO0lBQ0ksaURBQVksR0FBbkIsVUFBb0IsQ0FBUyxFQUFFLENBQVMsRUFBRSxTQUEwQjtRQUExQiwwQkFBQSxFQUFBLGlCQUEwQjtRQUNoRSxJQUFJLENBQUMsSUFBSSxDQUFDLGlCQUFpQixDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRTtZQUMvQixPQUFPLENBQUMsSUFBSSxDQUFDLDREQUFhLENBQUMsVUFBSyxDQUFDLGtCQUFLLENBQUMsQ0FBQztZQUN4QyxPQUFPO1NBQ1Y7UUFFRCxTQUFTO1FBQ1QsSUFBTSxRQUFRLEdBQUcsSUFBSSxDQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUMsSUFBSSxJQUFJLENBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQzNELElBQUksUUFBUSxFQUFFO1lBQ1YsSUFBSSxTQUFTLEVBQUU7Z0JBQ1gsYUFBYTtnQkFDYixRQUFRLENBQUMsTUFBTSxHQUFHLEtBQUssQ0FBQzthQUMzQjtpQkFBTTtnQkFDSCxjQUFjO2dCQUNkLElBQUksQ0FBQyxxQkFBcUIsQ0FBQyxRQUFRLENBQUMsQ0FBQzthQUN4QztTQUNKO0lBQ0wsQ0FBQztJQUVEOzs7O09BSUc7SUFDSywwREFBcUIsR0FBN0IsVUFBOEIsUUFBaUI7UUFDM0MsSUFBSSxDQUFDLFFBQVE7WUFBRSxPQUFPO1FBRXRCLDBCQUEwQjtRQUMxQixRQUFRLENBQUMsY0FBYyxFQUFFLENBQUM7UUFFMUIscUJBQXFCO1FBQ3JCLElBQUksQ0FBQyxRQUFRLENBQUMsa0JBQWtCLENBQUMsRUFBRTtZQUMvQixRQUFRLENBQUMsa0JBQWtCLENBQUMsR0FBRyxRQUFRLENBQUMsV0FBVyxFQUFFLENBQUM7U0FDekQ7UUFFRCx3QkFBd0I7UUFDeEIsSUFBTSxjQUFjLEdBQUcsUUFBUSxDQUFDLE1BQU0sQ0FBQztRQUV2QywrQkFBK0I7UUFDL0IsUUFBUSxDQUFDLE1BQU0sR0FBRyxJQUFJLENBQUM7UUFFdkIsbUNBQW1DO1FBQ25DLElBQU0sY0FBYyxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLE1BQU0sRUFBRSxHQUFHLENBQUMsQ0FBQyxDQUFDO1FBQ3JELElBQUksS0FBSyxHQUFHLENBQUMsQ0FBQztRQUNkLElBQUksS0FBSyxHQUFHLEdBQUcsQ0FBQyxDQUFDLGdCQUFnQjtRQUVqQyxRQUFRLGNBQWMsRUFBRTtZQUNwQixLQUFLLENBQUMsRUFBRSxLQUFLO2dCQUNULEtBQUssR0FBRyxDQUFDLENBQUM7Z0JBQ1YsTUFBTTtZQUNWLEtBQUssQ0FBQyxFQUFFLFFBQVE7Z0JBQ1osS0FBSyxHQUFHLElBQUksQ0FBQyxHQUFHLENBQUMsRUFBRSxHQUFHLElBQUksQ0FBQyxFQUFFLEdBQUcsR0FBRyxDQUFDLEdBQUcsS0FBSyxDQUFDO2dCQUM3QyxNQUFNO1lBQ1YsS0FBSyxDQUFDLEVBQUUsUUFBUTtnQkFDWixLQUFLLEdBQUcsQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUUsR0FBRyxJQUFJLENBQUMsRUFBRSxHQUFHLEdBQUcsQ0FBQyxHQUFHLEtBQUssQ0FBQztnQkFDOUMsTUFBTTtTQUNiO1FBRUQsU0FBUztRQUNULElBQU0sYUFBYSxHQUFHLENBQUMsSUFBSSxDQUFDLE1BQU0sRUFBRSxHQUFHLElBQUksR0FBRyxHQUFHLENBQUMsR0FBRyxDQUFDLElBQUksQ0FBQyxNQUFNLEVBQUUsR0FBRyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLG1CQUFtQjtRQUV4RyxPQUFPO1FBQ1AsSUFBTSxNQUFNLEdBQUcsSUFBSSxDQUFDLENBQUMsU0FBUztRQUM5QixJQUFNLFFBQVEsR0FBRyxHQUFHLENBQUMsQ0FBQyxPQUFPO1FBQzdCLElBQU0sZUFBZSxHQUFHLFFBQVEsQ0FBQyxXQUFXLEVBQUUsQ0FBQztRQUUvQyxZQUFZO1FBQ1osSUFBTSxhQUFhLEdBQUcsRUFBRSxDQUFDLEtBQUssQ0FBQyxRQUFRLENBQUM7YUFDbkMsYUFBYSxDQUNWLEVBQUUsQ0FBQyxLQUFLLEVBQUUsQ0FBQyxFQUFFLENBQUMsR0FBRyxFQUFFLEVBQUUsS0FBSyxFQUFFLGFBQWEsR0FBRyxHQUFHLEVBQUUsQ0FBQyxDQUNyRCxDQUFDO1FBRU4sYUFBYTtRQUNiLElBQU0sYUFBYSxHQUFHLEVBQUUsQ0FBQyxLQUFLLENBQUMsUUFBUSxDQUFDO1lBQ3BDLFlBQVk7YUFDWCxFQUFFLENBQUMsTUFBTSxFQUFFO1lBQ1IsQ0FBQyxFQUFFLGVBQWUsQ0FBQyxDQUFDLEdBQUcsS0FBSztZQUM1QixDQUFDLEVBQUUsZUFBZSxDQUFDLENBQUMsR0FBRyxLQUFLO1NBQy9CLEVBQUUsRUFBRSxNQUFNLEVBQUUsU0FBUyxFQUFFLENBQUM7WUFDekIsWUFBWTthQUNYLEVBQUUsQ0FBQyxRQUFRLEVBQUU7WUFDVixDQUFDLEVBQUUsZUFBZSxDQUFDLENBQUMsR0FBRyxLQUFLLEdBQUcsQ0FBQyxJQUFJLENBQUMsTUFBTSxFQUFFLEdBQUcsR0FBRyxDQUFDLEdBQUcsR0FBRztZQUMxRCxDQUFDLEVBQUUsZUFBZSxDQUFDLENBQUMsR0FBRyxHQUFHLENBQUMsYUFBYTtTQUMzQyxFQUFFLEVBQUUsTUFBTSxFQUFFLFFBQVEsRUFBRSxDQUFDO2FBQ3ZCLElBQUksQ0FBQztZQUNGLFlBQVk7WUFDWixRQUFRLENBQUMsTUFBTSxHQUFHLEtBQUssQ0FBQztZQUN4QixTQUFTO1lBQ1QsUUFBUSxDQUFDLGNBQWMsRUFBRSxDQUFDO1lBQzFCLCtCQUErQjtZQUMvQixRQUFRLENBQUMsTUFBTSxHQUFHLGNBQWMsQ0FBQztRQUNyQyxDQUFDLENBQUMsQ0FBQztRQUVQLGNBQWM7UUFDZCxhQUFhLENBQUMsS0FBSyxFQUFFLENBQUM7UUFDdEIsYUFBYSxDQUFDLEtBQUssRUFBRSxDQUFDO0lBQzFCLENBQUM7SUFFRDs7Ozs7T0FLRztJQUNJLCtEQUEwQixHQUFqQyxVQUFrQyxDQUFTLEVBQUUsQ0FBUyxFQUFFLGFBQWtCO1FBR3RFLElBQUksYUFBYSxLQUFLLE1BQU0sSUFBSSxhQUFhLEtBQUssTUFBTSxFQUFFO1lBQ3RELHNCQUFzQjtZQUd0QixJQUFJLENBQUMsZ0JBQWdCLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxJQUFJLENBQUMsQ0FBQyxDQUFDLHVCQUF1QjtZQUUxRCxlQUFlO1lBQ2YsSUFBSSxDQUFDLGVBQWUsR0FBRyxJQUFJLENBQUM7U0FFL0I7YUFBTSxJQUFJLE9BQU8sYUFBYSxLQUFLLFFBQVEsSUFBSSxhQUFhLEdBQUcsQ0FBQyxFQUFFO1lBQy9ELE9BQU87WUFFUCxJQUFJLENBQUMsa0JBQWtCLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxhQUFhLENBQUMsQ0FBQztTQUNoRDthQUFNO1lBQ0gsMkJBQTJCO1NBRTlCO0lBQ0wsQ0FBQztJQUVEOzs7O09BSUc7SUFDSSxtREFBYyxHQUFyQixVQUFzQixDQUFTLEVBQUUsQ0FBUztRQUN0QyxJQUFJLENBQUMsSUFBSSxDQUFDLGlCQUFpQixDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRTtZQUMvQixPQUFPO1NBQ1Y7UUFFRCxJQUFNLFFBQVEsR0FBRyxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQ3JDLElBQUksUUFBUSxDQUFDLFNBQVMsSUFBSSxRQUFRLENBQUMsVUFBVSxJQUFJLFFBQVEsQ0FBQyxVQUFVLENBQUMsSUFBSSxLQUFLLFFBQVEsRUFBRTtZQUNwRixTQUFTO1lBQ1QsRUFBRSxDQUFDLEtBQUssQ0FBQyxRQUFRLENBQUMsVUFBVSxDQUFDO2lCQUN4QixFQUFFLENBQUMsR0FBRyxFQUFFLEVBQUUsTUFBTSxFQUFFLENBQUMsRUFBRSxNQUFNLEVBQUUsQ0FBQyxFQUFFLEVBQUUsRUFBRSxNQUFNLEVBQUUsUUFBUSxFQUFFLENBQUM7aUJBQ3ZELElBQUksQ0FBQztnQkFDRixRQUFRLENBQUMsVUFBVSxDQUFDLGdCQUFnQixFQUFFLENBQUM7Z0JBQ3ZDLFFBQVEsQ0FBQyxTQUFTLEdBQUcsS0FBSyxDQUFDO2dCQUMzQixRQUFRLENBQUMsVUFBVSxHQUFHLElBQUksQ0FBQztZQUMvQixDQUFDLENBQUM7aUJBQ0QsS0FBSyxFQUFFLENBQUM7U0FDaEI7SUFDTCxDQUFDO0lBRUQ7Ozs7O09BS0c7SUFDSSxnREFBVyxHQUFsQixVQUFtQixDQUFTLEVBQUUsQ0FBUztRQUNuQyxJQUFJLENBQUMsSUFBSSxDQUFDLGlCQUFpQixDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRTtZQUMvQixPQUFPLEtBQUssQ0FBQztTQUNoQjtRQUVELElBQU0sUUFBUSxHQUFHLElBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDckMsT0FBTyxRQUFRLENBQUMsU0FBUyxJQUFJLFFBQVEsQ0FBQyxVQUFVLElBQUksUUFBUSxDQUFDLFVBQVUsQ0FBQyxJQUFJLEtBQUssUUFBUSxDQUFDO0lBQzlGLENBQUM7SUFFRDs7O09BR0c7SUFDSSwwREFBcUIsR0FBNUI7UUFDSSxJQUFNLFNBQVMsR0FBa0MsRUFBRSxDQUFDO1FBRXBELElBQUksQ0FBQyxJQUFJLENBQUMsa0JBQWtCO1lBQUUsT0FBTyxTQUFTLENBQUM7UUFFL0MsS0FBSyxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxHQUFHLElBQUksQ0FBQyxrQkFBa0IsQ0FBQyxJQUFJLEVBQUUsQ0FBQyxFQUFFLEVBQUU7WUFDbkQsS0FBSyxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxHQUFHLElBQUksQ0FBQyxrQkFBa0IsQ0FBQyxJQUFJLEVBQUUsQ0FBQyxFQUFFLEVBQUU7Z0JBQ25ELElBQUksSUFBSSxDQUFDLFdBQVcsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUU7b0JBQ3hCLFNBQVMsQ0FBQyxJQUFJLENBQUMsRUFBQyxDQUFDLEdBQUEsRUFBRSxDQUFDLEdBQUEsRUFBQyxDQUFDLENBQUM7aUJBQzFCO2FBQ0o7U0FDSjtRQUVELE9BQU8sU0FBUyxDQUFDO0lBQ3JCLENBQUM7SUFFRDs7T0FFRztJQUNJLCtDQUFVLEdBQWpCO1FBQUEsaUJBc0JDO1FBbkJHLCtCQUErQjtRQUMvQixJQUFJLENBQUMsc0JBQXNCLEVBQUUsQ0FBQztRQUU5QixXQUFXO1FBQ1gsSUFBSSxDQUFDLHVCQUF1QixFQUFFLENBQUM7UUFFL0IsVUFBVTtRQUNWLElBQUksQ0FBQyxlQUFlLEVBQUUsQ0FBQztRQUV2QixZQUFZO1FBQ1osSUFBSSxDQUFDLHFCQUFxQixFQUFFLENBQUM7UUFFN0IsU0FBUztRQUNULElBQUksQ0FBQyxrQkFBa0IsRUFBRSxDQUFDO1FBRTFCLFdBQVc7UUFDWCxJQUFJLENBQUMsWUFBWSxDQUFDO1lBQ2QsS0FBSSxDQUFDLDJCQUEyQixFQUFFLENBQUM7UUFDdkMsQ0FBQyxFQUFFLEdBQUcsQ0FBQyxDQUFDO0lBQ1osQ0FBQztJQUVEOztPQUVHO0lBQ0ksd0RBQW1CLEdBQTFCO1FBQ0ksSUFBSSxDQUFDLElBQUksQ0FBQyxrQkFBa0I7WUFBRSxPQUFPO1FBRXJDLEtBQUssSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsR0FBRyxJQUFJLENBQUMsa0JBQWtCLENBQUMsSUFBSSxFQUFFLENBQUMsRUFBRSxFQUFFO1lBQ25ELEtBQUssSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsR0FBRyxJQUFJLENBQUMsa0JBQWtCLENBQUMsSUFBSSxFQUFFLENBQUMsRUFBRSxFQUFFO2dCQUNuRCxJQUFNLFFBQVEsR0FBRyxJQUFJLENBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQyxJQUFJLElBQUksQ0FBQyxTQUFTLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7Z0JBQzNELElBQUksUUFBUSxFQUFFO29CQUNWLFFBQVEsQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsV0FBVyxDQUFDLENBQUM7b0JBQzVDLFFBQVEsQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsU0FBUyxDQUFDLENBQUM7b0JBQzFDLFFBQVEsQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsWUFBWSxDQUFDLENBQUM7aUJBQ2hEO2FBQ0o7U0FDSjtJQUNMLENBQUM7SUFFRDs7T0FFRztJQUNJLHVEQUFrQixHQUF6QjtRQUNJLElBQUksQ0FBQywyQkFBMkIsRUFBRSxDQUFDO0lBQ3ZDLENBQUM7SUF0OUNEO1FBREMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxNQUFNLENBQUM7a0VBQ1M7SUFHN0I7UUFEQyxRQUFRLENBQUMsRUFBRSxDQUFDLE1BQU0sQ0FBQztvRUFDVztJQUcvQjtRQURDLFFBQVEsQ0FBQyxFQUFFLENBQUMsTUFBTSxDQUFDO21FQUNVO0lBRzlCO1FBREMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxNQUFNLENBQUM7bUVBQ1U7SUFHOUI7UUFEQyxRQUFRLENBQUMsRUFBRSxDQUFDLE1BQU0sQ0FBQzttRUFDVTtJQUc5QjtRQURDLFFBQVEsQ0FBQyxFQUFFLENBQUMsTUFBTSxDQUFDO21FQUNVO0lBRzlCO1FBREMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxNQUFNLENBQUM7bUVBQ1U7SUFHOUI7UUFEQyxRQUFRLENBQUMsRUFBRSxDQUFDLE1BQU0sQ0FBQzttRUFDVTtJQUc5QjtRQURDLFFBQVEsQ0FBQyxFQUFFLENBQUMsTUFBTSxDQUFDO21FQUNVO0lBRzlCO1FBREMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxNQUFNLENBQUM7bUVBQ1U7SUFJOUI7UUFEQyxRQUFRLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQztvRUFDVztJQUc3QjtRQURDLFFBQVEsQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDO29FQUNXO0lBRzdCO1FBREMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUM7b0VBQ1c7SUFHN0I7UUFEQyxRQUFRLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQztxRUFDWTtJQUc5QjtRQURDLFFBQVEsQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDO3NFQUNhO0lBOUNkLDBCQUEwQjtRQUQ5QyxPQUFPO09BQ2EsMEJBQTBCLENBMDlDOUM7SUFBRCxpQ0FBQztDQTE5Q0QsQUEwOUNDLENBMTlDdUQsRUFBRSxDQUFDLFNBQVMsR0EwOUNuRTtrQkExOUNvQiwwQkFBMEIiLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyIvLyBMZWFybiBUeXBlU2NyaXB0OlxuLy8gIC0gaHR0cHM6Ly9kb2NzLmNvY29zLmNvbS9jcmVhdG9yLzIuNC9tYW51YWwvZW4vc2NyaXB0aW5nL3R5cGVzY3JpcHQuaHRtbFxuLy8gTGVhcm4gQXR0cmlidXRlOlxuLy8gIC0gaHR0cHM6Ly9kb2NzLmNvY29zLmNvbS9jcmVhdG9yLzIuNC9tYW51YWwvZW4vc2NyaXB0aW5nL3JlZmVyZW5jZS9hdHRyaWJ1dGVzLmh0bWxcbi8vIExlYXJuIGxpZmUtY3ljbGUgY2FsbGJhY2tzOlxuLy8gIC0gaHR0cHM6Ly9kb2NzLmNvY29zLmNvbS9jcmVhdG9yLzIuNC9tYW51YWwvZW4vc2NyaXB0aW5nL2xpZmUtY3ljbGUtY2FsbGJhY2tzLmh0bWxcblxuaW1wb3J0IHsgV2ViU29ja2V0TWFuYWdlciB9IGZyb20gXCIuLi8uLi9uZXQvV2ViU29ja2V0TWFuYWdlclwiO1xuaW1wb3J0IHsgTWVzc2FnZUlkIH0gZnJvbSBcIi4uLy4uL25ldC9NZXNzYWdlSWRcIjtcblxuY29uc3Qge2NjY2xhc3MsIHByb3BlcnR5fSA9IGNjLl9kZWNvcmF0b3I7XG5cbi8vIOWNleacuuaooeW8j+aji+ebmOagvOWtkOaVsOaNruaOpeWPo1xuZXhwb3J0IGludGVyZmFjZSBTaW5nbGVHcmlkRGF0YSB7XG4gICAgeDogbnVtYmVyOyAgLy8g5qC85a2Q55qEeOWdkOagh1xuICAgIHk6IG51bWJlcjsgIC8vIOagvOWtkOeahHnlnZDmoIdcbiAgICB3b3JsZFBvczogY2MuVmVjMjsgIC8vIOagvOWtkOWcqOS4lueVjOWdkOagh+ezu+S4reeahOS9jee9rlxuICAgIGhhc1BsYXllcjogYm9vbGVhbjsgIC8vIOaYr+WQpuW3sue7j+aUvue9ruS6humihOWItuS9k1xuICAgIHBsYXllck5vZGU/OiBjYy5Ob2RlOyAgLy8g5pS+572u55qE6IqC54K55byV55SoXG59XG5cbi8vIOaji+ebmOmFjee9ruaOpeWPo1xuaW50ZXJmYWNlIEJvYXJkQ29uZmlnIHtcbiAgICB3aWR0aDogbnVtYmVyOyAgICAvLyDmo4vnm5jlrr3luqZcbiAgICBoZWlnaHQ6IG51bWJlcjsgICAvLyDmo4vnm5jpq5jluqZcbiAgICByb3dzOiBudW1iZXI7ICAgICAvLyDooYzmlbBcbiAgICBjb2xzOiBudW1iZXI7ICAgICAvLyDliJfmlbBcbiAgICBncmlkV2lkdGg6IG51bWJlcjsgIC8vIOagvOWtkOWuveW6plxuICAgIGdyaWRIZWlnaHQ6IG51bWJlcjsgLy8g5qC85a2Q6auY5bqmXG59XG5cbi8vIOS6lOenjeaji+ebmOmFjee9rlxuY29uc3QgQk9BUkRfQ09ORklHUzogeyBba2V5OiBzdHJpbmddOiBCb2FyZENvbmZpZyB9ID0ge1xuICAgIFwiOHg4XCI6IHtcbiAgICAgICAgd2lkdGg6IDc1MixcbiAgICAgICAgaGVpZ2h0OiA3NTIsXG4gICAgICAgIHJvd3M6IDgsXG4gICAgICAgIGNvbHM6IDgsXG4gICAgICAgIGdyaWRXaWR0aDogODgsXG4gICAgICAgIGdyaWRIZWlnaHQ6IDg4XG4gICAgfSxcbiAgICBcIjh4OVwiOiB7XG4gICAgICAgIHdpZHRoOiA3NTIsXG4gICAgICAgIGhlaWdodDogODQ1LFxuICAgICAgICByb3dzOiA5LCAgLy8gOeihjFxuICAgICAgICBjb2xzOiA4LCAgLy8gOOWIl1xuICAgICAgICBncmlkV2lkdGg6IDg4LFxuICAgICAgICBncmlkSGVpZ2h0OiA4OFxuICAgIH0sXG4gICAgXCI5eDlcIjoge1xuICAgICAgICB3aWR0aDogNzUyLFxuICAgICAgICBoZWlnaHQ6IDc0NyxcbiAgICAgICAgcm93czogOSxcbiAgICAgICAgY29sczogOSxcbiAgICAgICAgZ3JpZFdpZHRoOiA3NixcbiAgICAgICAgZ3JpZEhlaWdodDogNzZcbiAgICB9LFxuICAgIFwiOXgxMFwiOiB7XG4gICAgICAgIHdpZHRoOiA3NTIsXG4gICAgICAgIGhlaWdodDogODMwLFxuICAgICAgICByb3dzOiAxMCwgIC8vIDEw6KGMXG4gICAgICAgIGNvbHM6IDksICAgLy8gOeWIl1xuICAgICAgICBncmlkV2lkdGg6IDc4LFxuICAgICAgICBncmlkSGVpZ2h0OiA3OFxuICAgIH0sXG4gICAgXCIxMHgxMFwiOiB7XG4gICAgICAgIHdpZHRoOiA3NTIsXG4gICAgICAgIGhlaWdodDogNzQ1LFxuICAgICAgICByb3dzOiAxMCxcbiAgICAgICAgY29sczogMTAsXG4gICAgICAgIGdyaWRXaWR0aDogNjksXG4gICAgICAgIGdyaWRIZWlnaHQ6IDY5XG4gICAgfVxufTtcblxuQGNjY2xhc3NcbmV4cG9ydCBkZWZhdWx0IGNsYXNzIFNpbmdsZUNoZXNzQm9hcmRDb250cm9sbGVyIGV4dGVuZHMgY2MuQ29tcG9uZW50IHtcblxuICAgIEBwcm9wZXJ0eShjYy5QcmVmYWIpXG4gICAgYm9vbVByZWZhYjogY2MuUHJlZmFiID0gbnVsbDsgIC8vIGJvb23pooTliLbkvZNcblxuICAgIEBwcm9wZXJ0eShjYy5QcmVmYWIpXG4gICAgYmlhb2ppUHJlZmFiOiBjYy5QcmVmYWIgPSBudWxsOyAgLy8gYmlhb2pp6aKE5Yi25L2TXG5cbiAgICBAcHJvcGVydHkoY2MuUHJlZmFiKVxuICAgIGJvb20xUHJlZmFiOiBjYy5QcmVmYWIgPSBudWxsOyAgLy8g5pWw5a2XMemihOWItuS9k1xuXG4gICAgQHByb3BlcnR5KGNjLlByZWZhYilcbiAgICBib29tMlByZWZhYjogY2MuUHJlZmFiID0gbnVsbDsgIC8vIOaVsOWtlzLpooTliLbkvZNcblxuICAgIEBwcm9wZXJ0eShjYy5QcmVmYWIpXG4gICAgYm9vbTNQcmVmYWI6IGNjLlByZWZhYiA9IG51bGw7ICAvLyDmlbDlrZcz6aKE5Yi25L2TXG5cbiAgICBAcHJvcGVydHkoY2MuUHJlZmFiKVxuICAgIGJvb200UHJlZmFiOiBjYy5QcmVmYWIgPSBudWxsOyAgLy8g5pWw5a2XNOmihOWItuS9k1xuXG4gICAgQHByb3BlcnR5KGNjLlByZWZhYilcbiAgICBib29tNVByZWZhYjogY2MuUHJlZmFiID0gbnVsbDsgIC8vIOaVsOWtlzXpooTliLbkvZNcblxuICAgIEBwcm9wZXJ0eShjYy5QcmVmYWIpXG4gICAgYm9vbTZQcmVmYWI6IGNjLlByZWZhYiA9IG51bGw7ICAvLyDmlbDlrZc26aKE5Yi25L2TXG5cbiAgICBAcHJvcGVydHkoY2MuUHJlZmFiKVxuICAgIGJvb203UHJlZmFiOiBjYy5QcmVmYWIgPSBudWxsOyAgLy8g5pWw5a2XN+mihOWItuS9k1xuXG4gICAgQHByb3BlcnR5KGNjLlByZWZhYilcbiAgICBib29tOFByZWZhYjogY2MuUHJlZmFiID0gbnVsbDsgIC8vIOaVsOWtlzjpooTliLbkvZNcblxuICAgIC8vIOS6lOS4quaji+ebmOiKgueCuVxuICAgIEBwcm9wZXJ0eShjYy5Ob2RlKVxuICAgIHFpcGFuOHg4Tm9kZTogY2MuTm9kZSA9IG51bGw7ICAvLyA4eDjmo4vnm5joioLngrlcblxuICAgIEBwcm9wZXJ0eShjYy5Ob2RlKVxuICAgIHFpcGFuOHg5Tm9kZTogY2MuTm9kZSA9IG51bGw7ICAvLyA4eDnmo4vnm5joioLngrlcblxuICAgIEBwcm9wZXJ0eShjYy5Ob2RlKVxuICAgIHFpcGFuOXg5Tm9kZTogY2MuTm9kZSA9IG51bGw7ICAvLyA5eDnmo4vnm5joioLngrlcblxuICAgIEBwcm9wZXJ0eShjYy5Ob2RlKVxuICAgIHFpcGFuOXgxME5vZGU6IGNjLk5vZGUgPSBudWxsOyAgLy8gOXgxMOaji+ebmOiKgueCuVxuXG4gICAgQHByb3BlcnR5KGNjLk5vZGUpXG4gICAgcWlwYW4xMHgxME5vZGU6IGNjLk5vZGUgPSBudWxsOyAgLy8gMTB4MTDmo4vnm5joioLngrlcblxuICAgIC8vIOW9k+WJjeS9v+eUqOeahOaji+ebmOiKgueCuVxuICAgIHByaXZhdGUgY3VycmVudEJvYXJkTm9kZTogY2MuTm9kZSA9IG51bGw7XG5cbiAgICAvLyDlvZPliY3mo4vnm5jphY3nva5cbiAgICBwcml2YXRlIGN1cnJlbnRCb2FyZENvbmZpZzogQm9hcmRDb25maWcgPSBudWxsO1xuICAgIHByaXZhdGUgY3VycmVudEJvYXJkVHlwZTogc3RyaW5nID0gXCI4eDhcIjsgIC8vIOm7mOiupDh4OOaji+ebmFxuXG4gICAgLy8g54K45by554iG54K45qCH6K6wXG4gICAgcHJpdmF0ZSBoYXNCb21iRXhwbG9kZWQ6IGJvb2xlYW4gPSBmYWxzZTtcblxuICAgIC8vIOagvOWtkOaVsOaNruWtmOWCqFxuICAgIHByaXZhdGUgZ3JpZERhdGE6IFNpbmdsZUdyaWREYXRhW11bXSA9IFtdOyAgLy8g5LqM57u05pWw57uE5a2Y5YKo5qC85a2Q5pWw5o2uXG4gICAgcHJpdmF0ZSBncmlkTm9kZXM6IGNjLk5vZGVbXVtdID0gW107ICAvLyDkuoznu7TmlbDnu4TlrZjlgqjmoLzlrZDoioLngrlcblxuICAgIC8vIOmYsumHjeWkjeWPkemAgea2iOaBr1xuICAgIHByaXZhdGUgbGFzdENsaWNrVGltZTogbnVtYmVyID0gMDtcbiAgICBwcml2YXRlIGxhc3RDbGlja1Bvc2l0aW9uOiBzdHJpbmcgPSBcIlwiO1xuICAgIHByaXZhdGUgcmVhZG9ubHkgQ0xJQ0tfQ09PTERPV04gPSAyMDA7IC8vIDIwMOavq+enkuWGt+WNtOaXtumXtFxuXG4gICAgb25Mb2FkKCkge1xuICAgICAgICAvLyDkuI3ov5vooYzpu5jorqTliJ3lp4vljJbvvIznrYnlvoXlpJbpg6josIPnlKhpbml0Qm9hcmRcbiAgICB9XG5cbiAgICBzdGFydCgpIHtcbiAgICAgICAgLy8gc3RhcnTmlrnms5XkuI3lho3oh6rliqjlkK/nlKjop6bmkbjkuovku7bvvIzpgb/lhY3kuI5pbml0Qm9hcmTph43lpI1cbiAgICAgICAgLy8g6Kem5pG45LqL5Lu255qE5ZCv55So55SxaW5pdEJvYXJk5pa55rOV6LSf6LSjXG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5qC55o2u5qOL55uY57G75Z6L6I635Y+W5a+55bqU55qE5qOL55uY6IqC54K5XG4gICAgICogQHBhcmFtIGJvYXJkVHlwZSDmo4vnm5jnsbvlnotcbiAgICAgKi9cbiAgICBwcml2YXRlIGdldEJvYXJkTm9kZUJ5VHlwZShib2FyZFR5cGU6IHN0cmluZyk6IGNjLk5vZGUgfCBudWxsIHtcbiAgICAgICAgc3dpdGNoIChib2FyZFR5cGUpIHtcbiAgICAgICAgICAgIGNhc2UgXCI4eDhcIjpcbiAgICAgICAgICAgICAgICByZXR1cm4gdGhpcy5xaXBhbjh4OE5vZGU7XG4gICAgICAgICAgICBjYXNlIFwiOHg5XCI6XG4gICAgICAgICAgICAgICAgcmV0dXJuIHRoaXMucWlwYW44eDlOb2RlO1xuICAgICAgICAgICAgY2FzZSBcIjl4OVwiOlxuICAgICAgICAgICAgICAgIHJldHVybiB0aGlzLnFpcGFuOXg5Tm9kZTtcbiAgICAgICAgICAgIGNhc2UgXCI5eDEwXCI6XG4gICAgICAgICAgICAgICAgcmV0dXJuIHRoaXMucWlwYW45eDEwTm9kZTtcbiAgICAgICAgICAgIGNhc2UgXCIxMHgxMFwiOlxuICAgICAgICAgICAgICAgIHJldHVybiB0aGlzLnFpcGFuMTB4MTBOb2RlO1xuICAgICAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOWIneWni+WMluaMh+Wumuexu+Wei+eahOaji+ebmFxuICAgICAqIEBwYXJhbSBib2FyZFR5cGUg5qOL55uY57G75Z6LIChcIjh4OFwiLCBcIjh4OVwiLCBcIjl4OVwiLCBcIjl4MTBcIiwgXCIxMHgxMFwiKVxuICAgICAqL1xuICAgIHB1YmxpYyBpbml0Qm9hcmQoYm9hcmRUeXBlOiBzdHJpbmcpIHtcbiAgICAgICAgaWYgKCFCT0FSRF9DT05GSUdTW2JvYXJkVHlwZV0pIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoYOS4jeaUr+aMgeeahOaji+ebmOexu+WeizogJHtib2FyZFR5cGV9YCk7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cblxuICAgICAgICAvLyDmoLnmja7mo4vnm5jnsbvlnovojrflj5blr7nlupTnmoToioLngrlcbiAgICAgICAgdGhpcy5jdXJyZW50Qm9hcmROb2RlID0gdGhpcy5nZXRCb2FyZE5vZGVCeVR5cGUoYm9hcmRUeXBlKTtcbiAgICAgICAgaWYgKCF0aGlzLmN1cnJlbnRCb2FyZE5vZGUpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoYOaji+ebmOiKgueCueacquiuvue9ru+8geaji+ebmOexu+WeizogJHtib2FyZFR5cGV9YCk7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cblxuICAgICAgICB0aGlzLmN1cnJlbnRCb2FyZFR5cGUgPSBib2FyZFR5cGU7XG4gICAgICAgIHRoaXMuY3VycmVudEJvYXJkQ29uZmlnID0gQk9BUkRfQ09ORklHU1tib2FyZFR5cGVdO1xuXG4gICAgICAgIC8vIOa4heepuueOsOacieaVsOaNrlxuICAgICAgICB0aGlzLmdyaWREYXRhID0gW107XG4gICAgICAgIHRoaXMuZ3JpZE5vZGVzID0gW107XG5cbiAgICAgICAgLy8g5Yid5aeL5YyW5pWw5o2u5pWw57uEXG4gICAgICAgIGZvciAobGV0IHggPSAwOyB4IDwgdGhpcy5jdXJyZW50Qm9hcmRDb25maWcuY29sczsgeCsrKSB7XG4gICAgICAgICAgICB0aGlzLmdyaWREYXRhW3hdID0gW107XG4gICAgICAgICAgICB0aGlzLmdyaWROb2Rlc1t4XSA9IFtdO1xuICAgICAgICAgICAgZm9yIChsZXQgeSA9IDA7IHkgPCB0aGlzLmN1cnJlbnRCb2FyZENvbmZpZy5yb3dzOyB5KyspIHtcbiAgICAgICAgICAgICAgICB0aGlzLmdyaWREYXRhW3hdW3ldID0ge1xuICAgICAgICAgICAgICAgICAgICB4OiB4LFxuICAgICAgICAgICAgICAgICAgICB5OiB5LFxuICAgICAgICAgICAgICAgICAgICB3b3JsZFBvczogdGhpcy5nZXRHcmlkV29ybGRQb3NpdGlvbih4LCB5KSxcbiAgICAgICAgICAgICAgICAgICAgaGFzUGxheWVyOiBmYWxzZVxuICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICB0aGlzLmNyZWF0ZUdyaWROb2RlcygpO1xuICAgIH1cblxuICAgIC8vIOWQr+eUqOeOsOacieagvOWtkOeahOinpuaRuOS6i+S7tlxuICAgIHByaXZhdGUgY3JlYXRlR3JpZE5vZGVzKCkge1xuICAgICAgICBpZiAoIXRoaXMuY3VycmVudEJvYXJkTm9kZSkge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcihcIuaji+ebmOiKgueCueacquiuvue9ru+8gVwiKTtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIOWmguaenOagvOWtkOW3sue7j+WtmOWcqO+8jOebtOaOpeWQr+eUqOinpuaRuOS6i+S7tlxuICAgICAgICB0aGlzLmVuYWJsZVRvdWNoRm9yRXhpc3RpbmdHcmlkcygpO1xuICAgIH1cblxuICAgIC8vIOS4uueOsOacieagvOWtkOWQr+eUqOinpuaRuOS6i+S7tlxuICAgIHByaXZhdGUgZW5hYmxlVG91Y2hGb3JFeGlzdGluZ0dyaWRzKCkge1xuICAgICAgICAvLyDmo4Dmn6Xmo4vnm5joioLngrnmmK/lkKblrZjlnKhcbiAgICAgICAgaWYgKCF0aGlzLmN1cnJlbnRCb2FyZE5vZGUpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCLmo4vnm5joioLngrnmnKrorr7nva7vvIzml6Dms5XlkK/nlKjop6bmkbjkuovku7bvvIFcIik7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cblxuICAgICAgICAvLyDpgY3ljobmo4vnm5joioLngrnnmoTmiYDmnInlrZDoioLngrlcbiAgICAgICAgbGV0IGNoaWxkcmVuID0gdGhpcy5jdXJyZW50Qm9hcmROb2RlLmNoaWxkcmVuO1xuXG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgY2hpbGRyZW4ubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgICAgIGxldCBjaGlsZCA9IGNoaWxkcmVuW2ldO1xuXG4gICAgICAgICAgICAvLyDlsJ3or5Xku47oioLngrnlkI3np7Dop6PmnpDlnZDmoIdcbiAgICAgICAgICAgIGxldCBjb29yZHMgPSB0aGlzLnBhcnNlR3JpZENvb3JkaW5hdGVGcm9tTmFtZShjaGlsZC5uYW1lKTtcbiAgICAgICAgICAgIGlmIChjb29yZHMpIHtcbiAgICAgICAgICAgICAgICB0aGlzLnNldHVwR3JpZFRvdWNoRXZlbnRzKGNoaWxkLCBjb29yZHMueCwgY29vcmRzLnkpO1xuICAgICAgICAgICAgICAgIHRoaXMuZ3JpZE5vZGVzW2Nvb3Jkcy54XSA9IHRoaXMuZ3JpZE5vZGVzW2Nvb3Jkcy54XSB8fCBbXTtcbiAgICAgICAgICAgICAgICB0aGlzLmdyaWROb2Rlc1tjb29yZHMueF1bY29vcmRzLnldID0gY2hpbGQ7XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIC8vIOWmguaenOaXoOazleS7juWQjeensOino+aekO+8jOWwneivleS7juS9jee9ruiuoeeul1xuICAgICAgICAgICAgICAgIGxldCBwb3MgPSBjaGlsZC5nZXRQb3NpdGlvbigpO1xuICAgICAgICAgICAgICAgIGxldCBjb29yZHMgPSB0aGlzLmdldEdyaWRDb29yZGluYXRlRnJvbVBvc2l0aW9uKHBvcyk7XG4gICAgICAgICAgICAgICAgaWYgKGNvb3Jkcykge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnNldHVwR3JpZFRvdWNoRXZlbnRzKGNoaWxkLCBjb29yZHMueCwgY29vcmRzLnkpO1xuICAgICAgICAgICAgICAgICAgICB0aGlzLmdyaWROb2Rlc1tjb29yZHMueF0gPSB0aGlzLmdyaWROb2Rlc1tjb29yZHMueF0gfHwgW107XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuZ3JpZE5vZGVzW2Nvb3Jkcy54XVtjb29yZHMueV0gPSBjaGlsZDtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDku47oioLngrnlkI3np7Dop6PmnpDmoLzlrZDlnZDmoIdcbiAgICBwcml2YXRlIHBhcnNlR3JpZENvb3JkaW5hdGVGcm9tTmFtZShub2RlTmFtZTogc3RyaW5nKToge3g6IG51bWJlciwgeTogbnVtYmVyfSB8IG51bGwge1xuICAgICAgICAvLyDlsJ3or5XljLnphY0gR3JpZF94X3kg5qC85byPXG4gICAgICAgIGxldCBtYXRjaCA9IG5vZGVOYW1lLm1hdGNoKC9HcmlkXyhcXGQrKV8oXFxkKykvKTtcbiAgICAgICAgaWYgKG1hdGNoKSB7XG4gICAgICAgICAgICByZXR1cm4ge3g6IHBhcnNlSW50KG1hdGNoWzFdKSwgeTogcGFyc2VJbnQobWF0Y2hbMl0pfTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG5cbiAgICAvLyDku47kvY3nva7orqHnrpfmoLzlrZDlnZDmoIfvvIjpnIDopoHogIPomZHkuI3lkIzmo4vnm5jnsbvlnovnmoTovrnot53vvIlcbiAgICBwcml2YXRlIGdldEdyaWRDb29yZGluYXRlRnJvbVBvc2l0aW9uKHBvczogY2MuVmVjMik6IHt4OiBudW1iZXIsIHk6IG51bWJlcn0gfCBudWxsIHtcbiAgICAgICAgaWYgKCF0aGlzLmN1cnJlbnRCb2FyZENvbmZpZykgcmV0dXJuIG51bGw7XG5cbiAgICAgICAgLy8g5qC55o2u5LiN5ZCM5qOL55uY57G75Z6L5L2/55So5LiN5ZCM55qE6K6h566X5pa55byPXG4gICAgICAgIHN3aXRjaCAodGhpcy5jdXJyZW50Qm9hcmRUeXBlKSB7XG4gICAgICAgICAgICBjYXNlIFwiOHg5XCI6XG4gICAgICAgICAgICAgICAgcmV0dXJuIHRoaXMuZ2V0R3JpZENvb3JkaW5hdGVGcm9tUG9zaXRpb25Gb3I4eDkocG9zKTtcbiAgICAgICAgICAgIGNhc2UgXCI5eDlcIjpcbiAgICAgICAgICAgICAgICByZXR1cm4gdGhpcy5nZXRHcmlkQ29vcmRpbmF0ZUZyb21Qb3NpdGlvbkZvcjl4OShwb3MpO1xuICAgICAgICAgICAgY2FzZSBcIjl4MTBcIjpcbiAgICAgICAgICAgICAgICByZXR1cm4gdGhpcy5nZXRHcmlkQ29vcmRpbmF0ZUZyb21Qb3NpdGlvbkZvcjl4MTAocG9zKTtcbiAgICAgICAgICAgIGNhc2UgXCIxMHgxMFwiOlxuICAgICAgICAgICAgICAgIHJldHVybiB0aGlzLmdldEdyaWRDb29yZGluYXRlRnJvbVBvc2l0aW9uRm9yMTB4MTAocG9zKTtcbiAgICAgICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICAgICAgLy8g6buY6K6k6K6h566X5pa55byP77yI6YCC55So5LqO5YW25LuW5qOL55uY57G75Z6L77yJXG4gICAgICAgICAgICAgICAgbGV0IHggPSBNYXRoLmZsb29yKChwb3MueCArIHRoaXMuY3VycmVudEJvYXJkQ29uZmlnLndpZHRoIC8gMikgLyB0aGlzLmN1cnJlbnRCb2FyZENvbmZpZy5ncmlkV2lkdGgpO1xuICAgICAgICAgICAgICAgIGxldCB5ID0gTWF0aC5mbG9vcigocG9zLnkgKyB0aGlzLmN1cnJlbnRCb2FyZENvbmZpZy5oZWlnaHQgLyAyKSAvIHRoaXMuY3VycmVudEJvYXJkQ29uZmlnLmdyaWRIZWlnaHQpO1xuXG4gICAgICAgICAgICAgICAgaWYgKHRoaXMuaXNWYWxpZENvb3JkaW5hdGUoeCwgeSkpIHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHt4OiB4LCB5OiB5fTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyA4eDnmo4vnm5jnmoTkvY3nva7liLDlnZDmoIfovazmjaLvvIjln7rkuo7nsr7noa7lnZDmoIfvvIzlt6bkuIvop5LkuLooMCwwKe+8iVxuICAgIHByaXZhdGUgZ2V0R3JpZENvb3JkaW5hdGVGcm9tUG9zaXRpb25Gb3I4eDkocG9zOiBjYy5WZWMyKToge3g6IG51bWJlciwgeTogbnVtYmVyfSB8IG51bGwge1xuICAgICAgICAvLyDkvb/nlKjkuI7pooTliLbkvZPkvY3nva7orqHnrpfnm7jlkIznmoTnsr7noa7lj4LmlbBcbiAgICAgICAgY29uc3Qgc3RhcnRYID0gLTMyMTsgIC8vIOW3puS4i+inkljlnZDmoIdcbiAgICAgICAgY29uc3Qgc3RhcnRZID0gLTM2NDsgIC8vIOW3puS4i+inklnlnZDmoIdcbiAgICAgICAgY29uc3Qgc3RlcFggPSA5MS4xNDsgIC8vIFjmlrnlkJHnsr7noa7mraXplb9cbiAgICAgICAgY29uc3Qgc3RlcFkgPSA5MS4xMjU7IC8vIFnmlrnlkJHnsr7noa7mraXplb9cblxuICAgICAgICAvLyDku47kvY3nva7lj43mjqjlnZDmoIdcbiAgICAgICAgbGV0IHggPSBNYXRoLnJvdW5kKChwb3MueCAtIHN0YXJ0WCkgLyBzdGVwWCk7XG4gICAgICAgIGxldCB5ID0gTWF0aC5yb3VuZCgocG9zLnkgLSBzdGFydFkpIC8gc3RlcFkpO1xuXG4gICAgICAgXG5cbiAgICAgICAgaWYgKHRoaXMuaXNWYWxpZENvb3JkaW5hdGUoeCwgeSkpIHtcbiAgICAgICAgICAgIHJldHVybiB7eDogeCwgeTogeX07XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuXG4gICAgLy8gOXg55qOL55uY55qE5L2N572u5Yiw5Z2Q5qCH6L2s5o2i77yI5Z+65LqO57K+56Gu5Z2Q5qCH77yM5bem5LiL6KeS5Li6KDAsMCnvvIlcbiAgICBwcml2YXRlIGdldEdyaWRDb29yZGluYXRlRnJvbVBvc2l0aW9uRm9yOXg5KHBvczogY2MuVmVjMik6IHt4OiBudW1iZXIsIHk6IG51bWJlcn0gfCBudWxsIHtcbiAgICAgICAgLy8g5L2/55So5LiO6aKE5Yi25L2T5L2N572u6K6h566X55u45ZCM55qE57K+56Gu5Y+C5pWwXG4gICAgICAgIGNvbnN0IHN0YXJ0WCA9IC0zMjI7ICAvLyDlt6bkuIvop5JY5Z2Q5qCHXG4gICAgICAgIGNvbnN0IHN0YXJ0WSA9IC0zMjA7ICAvLyDlt6bkuIvop5JZ5Z2Q5qCHXG4gICAgICAgIGNvbnN0IHN0ZXBYID0gODAuMjU7ICAvLyBY5pa55ZCR57K+56Gu5q2l6ZW/XG4gICAgICAgIGNvbnN0IHN0ZXBZID0gODAuMzc1OyAvLyBZ5pa55ZCR57K+56Gu5q2l6ZW/XG5cbiAgICAgICAgLy8g5LuO5L2N572u5Y+N5o6o5Z2Q5qCHXG4gICAgICAgIGxldCB4ID0gTWF0aC5yb3VuZCgocG9zLnggLSBzdGFydFgpIC8gc3RlcFgpO1xuICAgICAgICBsZXQgeSA9IE1hdGgucm91bmQoKHBvcy55IC0gc3RhcnRZKSAvIHN0ZXBZKTtcblxuICAgICAgIFxuXG4gICAgICAgIGlmICh0aGlzLmlzVmFsaWRDb29yZGluYXRlKHgsIHkpKSB7XG4gICAgICAgICAgICByZXR1cm4ge3g6IHgsIHk6IHl9O1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBudWxsO1xuICAgIH1cblxuICAgIC8vIDl4MTDmo4vnm5jnmoTkvY3nva7liLDlnZDmoIfovazmjaLvvIjln7rkuo7nsr7noa7lnZDmoIfvvIzlt6bkuIvop5LkuLooMCwwKe+8iVxuICAgIHByaXZhdGUgZ2V0R3JpZENvb3JkaW5hdGVGcm9tUG9zaXRpb25Gb3I5eDEwKHBvczogY2MuVmVjMik6IHt4OiBudW1iZXIsIHk6IG51bWJlcn0gfCBudWxsIHtcbiAgICAgICAgLy8g5L2/55So5LiO6aKE5Yi25L2T5L2N572u6K6h566X55u45ZCM55qE57K+56Gu5Y+C5pWwXG4gICAgICAgIGNvbnN0IHN0YXJ0WCA9IC0zMjA7ICAvLyDlt6bkuIvop5JY5Z2Q5qCHXG4gICAgICAgIGNvbnN0IHN0YXJ0WSA9IC0zNjE7ICAvLyDlt6bkuIvop5JZ5Z2Q5qCHXG4gICAgICAgIGNvbnN0IHN0ZXBYID0gODA7ICAgICAvLyBY5pa55ZCR57K+56Gu5q2l6ZW/XG4gICAgICAgIGNvbnN0IHN0ZXBZID0gODAuMzM7ICAvLyBZ5pa55ZCR57K+56Gu5q2l6ZW/XG5cbiAgICAgICAgLy8g5LuO5L2N572u5Y+N5o6o5Z2Q5qCHXG4gICAgICAgIGxldCB4ID0gTWF0aC5yb3VuZCgocG9zLnggLSBzdGFydFgpIC8gc3RlcFgpO1xuICAgICAgICBsZXQgeSA9IE1hdGgucm91bmQoKHBvcy55IC0gc3RhcnRZKSAvIHN0ZXBZKTtcblxuICAgICAgIFxuXG4gICAgICAgIGlmICh0aGlzLmlzVmFsaWRDb29yZGluYXRlKHgsIHkpKSB7XG4gICAgICAgICAgICByZXR1cm4ge3g6IHgsIHk6IHl9O1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBudWxsO1xuICAgIH1cblxuICAgIC8vIDEweDEw5qOL55uY55qE5L2N572u5Yiw5Z2Q5qCH6L2s5o2i77yI5Z+65LqO57K+56Gu5Z2Q5qCH77yM5bem5LiL6KeS5Li6KDAsMCnvvIlcbiAgICBwcml2YXRlIGdldEdyaWRDb29yZGluYXRlRnJvbVBvc2l0aW9uRm9yMTB4MTAocG9zOiBjYy5WZWMyKToge3g6IG51bWJlciwgeTogbnVtYmVyfSB8IG51bGwge1xuICAgICAgICAvLyDkvb/nlKjkuI7pooTliLbkvZPkvY3nva7orqHnrpfnm7jlkIznmoTnsr7noa7lj4LmlbBcbiAgICAgICAgY29uc3Qgc3RhcnRYID0gLTMyODsgIC8vIOW3puS4i+inkljlnZDmoIdcbiAgICAgICAgY29uc3Qgc3RhcnRZID0gLTMyMjsgIC8vIOW3puS4i+inklnlnZDmoIdcbiAgICAgICAgY29uc3Qgc3RlcFggPSA3Mi41NjsgIC8vIFjmlrnlkJHnsr7noa7mraXplb9cbiAgICAgICAgY29uc3Qgc3RlcFkgPSA3MjsgICAgIC8vIFnmlrnlkJHnsr7noa7mraXplb9cblxuICAgICAgICAvLyDku47kvY3nva7lj43mjqjlnZDmoIdcbiAgICAgICAgbGV0IHggPSBNYXRoLnJvdW5kKChwb3MueCAtIHN0YXJ0WCkgLyBzdGVwWCk7XG4gICAgICAgIGxldCB5ID0gTWF0aC5yb3VuZCgocG9zLnkgLSBzdGFydFkpIC8gc3RlcFkpO1xuXG4gICAgICAgXG5cbiAgICAgICAgaWYgKHRoaXMuaXNWYWxpZENvb3JkaW5hdGUoeCwgeSkpIHtcbiAgICAgICAgICAgXG4gICAgICAgICAgICByZXR1cm4ge3g6IHgsIHk6IHl9O1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICBcbiAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgLy8g5Li65qC85a2Q6K6+572u6Kem5pG45LqL5Lu2XG4gICAgcHJpdmF0ZSBzZXR1cEdyaWRUb3VjaEV2ZW50cyhncmlkTm9kZTogY2MuTm9kZSwgeDogbnVtYmVyLCB5OiBudW1iZXIpIHtcbiAgICAgICAgLy8g5YWI5riF6Zmk5bey5pyJ55qE6Kem5pG45LqL5Lu277yM6Ziy5q2i6YeN5aSN57uR5a6aXG4gICAgICAgIGdyaWROb2RlLm9mZihjYy5Ob2RlLkV2ZW50VHlwZS5UT1VDSF9TVEFSVCk7XG4gICAgICAgIGdyaWROb2RlLm9mZihjYy5Ob2RlLkV2ZW50VHlwZS5UT1VDSF9FTkQpO1xuICAgICAgICBncmlkTm9kZS5vZmYoY2MuTm9kZS5FdmVudFR5cGUuVE9VQ0hfQ0FOQ0VMKTtcblxuICAgICAgICAvLyDplb/mjInnm7jlhbPlj5jph49cbiAgICAgICAgbGV0IGlzTG9uZ1ByZXNzaW5nID0gZmFsc2U7XG4gICAgICAgIGxldCBsb25nUHJlc3NUaW1lciA9IDA7XG4gICAgICAgIGxldCBsb25nUHJlc3NDYWxsYmFjazogRnVuY3Rpb24gPSBudWxsO1xuICAgICAgICBsZXQgaGFzVHJpZ2dlcmVkTG9uZ1ByZXNzID0gZmFsc2U7IC8vIOagh+iusOaYr+WQpuW3suinpuWPkemVv+aMiVxuICAgICAgICBjb25zdCBMT05HX1BSRVNTX1RJTUUgPSAxLjA7IC8vIDHnp5Lplb/mjInml7bpl7RcblxuICAgICAgICAvLyDop6bmkbjlvIDlp4vkuovku7ZcbiAgICAgICAgZ3JpZE5vZGUub24oY2MuTm9kZS5FdmVudFR5cGUuVE9VQ0hfU1RBUlQsIChfZXZlbnQ6IGNjLkV2ZW50LkV2ZW50VG91Y2gpID0+IHtcbiAgICAgICAgICAgIGlzTG9uZ1ByZXNzaW5nID0gdHJ1ZTtcbiAgICAgICAgICAgIGxvbmdQcmVzc1RpbWVyID0gMDtcbiAgICAgICAgICAgIGhhc1RyaWdnZXJlZExvbmdQcmVzcyA9IGZhbHNlOyAvLyDph43nva7plb/mjInmoIforrBcblxuICAgICAgICAgICBcblxuICAgICAgICAgICAgLy8g5byA5aeL6ZW/5oyJ5qOA5rWLXG4gICAgICAgICAgICBsb25nUHJlc3NDYWxsYmFjayA9ICgpID0+IHtcbiAgICAgICAgICAgICAgICBpZiAoaXNMb25nUHJlc3NpbmcpIHtcbiAgICAgICAgICAgICAgICAgICAgbG9uZ1ByZXNzVGltZXIgKz0gMC4xO1xuICAgICAgICAgICAgICAgICAgICBpZiAobG9uZ1ByZXNzVGltZXIgPj0gTE9OR19QUkVTU19USU1FICYmICFoYXNUcmlnZ2VyZWRMb25nUHJlc3MpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgICAgICAgaGFzVHJpZ2dlcmVkTG9uZ1ByZXNzID0gdHJ1ZTsgLy8g5qCH6K6w5bey6Kem5Y+R6ZW/5oyJXG4gICAgICAgICAgICAgICAgICAgICAgICBpc0xvbmdQcmVzc2luZyA9IGZhbHNlOyAvLyDnq4vljbPlgZzmraLplb/mjInnirbmgIHvvIzpmLLmraLop6bmkbjnu5PmnZ/ml7bmiafooYzngrnlh7tcblxuICAgICAgICAgICAgICAgICAgICAgICAgLy8g5omn6KGM6ZW/5oyJ5LqL5Lu2XG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLm9uR3JpZExvbmdQcmVzcyh4LCB5KTtcblxuICAgICAgICAgICAgICAgICAgICAgICAgLy8g5YGc5q2i6ZW/5oyJ5qOA5rWLXG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAobG9uZ1ByZXNzQ2FsbGJhY2spIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnVuc2NoZWR1bGUobG9uZ1ByZXNzQ2FsbGJhY2spO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxvbmdQcmVzc0NhbGxiYWNrID0gbnVsbDtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH07XG4gICAgICAgICAgICB0aGlzLnNjaGVkdWxlKGxvbmdQcmVzc0NhbGxiYWNrLCAwLjEpO1xuICAgICAgICB9LCB0aGlzKTtcblxuICAgICAgICAvLyDop6bmkbjnu5PmnZ/kuovku7ZcbiAgICAgICAgZ3JpZE5vZGUub24oY2MuTm9kZS5FdmVudFR5cGUuVE9VQ0hfRU5ELCAoZXZlbnQ6IGNjLkV2ZW50LkV2ZW50VG91Y2gpID0+IHtcbiAgICAgICAgICAgXG5cbiAgICAgICAgICAgIC8vIOWBnOatoumVv+aMieajgOa1i1xuICAgICAgICAgICAgaWYgKGxvbmdQcmVzc0NhbGxiYWNrKSB7XG4gICAgICAgICAgICAgICAgdGhpcy51bnNjaGVkdWxlKGxvbmdQcmVzc0NhbGxiYWNrKTtcbiAgICAgICAgICAgICAgICBsb25nUHJlc3NDYWxsYmFjayA9IG51bGw7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIC8vIOS4peagvOajgOafpe+8muWPquacieWcqOaJgOacieadoeS7tumDvea7oei2s+eahOaDheWGteS4i+aJjeaJp+ihjOeCueWHu+S6i+S7tlxuICAgICAgICAgICAgY29uc3Qgc2hvdWxkRXhlY3V0ZUNsaWNrID0gaXNMb25nUHJlc3NpbmcgJiZcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsb25nUHJlc3NUaW1lciA8IExPTkdfUFJFU1NfVElNRSAmJlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICFoYXNUcmlnZ2VyZWRMb25nUHJlc3M7XG5cbiAgICAgICAgICAgIGlmIChzaG91bGRFeGVjdXRlQ2xpY2spIHtcbiAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICB0aGlzLm9uR3JpZENsaWNrKHgsIHksIGV2ZW50KTtcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICBcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgLy8g5riF55CG6ZW/5oyJ5qOA5rWLXG4gICAgICAgICAgICBpc0xvbmdQcmVzc2luZyA9IGZhbHNlO1xuICAgICAgICAgICAgaGFzVHJpZ2dlcmVkTG9uZ1ByZXNzID0gZmFsc2U7XG4gICAgICAgIH0sIHRoaXMpO1xuXG4gICAgICAgIC8vIOinpuaRuOWPlua2iOS6i+S7tlxuICAgICAgICBncmlkTm9kZS5vbihjYy5Ob2RlLkV2ZW50VHlwZS5UT1VDSF9DQU5DRUwsIChfZXZlbnQ6IGNjLkV2ZW50LkV2ZW50VG91Y2gpID0+IHtcbiAgICAgICAgICAgIFxuXG4gICAgICAgICAgICAvLyDmuIXnkIbplb/mjInmo4DmtYtcbiAgICAgICAgICAgIGlzTG9uZ1ByZXNzaW5nID0gZmFsc2U7XG4gICAgICAgICAgICBoYXNUcmlnZ2VyZWRMb25nUHJlc3MgPSBmYWxzZTtcbiAgICAgICAgICAgIGlmIChsb25nUHJlc3NDYWxsYmFjaykge1xuICAgICAgICAgICAgICAgIHRoaXMudW5zY2hlZHVsZShsb25nUHJlc3NDYWxsYmFjayk7XG4gICAgICAgICAgICAgICAgbG9uZ1ByZXNzQ2FsbGJhY2sgPSBudWxsO1xuICAgICAgICAgICAgfVxuICAgICAgICB9LCB0aGlzKTtcbiAgICB9XG5cbiAgICAvLyDorqHnrpfmoLzlrZDnmoTkuJbnlYzlnZDmoIfkvY3nva7vvIjlt6bkuIvop5LkuLooMCwwKe+8iVxuICAgIHByaXZhdGUgZ2V0R3JpZFdvcmxkUG9zaXRpb24oeDogbnVtYmVyLCB5OiBudW1iZXIpOiBjYy5WZWMyIHtcbiAgICAgICAgaWYgKCF0aGlzLmN1cnJlbnRCb2FyZENvbmZpZykgcmV0dXJuIGNjLnYyKDAsIDApO1xuXG4gICAgICAgIC8vIOiuoeeul+agvOWtkOS4reW/g+eCueS9jee9rlxuICAgICAgICAvLyDlt6bkuIvop5LkuLooMCwwKe+8jOaJgOS7pXnlnZDmoIfpnIDopoHku47kuIvlvoDkuIrorqHnrpdcbiAgICAgICAgbGV0IHBvc1ggPSAoeCAqIHRoaXMuY3VycmVudEJvYXJkQ29uZmlnLmdyaWRXaWR0aCkgKyAodGhpcy5jdXJyZW50Qm9hcmRDb25maWcuZ3JpZFdpZHRoIC8gMikgLSAodGhpcy5jdXJyZW50Qm9hcmRDb25maWcud2lkdGggLyAyKTtcbiAgICAgICAgbGV0IHBvc1kgPSAoeSAqIHRoaXMuY3VycmVudEJvYXJkQ29uZmlnLmdyaWRIZWlnaHQpICsgKHRoaXMuY3VycmVudEJvYXJkQ29uZmlnLmdyaWRIZWlnaHQgLyAyKSAtICh0aGlzLmN1cnJlbnRCb2FyZENvbmZpZy5oZWlnaHQgLyAyKTtcbiAgICAgICAgXG4gICAgICAgIHJldHVybiBjYy52Mihwb3NYLCBwb3NZKTtcbiAgICB9XG5cbiAgICAvLyDmoLzlrZDngrnlh7vkuovku7YgLSDlj5HpgIHmjJbmjpjmk43kvZxcbiAgICBwcml2YXRlIG9uR3JpZENsaWNrKHg6IG51bWJlciwgeTogbnVtYmVyLCBfZXZlbnQ/OiBjYy5FdmVudC5FdmVudFRvdWNoKSB7XG4gICAgICAgIC8vIOajgOafpeWdkOagh+aYr+WQpuacieaViFxuICAgICAgICBpZiAoIXRoaXMuaXNWYWxpZENvb3JkaW5hdGUoeCwgeSkpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIOajgOafpeivpeS9jee9ruaYr+WQpuW3sue7j+acieS7u+S9lemihOWItuS9k++8iOWMheaLrGJpYW9qae+8iVxuICAgICAgICBpZiAodGhpcy5ncmlkRGF0YVt4XVt5XS5oYXNQbGF5ZXIpIHtcbiAgICAgICAgICAgIFxuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8g5Y+R6YCBTGV2ZWxDbGlja0Jsb2Nr5raI5oGvXG4gICAgICAgIFxuICAgICAgICB0aGlzLnNlbmRMZXZlbENsaWNrQmxvY2soeCwgeSwgMSk7XG4gICAgfVxuXG4gICAgLy8g5qC85a2Q6ZW/5oyJ5LqL5Lu2IC0g5qCH6K6wL+WPlua2iOagh+iusOaTjeS9nO+8iOWPguiAg+iBlOacuueJiOmAu+i+ke+8iVxuICAgIHByaXZhdGUgb25HcmlkTG9uZ1ByZXNzKHg6IG51bWJlciwgeTogbnVtYmVyKSB7XG4gICAgICAgIFxuXG4gICAgICAgIC8vIOajgOafpeWdkOagh+aYr+WQpuacieaViFxuICAgICAgICBpZiAoIXRoaXMuaXNWYWxpZENvb3JkaW5hdGUoeCwgeSkpIHtcbiAgICAgICAgICBcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIOajgOafpeivpeS9jee9ruaYr+WQpuW3sue7j+aciWJpYW9qaemihOWItuS9k1xuICAgICAgICBpZiAodGhpcy5oYXNCaWFvamlBdCh4LCB5KSkge1xuICAgICAgICAgICAgLy8g5aaC5p6c5bey57uP5pyJYmlhb2pp77yM5YiZ5Yig6Zmk5a6D77yI5pys5Zyw56uL5Y2z5aSE55CG77yJXG4gICAgICAgICAgIFxuICAgICAgICAgICAgdGhpcy5yZW1vdmVCaWFvamlBdCh4LCB5KTtcblxuICAgICAgICAgICAgLy8g5Y+R6YCB5Y+W5raI5qCH6K6w5raI5oGv77yI5L2G5LiN562J5b6F5ZON5bqU77yM5Zug5Li65bey57uP5pys5Zyw5aSE55CG5LqG77yJXG4gICAgICAgICAgICBcbiAgICAgICAgICAgIHRoaXMuc2VuZExldmVsQ2xpY2tCbG9jayh4LCB5LCAyKTtcbiAgICAgICAgfSBlbHNlIGlmICghdGhpcy5ncmlkRGF0YVt4XVt5XS5oYXNQbGF5ZXIpIHtcbiAgICAgICAgICAgIC8vIOWmguaenOayoeacieS7u+S9lemihOWItuS9k++8jOWImeeUn+aIkGJpYW9qae+8iOacrOWcsOeri+WNs+WkhOeQhu+8iVxuICAgICAgICAgICBcbiAgICAgICAgICAgIHRoaXMuY3JlYXRlQmlhb2ppUHJlZmFiKHgsIHkpO1xuXG4gICAgICAgICAgICAvLyDlj5HpgIHmoIforrDmtojmga/vvIjkvYbkuI3nrYnlvoXlk43lupTvvIzlm6DkuLrlt7Lnu4/mnKzlnLDlpITnkIbkuobvvIlcbiAgICAgICAgICAgXG4gICAgICAgICAgICB0aGlzLnNlbmRMZXZlbENsaWNrQmxvY2soeCwgeSwgMik7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAvLyDlpoLmnpzmnInlhbbku5bnsbvlnovnmoTpooTliLbkvZPvvIjlpoLmlbDlrZfjgIFib29t77yJ77yM5YiZ5LiN5aSE55CGXG4gICAgICAgICBcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8vIOWPkemAgUxldmVsQ2xpY2tCbG9ja+a2iOaBr1xuICAgIHByaXZhdGUgc2VuZExldmVsQ2xpY2tCbG9jayh4OiBudW1iZXIsIHk6IG51bWJlciwgYWN0aW9uOiBudW1iZXIpIHtcbiAgICAgICAgLy8g6Ziy6YeN5aSN5Y+R6YCB5qOA5p+lXG4gICAgICAgIGNvbnN0IGN1cnJlbnRUaW1lID0gRGF0ZS5ub3coKTtcbiAgICAgICAgY29uc3QgcG9zaXRpb25LZXkgPSBgJHt4fSwke3l9LCR7YWN0aW9ufWA7XG5cbiAgICAgICAgaWYgKGN1cnJlbnRUaW1lIC0gdGhpcy5sYXN0Q2xpY2tUaW1lIDwgdGhpcy5DTElDS19DT09MRE9XTiAmJiB0aGlzLmxhc3RDbGlja1Bvc2l0aW9uID09PSBwb3NpdGlvbktleSkge1xuICAgICAgICAgICBcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuXG4gICAgICAgIHRoaXMubGFzdENsaWNrVGltZSA9IGN1cnJlbnRUaW1lO1xuICAgICAgICB0aGlzLmxhc3RDbGlja1Bvc2l0aW9uID0gcG9zaXRpb25LZXk7XG5cbiAgICAgICAgY29uc3QgY2xpY2tEYXRhID0ge1xuICAgICAgICAgICAgeDogeCxcbiAgICAgICAgICAgIHk6IHksXG4gICAgICAgICAgICBhY3Rpb246IGFjdGlvbiAvLyAxPeaMluaOmOaWueWdl++8jDI95qCH6K6wL+WPlua2iOagh+iusOWcsOmbt1xuICAgICAgICB9O1xuXG4gICAgICAgIFxuICAgICAgICBXZWJTb2NrZXRNYW5hZ2VyLkdldEluc3RhbmNlKCkuc2VuZE1zZyhNZXNzYWdlSWQuTXNnVHlwZUxldmVsQ2xpY2tCbG9jaywgY2xpY2tEYXRhKTtcbiAgICB9XG5cbiAgICAvLyDmo4Dmn6XlnZDmoIfmmK/lkKbmnInmlYhcbiAgICBwcml2YXRlIGlzVmFsaWRDb29yZGluYXRlKHg6IG51bWJlciwgeTogbnVtYmVyKTogYm9vbGVhbiB7XG4gICAgICAgIGlmICghdGhpcy5jdXJyZW50Qm9hcmRDb25maWcpIHJldHVybiBmYWxzZTtcbiAgICAgICAgcmV0dXJuIHggPj0gMCAmJiB4IDwgdGhpcy5jdXJyZW50Qm9hcmRDb25maWcuY29scyAmJiB5ID49IDAgJiYgeSA8IHRoaXMuY3VycmVudEJvYXJkQ29uZmlnLnJvd3M7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5Zyo5oyH5a6a5L2N572u5Yib5bu6Ymlhb2pp6aKE5Yi25L2TXG4gICAgICogQHBhcmFtIHgg5qC85a2QeOWdkOagh1xuICAgICAqIEBwYXJhbSB5IOagvOWtkHnlnZDmoIdcbiAgICAgKi9cbiAgICBwdWJsaWMgY3JlYXRlQmlhb2ppUHJlZmFiKHg6IG51bWJlciwgeTogbnVtYmVyKSB7XG4gICAgICAgIGlmICghdGhpcy5iaWFvamlQcmVmYWIpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCJiaWFvamlQcmVmYWIg6aKE5Yi25L2T5pyq6K6+572u77yM6K+35Zyo57yW6L6R5Zmo5Lit5oyC6L29XCIpO1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8g5a6e5L6L5YyWYmlhb2pp6aKE5Yi25L2TXG4gICAgICAgIGNvbnN0IGJpYW9qaU5vZGUgPSBjYy5pbnN0YW50aWF0ZSh0aGlzLmJpYW9qaVByZWZhYik7XG4gICAgICAgIGJpYW9qaU5vZGUubmFtZSA9IFwiQmlhb2ppXCI7XG5cbiAgICAgICAgLy8g6K6+572u5L2N572uXG4gICAgICAgIGNvbnN0IHBvc2l0aW9uID0gdGhpcy5jYWxjdWxhdGVQcmVmYWJQb3NpdGlvbih4LCB5KTtcbiAgICAgICAgYmlhb2ppTm9kZS5zZXRQb3NpdGlvbihwb3NpdGlvbik7XG5cbiAgICAgICAgLy8g5re75Yqg5Yiw5qOL55uYXG4gICAgICAgIHRoaXMuY3VycmVudEJvYXJkTm9kZS5hZGRDaGlsZChiaWFvamlOb2RlKTtcblxuICAgICAgICAvLyDmkq3mlL7lh7rnjrDliqjnlLvvvIwxMHgxMOaji+ebmOS9v+eUqDAuOOe8qeaUvlxuICAgICAgICBjb25zdCB0YXJnZXRTY2FsZSA9IHRoaXMuY3VycmVudEJvYXJkVHlwZSA9PT0gXCIxMHgxMFwiID8gMC44IDogMS4wO1xuICAgICAgICBiaWFvamlOb2RlLnNldFNjYWxlKDApO1xuICAgICAgICBjYy50d2VlbihiaWFvamlOb2RlKVxuICAgICAgICAgICAgLnRvKDAuMiwgeyBzY2FsZVg6IHRhcmdldFNjYWxlLCBzY2FsZVk6IHRhcmdldFNjYWxlIH0sIHsgZWFzaW5nOiAnYmFja091dCcgfSlcbiAgICAgICAgICAgIC5zdGFydCgpO1xuXG4gICAgICAgIC8vIOabtOaWsOagvOWtkOaVsOaNrlxuICAgICAgICB0aGlzLmdyaWREYXRhW3hdW3ldLmhhc1BsYXllciA9IHRydWU7XG4gICAgICAgIHRoaXMuZ3JpZERhdGFbeF1beV0ucGxheWVyTm9kZSA9IGJpYW9qaU5vZGU7XG5cbiAgICAgICBcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDlnKjmjIflrprkvY3nva7liJvlu7pib29t6aKE5Yi25L2TXG4gICAgICogQHBhcmFtIHgg5qC85a2QeOWdkOagh1xuICAgICAqIEBwYXJhbSB5IOagvOWtkHnlnZDmoIdcbiAgICAgKiBAcGFyYW0gaXNDdXJyZW50VXNlciDmmK/lkKbmmK/lvZPliY3nlKjmiLfngrnliLDnmoTpm7fvvIjlj6/pgInvvIzpu5jorqTkuLp0cnVl5Lul5L+d5oyB5ZCR5ZCO5YW85a6577yJXG4gICAgICovXG4gICAgcHVibGljIGNyZWF0ZUJvb21QcmVmYWIoeDogbnVtYmVyLCB5OiBudW1iZXIsIGlzQ3VycmVudFVzZXI6IGJvb2xlYW4gPSB0cnVlKSB7XG4gICAgICAgXG5cbiAgICAgICAgaWYgKCF0aGlzLmJvb21QcmVmYWIpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCJTaW5nbGVDaGVzc0JvYXJkQ29udHJvbGxlcjogYm9vbVByZWZhYiDpooTliLbkvZPmnKrorr7nva7vvIzor7flnKjnvJbovpHlmajkuK3mjILovb1cIik7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cblxuICAgICAgICBcblxuICAgICAgICAvLyDlrp7kvovljJZib29t6aKE5Yi25L2TXG4gICAgICAgIGNvbnN0IGJvb21Ob2RlID0gY2MuaW5zdGFudGlhdGUodGhpcy5ib29tUHJlZmFiKTtcbiAgICAgICAgYm9vbU5vZGUubmFtZSA9IFwiQm9vbVwiO1xuXG4gICAgICAgIC8vIOiuvue9ruS9jee9rlxuICAgICAgICBjb25zdCBwb3NpdGlvbiA9IHRoaXMuY2FsY3VsYXRlUHJlZmFiUG9zaXRpb24oeCwgeSk7XG4gICAgICAgIGJvb21Ob2RlLnNldFBvc2l0aW9uKHBvc2l0aW9uKTtcblxuICAgICAgICAvLyDmt7vliqDliLDmo4vnm5hcbiAgICAgICAgdGhpcy5jdXJyZW50Qm9hcmROb2RlLmFkZENoaWxkKGJvb21Ob2RlKTtcblxuICAgICAgICAvLyDmkq3mlL7lh7rnjrDliqjnlLvvvIwxMHgxMOaji+ebmOS9v+eUqDAuOOe8qeaUvlxuICAgICAgICBjb25zdCB0YXJnZXRTY2FsZSA9IHRoaXMuY3VycmVudEJvYXJkVHlwZSA9PT0gXCIxMHgxMFwiID8gMC44IDogMS4wO1xuICAgICAgICBjb25zdCBib3VuY2VTY2FsZSA9IHRhcmdldFNjYWxlICogMS4yOyAvLyDlvLnot7PmlYjmnpzmr5Tnm67moIfnvKnmlL7lpKcyMCVcbiAgICAgICAgYm9vbU5vZGUuc2V0U2NhbGUoMCk7XG4gICAgICAgIGNjLnR3ZWVuKGJvb21Ob2RlKVxuICAgICAgICAgICAgLnRvKDAuMywgeyBzY2FsZVg6IGJvdW5jZVNjYWxlLCBzY2FsZVk6IGJvdW5jZVNjYWxlIH0sIHsgZWFzaW5nOiAnYmFja091dCcgfSlcbiAgICAgICAgICAgIC50bygwLjEsIHsgc2NhbGVYOiB0YXJnZXRTY2FsZSwgc2NhbGVZOiB0YXJnZXRTY2FsZSB9KVxuICAgICAgICAgICAgLnN0YXJ0KCk7XG5cbiAgICAgICAgLy8g5Y+q5pyJ5b2T5YmN55So5oi354K55Yiw6Zu35pe25omN5pKt5pS+5qOL55uY6ZyH5Yqo5pWI5p6cXG4gICAgICAgIGlmIChpc0N1cnJlbnRVc2VyKSB7XG4gICAgICAgICAgIFxuICAgICAgICAgICAgdGhpcy5wbGF5Qm9hcmRTaGFrZUFuaW1hdGlvbigpO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8g5pu05paw5qC85a2Q5pWw5o2uXG4gICAgICAgIHRoaXMuZ3JpZERhdGFbeF1beV0uaGFzUGxheWVyID0gdHJ1ZTtcbiAgICAgICAgdGhpcy5ncmlkRGF0YVt4XVt5XS5wbGF5ZXJOb2RlID0gYm9vbU5vZGU7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5Zyo5oyH5a6a5L2N572u5Yib5bu65pWw5a2X6aKE5Yi25L2TXG4gICAgICogQHBhcmFtIHgg5qC85a2QeOWdkOagh1xuICAgICAqIEBwYXJhbSB5IOagvOWtkHnlnZDmoIdcbiAgICAgKiBAcGFyYW0gbnVtYmVyIOaVsOWtlygxLTgpXG4gICAgICovXG4gICAgcHVibGljIGNyZWF0ZU51bWJlclByZWZhYih4OiBudW1iZXIsIHk6IG51bWJlciwgbnVtYmVyOiBudW1iZXIpIHtcbiAgICAgICAgaWYgKG51bWJlciA8IDEgfHwgbnVtYmVyID4gOCkge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcihg5peg5pWI55qE5pWw5a2XOiAke251bWJlcn1gKTtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIOiOt+WPluWvueW6lOeahOaVsOWtl+mihOWItuS9k1xuICAgICAgICBjb25zdCBwcmVmYWIgPSB0aGlzLmdldE51bWJlclByZWZhYihudW1iZXIpO1xuICAgICAgICBpZiAoIXByZWZhYikge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcihg5pWw5a2XJHtudW1iZXJ96aKE5Yi25L2T5pyq6K6+572uYCk7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cblxuICAgICAgICAvLyDlrp7kvovljJbmlbDlrZfpooTliLbkvZNcbiAgICAgICAgY29uc3QgbnVtYmVyTm9kZSA9IGNjLmluc3RhbnRpYXRlKHByZWZhYik7XG4gICAgICAgIG51bWJlck5vZGUubmFtZSA9IGBCb29tJHtudW1iZXJ9YDtcblxuICAgICAgICAvLyDorr7nva7kvY3nva5cbiAgICAgICAgY29uc3QgcG9zaXRpb24gPSB0aGlzLmNhbGN1bGF0ZVByZWZhYlBvc2l0aW9uKHgsIHkpO1xuICAgICAgICBudW1iZXJOb2RlLnNldFBvc2l0aW9uKHBvc2l0aW9uKTtcblxuICAgICAgICAvLyDmt7vliqDliLDmo4vnm5hcbiAgICAgICAgdGhpcy5jdXJyZW50Qm9hcmROb2RlLmFkZENoaWxkKG51bWJlck5vZGUpO1xuXG4gICAgICAgIC8vIOaSreaUvuWHuueOsOWKqOeUu++8jDEweDEw5qOL55uY5L2/55SoMC4457yp5pS+XG4gICAgICAgIGNvbnN0IHRhcmdldFNjYWxlID0gdGhpcy5jdXJyZW50Qm9hcmRUeXBlID09PSBcIjEweDEwXCIgPyAwLjggOiAxLjA7XG4gICAgICAgIG51bWJlck5vZGUuc2V0U2NhbGUoMCk7XG4gICAgICAgIGNjLnR3ZWVuKG51bWJlck5vZGUpXG4gICAgICAgICAgICAudG8oMC4yLCB7IHNjYWxlWDogdGFyZ2V0U2NhbGUsIHNjYWxlWTogdGFyZ2V0U2NhbGUgfSwgeyBlYXNpbmc6ICdiYWNrT3V0JyB9KVxuICAgICAgICAgICAgLnN0YXJ0KCk7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5Zyo5oyH5a6a5L2N572u5Yib5bu66Ieq5a6a5LmJ6aKE5Yi25L2T77yI55So5LqO5rWL6K+V562J5Yqf6IO977yJXG4gICAgICogQHBhcmFtIHgg5qC85a2QeOWdkOagh1xuICAgICAqIEBwYXJhbSB5IOagvOWtkHnlnZDmoIdcbiAgICAgKiBAcGFyYW0gcHJlZmFiIOimgeWIm+W7uueahOmihOWItuS9k1xuICAgICAqIEBwYXJhbSBuYW1lIOiKgueCueWQjeensFxuICAgICAqL1xuICAgIHB1YmxpYyBjcmVhdGVDdXN0b21QcmVmYWIoeDogbnVtYmVyLCB5OiBudW1iZXIsIHByZWZhYjogY2MuUHJlZmFiLCBuYW1lOiBzdHJpbmcgPSBcIkN1c3RvbVByZWZhYlwiKSB7XG4gICAgICAgXG4gICAgICAgIGlmICghcHJlZmFiKSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKFwi6aKE5Yi25L2T5pyq6K6+572uXCIpO1xuICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgIH1cblxuICAgICAgICBpZiAoIXRoaXMuY3VycmVudEJvYXJkTm9kZSkge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcihcImN1cnJlbnRCb2FyZE5vZGUg5pyq6K6+572u77yM5peg5rOV5re75Yqg6aKE5Yi25L2TXCIpO1xuICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgIH1cblxuICAgICAgICB0cnkge1xuICAgICAgICAgICAgLy8g5a6e5L6L5YyW6aKE5Yi25L2TXG4gICAgICAgICAgXG4gICAgICAgICAgICBjb25zdCBjdXN0b21Ob2RlID0gY2MuaW5zdGFudGlhdGUocHJlZmFiKTtcbiAgICAgICAgICAgIGN1c3RvbU5vZGUubmFtZSA9IG5hbWU7XG4gICAgICAgICAgIFxuXG4gICAgICAgICAgICAvLyDorr7nva7kvY3nva5cbiAgICAgICAgICAgXG4gICAgICAgICAgICBjb25zdCBwb3NpdGlvbiA9IHRoaXMuY2FsY3VsYXRlUHJlZmFiUG9zaXRpb24oeCwgeSk7XG4gICAgICAgICAgIFxuICAgICAgICAgICAgY3VzdG9tTm9kZS5zZXRQb3NpdGlvbihwb3NpdGlvbik7XG5cbiAgICAgICAgICAgIC8vIOa3u+WKoOWIsOaji+ebmFxuICAgICAgICAgICAgXG4gICAgICAgICAgICB0aGlzLmN1cnJlbnRCb2FyZE5vZGUuYWRkQ2hpbGQoY3VzdG9tTm9kZSk7XG4gICAgICAgICAgIFxuXG4gICAgICAgICAgICAvLyDmkq3mlL7lh7rnjrDliqjnlLvvvIwxMHgxMOaji+ebmOS9v+eUqDAuOOe8qeaUvlxuICAgICAgICAgICAgY29uc3QgdGFyZ2V0U2NhbGUgPSB0aGlzLmN1cnJlbnRCb2FyZFR5cGUgPT09IFwiMTB4MTBcIiA/IDAuOCA6IDEuMDtcbiAgICAgICAgICAgXG4gICAgICAgICAgICBjdXN0b21Ob2RlLnNldFNjYWxlKDApO1xuICAgICAgICAgICAgY2MudHdlZW4oY3VzdG9tTm9kZSlcbiAgICAgICAgICAgICAgICAudG8oMC4zLCB7IHNjYWxlWDogdGFyZ2V0U2NhbGUgKiAxLjIsIHNjYWxlWTogdGFyZ2V0U2NhbGUgKiAxLjIgfSwgeyBlYXNpbmc6ICdiYWNrT3V0JyB9KVxuICAgICAgICAgICAgICAgIC50bygwLjEsIHsgc2NhbGVYOiB0YXJnZXRTY2FsZSwgc2NhbGVZOiB0YXJnZXRTY2FsZSB9KVxuICAgICAgICAgICAgICAgIC5zdGFydCgpO1xuXG4gICAgICAgICAgXG4gICAgICAgICAgICByZXR1cm4gY3VzdG9tTm9kZTtcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgIGNjLmVycm9yKFwi4p2MIOWIm+W7uuiHquWumuS5iemihOWItuS9k+aXtuWPkeeUn+mUmeivrzpcIiwgZXJyb3IpO1xuICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDojrflj5bmlbDlrZfpooTliLbkvZNcbiAgICBwcml2YXRlIGdldE51bWJlclByZWZhYihudW1iZXI6IG51bWJlcik6IGNjLlByZWZhYiB8IG51bGwge1xuICAgICAgICBzd2l0Y2ggKG51bWJlcikge1xuICAgICAgICAgICAgY2FzZSAxOiByZXR1cm4gdGhpcy5ib29tMVByZWZhYjtcbiAgICAgICAgICAgIGNhc2UgMjogcmV0dXJuIHRoaXMuYm9vbTJQcmVmYWI7XG4gICAgICAgICAgICBjYXNlIDM6IHJldHVybiB0aGlzLmJvb20zUHJlZmFiO1xuICAgICAgICAgICAgY2FzZSA0OiByZXR1cm4gdGhpcy5ib29tNFByZWZhYjtcbiAgICAgICAgICAgIGNhc2UgNTogcmV0dXJuIHRoaXMuYm9vbTVQcmVmYWI7XG4gICAgICAgICAgICBjYXNlIDY6IHJldHVybiB0aGlzLmJvb202UHJlZmFiO1xuICAgICAgICAgICAgY2FzZSA3OiByZXR1cm4gdGhpcy5ib29tN1ByZWZhYjtcbiAgICAgICAgICAgIGNhc2UgODogcmV0dXJuIHRoaXMuYm9vbThQcmVmYWI7XG4gICAgICAgICAgICBkZWZhdWx0OiByZXR1cm4gbnVsbDtcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOiuoeeul+mihOWItuS9k+eahOeyvuehruS9jee9ru+8iOWPguiAg+iBlOacuueJiENoZXNzQm9hcmRDb250cm9sbGVy77yJXG4gICAgICogQHBhcmFtIHgg5qC85a2QeOWdkOagh1xuICAgICAqIEBwYXJhbSB5IOagvOWtkHnlnZDmoIdcbiAgICAgKiBAcmV0dXJucyDpooTliLbkvZPlupTor6XmlL7nva7nmoTnsr7noa7kvY3nva5cbiAgICAgKi9cbiAgICBwcml2YXRlIGNhbGN1bGF0ZVByZWZhYlBvc2l0aW9uKHg6IG51bWJlciwgeTogbnVtYmVyKTogY2MuVmVjMiB7XG4gICAgICAgIGlmICghdGhpcy5jdXJyZW50Qm9hcmRDb25maWcpIHtcbiAgICAgICAgICAgIHJldHVybiBjYy52MigwLCAwKTtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIOagueaNruS4jeWQjOaji+ebmOexu+Wei+S9v+eUqOS4jeWQjOeahOiuoeeul+aWueW8j1xuICAgICAgICBzd2l0Y2ggKHRoaXMuY3VycmVudEJvYXJkVHlwZSkge1xuICAgICAgICAgICAgY2FzZSBcIjh4OFwiOlxuICAgICAgICAgICAgICAgIHJldHVybiB0aGlzLmNhbGN1bGF0ZVByZWZhYlBvc2l0aW9uRm9yOHg4KHgsIHkpO1xuICAgICAgICAgICAgY2FzZSBcIjh4OVwiOlxuICAgICAgICAgICAgICAgIHJldHVybiB0aGlzLmNhbGN1bGF0ZVByZWZhYlBvc2l0aW9uRm9yOHg5KHgsIHkpO1xuICAgICAgICAgICAgY2FzZSBcIjl4OVwiOlxuICAgICAgICAgICAgICAgIHJldHVybiB0aGlzLmNhbGN1bGF0ZVByZWZhYlBvc2l0aW9uRm9yOXg5KHgsIHkpO1xuICAgICAgICAgICAgY2FzZSBcIjl4MTBcIjpcbiAgICAgICAgICAgICAgICByZXR1cm4gdGhpcy5jYWxjdWxhdGVQcmVmYWJQb3NpdGlvbkZvcjl4MTAoeCwgeSk7XG4gICAgICAgICAgICBjYXNlIFwiMTB4MTBcIjpcbiAgICAgICAgICAgICAgICByZXR1cm4gdGhpcy5jYWxjdWxhdGVQcmVmYWJQb3NpdGlvbkZvcjEweDEwKHgsIHkpO1xuICAgICAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgICAgICByZXR1cm4gdGhpcy5nZXRHcmlkV29ybGRQb3NpdGlvbih4LCB5KTtcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8vIDh4OOaji+ebmOeahOmihOWItuS9k+S9jee9ruiuoeeul++8iOWPguiAg+iBlOacuueJiO+8iVxuICAgIHByaXZhdGUgY2FsY3VsYXRlUHJlZmFiUG9zaXRpb25Gb3I4eDgoeDogbnVtYmVyLCB5OiBudW1iZXIpOiBjYy5WZWMyIHtcbiAgICAgICAgLy8g5qC55o2u6IGU5py654mI55qE5Z2Q5qCH6KeE5b6L6K6h566X77yaXG4gICAgICAgIC8vICgwLDApIOKGkiAoLTMxNCwgLTMxMClcbiAgICAgICAgLy8gKDEsMCkg4oaSICgtMjI0LCAtMzEwKSAgLy8geOWinuWKoDkwXG4gICAgICAgIC8vICgwLDEpIOKGkiAoLTMxNCwgLTIyMikgIC8vIHnlop7liqA4OFxuICAgICAgICAvLyAoNyw3KSDihpIgKDMxMCwgMzEyKVxuICAgICAgICBjb25zdCBzdGFydFggPSAtMzE0OyAgLy8g6LW35aeLWOWdkOagh1xuICAgICAgICBjb25zdCBzdGFydFkgPSAtMzEwOyAgLy8g6LW35aeLWeWdkOagh1xuICAgICAgICBjb25zdCBzdGVwWCA9IDkwOyAgICAgLy8gWOaWueWQkeatpemVv1xuICAgICAgICBjb25zdCBzdGVwWSA9IDg4OyAgICAgLy8gWeaWueWQkeatpemVv1xuXG4gICAgICAgIGNvbnN0IGZpbmFsWCA9IHN0YXJ0WCArICh4ICogc3RlcFgpO1xuICAgICAgICBjb25zdCBmaW5hbFkgPSBzdGFydFkgKyAoeSAqIHN0ZXBZKTtcblxuICAgICAgICByZXR1cm4gY2MudjIoZmluYWxYLCBmaW5hbFkpO1xuICAgIH1cblxuICAgIC8vIDh4Oeaji+ebmOeahOmihOWItuS9k+S9jee9ruiuoeeul++8iOWfuuS6jueyvuehruWdkOagh++8jOW3puS4i+inkuS4uigwLDAp77yJXG4gICAgcHJpdmF0ZSBjYWxjdWxhdGVQcmVmYWJQb3NpdGlvbkZvcjh4OSh4OiBudW1iZXIsIHk6IG51bWJlcik6IGNjLlZlYzIge1xuICAgICAgICAvLyDmoLnmja7mj5DkvpvnmoTnsr7noa7lnZDmoIfvvJpcbiAgICAgICAgLy8g5bem5LiL6KeSKDAsMCnvvJooLTMyMSwgLTM2NClcbiAgICAgICAgLy8g5Y+z5LiL6KeSKDcsMCnvvJooMzE3LCAtMzY0KVxuICAgICAgICAvLyDlt6bkuIrop5IoMCw4Ke+8migtMzIxLCAzNjUpXG4gICAgICAgIC8vIOWPs+S4iuinkig3LDgp77yaKDMxNywgMzY1KVxuXG4gICAgICAgIC8vIOiuoeeul+atpemVv++8mlxuICAgICAgICAvLyBY5pa55ZCR5q2l6ZW/77yaKDMxNyAtICgtMzIxKSkgLyA3ID0gNjM4IC8gNyDiiYggOTEuMTRcbiAgICAgICAgLy8gWeaWueWQkeatpemVv++8migzNjUgLSAoLTM2NCkpIC8gOCA9IDcyOSAvIDgg4omIIDkxLjEyNVxuXG4gICAgICAgIGNvbnN0IHN0YXJ0WCA9IC0zMjE7ICAvLyDlt6bkuIvop5JY5Z2Q5qCHXG4gICAgICAgIGNvbnN0IHN0YXJ0WSA9IC0zNjQ7ICAvLyDlt6bkuIvop5JZ5Z2Q5qCHXG4gICAgICAgIGNvbnN0IHN0ZXBYID0gOTEuMTQ7ICAvLyBY5pa55ZCR57K+56Gu5q2l6ZW/XG4gICAgICAgIGNvbnN0IHN0ZXBZID0gOTEuMTI1OyAvLyBZ5pa55ZCR57K+56Gu5q2l6ZW/XG5cbiAgICAgICAgY29uc3QgZmluYWxYID0gc3RhcnRYICsgKHggKiBzdGVwWCk7XG4gICAgICAgIGNvbnN0IGZpbmFsWSA9IHN0YXJ0WSArICh5ICogc3RlcFkpO1xuXG4gICAgICBcbiAgICAgICAgcmV0dXJuIGNjLnYyKGZpbmFsWCwgZmluYWxZKTtcbiAgICB9XG5cbiAgICAvLyA5eDnmo4vnm5jnmoTpooTliLbkvZPkvY3nva7orqHnrpfvvIjln7rkuo7nsr7noa7lnZDmoIfvvIzlt6bkuIvop5LkuLooMCwwKe+8iVxuICAgIHByaXZhdGUgY2FsY3VsYXRlUHJlZmFiUG9zaXRpb25Gb3I5eDkoeDogbnVtYmVyLCB5OiBudW1iZXIpOiBjYy5WZWMyIHtcbiAgICAgICAgLy8g5qC55o2u5o+Q5L6b55qE57K+56Gu5Z2Q5qCH77yaXG4gICAgICAgIC8vIOW3puS4i+inkigwLDAp77yaKC0zMjIsIC0zMjApXG4gICAgICAgIC8vIOWPs+S4i+inkig4LDAp77yaKDMyMCwgLTMyMClcbiAgICAgICAgLy8g5bem5LiK6KeSKDAsOCnvvJooLTMyMiwgMzY1KVxuICAgICAgICAvLyDlj7PkuIrop5IoOCw4Ke+8migzMjAsIDMyMylcblxuICAgICAgICAvLyDorqHnrpfmraXplb/vvJpcbiAgICAgICAgLy8gWOaWueWQkeatpemVv++8migzMjAgLSAoLTMyMikpIC8gOCA9IDY0MiAvIDggPSA4MC4yNVxuICAgICAgICAvLyBZ5pa55ZCR5q2l6ZW/77yaKDMyMyAtICgtMzIwKSkgLyA4ID0gNjQzIC8gOCA9IDgwLjM3NVxuXG4gICAgICAgIGNvbnN0IHN0YXJ0WCA9IC0zMjI7ICAvLyDlt6bkuIvop5JY5Z2Q5qCHXG4gICAgICAgIGNvbnN0IHN0YXJ0WSA9IC0zMjA7ICAvLyDlt6bkuIvop5JZ5Z2Q5qCHXG4gICAgICAgIGNvbnN0IHN0ZXBYID0gODAuMjU7ICAvLyBY5pa55ZCR57K+56Gu5q2l6ZW/XG4gICAgICAgIGNvbnN0IHN0ZXBZID0gODAuMzc1OyAvLyBZ5pa55ZCR57K+56Gu5q2l6ZW/XG5cbiAgICAgICAgY29uc3QgZmluYWxYID0gc3RhcnRYICsgKHggKiBzdGVwWCk7XG4gICAgICAgIGNvbnN0IGZpbmFsWSA9IHN0YXJ0WSArICh5ICogc3RlcFkpO1xuXG4gICAgICAgIFxuICAgICAgICByZXR1cm4gY2MudjIoZmluYWxYLCBmaW5hbFkpO1xuICAgIH1cblxuICAgIC8vIDl4MTDmo4vnm5jnmoTpooTliLbkvZPkvY3nva7orqHnrpfvvIjln7rkuo7nsr7noa7lnZDmoIfvvIzlt6bkuIvop5LkuLooMCwwKe+8iVxuICAgIHByaXZhdGUgY2FsY3VsYXRlUHJlZmFiUG9zaXRpb25Gb3I5eDEwKHg6IG51bWJlciwgeTogbnVtYmVyKTogY2MuVmVjMiB7XG4gICAgICAgIC8vIOagueaNruaPkOS+m+eahOeyvuehruWdkOagh++8mlxuICAgICAgICAvLyDlt6bkuIvop5IoMCwwKe+8migtMzIwLCAtMzYxKVxuICAgICAgICAvLyDlj7PkuIvop5IoOCwwKe+8migzMjAsIC0zNjEpXG4gICAgICAgIC8vIOW3puS4iuinkigwLDkp77yaKC0zMjAsIDM2MilcbiAgICAgICAgLy8g5Y+z5LiK6KeSKDgsOSnvvJooMzIwLCAzNjIpXG5cbiAgICAgICAgLy8g6K6h566X5q2l6ZW/77yaXG4gICAgICAgIC8vIFjmlrnlkJHmraXplb/vvJooMzIwIC0gKC0zMjApKSAvIDggPSA2NDAgLyA4ID0gODBcbiAgICAgICAgLy8gWeaWueWQkeatpemVv++8migzNjIgLSAoLTM2MSkpIC8gOSA9IDcyMyAvIDkgPSA4MC4zM1xuXG4gICAgICAgIGNvbnN0IHN0YXJ0WCA9IC0zMjA7ICAvLyDlt6bkuIvop5JY5Z2Q5qCHXG4gICAgICAgIGNvbnN0IHN0YXJ0WSA9IC0zNjE7ICAvLyDlt6bkuIvop5JZ5Z2Q5qCHXG4gICAgICAgIGNvbnN0IHN0ZXBYID0gODA7ICAgICAvLyBY5pa55ZCR57K+56Gu5q2l6ZW/XG4gICAgICAgIGNvbnN0IHN0ZXBZID0gODAuMzM7ICAvLyBZ5pa55ZCR57K+56Gu5q2l6ZW/XG5cbiAgICAgICAgY29uc3QgZmluYWxYID0gc3RhcnRYICsgKHggKiBzdGVwWCk7XG4gICAgICAgIGNvbnN0IGZpbmFsWSA9IHN0YXJ0WSArICh5ICogc3RlcFkpO1xuXG4gICAgICAgXG4gICAgICAgIHJldHVybiBjYy52MihmaW5hbFgsIGZpbmFsWSk7XG4gICAgfVxuXG4gICAgLy8gMTB4MTDmo4vnm5jnmoTpooTliLbkvZPkvY3nva7orqHnrpfvvIjln7rkuo7nsr7noa7lnZDmoIfvvIzlt6bkuIvop5LkuLooMCwwKe+8iVxuICAgIHByaXZhdGUgY2FsY3VsYXRlUHJlZmFiUG9zaXRpb25Gb3IxMHgxMCh4OiBudW1iZXIsIHk6IG51bWJlcik6IGNjLlZlYzIge1xuICAgICAgICAvLyDmoLnmja7mj5DkvpvnmoTnsr7noa7lnZDmoIfvvJpcbiAgICAgICAgLy8g5bem5LiL6KeSKDAsMCnvvJooLTMyOCwgLTMyMilcbiAgICAgICAgLy8g5Y+z5LiL6KeSKDksMCnvvJooMzI1LCAtMzIyKVxuICAgICAgICAvLyDlt6bkuIrop5IoMCw5Ke+8migtMzI4LCAzMjYpXG4gICAgICAgIC8vIOWPs+S4iuinkig5LDkp77yaKDMyNSwgMzI2KVxuXG4gICAgICAgIC8vIOiuoeeul+atpemVv++8mlxuICAgICAgICAvLyBY5pa55ZCR5q2l6ZW/77yaKDMyNSAtICgtMzI4KSkgLyA5ID0gNjUzIC8gOSA9IDcyLjU2XG4gICAgICAgIC8vIFnmlrnlkJHmraXplb/vvJooMzI2IC0gKC0zMjIpKSAvIDkgPSA2NDggLyA5ID0gNzJcblxuICAgICAgICBjb25zdCBzdGFydFggPSAtMzI4OyAgLy8g5bem5LiL6KeSWOWdkOagh1xuICAgICAgICBjb25zdCBzdGFydFkgPSAtMzIyOyAgLy8g5bem5LiL6KeSWeWdkOagh1xuICAgICAgICBjb25zdCBzdGVwWCA9IDcyLjU2OyAgLy8gWOaWueWQkeeyvuehruatpemVv1xuICAgICAgICBjb25zdCBzdGVwWSA9IDcyOyAgICAgLy8gWeaWueWQkeeyvuehruatpemVv1xuXG4gICAgICAgIGNvbnN0IGZpbmFsWCA9IHN0YXJ0WCArICh4ICogc3RlcFgpO1xuICAgICAgICBjb25zdCBmaW5hbFkgPSBzdGFydFkgKyAoeSAqIHN0ZXBZKTtcblxuICAgICAgIFxuICAgICAgICByZXR1cm4gY2MudjIoZmluYWxYLCBmaW5hbFkpO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOaSreaUvuaji+ebmOmch+WKqOWKqOeUu++8iOWMheaLrOaJgOacieWwj+agvOWtkO+8iVxuICAgICAqL1xuICAgIHByaXZhdGUgcGxheUJvYXJkU2hha2VBbmltYXRpb24oKSB7XG4gICAgICAgIGlmICghdGhpcy5jdXJyZW50Qm9hcmROb2RlKSByZXR1cm47XG5cbiAgICAgICAgLy8g6ZyH5Yqo5Y+C5pWwIC0g5aKe5by66ZyH5Yqo5pWI5p6cXG4gICAgICAgIGNvbnN0IHNoYWtlSW50ZW5zaXR5ID0gMzA7IC8vIOmch+WKqOW8uuW6plxuICAgICAgICBjb25zdCBzaGFrZUR1cmF0aW9uID0gMS4wOyAvLyDpnIfliqjmjIHnu63ml7bpl7RcbiAgICAgICAgY29uc3Qgc2hha2VGcmVxdWVuY3kgPSA0MDsgLy8g6ZyH5Yqo6aKR546HXG5cbiAgICAgICAgLy8g6ZyH5Yqo5qOL55uYXG4gICAgICAgIHRoaXMuc2hha2VCb2FyZE5vZGUoc2hha2VJbnRlbnNpdHksIHNoYWtlRHVyYXRpb24sIHNoYWtlRnJlcXVlbmN5KTtcblxuICAgICAgICAvLyDpnIfliqjmiYDmnInlsI/moLzlrZBcbiAgICAgICAgdGhpcy5zaGFrZUFsbEdyaWRzKHNoYWtlSW50ZW5zaXR5ICogMC42LCBzaGFrZUR1cmF0aW9uLCBzaGFrZUZyZXF1ZW5jeSk7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog6ZyH5Yqo5qOL55uY6IqC54K5XG4gICAgICovXG4gICAgcHJpdmF0ZSBzaGFrZUJvYXJkTm9kZShpbnRlbnNpdHk6IG51bWJlciwgZHVyYXRpb246IG51bWJlciwgZnJlcXVlbmN5OiBudW1iZXIpIHtcbiAgICAgICAgLy8g5L+d5a2Y5Y6f5aeL5L2N572uXG4gICAgICAgIGNvbnN0IG9yaWdpbmFsUG9zaXRpb24gPSB0aGlzLmN1cnJlbnRCb2FyZE5vZGUucG9zaXRpb24uY2xvbmUoKTtcblxuICAgICAgICAvLyDliJvlu7rpnIfliqjliqjnlLvvvIzkvb/nlKjpgJLlh4/lvLrluqZcbiAgICAgICAgbGV0IGN1cnJlbnRJbnRlbnNpdHkgPSBpbnRlbnNpdHk7XG4gICAgICAgIGNvbnN0IGludGVuc2l0eURlY2F5ID0gMC45MjsgLy8g5by65bqm6KGw5YeP57O75pWwXG5cbiAgICAgICAgY29uc3QgY3JlYXRlU2hha2VTdGVwID0gKHNoYWtlSW50ZW5zaXR5OiBudW1iZXIpID0+IHtcbiAgICAgICAgICAgIHJldHVybiBjYy50d2VlbigpXG4gICAgICAgICAgICAgICAgLnRvKDAuMDI1LCB7XG4gICAgICAgICAgICAgICAgICAgIHg6IG9yaWdpbmFsUG9zaXRpb24ueCArIChNYXRoLnJhbmRvbSgpIC0gMC41KSAqIHNoYWtlSW50ZW5zaXR5ICogMixcbiAgICAgICAgICAgICAgICAgICAgeTogb3JpZ2luYWxQb3NpdGlvbi55ICsgKE1hdGgucmFuZG9tKCkgLSAwLjUpICogc2hha2VJbnRlbnNpdHkgKiAyXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgIH07XG5cbiAgICAgICAgLy8g5Yib5bu66ZyH5Yqo5bqP5YiX77yM5by65bqm6YCQ5riQ6KGw5YePXG4gICAgICAgIGxldCBzaGFrZVR3ZWVuID0gY2MudHdlZW4odGhpcy5jdXJyZW50Qm9hcmROb2RlKTtcbiAgICAgICAgY29uc3QgdG90YWxTdGVwcyA9IE1hdGguZmxvb3IoZHVyYXRpb24gKiBmcmVxdWVuY3kpO1xuXG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdG90YWxTdGVwczsgaSsrKSB7XG4gICAgICAgICAgICBzaGFrZVR3ZWVuID0gc2hha2VUd2Vlbi50aGVuKGNyZWF0ZVNoYWtlU3RlcChjdXJyZW50SW50ZW5zaXR5KSk7XG4gICAgICAgICAgICBjdXJyZW50SW50ZW5zaXR5ICo9IGludGVuc2l0eURlY2F5OyAvLyDpgJDmuJDlh4/lvLHpnIfliqjlvLrluqZcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIOacgOWQjuaBouWkjeWIsOWOn+S9jee9rlxuICAgICAgICBzaGFrZVR3ZWVuLnRvKDAuMiwge1xuICAgICAgICAgICAgeDogb3JpZ2luYWxQb3NpdGlvbi54LFxuICAgICAgICAgICAgeTogb3JpZ2luYWxQb3NpdGlvbi55XG4gICAgICAgIH0sIHsgZWFzaW5nOiAnYmFja091dCcgfSlcbiAgICAgICAgLnN0YXJ0KCk7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog6ZyH5Yqo5omA5pyJ5bCP5qC85a2QXG4gICAgICovXG4gICAgcHJpdmF0ZSBzaGFrZUFsbEdyaWRzKGludGVuc2l0eTogbnVtYmVyLCBkdXJhdGlvbjogbnVtYmVyLCBmcmVxdWVuY3k6IG51bWJlcikge1xuICAgICAgICBpZiAoIXRoaXMuZ3JpZE5vZGVzKSByZXR1cm47XG5cbiAgICAgICAgLy8g6YGN5Y6G5omA5pyJ5qC85a2Q6IqC54K5XG4gICAgICAgIGZvciAobGV0IHggPSAwOyB4IDwgdGhpcy5ncmlkTm9kZXMubGVuZ3RoOyB4KyspIHtcbiAgICAgICAgICAgIGlmICghdGhpcy5ncmlkTm9kZXNbeF0pIGNvbnRpbnVlO1xuXG4gICAgICAgICAgICBmb3IgKGxldCB5ID0gMDsgeSA8IHRoaXMuZ3JpZE5vZGVzW3hdLmxlbmd0aDsgeSsrKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgZ3JpZE5vZGUgPSB0aGlzLmdyaWROb2Rlc1t4XVt5XTtcbiAgICAgICAgICAgICAgICBpZiAoIWdyaWROb2RlIHx8ICFncmlkTm9kZS5hY3RpdmUpIGNvbnRpbnVlO1xuXG4gICAgICAgICAgICAgICAgLy8g5Li65q+P5Liq5qC85a2Q5Yib5bu654us56uL55qE6ZyH5Yqo5Yqo55S7XG4gICAgICAgICAgICAgICAgdGhpcy5zaGFrZUdyaWROb2RlKGdyaWROb2RlLCBpbnRlbnNpdHksIGR1cmF0aW9uLCBmcmVxdWVuY3kpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog6ZyH5Yqo5Y2V5Liq5qC85a2Q6IqC54K5XG4gICAgICovXG4gICAgcHJpdmF0ZSBzaGFrZUdyaWROb2RlKGdyaWROb2RlOiBjYy5Ob2RlLCBpbnRlbnNpdHk6IG51bWJlciwgZHVyYXRpb246IG51bWJlciwgZnJlcXVlbmN5OiBudW1iZXIpIHtcbiAgICAgICAgLy8g5L+d5a2Y5Y6f5aeL5L2N572uXG4gICAgICAgIGNvbnN0IG9yaWdpbmFsUG9zaXRpb24gPSBncmlkTm9kZS5wb3NpdGlvbi5jbG9uZSgpO1xuXG4gICAgICAgIC8vIOS4uuavj+S4quagvOWtkOa3u+WKoOmaj+acuuW7tui/n++8jOWIm+mAoOazoua1quaViOaenFxuICAgICAgICBjb25zdCByYW5kb21EZWxheSA9IE1hdGgucmFuZG9tKCkgKiAwLjE7XG5cbiAgICAgICAgdGhpcy5zY2hlZHVsZU9uY2UoKCkgPT4ge1xuICAgICAgICAgICAgLy8g5Yib5bu66ZyH5Yqo5Yqo55S777yM5L2/55So6YCS5YeP5by65bqmXG4gICAgICAgICAgICBsZXQgY3VycmVudEludGVuc2l0eSA9IGludGVuc2l0eTtcbiAgICAgICAgICAgIGNvbnN0IGludGVuc2l0eURlY2F5ID0gMC45NDsgLy8g5qC85a2Q6ZyH5Yqo6KGw5YeP56iN5oWi5LiA5LqbXG5cbiAgICAgICAgICAgIGNvbnN0IGNyZWF0ZUdyaWRTaGFrZVN0ZXAgPSAoc2hha2VJbnRlbnNpdHk6IG51bWJlcikgPT4ge1xuICAgICAgICAgICAgICAgIHJldHVybiBjYy50d2VlbigpXG4gICAgICAgICAgICAgICAgICAgIC50bygwLjAyLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICB4OiBvcmlnaW5hbFBvc2l0aW9uLnggKyAoTWF0aC5yYW5kb20oKSAtIDAuNSkgKiBzaGFrZUludGVuc2l0eSAqIDIsXG4gICAgICAgICAgICAgICAgICAgICAgICB5OiBvcmlnaW5hbFBvc2l0aW9uLnkgKyAoTWF0aC5yYW5kb20oKSAtIDAuNSkgKiBzaGFrZUludGVuc2l0eSAqIDJcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9O1xuXG4gICAgICAgICAgICAvLyDliJvlu7rpnIfliqjluo/liJdcbiAgICAgICAgICAgIGxldCBzaGFrZVR3ZWVuID0gY2MudHdlZW4oZ3JpZE5vZGUpO1xuICAgICAgICAgICAgY29uc3QgdG90YWxTdGVwcyA9IE1hdGguZmxvb3IoZHVyYXRpb24gKiBmcmVxdWVuY3kgKiAwLjgpOyAvLyDmoLzlrZDpnIfliqjml7bpl7TnqI3nn61cblxuICAgICAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0b3RhbFN0ZXBzOyBpKyspIHtcbiAgICAgICAgICAgICAgICBzaGFrZVR3ZWVuID0gc2hha2VUd2Vlbi50aGVuKGNyZWF0ZUdyaWRTaGFrZVN0ZXAoY3VycmVudEludGVuc2l0eSkpO1xuICAgICAgICAgICAgICAgIGN1cnJlbnRJbnRlbnNpdHkgKj0gaW50ZW5zaXR5RGVjYXk7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIC8vIOacgOWQjuaBouWkjeWIsOWOn+S9jee9rlxuICAgICAgICAgICAgc2hha2VUd2Vlbi50bygwLjE1LCB7XG4gICAgICAgICAgICAgICAgeDogb3JpZ2luYWxQb3NpdGlvbi54LFxuICAgICAgICAgICAgICAgIHk6IG9yaWdpbmFsUG9zaXRpb24ueVxuICAgICAgICAgICAgfSwgeyBlYXNpbmc6ICdiYWNrT3V0JyB9KVxuICAgICAgICAgICAgLnN0YXJ0KCk7XG4gICAgICAgIH0sIHJhbmRvbURlbGF5KTtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDmmL7npLrmiYDmnInpmpDol4/nmoTmoLzlrZDvvIjmuLjmiI/nu5PmnZ/ml7bosIPnlKjvvIlcbiAgICAgKi9cbiAgICBwdWJsaWMgc2hvd0FsbEhpZGRlbkdyaWRzKCkge1xuICAgICAgICBpZiAoIXRoaXMuY3VycmVudEJvYXJkTm9kZSkge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcihcIuaji+ebmOiKgueCueacquiuvue9ru+8jOaXoOazleaYvuekuumakOiXj+agvOWtkO+8gVwiKTtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIOmBjeWOhuaji+ebmOeahOaJgOacieWtkOiKgueCue+8jOaJvuWIsOWwj+agvOWtkOW5tuaYvuekulxuICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHRoaXMuY3VycmVudEJvYXJkTm9kZS5jaGlsZHJlbi5sZW5ndGg7IGkrKykge1xuICAgICAgICAgICAgY29uc3QgY2hpbGQgPSB0aGlzLmN1cnJlbnRCb2FyZE5vZGUuY2hpbGRyZW5baV07XG5cbiAgICAgICAgICAgIC8vIOWmguaenOaYr+Wwj+agvOWtkOiKgueCuVxuICAgICAgICAgICAgaWYgKGNoaWxkLm5hbWUuc3RhcnRzV2l0aChcIkdyaWRfXCIpIHx8IGNoaWxkLm5hbWUgPT09IFwiYmxvY2tcIikge1xuICAgICAgICAgICAgICAgIC8vIOWBnOatouaJgOacieWPr+iDveato+WcqOi/m+ihjOeahOWKqOeUu1xuICAgICAgICAgICAgICAgIGNoaWxkLnN0b3BBbGxBY3Rpb25zKCk7XG5cbiAgICAgICAgICAgICAgICAvLyDmgaLlpI3mmL7npLrnirbmgIFcbiAgICAgICAgICAgICAgICBjaGlsZC5hY3RpdmUgPSB0cnVlO1xuICAgICAgICAgICAgICAgIGNoaWxkLm9wYWNpdHkgPSAyNTU7XG4gICAgICAgICAgICAgICAgY2hpbGQuc2NhbGVYID0gMTtcbiAgICAgICAgICAgICAgICBjaGlsZC5zY2FsZVkgPSAxO1xuICAgICAgICAgICAgICAgIGNoaWxkLmFuZ2xlID0gMDsgLy8g6YeN572u5peL6L2s6KeS5bqmXG5cbiAgICAgICAgICAgICAgICAvLyDmgaLlpI3ljp/lp4vkvY3nva7vvIjlpoLmnpzmnInkv53lrZjnmoTor53vvIlcbiAgICAgICAgICAgICAgICBpZiAoY2hpbGRbJ29yaWdpbmFsUG9zaXRpb24nXSkge1xuICAgICAgICAgICAgICAgICAgICBjaGlsZC5zZXRQb3NpdGlvbihjaGlsZFsnb3JpZ2luYWxQb3NpdGlvbiddKTtcbiAgICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgICAvLyDnoa7kv53moLzlrZDlj6/ku6XkuqTkupJcbiAgICAgICAgICAgICAgICBjb25zdCBidXR0b24gPSBjaGlsZC5nZXRDb21wb25lbnQoY2MuQnV0dG9uKTtcbiAgICAgICAgICAgICAgICBpZiAoYnV0dG9uKSB7XG4gICAgICAgICAgICAgICAgICAgIGJ1dHRvbi5lbmFibGVkID0gdHJ1ZTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDmuIXpmaTmiYDmnInpooTliLbkvZPvvIjmuLjmiI/nu5PmnZ/ml7bosIPnlKjvvIlcbiAgICAgKi9cbiAgICBwdWJsaWMgY2xlYXJBbGxQcmVmYWJzKCkge1xuICAgICAgICBpZiAoIXRoaXMuY3VycmVudEJvYXJkTm9kZSkge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcihcIuaji+ebmOiKgueCueacquiuvue9ru+8jOaXoOazlea4hemZpOmihOWItuS9k++8gVwiKTtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuXG4gICAgICAgIGNvbnN0IGNoaWxkcmVuVG9SZW1vdmU6IGNjLk5vZGVbXSA9IFtdO1xuXG4gICAgICAgIC8vIOmBjeWOhuaji+ebmOeahOaJgOacieWtkOiKgueCuVxuICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHRoaXMuY3VycmVudEJvYXJkTm9kZS5jaGlsZHJlbi5sZW5ndGg7IGkrKykge1xuICAgICAgICAgICAgY29uc3QgY2hpbGQgPSB0aGlzLmN1cnJlbnRCb2FyZE5vZGUuY2hpbGRyZW5baV07XG5cbiAgICAgICAgICAgIC8vIOajgOafpeaYr+WQpuaYr+mihOWItuS9k++8iOmAmui/h+WQjeensOWIpOaWre+8iVxuICAgICAgICAgICAgaWYgKGNoaWxkLm5hbWUgPT09IFwiQmlhb2ppXCIgfHwgY2hpbGQubmFtZSA9PT0gXCJCb29tXCIgfHwgY2hpbGQubmFtZS5zdGFydHNXaXRoKFwiQm9vbVwiKSkge1xuICAgICAgICAgICAgICAgIGNoaWxkcmVuVG9SZW1vdmUucHVzaChjaGlsZCk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICAvLyDnp7vpmaTmib7liLDnmoTpooTliLbkvZNcbiAgICAgICAgY2hpbGRyZW5Ub1JlbW92ZS5mb3JFYWNoKGNoaWxkID0+IHtcbiAgICAgICAgICAgIGNoaWxkLnJlbW92ZUZyb21QYXJlbnQoKTtcbiAgICAgICAgfSk7XG5cbiAgICAgICAgLy8g6YeN572u5qC85a2Q5pWw5o2uXG4gICAgICAgIHRoaXMucmVpbml0aWFsaXplQm9hcmREYXRhKCk7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog6YeN5paw5Yid5aeL5YyW5qOL55uY5pWw5o2u77yI5LuF5Zyo5byA5aeL5paw5ri45oiP5pe26LCD55So77yJXG4gICAgICovXG4gICAgcHJpdmF0ZSByZWluaXRpYWxpemVCb2FyZERhdGEoKSB7XG4gICAgICAgIGlmICghdGhpcy5jdXJyZW50Qm9hcmRDb25maWcpIHJldHVybjtcblxuICAgICAgXG5cbiAgICAgICAgLy8g6YeN572uZ3JpZERhdGHkuK3nmoTpooTliLbkvZPnirbmgIFcbiAgICAgICAgZm9yIChsZXQgeCA9IDA7IHggPCB0aGlzLmN1cnJlbnRCb2FyZENvbmZpZy5jb2xzOyB4KyspIHtcbiAgICAgICAgICAgIGZvciAobGV0IHkgPSAwOyB5IDwgdGhpcy5jdXJyZW50Qm9hcmRDb25maWcucm93czsgeSsrKSB7XG4gICAgICAgICAgICAgICAgaWYgKHRoaXMuZ3JpZERhdGFbeF0gJiYgdGhpcy5ncmlkRGF0YVt4XVt5XSkge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLmdyaWREYXRhW3hdW3ldLmhhc1BsYXllciA9IGZhbHNlO1xuICAgICAgICAgICAgICAgICAgICB0aGlzLmdyaWREYXRhW3hdW3ldLnBsYXllck5vZGUgPSBudWxsO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOWkhOeQhkV4dGVuZExldmVsSW5mb+a2iOaBr++8iOa4uOaIj+e7k+adn+aXtuiwg+eUqO+8iVxuICAgICAqL1xuICAgIHB1YmxpYyBvbkV4dGVuZExldmVsSW5mbygpIHtcbiAgICAgICAgdGhpcy5zaG93QWxsSGlkZGVuR3JpZHMoKTtcbiAgICAgICAgdGhpcy5jbGVhckFsbFByZWZhYnMoKTtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDlpITnkIZFeHRlbmRMZXZlbEluZm/mlq3nur/ph43ov57vvIjmgaLlpI3muLjmiI/nirbmgIHvvIlcbiAgICAgKiBAcGFyYW0gbGV2ZWxJbmZvIOWFs+WNoeS/oeaBr+WTjeW6lOaVsOaNrlxuICAgICAqL1xuICAgIHB1YmxpYyBvbkV4dGVuZExldmVsSW5mb1JlY29ubmVjdChsZXZlbEluZm86IGFueSkge1xuICAgICAgICBjb25zb2xlLmxvZyhcIlNpbmdsZUNoZXNzQm9hcmRDb250cm9sbGVyOiDlpITnkIbmlq3nur/ph43ov57vvIzmgaLlpI3muLjmiI/nirbmgIFcIik7XG5cbiAgICAgICAgLy8g5riF55CG5b2T5YmN54q25oCBXG4gICAgICAgIHRoaXMuY2xlYXJBbGxQcmVmYWJzKCk7XG4gICAgICAgIHRoaXMuc2hvd0FsbEhpZGRlbkdyaWRzKCk7XG5cbiAgICAgICAgLy8g5aaC5p6c5pyJ5Zyw5Zu+54q25oCB5L+h5oGv77yM5oGi5aSN5qOL55uY54q25oCBXG4gICAgICAgIGlmIChsZXZlbEluZm8ubWluZU1hcCkge1xuICAgICAgICAgICAgdGhpcy5yZXN0b3JlQm9hcmRTdGF0ZShsZXZlbEluZm8ubWluZU1hcCk7XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDmgaLlpI3mo4vnm5jnirbmgIHvvIjmlq3nur/ph43ov57ml7bkvb/nlKjvvIlcbiAgICAgKiBAcGFyYW0gbWluZU1hcCDlnLDlm77nirbmgIHkv6Hmga9cbiAgICAgKi9cbiAgICBwcml2YXRlIHJlc3RvcmVCb2FyZFN0YXRlKG1pbmVNYXA6IGFueSkge1xuICAgICAgICBjb25zb2xlLmxvZyhcIlNpbmdsZUNoZXNzQm9hcmRDb250cm9sbGVyOiDmgaLlpI3mo4vnm5jnirbmgIFcIiwgbWluZU1hcCk7XG5cbiAgICAgICAgLy8g5oGi5aSN5bey5oyW5o6Y55qE5pa55Z2XXG4gICAgICAgIGlmIChtaW5lTWFwLnJldmVhbGVkQmxvY2tzICYmIEFycmF5LmlzQXJyYXkobWluZU1hcC5yZXZlYWxlZEJsb2NrcykpIHtcbiAgICAgICAgICAgIG1pbmVNYXAucmV2ZWFsZWRCbG9ja3MuZm9yRWFjaCgoYmxvY2s6IGFueSkgPT4ge1xuICAgICAgICAgICAgICAgIGNvbnN0IHggPSBibG9jay54O1xuICAgICAgICAgICAgICAgIGNvbnN0IHkgPSBibG9jay55O1xuICAgICAgICAgICAgICAgIGNvbnN0IG5laWdoYm9yTWluZXMgPSBibG9jay5uZWlnaGJvck1pbmVzO1xuXG4gICAgICAgICAgICAgICAgaWYgKHRoaXMuaXNWYWxpZENvb3JkaW5hdGUoeCwgeSkpIHtcbiAgICAgICAgICAgICAgICAgICAgLy8g6ZqQ6JeP5qC85a2QXG4gICAgICAgICAgICAgICAgICAgIHRoaXMuaGlkZUdyaWRBdCh4LCB5KTtcblxuICAgICAgICAgICAgICAgICAgICAvLyDmmL7npLrmjJbmjpjnu5PmnpxcbiAgICAgICAgICAgICAgICAgICAgaWYgKG5laWdoYm9yTWluZXMgPiAwKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmNyZWF0ZU51bWJlclByZWZhYih4LCB5LCBuZWlnaGJvck1pbmVzKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAgICAgICAgIC8vIOagh+iusOagvOWtkOW3suiiq+WkhOeQhlxuICAgICAgICAgICAgICAgICAgICB0aGlzLmdyaWREYXRhW3hdW3ldLmhhc1BsYXllciA9IHRydWU7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH1cblxuICAgICAgICAvLyDmgaLlpI3lt7LmoIforrDnmoTmlrnlnZdcbiAgICAgICAgaWYgKG1pbmVNYXAubWFya2VkQmxvY2tzICYmIEFycmF5LmlzQXJyYXkobWluZU1hcC5tYXJrZWRCbG9ja3MpKSB7XG4gICAgICAgICAgICBtaW5lTWFwLm1hcmtlZEJsb2Nrcy5mb3JFYWNoKChibG9jazogYW55KSA9PiB7XG4gICAgICAgICAgICAgICAgY29uc3QgeCA9IGJsb2NrLng7XG4gICAgICAgICAgICAgICAgY29uc3QgeSA9IGJsb2NrLnk7XG5cbiAgICAgICAgICAgICAgICBpZiAodGhpcy5pc1ZhbGlkQ29vcmRpbmF0ZSh4LCB5KSkge1xuICAgICAgICAgICAgICAgICAgICAvLyDliJvlu7rmoIforrDpooTliLbkvZNcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5jcmVhdGVCaWFvamlQcmVmYWIoeCwgeSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDlpITnkIZMZXZlbEdhbWVFbmTmtojmga/vvIjmuLjmiI/nu5PmnZ/ml7bosIPnlKjvvIlcbiAgICAgKiDms6jmhI/vvJrkuI3muIXnkIbku7vkvZXmlbDmja7vvIzkv53mjIHnjqnlrrbnmoTmuLjnjqnnl5Xov7lcbiAgICAgKi9cbiAgICBwdWJsaWMgb25MZXZlbEdhbWVFbmQoKSB7XG5cblxuICAgICAgICAvLyDkuI3mmL7npLrpmpDol4/nmoTmoLzlrZDvvIzkv53mjIHlvZPliY3nirbmgIFcbiAgICAgICAgLy8g5LiN5riF55CG6aKE5Yi25L2T77yM5LiN6YeN572u5qC85a2Q54q25oCB77yM5L+d5oyB5ri45oiP57uT5p6c5pi+56S6XG4gICAgICAgIC8vIOiuqeeOqeWutuWPr+S7peeci+WIsOiHquW3seeahOagh+iusO+8iGJpYW9qae+8ieOAgeaMluaOmOe7k+aenO+8iOaVsOWtl+OAgWJvb23vvInnrYlcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDmo4Dmn6XmmK/lkKbngrnliLDkuobngrjlvLlcbiAgICAgKiBAcmV0dXJucyDmmK/lkKbngrnliLDkuobngrjlvLlcbiAgICAgKi9cbiAgICBwdWJsaWMgaGFzQm9tYkV4cGxvZGVkSW5UaGlzR2FtZSgpOiBib29sZWFuIHtcbiAgICAgICBcbiAgICAgICAgcmV0dXJuIHRoaXMuaGFzQm9tYkV4cGxvZGVkO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOmHjee9rueCuOW8ueeIhueCuOeKtuaAge+8iOW8gOWni+aWsOa4uOaIj+aXtuiwg+eUqO+8iVxuICAgICAqL1xuICAgIHB1YmxpYyByZXNldEJvbWJFeHBsb2RlZFN0YXR1cygpIHtcbiAgICAgIFxuICAgICAgICB0aGlzLmhhc0JvbWJFeHBsb2RlZCA9IGZhbHNlO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOmakOiXj+aMh+WumuS9jee9rueahOagvOWtkO+8iOeCueWHu+aXtuiwg+eUqO+8iVxuICAgICAqL1xuICAgIHB1YmxpYyBoaWRlR3JpZEF0KHg6IG51bWJlciwgeTogbnVtYmVyKSB7XG4gICAgICAgIGlmICghdGhpcy5pc1ZhbGlkQ29vcmRpbmF0ZSh4LCB5KSkge1xuICAgICAgICAgICAgY29uc29sZS53YXJuKGDpmpDol4/moLzlrZDlpLHotKXvvJrlnZDmoIcoJHt4fSwgJHt5fSnml6DmlYhgKTtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIOiOt+WPluagvOWtkOiKgueCuVxuICAgICAgICBjb25zdCBncmlkTm9kZSA9IHRoaXMuZ3JpZE5vZGVzW3hdICYmIHRoaXMuZ3JpZE5vZGVzW3hdW3ldO1xuICAgICAgICBpZiAoZ3JpZE5vZGUpIHtcbiAgICAgICAgICAgIC8vIOS9v+eUqOWKqOeUu+makOiXj+agvOWtkFxuICAgICAgICAgICAgY2MudHdlZW4oZ3JpZE5vZGUpXG4gICAgICAgICAgICAgICAgLnRvKDAuMywgeyBvcGFjaXR5OiAwLCBzY2FsZVg6IDAsIHNjYWxlWTogMCB9LCB7IGVhc2luZzogJ3NpbmVJbicgfSlcbiAgICAgICAgICAgICAgICAuY2FsbCgoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGdyaWROb2RlLmFjdGl2ZSA9IGZhbHNlO1xuICAgICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICAgICAgLnN0YXJ0KCk7XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDojrflj5blvZPliY3mo4vnm5jnsbvlnotcbiAgICAgKi9cbiAgICBwdWJsaWMgZ2V0Q3VycmVudEJvYXJkVHlwZSgpOiBzdHJpbmcge1xuICAgICAgICByZXR1cm4gdGhpcy5jdXJyZW50Qm9hcmRUeXBlO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOiOt+WPluW9k+WJjeaji+ebmOmFjee9rlxuICAgICAqL1xuICAgIHB1YmxpYyBnZXRDdXJyZW50Qm9hcmRDb25maWcoKTogQm9hcmRDb25maWcge1xuICAgICAgICByZXR1cm4gdGhpcy5jdXJyZW50Qm9hcmRDb25maWc7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5aSE55CG54K55Ye75ZON5bqU77yM5qC55o2u5pyN5Yqh5Zmo6L+U5Zue55qE57uT5p6c5pu05paw5qOL55uYIC0g5Y+C6ICD6IGU5py654mI55qE5Zyw5Zu+5pu05paw6YC76L6RXG4gICAgICogQHBhcmFtIHgg5qC85a2QeOWdkOagh1xuICAgICAqIEBwYXJhbSB5IOagvOWtkHnlnZDmoIdcbiAgICAgKiBAcGFyYW0gcmVzdWx0IOeCueWHu+e7k+aenCAoXCJib29tXCIgfCBcInNhZmVcIiB8IG51bWJlcilcbiAgICAgKi9cbiAgICBwdWJsaWMgaGFuZGxlQ2xpY2tSZXNwb25zZSh4OiBudW1iZXIsIHk6IG51bWJlciwgcmVzdWx0OiBhbnkpIHtcbiAgICAgICAgaWYgKCF0aGlzLmlzVmFsaWRDb29yZGluYXRlKHgsIHkpKSB7XG4gICAgICAgICAgICBjb25zb2xlLndhcm4oYOWkhOeQhueCueWHu+WTjeW6lOWksei0pe+8muWdkOaghygke3h9LCAke3l9KeaXoOaViGApO1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG5cbiAgICAgXG5cbiAgICAgICAgLy8g5aaC5p6c5qC85a2Q5LiK5pyJYmlhb2pp6aKE5Yi25L2T77yM5YWI56e76Zmk5a6DXG4gICAgICAgIGlmICh0aGlzLmhhc0JpYW9qaUF0KHgsIHkpKSB7XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIC8vIOebtOaOpeenu+mZpO+8jOS4jeaSreaUvuWKqOeUu1xuICAgICAgICAgICAgY29uc3QgZ3JpZERhdGEgPSB0aGlzLmdyaWREYXRhW3hdW3ldO1xuICAgICAgICAgICAgaWYgKGdyaWREYXRhLnBsYXllck5vZGUpIHtcbiAgICAgICAgICAgICAgICBncmlkRGF0YS5wbGF5ZXJOb2RlLnJlbW92ZUZyb21QYXJlbnQoKTtcbiAgICAgICAgICAgICAgICBncmlkRGF0YS5wbGF5ZXJOb2RlID0gbnVsbDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIC8vIOagh+iusOagvOWtkOW3suiiq+WkhOeQhu+8jOmYsuatoumHjeWkjeeCueWHu1xuICAgICAgICB0aGlzLmdyaWREYXRhW3hdW3ldLmhhc1BsYXllciA9IHRydWU7XG5cbiAgICAgICAgLy8g5L2/55So6L+e6ZSB5Yqo55S755qE5pa55byP5aSE55CG5Y2V5Liq5qC85a2Q77yM5L+d5oyB5LiA6Ie05oCnXG4gICAgICAgIHRoaXMucGxheUdyaWREaXNhcHBlYXJBbmltYXRpb24oeCwgeSwgcmVzdWx0KTtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDlpITnkIbov57plIHlsZXlvIDnu5PmnpxcbiAgICAgKiBAcGFyYW0gZmxvb2RGaWxsUmVzdWx0cyDov57plIHlsZXlvIDmlbDmja7mlbDnu4RcbiAgICAgKi9cbiAgICBwdWJsaWMgaGFuZGxlRmxvb2RGaWxsUmVzdWx0cyhmbG9vZEZpbGxSZXN1bHRzOiBhbnlbXSkge1xuXG5cbiAgICAgICAgLy8g5ZCM5pe25pKt5pS+5omA5pyJ5qC85a2Q55qE5raI5aSx5Yqo55S777yM5LiN5L2/55So5bu26L+fXG4gICAgICAgIGZsb29kRmlsbFJlc3VsdHMuZm9yRWFjaCgoZ3JpZFJlc3VsdCkgPT4ge1xuICAgICAgICAgICAgY29uc3QgeyB4LCB5LCBuZWlnaGJvck1pbmVzIH0gPSBncmlkUmVzdWx0O1xuXG4gICAgICAgICAgICBpZiAoIXRoaXMuaXNWYWxpZENvb3JkaW5hdGUoeCwgeSkpIHtcbiAgICAgICAgICAgICAgICBjb25zb2xlLndhcm4oYOi/numUgeWxleW8gOi3s+i/h+aXoOaViOWdkOaghzogKCR7eH0sICR7eX0pYCk7XG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAvLyDnq4vljbPmkq3mlL7liqjnlLvvvIzkuI3lu7bov59cbiAgICAgICAgICAgIHRoaXMucGxheUdyaWREaXNhcHBlYXJBbmltYXRpb24oeCwgeSwgbmVpZ2hib3JNaW5lcyk7XG4gICAgICAgIH0pO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOaJuemHj+WkhOeQhui/numUgeWPjeW6lOeahOagvOWtkO+8iOWPguiAg+iBlOacuueJiOeahHByb2Nlc3NGbG9vZEZpbGxSZXN1bHTvvIlcbiAgICAgKiBAcGFyYW0gcmV2ZWFsZWRHcmlkcyDooqvmj63lvIDnmoTmoLzlrZDliJfooagge3g6IG51bWJlciwgeTogbnVtYmVyLCBuZWlnaGJvck1pbmVzOiBudW1iZXJ9W11cbiAgICAgKi9cbiAgICBwdWJsaWMgaGFuZGxlQ2hhaW5SZWFjdGlvbihyZXZlYWxlZEdyaWRzOiBBcnJheTx7eDogbnVtYmVyLCB5OiBudW1iZXIsIG5laWdoYm9yTWluZXM6IG51bWJlcn0+KSB7XG5cblxuICAgICAgICBpZiAoIXJldmVhbGVkR3JpZHMgfHwgcmV2ZWFsZWRHcmlkcy5sZW5ndGggPT09IDApIHtcblxuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8g5ZCM5pe25pKt5pS+5omA5pyJ6L+e6ZSB5qC85a2Q55qE5raI5aSx5Yqo55S777yM5LiN5L2/55So5bu26L+fXG4gICAgICAgIHJldmVhbGVkR3JpZHMuZm9yRWFjaCgoYmxvY2spID0+IHtcbiAgICAgICAgICAgIC8vIOeri+WNs+aSreaUvuWKqOeUu++8jOS4jeW7tui/n1xuICAgICAgICAgICAgdGhpcy5wbGF5R3JpZERpc2FwcGVhckFuaW1hdGlvbihibG9jay54LCBibG9jay55LCBibG9jay5uZWlnaGJvck1pbmVzKTtcbiAgICAgICAgfSk7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5pKt5pS+5qC85a2Q5raI5aSx5Yqo55S777yI6L+e6ZSB5pWI5p6c77yJLSDlj4LogIPogZTmnLrniYhDaGVzc0JvYXJkQ29udHJvbGxlclxuICAgICAqIEBwYXJhbSB4IOagvOWtkHjlnZDmoIdcbiAgICAgKiBAcGFyYW0geSDmoLzlrZB55Z2Q5qCHXG4gICAgICogQHBhcmFtIG5laWdoYm9yTWluZXMg5ZGo5Zu05Zyw6Zu35pWw6YeP5oiW57uT5p6c57G75Z6L77yI5Y+v5Lul5piv5pWw5a2X44CBXCJtaW5lXCLjgIFcImJvb21cIuetie+8iVxuICAgICAqL1xuICAgIHB1YmxpYyBwbGF5R3JpZERpc2FwcGVhckFuaW1hdGlvbih4OiBudW1iZXIsIHk6IG51bWJlciwgbmVpZ2hib3JNaW5lczogYW55KSB7XG4gICAgICAgXG5cbiAgICAgICAgLy8g5aaC5p6c5qC85a2Q5LiK5pyJYmlhb2pp6aKE5Yi25L2T77yM5YWI56e76Zmk5a6D77yI6L+e6ZSB5bGV5byA5pe277yJXG4gICAgICAgIGlmICh0aGlzLmhhc0JpYW9qaUF0KHgsIHkpKSB7XG4gICAgICAgICAgIFxuICAgICAgICAgICAgY29uc3QgZ3JpZERhdGEgPSB0aGlzLmdyaWREYXRhW3hdW3ldO1xuICAgICAgICAgICAgaWYgKGdyaWREYXRhLnBsYXllck5vZGUpIHtcbiAgICAgICAgICAgICAgICBncmlkRGF0YS5wbGF5ZXJOb2RlLnJlbW92ZUZyb21QYXJlbnQoKTtcbiAgICAgICAgICAgICAgICBncmlkRGF0YS5wbGF5ZXJOb2RlID0gbnVsbDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIC8vIOagh+iusOagvOWtkOW3suiiq+WkhOeQhu+8iOWvueS6jui/numUgeagvOWtkO+8iVxuICAgICAgICBpZiAodGhpcy5pc1ZhbGlkQ29vcmRpbmF0ZSh4LCB5KSkge1xuICAgICAgICAgICAgdGhpcy5ncmlkRGF0YVt4XVt5XS5oYXNQbGF5ZXIgPSB0cnVlO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8g5YWI5Yig6Zmk5qC85a2QXG4gICAgICAgIHRoaXMucmVtb3ZlR3JpZEF0KHgsIHkpO1xuXG4gICAgICAgIC8vIOW7tui/nzAuM+enkuWQjuaYvuekuuaVsOWtl++8iOetieagvOWtkOa2iOWkseWKqOeUu+WujOaIkO+8iVxuICAgICAgICAvLyDkvb/nlKjluKbmoIfor4bnmoTlu7bov5/ku7vliqHvvIzmlrnkvr/ph43nva7ml7bmuIXnkIZcbiAgICAgICAgY29uc3QgZGVsYXlDYWxsYmFjayA9ICgpID0+IHtcbiAgICAgICAgICBcbiAgICAgICAgICAgIHRoaXMudXBkYXRlTmVpZ2hib3JNaW5lc0Rpc3BsYXkoeCwgeSwgbmVpZ2hib3JNaW5lcyk7XG4gICAgICAgIH07XG4gICAgICAgIHRoaXMuc2NoZWR1bGVPbmNlKGRlbGF5Q2FsbGJhY2ssIDAuMyk7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog6ZqQ6JeP5oyH5a6a5L2N572u55qE5qC85a2Q77yI5LiN6ZSA5q+B77yM5Lul5L6/6YeN572u5pe25Y+v5Lul6YeN5paw5pi+56S677yJLSDlj4LogIPogZTmnLrniYhcbiAgICAgKiBAcGFyYW0geCDmoLzlrZB45Z2Q5qCHXG4gICAgICogQHBhcmFtIHkg5qC85a2QeeWdkOagh1xuICAgICAqIEBwYXJhbSBpbW1lZGlhdGUg5piv5ZCm56uL5Y2z6ZqQ6JeP77yI5LiN5pKt5pS+5Yqo55S777yJXG4gICAgICovXG4gICAgcHVibGljIHJlbW92ZUdyaWRBdCh4OiBudW1iZXIsIHk6IG51bWJlciwgaW1tZWRpYXRlOiBib29sZWFuID0gZmFsc2UpIHtcbiAgICAgICAgaWYgKCF0aGlzLmlzVmFsaWRDb29yZGluYXRlKHgsIHkpKSB7XG4gICAgICAgICAgICBjb25zb2xlLndhcm4oYOmakOiXj+agvOWtkOWksei0pe+8muWdkOaghygke3h9LCAke3l9KeaXoOaViGApO1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8g6I635Y+W5qC85a2Q6IqC54K5XG4gICAgICAgIGNvbnN0IGdyaWROb2RlID0gdGhpcy5ncmlkTm9kZXNbeF0gJiYgdGhpcy5ncmlkTm9kZXNbeF1beV07XG4gICAgICAgIGlmIChncmlkTm9kZSkge1xuICAgICAgICAgICAgaWYgKGltbWVkaWF0ZSkge1xuICAgICAgICAgICAgICAgIC8vIOeri+WNs+makOiXj++8jOS4jeaSreaUvuWKqOeUu1xuICAgICAgICAgICAgICAgIGdyaWROb2RlLmFjdGl2ZSA9IGZhbHNlO1xuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICAvLyDmkq3mlL7lm5vovrnlvaLmoLzlrZDmtojlpLHliqjnlLtcbiAgICAgICAgICAgICAgICB0aGlzLnBsYXlHcmlkRmFsbEFuaW1hdGlvbihncmlkTm9kZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDmkq3mlL7lm5vovrnlvaLmoLzlrZDmtojlpLHliqjnlLtcbiAgICAgKiDmlYjmnpzvvJrmoLzlrZDmjIHnu63ml4vovazvvIznu5nkuIDkuKrpmo/mnLrlkJHkuIrnmoTlipvvvIznhLblkI7ml4vovaznnYDoh6rnlLHokL3kvZNcbiAgICAgKiBAcGFyYW0gZ3JpZE5vZGUg5qC85a2Q6IqC54K5XG4gICAgICovXG4gICAgcHJpdmF0ZSBwbGF5R3JpZEZhbGxBbmltYXRpb24oZ3JpZE5vZGU6IGNjLk5vZGUpIHtcbiAgICAgICAgaWYgKCFncmlkTm9kZSkgcmV0dXJuO1xuXG4gICAgICAgIC8vIOWBnOatouivpeagvOWtkOS4iuaJgOacieato+WcqOi/m+ihjOeahOWKqOeUu++8iOWMheaLrOmch+WKqOWKqOeUu++8iVxuICAgICAgICBncmlkTm9kZS5zdG9wQWxsQWN0aW9ucygpO1xuXG4gICAgICAgIC8vIOS/neWtmOagvOWtkOeahOWOn+Wni+S9jee9ru+8iOeUqOS6jumHjee9ruaXtuaBouWkje+8iVxuICAgICAgICBpZiAoIWdyaWROb2RlWydvcmlnaW5hbFBvc2l0aW9uJ10pIHtcbiAgICAgICAgICAgIGdyaWROb2RlWydvcmlnaW5hbFBvc2l0aW9uJ10gPSBncmlkTm9kZS5nZXRQb3NpdGlvbigpO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8g5L+d5a2Y5Y6f5aeL55qEekluZGV477yM55So5LqO5Yqo55S757uT5p2f5ZCO5oGi5aSNXG4gICAgICAgIGNvbnN0IG9yaWdpbmFsWkluZGV4ID0gZ3JpZE5vZGUuekluZGV4O1xuXG4gICAgICAgIC8vIOiuvue9ruabtOmrmOeahOWxgue6p++8jOehruS/neS4i+iQveeahOagvOWtkOWcqOaVsOWtl+mihOWItuS9k+WSjOWFtuS7luWFg+e0oOS5i+S4ilxuICAgICAgICBncmlkTm9kZS56SW5kZXggPSAxMDAwO1xuXG4gICAgICAgIC8vIOmaj+acuumAieaLqeWQkeS4iueahOWKm+eahOaWueWQke+8mjA95ZCR5LiK77yMMT3lj7PkuIoxNeW6pu+8jDI95bem5LiKMTXluqZcbiAgICAgICAgY29uc3QgZm9yY2VEaXJlY3Rpb24gPSBNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiAzKTtcbiAgICAgICAgbGV0IG1vdmVYID0gMDtcbiAgICAgICAgbGV0IG1vdmVZID0gMjAwOyAvLyDlkJHkuIrnmoTln7rnoYDot53nprvvvIjlop7liqDpq5jluqbvvIlcblxuICAgICAgICBzd2l0Y2ggKGZvcmNlRGlyZWN0aW9uKSB7XG4gICAgICAgICAgICBjYXNlIDA6IC8vIOWQkeS4ilxuICAgICAgICAgICAgICAgIG1vdmVYID0gMDtcbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIGNhc2UgMTogLy8g5Y+z5LiKMTXluqZcbiAgICAgICAgICAgICAgICBtb3ZlWCA9IE1hdGguc2luKDIwICogTWF0aC5QSSAvIDE4MCkgKiBtb3ZlWTtcbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIGNhc2UgMjogLy8g5bem5LiKMTXluqZcbiAgICAgICAgICAgICAgICBtb3ZlWCA9IC1NYXRoLnNpbigyMCAqIE1hdGguUEkgLyAxODApICogbW92ZVk7XG4gICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgIH1cblxuICAgICAgICAvLyDpmo/mnLrml4vovazpgJ/luqZcbiAgICAgICAgY29uc3Qgcm90YXRpb25TcGVlZCA9IChNYXRoLnJhbmRvbSgpICogMTQ0MCArIDcyMCkgKiAoTWF0aC5yYW5kb20oKSA+IDAuNSA/IDEgOiAtMSk7IC8vIDM2MC0xMDgw5bqmL+enku+8jOmaj+acuuaWueWQkVxuXG4gICAgICAgIC8vIOWKqOeUu+WPguaVsFxuICAgICAgICBjb25zdCB1cFRpbWUgPSAwLjE1OyAvLyDlkJHkuIrov5Dliqjml7bpl7RcbiAgICAgICAgY29uc3QgZmFsbFRpbWUgPSAwLjM7IC8vIOS4i+iQveaXtumXtFxuICAgICAgICBjb25zdCBpbml0aWFsUG9zaXRpb24gPSBncmlkTm9kZS5nZXRQb3NpdGlvbigpO1xuXG4gICAgICAgIC8vIOWIm+W7uuaMgee7reaXi+i9rOeahOWKqOeUu1xuICAgICAgICBjb25zdCByb3RhdGlvblR3ZWVuID0gY2MudHdlZW4oZ3JpZE5vZGUpXG4gICAgICAgICAgICAucmVwZWF0Rm9yZXZlcihcbiAgICAgICAgICAgICAgICBjYy50d2VlbigpLmJ5KDAuMSwgeyBhbmdsZTogcm90YXRpb25TcGVlZCAqIDAuMSB9KVxuICAgICAgICAgICAgKTtcblxuICAgICAgICAvLyDliJvlu7rliIbpmLbmrrXnmoTov5DliqjliqjnlLtcbiAgICAgICAgY29uc3QgbW92ZW1lbnRUd2VlbiA9IGNjLnR3ZWVuKGdyaWROb2RlKVxuICAgICAgICAgICAgLy8g56ys5LiA6Zi25q6177ya5ZCR5LiK5oqb5Ye6XG4gICAgICAgICAgICAudG8odXBUaW1lLCB7XG4gICAgICAgICAgICAgICAgeDogaW5pdGlhbFBvc2l0aW9uLnggKyBtb3ZlWCxcbiAgICAgICAgICAgICAgICB5OiBpbml0aWFsUG9zaXRpb24ueSArIG1vdmVZXG4gICAgICAgICAgICB9LCB7IGVhc2luZzogJ3F1YWRPdXQnIH0pXG4gICAgICAgICAgICAvLyDnrKzkuozpmLbmrrXvvJroh6rnlLHokL3kvZNcbiAgICAgICAgICAgIC50byhmYWxsVGltZSwge1xuICAgICAgICAgICAgICAgIHg6IGluaXRpYWxQb3NpdGlvbi54ICsgbW92ZVggKyAoTWF0aC5yYW5kb20oKSAtIDAuNSkgKiAxMDAsIC8vIOa3u+WKoOabtOWkmumaj+acuuawtOW5s+WBj+enu1xuICAgICAgICAgICAgICAgIHk6IGluaXRpYWxQb3NpdGlvbi55IC0gNTAwIC8vIOS4i+iQveWIsOWxj+W5leS4i+aWueabtOi/nOWkhFxuICAgICAgICAgICAgfSwgeyBlYXNpbmc6ICdxdWFkSW4nIH0pXG4gICAgICAgICAgICAuY2FsbCgoKSA9PiB7XG4gICAgICAgICAgICAgICAgLy8g5Yqo55S757uT5p2f5ZCO6ZqQ6JeP5qC85a2QXG4gICAgICAgICAgICAgICAgZ3JpZE5vZGUuYWN0aXZlID0gZmFsc2U7XG4gICAgICAgICAgICAgICAgLy8g5YGc5q2i5peL6L2s5Yqo55S7XG4gICAgICAgICAgICAgICAgZ3JpZE5vZGUuc3RvcEFsbEFjdGlvbnMoKTtcbiAgICAgICAgICAgICAgICAvLyDmgaLlpI3ljp/lp4vnmoR6SW5kZXjvvIjomb3nhLbmoLzlrZDlt7Lnu4/pmpDol4/vvIzkvYbkv53mjIHkuIDoh7TmgKfvvIlcbiAgICAgICAgICAgICAgICBncmlkTm9kZS56SW5kZXggPSBvcmlnaW5hbFpJbmRleDtcbiAgICAgICAgICAgIH0pO1xuXG4gICAgICAgIC8vIOWQjOaXtuW8gOWni+aXi+i9rOWSjOenu+WKqOWKqOeUu1xuICAgICAgICByb3RhdGlvblR3ZWVuLnN0YXJ0KCk7XG4gICAgICAgIG1vdmVtZW50VHdlZW4uc3RhcnQoKTtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDmm7TmlrDmjIflrprkvY3nva7nmoRuZWlnaGJvck1pbmVz5pi+56S677yI5L2/55SoYm9vbeaVsOWtl+mihOWItuS9k++8iS0g5Y+C6ICD6IGU5py654mIXG4gICAgICogQHBhcmFtIHgg5qC85a2QeOWdkOagh1xuICAgICAqIEBwYXJhbSB5IOagvOWtkHnlnZDmoIdcbiAgICAgKiBAcGFyYW0gbmVpZ2hib3JNaW5lcyDlkajlm7TlnLDpm7fmlbDph4/miJbnu5PmnpznsbvlnotcbiAgICAgKi9cbiAgICBwdWJsaWMgdXBkYXRlTmVpZ2hib3JNaW5lc0Rpc3BsYXkoeDogbnVtYmVyLCB5OiBudW1iZXIsIG5laWdoYm9yTWluZXM6IGFueSkge1xuICAgICAgICBcblxuICAgICAgICBpZiAobmVpZ2hib3JNaW5lcyA9PT0gXCJib29tXCIgfHwgbmVpZ2hib3JNaW5lcyA9PT0gXCJtaW5lXCIpIHtcbiAgICAgICAgICAgIC8vIOi4qeWIsOWcsOmbt++8jOeUn+aIkGJvb23pooTliLbkvZPlubbop6blj5HpnIfliqhcbiAgICAgICAgICAgXG5cbiAgICAgICAgICAgIHRoaXMuY3JlYXRlQm9vbVByZWZhYih4LCB5LCB0cnVlKTsgLy8gdHJ1ZeihqOekuuaYr+W9k+WJjeeUqOaIt+i4qeWIsOeahOmbt++8jOmcgOimgemch+WKqFxuXG4gICAgICAgICAgICAvLyDorr7nva7moIforrDvvIzooajnpLrngrnliLDkuobngrjlvLlcbiAgICAgICAgICAgIHRoaXMuaGFzQm9tYkV4cGxvZGVkID0gdHJ1ZTtcbiAgICAgICAgICAgIFxuICAgICAgICB9IGVsc2UgaWYgKHR5cGVvZiBuZWlnaGJvck1pbmVzID09PSBcIm51bWJlclwiICYmIG5laWdoYm9yTWluZXMgPiAwKSB7XG4gICAgICAgICAgICAvLyDmmL7npLrmlbDlrZdcbiAgICAgICAgICAgXG4gICAgICAgICAgICB0aGlzLmNyZWF0ZU51bWJlclByZWZhYih4LCB5LCBuZWlnaGJvck1pbmVzKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIC8vIOWmguaenOaYrzDjgIFcInNhZmVcIuaIluWFtuS7lu+8jOWImeS4jeaYvuekuuS7u+S9lemihOWItuS9k1xuICAgICAgICAgICBcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOenu+mZpOaMh+WumuS9jee9rueahGJpYW9qaemihOWItuS9k1xuICAgICAqIEBwYXJhbSB4IOagvOWtkHjlnZDmoIdcbiAgICAgKiBAcGFyYW0geSDmoLzlrZB55Z2Q5qCHXG4gICAgICovXG4gICAgcHVibGljIHJlbW92ZUJpYW9qaUF0KHg6IG51bWJlciwgeTogbnVtYmVyKSB7XG4gICAgICAgIGlmICghdGhpcy5pc1ZhbGlkQ29vcmRpbmF0ZSh4LCB5KSkge1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG5cbiAgICAgICAgY29uc3QgZ3JpZERhdGEgPSB0aGlzLmdyaWREYXRhW3hdW3ldO1xuICAgICAgICBpZiAoZ3JpZERhdGEuaGFzUGxheWVyICYmIGdyaWREYXRhLnBsYXllck5vZGUgJiYgZ3JpZERhdGEucGxheWVyTm9kZS5uYW1lID09PSBcIkJpYW9qaVwiKSB7XG4gICAgICAgICAgICAvLyDmkq3mlL7mtojlpLHliqjnlLtcbiAgICAgICAgICAgIGNjLnR3ZWVuKGdyaWREYXRhLnBsYXllck5vZGUpXG4gICAgICAgICAgICAgICAgLnRvKDAuMiwgeyBzY2FsZVg6IDAsIHNjYWxlWTogMCB9LCB7IGVhc2luZzogJ3NpbmVJbicgfSlcbiAgICAgICAgICAgICAgICAuY2FsbCgoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGdyaWREYXRhLnBsYXllck5vZGUucmVtb3ZlRnJvbVBhcmVudCgpO1xuICAgICAgICAgICAgICAgICAgICBncmlkRGF0YS5oYXNQbGF5ZXIgPSBmYWxzZTtcbiAgICAgICAgICAgICAgICAgICAgZ3JpZERhdGEucGxheWVyTm9kZSA9IG51bGw7XG4gICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgICAgICAuc3RhcnQoKTtcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOajgOafpeaMh+WumuS9jee9ruaYr+WQpuaciWJpYW9qaemihOWItuS9k1xuICAgICAqIEBwYXJhbSB4IOagvOWtkHjlnZDmoIdcbiAgICAgKiBAcGFyYW0geSDmoLzlrZB55Z2Q5qCHXG4gICAgICogQHJldHVybnMg5piv5ZCm5pyJYmlhb2pp6aKE5Yi25L2TXG4gICAgICovXG4gICAgcHVibGljIGhhc0JpYW9qaUF0KHg6IG51bWJlciwgeTogbnVtYmVyKTogYm9vbGVhbiB7XG4gICAgICAgIGlmICghdGhpcy5pc1ZhbGlkQ29vcmRpbmF0ZSh4LCB5KSkge1xuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICB9XG5cbiAgICAgICAgY29uc3QgZ3JpZERhdGEgPSB0aGlzLmdyaWREYXRhW3hdW3ldO1xuICAgICAgICByZXR1cm4gZ3JpZERhdGEuaGFzUGxheWVyICYmIGdyaWREYXRhLnBsYXllck5vZGUgJiYgZ3JpZERhdGEucGxheWVyTm9kZS5uYW1lID09PSBcIkJpYW9qaVwiO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOiOt+WPluaJgOaciWJpYW9qaeeahOS9jee9rlxuICAgICAqIEByZXR1cm5zIGJpYW9qaeS9jee9ruaVsOe7hFxuICAgICAqL1xuICAgIHB1YmxpYyBnZXRBbGxCaWFvamlQb3NpdGlvbnMoKTogQXJyYXk8e3g6IG51bWJlciwgeTogbnVtYmVyfT4ge1xuICAgICAgICBjb25zdCBwb3NpdGlvbnM6IEFycmF5PHt4OiBudW1iZXIsIHk6IG51bWJlcn0+ID0gW107XG5cbiAgICAgICAgaWYgKCF0aGlzLmN1cnJlbnRCb2FyZENvbmZpZykgcmV0dXJuIHBvc2l0aW9ucztcblxuICAgICAgICBmb3IgKGxldCB4ID0gMDsgeCA8IHRoaXMuY3VycmVudEJvYXJkQ29uZmlnLmNvbHM7IHgrKykge1xuICAgICAgICAgICAgZm9yIChsZXQgeSA9IDA7IHkgPCB0aGlzLmN1cnJlbnRCb2FyZENvbmZpZy5yb3dzOyB5KyspIHtcbiAgICAgICAgICAgICAgICBpZiAodGhpcy5oYXNCaWFvamlBdCh4LCB5KSkge1xuICAgICAgICAgICAgICAgICAgICBwb3NpdGlvbnMucHVzaCh7eCwgeX0pO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIHJldHVybiBwb3NpdGlvbnM7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog6YeN572u5qOL55uY5Yiw5Yid5aeL54q25oCBXG4gICAgICovXG4gICAgcHVibGljIHJlc2V0Qm9hcmQoKSB7XG4gICAgICAgXG5cbiAgICAgICAgLy8g5riF55CG5omA5pyJ5bu26L+f5Lu75Yqh77yI6YeN6KaB77ya6Ziy5q2i5LiK5LiA5bGA55qE6L+e6ZSB5Yqo55S75b2x5ZON5paw5ri45oiP77yJXG4gICAgICAgIHRoaXMudW5zY2hlZHVsZUFsbENhbGxiYWNrcygpO1xuXG4gICAgICAgIC8vIOmHjee9rueCuOW8ueeIhueCuOeKtuaAgVxuICAgICAgICB0aGlzLnJlc2V0Qm9tYkV4cGxvZGVkU3RhdHVzKCk7XG5cbiAgICAgICAgLy8g5riF6Zmk5omA5pyJ6aKE5Yi25L2TXG4gICAgICAgIHRoaXMuY2xlYXJBbGxQcmVmYWJzKCk7XG5cbiAgICAgICAgLy8g6YeN5paw5Yid5aeL5YyW5qOL55uY5pWw5o2uXG4gICAgICAgIHRoaXMucmVpbml0aWFsaXplQm9hcmREYXRhKCk7XG5cbiAgICAgICAgLy8g5pi+56S65omA5pyJ5qC85a2QXG4gICAgICAgIHRoaXMuc2hvd0FsbEhpZGRlbkdyaWRzKCk7XG5cbiAgICAgICAgLy8g6YeN5paw5ZCv55So6Kem5pG45LqL5Lu2XG4gICAgICAgIHRoaXMuc2NoZWR1bGVPbmNlKCgpID0+IHtcbiAgICAgICAgICAgIHRoaXMuZW5hYmxlVG91Y2hGb3JFeGlzdGluZ0dyaWRzKCk7XG4gICAgICAgIH0sIDAuMSk7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog56aB55So5omA5pyJ5qC85a2Q55qE6Kem5pG45LqL5Lu277yI5ri45oiP57uT5p2f5pe26LCD55So77yJXG4gICAgICovXG4gICAgcHVibGljIGRpc2FibGVBbGxHcmlkVG91Y2goKSB7XG4gICAgICAgIGlmICghdGhpcy5jdXJyZW50Qm9hcmRDb25maWcpIHJldHVybjtcblxuICAgICAgICBmb3IgKGxldCB4ID0gMDsgeCA8IHRoaXMuY3VycmVudEJvYXJkQ29uZmlnLmNvbHM7IHgrKykge1xuICAgICAgICAgICAgZm9yIChsZXQgeSA9IDA7IHkgPCB0aGlzLmN1cnJlbnRCb2FyZENvbmZpZy5yb3dzOyB5KyspIHtcbiAgICAgICAgICAgICAgICBjb25zdCBncmlkTm9kZSA9IHRoaXMuZ3JpZE5vZGVzW3hdICYmIHRoaXMuZ3JpZE5vZGVzW3hdW3ldO1xuICAgICAgICAgICAgICAgIGlmIChncmlkTm9kZSkge1xuICAgICAgICAgICAgICAgICAgICBncmlkTm9kZS5vZmYoY2MuTm9kZS5FdmVudFR5cGUuVE9VQ0hfU1RBUlQpO1xuICAgICAgICAgICAgICAgICAgICBncmlkTm9kZS5vZmYoY2MuTm9kZS5FdmVudFR5cGUuVE9VQ0hfRU5EKTtcbiAgICAgICAgICAgICAgICAgICAgZ3JpZE5vZGUub2ZmKGNjLk5vZGUuRXZlbnRUeXBlLlRPVUNIX0NBTkNFTCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5ZCv55So5omA5pyJ5qC85a2Q55qE6Kem5pG45LqL5Lu2XG4gICAgICovXG4gICAgcHVibGljIGVuYWJsZUFsbEdyaWRUb3VjaCgpIHtcbiAgICAgICAgdGhpcy5lbmFibGVUb3VjaEZvckV4aXN0aW5nR3JpZHMoKTtcbiAgICB9XG59XG4iXX0=