
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/game/GamePageController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'ae7d7j8qCJHEr/tVZKmu8hm', 'GamePageController');
// scripts/game/GamePageController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalBean_1 = require("../bean/GlobalBean");
var LeaveDialogController_1 = require("../hall/LeaveDialogController");
var AudioManager_1 = require("../util/AudioManager");
var Config_1 = require("../util/Config");
var Tools_1 = require("../util/Tools");
var CongratsDialogController_1 = require("./CongratsDialogController");
var GameScoreController_1 = require("./GameScoreController");
var ChessBoardController_1 = require("./Chess/ChessBoardController");
var HexChessBoardController_1 = require("./Chess/HexChessBoardController");
var PlayerGameController_1 = require("../pfb/PlayerGameController ");
var WebSocketManager_1 = require("../net/WebSocketManager");
var MessageId_1 = require("../net/MessageId");
var AIManagedDialogController_1 = require("./AIManagedDialogController");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var GamePageController = /** @class */ (function (_super) {
    __extends(GamePageController, _super);
    function GamePageController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardBtnBack = null; //返回按钮
        _this.timeLabel = null; // 计时器显示标签
        _this.mineCountLabel = null; // 炸弹数量显示标签
        _this.squareMapNode = null; // 方形地图节点 (mapType = 0)
        _this.hexMapNode = null; // 六边形地图节点 (mapType = 1)
        _this.leaveDialogController = null; // 退出游戏弹窗
        _this.congratsDialogController = null; //结算弹窗
        _this.aiManagedDialogController = null; // AI托管页面
        _this.gameScoreController = null; //分数控制器
        _this.chessBoardController = null; //方形棋盘控制器
        _this.hexChessBoardController = null; //六边形棋盘控制器
        _this.gameStartNode = null; // 游戏开始节点
        _this.roundStartNode = null; // 回合开始节点
        _this.isLeaveGameDialogShow = false; //是否显示退出游戏的弹窗
        _this.isCongratsDialog = false; //是否显示结算的弹窗
        // 计时器相关属性
        _this.countdownInterval = null; // 倒计时定时器ID
        _this.currentCountdown = 0; // 当前倒计时秒数
        _this.currentRoundNumber = 0; // 当前回合编号
        // 游戏状态管理
        _this.canOperate = false; // 是否可以操作（在NoticeRoundStart和NoticeActionDisplay之间）
        _this.gameStatus = 0; // 游戏状态
        _this.hasOperatedThisRound = false; // 本回合是否已经操作过
        _this.isCurrentUserAIManaged = false; // 当前用户是否处于AI托管状态
        // 游戏数据
        _this.currentMapType = 0; // 当前地图类型 0-方形地图，1-六边形地图
        _this.currentMineCount = 0; // 当前炸弹数量
        // 当前NoticeActionDisplay数据，用于倒计时显示逻辑
        _this.currentNoticeActionData = null;
        return _this;
        // update (dt) {}
    }
    GamePageController.prototype.onLoad = function () {
        var _this = this;
        // 如果timeLabel没有在编辑器中设置，尝试通过路径查找
        if (!this.timeLabel) {
            // 根据场景结构查找time_label节点
            var timeBgNode = cc.find('Canvas/time_bg');
            if (timeBgNode) {
                var timeLabelNode = timeBgNode.getChildByName('time_label');
                if (timeLabelNode) {
                    this.timeLabel = timeLabelNode.getComponent(cc.Label);
                }
            }
        }
        // 如果mineCountLabel没有在编辑器中设置，尝试通过路径查找
        if (!this.mineCountLabel) {
            // 根据场景结构查找mine_count_label节点
            var mineCountBgNode = cc.find('Canvas/mine_count_bg');
            if (mineCountBgNode) {
                var mineCountLabelNode = mineCountBgNode.getChildByName('mine_count_label');
                if (mineCountLabelNode) {
                    this.mineCountLabel = mineCountLabelNode.getComponent(cc.Label);
                }
            }
        }
        // 将测试方法暴露到全局，方便调试
        window.testGameReset = function () {
            _this.testReset();
        };
        // 暴露 GamePageController 实例到全局
        window.gamePageController = this;
        // 初始化游戏开始和回合开始节点
        this.initializeAnimationNodes();
    };
    GamePageController.prototype.start = function () {
        var _this = this;
        Tools_1.Tools.imageButtonClick(this.boardBtnBack, Config_1.Config.buttonRes + 'side_btn_back_normal', Config_1.Config.buttonRes + 'side_btn_back_pressed', function () {
            _this.isLeaveGameDialogShow = true;
            _this.leaveDialogController.show(1, function () {
                _this.isLeaveGameDialogShow = false;
            });
        });
        // 监听棋盘点击事件
        if (this.chessBoardController) {
            this.chessBoardController.node.on('chess-board-click', this.onChessBoardClick, this);
        }
        // 监听六边形棋盘点击事件
        if (this.hexChessBoardController) {
            this.hexChessBoardController.node.on('hex-chess-board-click', this.onHexChessBoardClick, this);
        }
        // 为整个游戏页面添加点击事件监听，用于处理托管状态下的取消操作
        this.node.on(cc.Node.EventType.TOUCH_END, this.onGamePageClick, this);
    };
    /**
     * 处理游戏页面点击事件（用于托管状态下的取消操作）
     * @param event 点击事件
     */
    GamePageController.prototype.onGamePageClick = function (event) {
        // 只有在托管状态下才处理
        if (this.isCurrentUserAIManaged) {
            this.sendCancelAIManagement();
        }
    };
    /**
     * 发送取消AI托管消息
     */
    GamePageController.prototype.sendCancelAIManagement = function () {
        var cancelData = {
        // 可以根据需要添加其他参数
        };
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeCancelAIManagement, cancelData);
    };
    /**
     * 处理棋盘点击事件
     * @param event 事件数据 {x: number, y: number, action: number}
     */
    GamePageController.prototype.onChessBoardClick = function (event) {
        var _a = event.detail || event, x = _a.x, y = _a.y, action = _a.action;
        // 检查是否可以操作（在操作时间内）
        if (!this.isCanOperate()) {
            return;
        }
        // 检查本回合是否已经操作过
        if (this.hasOperatedThisRound) {
            return;
        }
        // 发送点击操作
        this.sendClickBlock(x, y, action);
        // 操作有效，通知棋盘生成预制体
        if (this.chessBoardController) {
            if (action === 1) {
                // 挖掘操作，生成不带旗子的预制体
                this.chessBoardController.placePlayerOnGrid(x, y, false);
            }
            else if (action === 2) {
                // 标记操作，生成带旗子的预制体
                this.chessBoardController.placePlayerOnGrid(x, y, true);
            }
        }
        // 标记本回合已经操作过，禁止后续交互
        this.hasOperatedThisRound = true;
    };
    /**
     * 处理六边形棋盘点击事件
     * @param event 事件数据 {q: number, r: number, action: number}
     */
    GamePageController.prototype.onHexChessBoardClick = function (event) {
        var _a = event.detail || event, q = _a.q, r = _a.r, action = _a.action;
        // 检查是否可以操作（在操作时间内）
        if (!this.isCanOperate()) {
            return;
        }
        // 检查本回合是否已经操作过
        if (this.hasOperatedThisRound) {
            return;
        }
        // 发送六边形点击操作（需要将六边形坐标转换为服务器期望的格式）
        this.sendHexClickBlock(q, r, action);
        // 操作有效，通知六边形棋盘生成预制体
        if (this.hexChessBoardController) {
            if (action === 1) {
                // 挖掘操作，生成不带旗子的预制体
                this.hexChessBoardController.placePlayerOnHexGrid(q, r, false);
            }
            else if (action === 2) {
                // 标记操作，生成带旗子的预制体
                this.hexChessBoardController.placePlayerOnHexGrid(q, r, true);
            }
        }
        // 标记本回合已经操作过，禁止后续交互
        this.hasOperatedThisRound = true;
    };
    //结算
    GamePageController.prototype.setCongratsDialog = function (noticeSettlement) {
        var _this = this;
        this.setCongrats(noticeSettlement);
        //退出弹窗正在显示的话  就先关闭
        if (this.isLeaveGameDialogShow) {
            this.leaveDialogController.hide();
        }
        this.isCongratsDialog = true;
        //弹出结算弹窗
        this.congratsDialogController.show(noticeSettlement, function () {
            _this.isCongratsDialog = false;
        });
    };
    GamePageController.prototype.onDisable = function () {
        //退出弹窗正在显示的话  就先关闭
        if (this.isLeaveGameDialogShow) {
            this.leaveDialogController.hide();
        }
        //结算弹窗正在显示的话就先关闭掉
        if (this.isCongratsDialog) {
            this.congratsDialogController.hide();
        }
        // 清理计时器
        this.clearCountdownTimer();
        // 停止所有动画并重置动画节点状态
        this.stopAllAnimations();
    };
    //结算
    GamePageController.prototype.setCongrats = function (noticeSettlement) {
        // 获取用户列表，优先使用 finalRanking，其次使用 users
        var userList = noticeSettlement.finalRanking || noticeSettlement.users;
        // 检查用户列表是否存在
        if (!noticeSettlement || !userList || !Array.isArray(userList)) {
            console.warn('NoticeSettlement 用户数据无效:', noticeSettlement);
            AudioManager_1.AudioManager.winAudio(); // 默认播放胜利音效
            return;
        }
        var currentUserId = GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.userId;
        var index = userList.findIndex(function (item) { return item.userId === currentUserId; }); //搜索
        if (index >= 0) { //自己参与的话 才会显示正常的胜利和失败的音效，自己不参与的话 就全部显示胜利的音效
            if (userList[index].rank === 1) { //判断自己是不是第一名
                AudioManager_1.AudioManager.winAudio();
            }
            else {
                AudioManager_1.AudioManager.loseAudio();
            }
        }
        else {
            AudioManager_1.AudioManager.winAudio();
        }
    };
    // 处理游戏开始通知，获取炸弹数量和地图类型
    GamePageController.prototype.onGameStart = function (data) {
        var _this = this;
        // 保存地图类型
        this.currentMapType = data.mapType || 0;
        // 根据地图类型重置对应的棋盘控制器
        if (this.currentMapType === 0) {
            // 方形地图
            if (this.chessBoardController) {
                this.chessBoardController.resetGameScene();
                // 如果是联机模式断线重连且有地图数据，恢复棋盘状态
                if (data.mapData) {
                    console.log("联机模式断线重连，恢复方形地图状态，mapData:", data.mapData);
                    this.scheduleOnce(function () {
                        _this.chessBoardController.restoreOnlineMapState(data.mapData);
                    }, 0.1);
                }
            }
            else {
                console.error("❌ chessBoardController 不存在！");
            }
        }
        else if (this.currentMapType === 1) {
            // 六边形地图
            if (this.hexChessBoardController) {
                this.hexChessBoardController.resetGameScene();
                // 忽略服务器的 validHexCoords，使用前端节点坐标
                this.hexChessBoardController.setValidHexCoords([]); // 传入空数组，会被忽略
                // 如果是联机模式断线重连且有地图数据，恢复棋盘状态
                if (data.mapData) {
                    console.log("联机模式断线重连，恢复六边形地图状态，mapData:", data.mapData);
                    this.scheduleOnce(function () {
                        _this.hexChessBoardController.restoreOnlineMapState(data.mapData);
                    }, 0.1);
                }
            }
            else {
                console.error("❌ hexChessBoardController 不存在！");
            }
        }
        // 重置游戏状态
        this.canOperate = false;
        this.hasOperatedThisRound = false;
        this.currentRoundNumber = 0;
        this.currentCountdown = 0;
        this.gameStatus = 0;
        this.isCurrentUserAIManaged = false; // 重置托管状态
        // 根据地图类型获取炸弹数量
        if (data.mapType === 0 && data.mapConfig) {
            // 方形地图
            this.currentMineCount = data.mapConfig.mineCount || 13;
        }
        else if (data.mapType === 1) {
            // 六边形地图，根据前端节点数量计算炸弹数量
            if (this.hexChessBoardController) {
                this.currentMineCount = this.hexChessBoardController.getRecommendedMineCount();
            }
            else {
                this.currentMineCount = 15; // 备用固定值
            }
        }
        else {
            // 默认值
            this.currentMineCount = 13;
        }
        // 更新炸弹数UI
        this.updateMineCountDisplay(this.currentMineCount);
        // 根据地图类型控制地图节点的显示与隐藏
        this.switchMapDisplay(this.currentMapType);
        // 初始化分数界面（使用后端传回来的真实数据）
        if (this.gameScoreController) {
            this.gameScoreController.initializeScoreView();
        }
        // 显示游戏开始节点动画
        this.showGameStartAnimation();
    };
    /**
     * 测试重置功能（可以在浏览器控制台手动调用）
     */
    GamePageController.prototype.testReset = function () {
        if (this.chessBoardController) {
            this.chessBoardController.resetGameScene();
        }
        else {
            console.error("❌ chessBoardController 不存在！");
        }
    };
    // 处理扫雷回合开始通知
    GamePageController.prototype.onNoticeRoundStart = function (data) {
        this.currentRoundNumber = data.roundNumber || 1;
        this.currentCountdown = data.countDown || 25;
        this.gameStatus = data.gameStatus || 0;
        // 隐藏游戏开始节点
        this.hideGameStartAnimation();
        // 新回合开始，重置操作状态
        this.canOperate = true;
        this.hasOperatedThisRound = false;
        // 清理棋盘上的所有玩家预制体
        if (this.currentMapType === 0 && this.chessBoardController) {
            // 方形地图
            this.chessBoardController.clearAllPlayerNodes();
        }
        else if (this.currentMapType === 1 && this.hexChessBoardController) {
            // 六边形地图
            this.hexChessBoardController.clearAllPlayerNodes();
        }
        // 开始倒计时
        this.startCountdown(this.currentCountdown);
    };
    // 处理扫雷操作展示通知
    GamePageController.prototype.onNoticeActionDisplay = function (data) {
        // 保存当前NoticeActionDisplay数据，用于倒计时显示逻辑
        this.currentNoticeActionData = data;
        // 进入展示阶段，不能再操作
        this.canOperate = false;
        this.gameStatus = data.gameStatus || 0;
        // 根据countDown重置倒计时为5秒
        this.currentCountdown = data.countDown || 5;
        this.updateCountdownDisplay(this.currentCountdown);
        this.startCountdown(this.currentCountdown);
        // 更新剩余炸弹数量显示
        if (data.remainingMines !== undefined) {
            this.updateMineCountDisplay(data.remainingMines);
        }
        // 在棋盘上显示所有玩家的操作（头像）
        this.displayPlayerActions(data.playerActions, data.playerTotalScores);
        // 立即显示先手+1（如果先手不是我）
        this.showFirstChoiceBonusImmediately(data.playerActions);
    };
    /**
     * 立即显示先手+1奖励（只为其他人显示，如果我是先手则不显示）
     * @param playerActions 玩家操作列表
     */
    GamePageController.prototype.showFirstChoiceBonusImmediately = function (playerActions) {
        var _a, _b;
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 查找先手玩家
        var firstChoicePlayer = playerActions.find(function (action) { return action.isFirstChoice; });
        // 如果先手玩家存在且不是我，才显示+1
        if (firstChoicePlayer && firstChoicePlayer.userId !== currentUserId) {
            var firstChoiceUserIndex = this.findUserIndex(firstChoicePlayer.userId);
            if (firstChoiceUserIndex !== -1) {
                // 立即显示先手+1
                this.showScoreInScorePanel(firstChoiceUserIndex, 1);
                // 同时在player_game_pfb显示先手+1
                this.showScoreOnPlayerAvatar(firstChoicePlayer.userId, 1);
            }
        }
    };
    /**
     * 延迟更新棋盘的回调方法
     * @param data NoticeActionDisplay数据
     */
    GamePageController.prototype.delayedUpdateBoard = function (data) {
        this.updateBoardAfterActions(data);
    };
    /**
     * 更新棋盘（删除格子、生成预制体、连锁动画）
     * @param data NoticeActionDisplay数据
     */
    GamePageController.prototype.updateBoardAfterActions = function (data) {
        // 注意：分数动画和头像删除现在由倒计时逻辑控制，这里直接处理格子隐藏和数字生成
        var _this = this;
        // 立即处理每个玩家的操作结果
        // 先按位置分组，处理同一位置有多个操作的情况
        var processedPositions = new Set();
        data.playerActions.forEach(function (action) {
            var positionKey = action.x + "," + action.y;
            // 如果这个位置已经处理过，跳过
            if (processedPositions.has(positionKey)) {
                return;
            }
            // 查找同一位置的所有操作
            var samePositionActions = data.playerActions.filter(function (a) {
                return a.x === action.x && a.y === action.y;
            });
            // 处理同一位置的操作结果（格子隐藏和数字生成）
            _this.processPositionResult(action.x, action.y, samePositionActions);
            // 标记这个位置已处理
            processedPositions.add(positionKey);
        });
        // 处理连锁展开结果
        if (data.floodFillResults && data.floodFillResults.length > 0) {
            data.floodFillResults.forEach(function (floodFill) {
                _this.processFloodFillResult(floodFill);
            });
        }
    };
    /**
     * 让所有头像消失（支持方形地图和六边形地图）
     * @param playerActions 玩家操作列表
     * @param onComplete 完成回调
     */
    GamePageController.prototype.hideAllAvatars = function (playerActions, onComplete) {
        // 根据地图类型调用对应的控制器
        if (this.currentMapType === 0 && this.chessBoardController) {
            // 方形地图：直接调用一次头像删除，不区分位置
            this.chessBoardController.hideAvatarsAtPosition(0, 0, function () {
                onComplete();
            });
        }
        else if (this.currentMapType === 1 && this.hexChessBoardController) {
            // 六边形地图：直接调用方法（已经编译成功）
            this.hexChessBoardController.hideAllHexAvatars(function () {
                onComplete();
            });
        }
        else {
            // 没有可用的控制器，直接执行回调
            console.warn("没有可用的棋盘控制器，跳过头像消失动画");
            onComplete();
        }
    };
    /**
     * 处理同一位置的多个操作结果
     * @param x 格子x坐标（方形地图）或q坐标（六边形地图）
     * @param y 格子y坐标（方形地图）或r坐标（六边形地图）
     * @param actions 该位置的所有操作
     */
    GamePageController.prototype.processPositionResult = function (x, y, actions) {
        var _this = this;
        var _a, _b;
        // 根据地图类型删除该位置的格子（播放动画）
        if (this.currentMapType === 0) {
            // 方形地图
            this.chessBoardController.removeGridAt(x, y, false);
        }
        else if (this.currentMapType === 1) {
            // 六边形地图，x实际是q坐标，y实际是r坐标
            if (this.hexChessBoardController) {
                this.hexChessBoardController.hideHexGridAt(x, y, false);
            }
        }
        // 检查是否有地雷被点击（action=1且result="mine"）
        var mineClickAction = actions.find(function (action) {
            return action.action === 1 && action.result === "mine";
        });
        if (mineClickAction) {
            // 如果有地雷被点击，直接显示炸弹，不管是否有标记
            // 判断是否是当前用户点到的雷
            var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
            var isCurrentUser = mineClickAction.userId === currentUserId;
            // 根据地图类型调用对应的方法
            if (this.currentMapType === 0) {
                // 方形地图
                this.chessBoardController.createBoomPrefab(x, y, isCurrentUser);
            }
            else if (this.currentMapType === 1) {
                // 六边形地图，x实际是q坐标，y实际是r坐标
                if (this.hexChessBoardController) {
                    this.hexChessBoardController.createHexBoomPrefab(x, y, isCurrentUser);
                }
            }
            return;
        }
        // 如果没有地雷被点击，按原逻辑处理第一个操作的结果
        var firstAction = actions[0];
        var result = firstAction.result;
        if (result === "correct_mark") {
            // 正确标记：生成biaoji预制体
            if (this.currentMapType === 0) {
                // 方形地图
                this.chessBoardController.createBiaojiPrefab(x, y);
            }
            else if (this.currentMapType === 1) {
                // 六边形地图
                if (this.hexChessBoardController) {
                    this.hexChessBoardController.createHexBiaojiPrefab(x, y);
                }
            }
        }
        else if (typeof result === "number") {
            // 数字：延迟更新neighborMines显示，等动画完成
            this.scheduleOnce(function () {
                if (_this.currentMapType === 0) {
                    // 方形地图
                    _this.chessBoardController.updateNeighborMinesDisplay(x, y, result);
                }
                else if (_this.currentMapType === 1) {
                    // 六边形地图
                    if (_this.hexChessBoardController) {
                        _this.hexChessBoardController.updateHexNeighborMinesDisplay(x, y, result);
                    }
                }
            }, 0.45); // 等待动画完成（0.15秒上升 + 0.3秒下落）
        }
    };
    /**
     * 处理单个玩家操作结果（保留原方法以防其他地方调用）
     * @param action 玩家操作数据
     */
    GamePageController.prototype.processPlayerActionResult = function (action) {
        var _a, _b;
        var x = action.x;
        var y = action.y;
        var result = action.result;
        // 删除该位置的格子
        this.chessBoardController.removeGridAt(x, y);
        // 根据结果生成相应的预制体
        if (result === "mine") {
            // 地雷：生成boom预制体
            // 判断是否是当前用户点到的雷
            var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
            var isCurrentUser = action.userId === currentUserId;
            this.chessBoardController.createBoomPrefab(x, y, isCurrentUser);
        }
        else if (result === "correct_mark") {
            // 正确标记：生成biaoji预制体
            this.chessBoardController.createBiaojiPrefab(x, y);
        }
        else if (typeof result === "number") {
            // 数字：更新neighborMines显示
            this.chessBoardController.updateNeighborMinesDisplay(x, y, result);
        }
    };
    /**
     * 处理连锁展开结果
     * @param floodFill 连锁展开数据
     */
    GamePageController.prototype.processFloodFillResult = function (floodFill) {
        var _this = this;
        // 同时播放所有连锁格子的消失动画
        floodFill.revealedBlocks.forEach(function (block) {
            if (_this.currentMapType === 0) {
                // 方形地图：播放消失动画
                _this.chessBoardController.removeGridAt(block.x, block.y, false);
                // 延迟显示数字，等动画完成
                _this.scheduleOnce(function () {
                    if (block.neighborMines > 0) {
                        _this.chessBoardController.updateNeighborMinesDisplay(block.x, block.y, block.neighborMines);
                    }
                }, 0.45); // 等待动画完成（0.15秒上升 + 0.3秒下落）
            }
            else if (_this.currentMapType === 1) {
                // 六边形地图，x实际是q坐标，y实际是r坐标
                if (_this.hexChessBoardController) {
                    // 播放消失动画
                    _this.hexChessBoardController.hideHexGridAt(block.x, block.y, false);
                    // 延迟显示数字，等动画完成
                    _this.scheduleOnce(function () {
                        if (block.neighborMines > 0) {
                            _this.hexChessBoardController.updateHexNeighborMinesDisplay(block.x, block.y, block.neighborMines);
                        }
                    }, 0.45); // 等待动画完成
                }
            }
        });
    };
    // 处理扫雷回合结束通知
    GamePageController.prototype.onNoticeRoundEnd = function (data) {
        // 进入回合结束阶段，不能再操作
        this.canOperate = false;
        this.gameStatus = data.gameStatus || 1;
        // 不再处理倒计时，让客户端自然倒计时到0，方便展示54321
        // 处理玩家分数动画和头像显示
        if (data.playerResults && data.playerResults.length > 0) {
            this.displayPlayerScoreAnimations(data.playerResults);
            // 如果本回合我没有操作，根据后端消息生成我的头像
            this.handleMyAvatarIfNotOperated(data.playerResults);
        }
        // 清理棋盘上的所有玩家预制体
        if (this.currentMapType === 0 && this.chessBoardController) {
            // 方形地图
            this.chessBoardController.clearAllPlayerNodes();
        }
        else if (this.currentMapType === 1 && this.hexChessBoardController) {
            // 六边形地图
            this.hexChessBoardController.clearAllPlayers();
        }
    };
    /**
     * 在棋盘上显示所有玩家的操作
     * @param playerActions 玩家操作列表
     * @param playerTotalScores 玩家总分数据
     */
    GamePageController.prototype.displayPlayerActions = function (playerActions, playerTotalScores) {
        var _this = this;
        var _a, _b;
        // 检查是否有可用的棋盘控制器
        var hasSquareBoard = this.chessBoardController && this.currentMapType === 0;
        var hasHexBoard = this.hexChessBoardController && this.currentMapType === 1;
        if ((!hasSquareBoard && !hasHexBoard) || !playerActions || playerActions.length === 0) {
            return;
        }
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 注意：分数动画已经在updateBoardAfterActions的第一步显示了，这里不再重复显示
        // 检查本回合是否进行了操作，如果没有，需要显示自己的头像
        var myAction = playerActions.find(function (action) { return action.userId === currentUserId; });
        var shouldDisplayMyAvatar = false;
        if (!this.hasOperatedThisRound && myAction) {
            shouldDisplayMyAvatar = true;
            // 生成我的头像
            var withFlag = (myAction.action === 2); // action=2表示标记操作，显示旗子
            if (this.currentMapType === 0) {
                // 方形地图
                this.chessBoardController.placePlayerOnGrid(myAction.x, myAction.y, withFlag);
            }
            else if (this.currentMapType === 1) {
                // 六边形地图，x实际是q坐标，y实际是r坐标
                if (this.hexChessBoardController) {
                    this.hexChessBoardController.placePlayerOnHexGrid(myAction.x, myAction.y, withFlag);
                }
            }
        }
        // 过滤掉自己的操作，只显示其他玩家的操作
        var otherPlayersActions = playerActions.filter(function (action) { return action.userId !== currentUserId; });
        if (otherPlayersActions.length === 0) {
            return;
        }
        // 按位置分组其他玩家的操作
        var positionGroups = this.groupActionsByPosition(otherPlayersActions);
        // 为每个位置生成预制体
        positionGroups.forEach(function (actions, positionKey) {
            var _a = positionKey.split(',').map(Number), x = _a[0], y = _a[1];
            if (_this.currentMapType === 0) {
                // 方形地图
                _this.chessBoardController.displayOtherPlayersAtPosition(x, y, actions);
            }
            else if (_this.currentMapType === 1) {
                // 六边形地图，x实际是q坐标，y实际是r坐标
                if (_this.hexChessBoardController) {
                    // 直接调用方法（已经编译成功）
                    _this.hexChessBoardController.displayOtherPlayersAtHexPosition(x, y, actions);
                }
            }
        });
    };
    /**
     * 按位置分组玩家操作
     * @param playerActions 玩家操作列表
     * @returns Map<string, PlayerActionDisplay[]> 位置为key，操作列表为value
     */
    GamePageController.prototype.groupActionsByPosition = function (playerActions) {
        var groups = new Map();
        for (var _i = 0, playerActions_1 = playerActions; _i < playerActions_1.length; _i++) {
            var action = playerActions_1[_i];
            var positionKey = action.x + "," + action.y;
            if (!groups.has(positionKey)) {
                groups.set(positionKey, []);
            }
            groups.get(positionKey).push(action);
        }
        return groups;
    };
    /**
     * 显示玩家分数动画
     * @param playerResults 玩家回合结果列表
     */
    GamePageController.prototype.displayPlayerScoreAnimations = function (playerResults) {
        var _this = this;
        var _a, _b;
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 为每个玩家显示分数动画
        playerResults.forEach(function (result, index) {
            // 延迟显示，让动画错开
            _this.scheduleOnce(function () {
                _this.showPlayerScoreAnimation(result, currentUserId);
            }, index * 0.2);
        });
    };
    /**
     * 显示单个玩家的分数动画
     * @param result 玩家回合结果
     * @param currentUserId 当前用户ID
     */
    GamePageController.prototype.showPlayerScoreAnimation = function (result, currentUserId) {
        var isMyself = result.userId === currentUserId;
        if (isMyself) {
            // 自己的分数动画：在player_game_pfb里只显示本回合得分
            this.showMyScoreAnimation(result);
        }
        else {
            // 其他人的分数动画：根据isFirstChoice决定显示逻辑
            this.showOtherPlayerScoreAnimation(result);
        }
    };
    /**
     * 显示自己的分数动画
     * @param result 玩家回合结果
     */
    GamePageController.prototype.showMyScoreAnimation = function (result) {
        // 在棋盘上的头像预制体中显示本回合得分
        if (this.currentMapType === 0 && this.chessBoardController) {
            // 方形地图
            this.chessBoardController.showScoreOnPlayerNode(result.x, result.y, result.score, false);
        }
        else if (this.currentMapType === 1 && this.hexChessBoardController) {
            // 六边形地图，x实际是q坐标，y实际是r坐标
            this.hexChessBoardController.showScoreOnHexPlayerNode(result.x, result.y, result.score, false);
        }
        // 在player_score_pfb中显示分数动画
        this.showScoreAnimationInScorePanel(result.userId, result.score, result.isFirstChoice);
    };
    /**
     * 显示其他玩家的分数动画
     * @param result 玩家回合结果
     */
    GamePageController.prototype.showOtherPlayerScoreAnimation = function (result) {
        if (result.isFirstChoice) {
            // 其他人为先手：player_game_pfb里不显示+1，只显示本回合得分
            if (this.currentMapType === 0 && this.chessBoardController) {
                // 方形地图
                this.chessBoardController.showScoreOnPlayerNode(result.x, result.y, result.score, false);
            }
            else if (this.currentMapType === 1 && this.hexChessBoardController) {
                // 六边形地图
                this.hexChessBoardController.showScoreOnHexPlayerNode(result.x, result.y, result.score, false);
            }
            // 在player_score_pfb里先显示+1，再显示本回合得分，然后更新总分
            this.showFirstChoiceScoreAnimation(result.userId, result.score);
        }
        else {
            // 其他人非先手：正常显示本回合得分
            if (this.currentMapType === 0 && this.chessBoardController) {
                // 方形地图
                this.chessBoardController.showScoreOnPlayerNode(result.x, result.y, result.score, false);
            }
            else if (this.currentMapType === 1 && this.hexChessBoardController) {
                // 六边形地图
                this.hexChessBoardController.showScoreOnHexPlayerNode(result.x, result.y, result.score, false);
            }
            // 在player_score_pfb中显示分数动画
            this.showScoreAnimationInScorePanel(result.userId, result.score, false);
        }
    };
    /**
     * 在分数面板中显示分数动画
     * @param userId 用户ID
     * @param score 本回合得分
     * @param isFirstChoice 是否为先手
     */
    GamePageController.prototype.showScoreAnimationInScorePanel = function (userId, score, isFirstChoice) {
        // 这里需要找到对应的PlayerScoreController并调用分数动画
        // 由于没有直接的引用，这里先用日志记录
        // TODO: 实现在player_score_pfb中显示分数动画的逻辑
        // 需要找到对应用户的PlayerScoreController实例并调用showAddScore方法
    };
    /**
     * 显示先手玩家的分数动画（先显示+1，再显示本回合得分）
     * @param userId 用户ID
     * @param score 本回合得分
     */
    GamePageController.prototype.showFirstChoiceScoreAnimation = function (userId, score) {
        var _this = this;
        // 先显示+1的先手奖励
        this.scheduleOnce(function () {
            _this.showScoreAnimationInScorePanel(userId, 1, true);
        }, 0.1);
        // 再显示本回合得分
        this.scheduleOnce(function () {
            _this.showScoreAnimationInScorePanel(userId, score, false);
        }, 1.2);
        // 最后更新总分
        this.scheduleOnce(function () {
            _this.updatePlayerTotalScore(userId, score + 1);
        }, 2.4);
    };
    /**
     * 更新玩家总分
     * @param userId 用户ID
     * @param totalScore 新的总分
     */
    GamePageController.prototype.updatePlayerTotalScore = function (userId, totalScore) {
        // TODO: 实现更新玩家总分的逻辑
        // 需要更新GlobalBean中的用户数据，并刷新UI显示
    };
    /**
     * 如果本回合我没有操作，根据后端消息生成我的头像
     * @param playerResults 玩家回合结果列表
     */
    GamePageController.prototype.handleMyAvatarIfNotOperated = function (playerResults) {
        var _a, _b;
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 检查本回合是否进行了操作
        if (this.hasOperatedThisRound) {
            return;
        }
        // 查找我的操作结果
        var myResult = playerResults.find(function (result) { return result.userId === currentUserId; });
        if (!myResult) {
            return;
        }
        // 根据后端消息生成我的头像
        if (this.chessBoardController) {
            // 根据操作类型决定是否显示旗子
            var withFlag = (myResult.action === 2); // action=2表示标记操作，显示旗子
            // 生成我的头像预制体
            this.chessBoardController.placePlayerOnGrid(myResult.x, myResult.y, withFlag);
        }
    };
    // 发送点击方块消息
    GamePageController.prototype.sendClickBlock = function (x, y, action) {
        if (!this.canOperate) {
            return;
        }
        // 检查本回合是否已经操作过
        if (this.hasOperatedThisRound) {
            return;
        }
        var clickData = {
            x: x,
            y: y,
            action: action // 1=挖掘方块，2=标记/取消标记地雷
        };
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeClickBlock, clickData);
        // 标记本回合已经操作过，防止重复操作
        this.hasOperatedThisRound = true;
    };
    // 发送六边形点击方块消息
    GamePageController.prototype.sendHexClickBlock = function (q, r, action) {
        if (!this.canOperate) {
            return;
        }
        // 检查本回合是否已经操作过
        if (this.hasOperatedThisRound) {
            return;
        }
        // 根据当前地图类型决定发送格式
        if (this.currentMapType === 1) {
            // 六边形地图：使用六边形坐标格式
            var hexClickData = {
                q: q,
                r: r,
                action: action // 1=挖掘方块，2=标记/取消标记地雷
            };
            // 注意：这里仍然使用 MsgTypeClickBlock，但数据格式不同
            // 后端应该根据当前房间的 mapType 来解析不同的坐标格式
            WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeClickBlock, hexClickData);
        }
        else {
            // 方形地图：转换为x,y坐标（备用方案）
            var clickData = {
                x: q,
                y: r,
                action: action
            };
            WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeClickBlock, clickData);
        }
        // 标记本回合已经操作过，防止重复操作
        this.hasOperatedThisRound = true;
    };
    // 检查是否可以操作
    GamePageController.prototype.isCanOperate = function () {
        return this.canOperate && !this.hasOperatedThisRound;
    };
    /**
     * 处理首选玩家奖励通知
     * @param data NoticeFirstChoiceBonus 消息数据
     */
    GamePageController.prototype.onNoticeFirstChoiceBonus = function (data) {
        var _a, _b;
        // 转发给GameScoreController处理所有玩家的分数更新和加分动画
        if (this.gameScoreController) {
            this.gameScoreController.onNoticeFirstChoiceBonus(data);
        }
        // 判断是否为当前用户，如果是则同时更新player_game_pfb中的change_score
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        var isMyself = (data.userId === currentUserId);
        if (isMyself) {
            // 更新player_game_pfb中的change_score显示
            this.updatePlayerGameScore(data.userId, data.bonusScore);
        }
    };
    /**
     * 处理AI托管状态变更通知
     * @param data AIStatusChange 消息数据
     */
    GamePageController.prototype.onAIStatusChange = function (data) {
        var _a, _b;
        var userId = data.userId, isAIManaged = data.isAIManaged;
        // 转发给GameScoreController处理托管状态显示
        if (this.gameScoreController) {
            this.gameScoreController.onAIStatusChange(userId, isAIManaged);
        }
        // 检查是否为当前用户的托管状态变更
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (userId === currentUserId) {
            // 更新当前用户的托管状态
            this.isCurrentUserAIManaged = isAIManaged;
            if (isAIManaged) {
                // 当前用户进入AI托管，显示托管页面
                this.showAIManagedDialog();
            }
            else {
                // 当前用户退出AI托管，隐藏托管页面
                this.hideAIManagedDialog();
            }
        }
    };
    /**
     * 显示AI托管页面
     */
    GamePageController.prototype.showAIManagedDialog = function () {
        if (this.aiManagedDialogController) {
            this.aiManagedDialogController.show();
        }
        else {
            console.warn("❌ AI托管页面控制器未设置，请在编辑器中设置 aiManagedDialogController 属性");
        }
    };
    /**
     * 隐藏AI托管页面
     */
    GamePageController.prototype.hideAIManagedDialog = function () {
        if (this.aiManagedDialogController) {
            this.aiManagedDialogController.hide();
        }
    };
    /**
     * 更新player_game_pfb中的change_score显示
     * @param userId 用户ID
     * @param bonusScore 奖励分数
     */
    GamePageController.prototype.updatePlayerGameScore = function (userId, bonusScore) {
        // 根据地图类型调用对应的控制器显示加分效果
        if (this.currentMapType === 0 && this.chessBoardController) {
            // 方形地图
            this.chessBoardController.showPlayerGameScore(userId, bonusScore);
        }
        else if (this.currentMapType === 1 && this.hexChessBoardController) {
            // 六边形地图
            this.hexChessBoardController.showHexPlayerGameScore(userId, bonusScore);
        }
        else {
            console.warn("\u5730\u56FE\u7C7B\u578B" + this.currentMapType + "\u7684\u68CB\u76D8\u63A7\u5236\u5668\u672A\u8BBE\u7F6E\uFF0C\u65E0\u6CD5\u663E\u793Aplayer_game_pfb\u52A0\u5206\u6548\u679C");
        }
    };
    // 获取当前地图类型
    GamePageController.prototype.getCurrentMapType = function () {
        return this.currentMapType;
    };
    // 获取当前炸弹数量
    GamePageController.prototype.getCurrentMineCount = function () {
        return this.currentMineCount;
    };
    // 获取当前回合操作状态（用于调试）
    GamePageController.prototype.getCurrentRoundStatus = function () {
        return {
            roundNumber: this.currentRoundNumber,
            canOperate: this.canOperate,
            hasOperated: this.hasOperatedThisRound
        };
    };
    GamePageController.prototype.onDestroy = function () {
        // 清理事件监听
        if (this.node) {
            this.node.off(cc.Node.EventType.TOUCH_END, this.onGamePageClick, this);
        }
        // 清理棋盘事件监听
        if (this.chessBoardController) {
            this.chessBoardController.node.off('chess-board-click', this.onChessBoardClick, this);
        }
        if (this.hexChessBoardController) {
            this.hexChessBoardController.node.off('hex-chess-board-click', this.onHexChessBoardClick, this);
        }
        // 清理倒计时
        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
            this.countdownInterval = null;
        }
        // 停止所有动画
        this.stopAllAnimations();
    };
    // 开始倒计时
    GamePageController.prototype.startCountdown = function (seconds) {
        var _this = this;
        // 清除之前的计时器
        this.clearCountdownTimer();
        var remainingSeconds = seconds;
        this.updateCountdownDisplay(remainingSeconds);
        this.countdownInterval = setInterval(function () {
            remainingSeconds--;
            _this.updateCountdownDisplay(remainingSeconds);
            // 在NoticeActionDisplay阶段，根据倒计时执行不同的显示逻辑
            if (_this.gameStatus === 0 && _this.currentNoticeActionData) {
                _this.handleCountdownBasedDisplay(remainingSeconds);
            }
            else {
            }
            if (remainingSeconds <= 0) {
                _this.clearCountdownTimer();
            }
        }, 1000);
    };
    // 更新倒计时显示
    GamePageController.prototype.updateCountdownDisplay = function (seconds) {
        if (this.timeLabel) {
            this.timeLabel.string = seconds + "s"; // 显示数字加s：5s, 4s, 3s, 2s, 1s, 0s
        }
        this.currentCountdown = seconds;
    };
    /**
     * 根据倒计时处理不同时机的显示逻辑
     * @param remainingSeconds 剩余秒数
     */
    GamePageController.prototype.handleCountdownBasedDisplay = function (remainingSeconds) {
        if (!this.currentNoticeActionData) {
            return;
        }
        var data = this.currentNoticeActionData;
        if (remainingSeconds === 4) {
            // 4s时：同时展示本回合加减分
            this.showCurrentRoundScores(data.playerActions, data.playerTotalScores);
        }
        else if (remainingSeconds === 3) {
            // 3s时：隐藏加减分并删除头像预制体
            this.hideScoreEffectsAndAvatars(data.playerActions);
            // 3s时：立即执行格子隐藏和生成数字预制体等操作
            this.updateBoardAfterActions(data);
        }
        else if (remainingSeconds === 2) {
            // 2s时：显示回合开始节点动画
            this.showRoundStartAnimation();
            // 在2s时清空数据，避免重复执行
            this.currentNoticeActionData = null;
        }
    };
    /**
     * 显示本回合所有玩家的加减分
     * @param playerActions 玩家操作列表
     * @param playerTotalScores 玩家总分数据
     */
    GamePageController.prototype.showCurrentRoundScores = function (playerActions, playerTotalScores) {
        var _this = this;
        var _a, _b;
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 为每个玩家显示本回合的加减分
        playerActions.forEach(function (action) {
            // 在player_game_pfb中显示本回合的加减分
            _this.showScoreOnPlayerAvatar(action.userId, action.score);
            // 在分数面板显示本回合的加减分
            var userIndex = _this.findUserIndex(action.userId);
            if (userIndex !== -1) {
                _this.showScoreInScorePanel(userIndex, action.score);
            }
        });
        // 延迟更新总分，让加减分动画先显示
        this.scheduleOnce(function () {
            // 更新所有玩家的总分
            playerActions.forEach(function (action) {
                var totalScore = playerTotalScores[action.userId] || 0;
                if (_this.gameScoreController) {
                    _this.gameScoreController.updatePlayerScore(action.userId, totalScore);
                    // 更新全局数据中的总分
                    _this.updatePlayerTotalScoreInGlobalData(action.userId, totalScore);
                }
            });
        }, 1.2);
    };
    /**
     * 隐藏加减分效果并删除头像预制体
     * @param playerActions 玩家操作列表
     */
    GamePageController.prototype.hideScoreEffectsAndAvatars = function (playerActions) {
        // 隐藏所有加减分效果
        this.hideAllScoreEffects();
        // 删除头像预制体（不等待完成回调）
        this.hideAllAvatars(playerActions, function () {
        });
    };
    /**
     * 隐藏所有加减分效果
     */
    GamePageController.prototype.hideAllScoreEffects = function () {
        // 隐藏分数面板的加减分效果
        // 注意：这里暂时不处理分数面板的隐藏，因为PlayerScoreController的hideScoreEffects会在1秒后自动隐藏
        // 隐藏棋盘上所有头像的加减分效果
        this.hideAllPlayerGameScoreEffects();
    };
    /**
     * 隐藏棋盘上所有头像的加减分效果
     */
    GamePageController.prototype.hideAllPlayerGameScoreEffects = function () {
        // 遍历棋盘上的所有PlayerGameController，调用hideScoreEffects方法
        if (this.currentMapType === 0 && this.chessBoardController && this.chessBoardController.boardNode) {
            // 方形地图
            var children = this.chessBoardController.boardNode.children;
            for (var i = 0; i < children.length; i++) {
                var child = children[i];
                var playerController = child.getComponent(PlayerGameController_1.default);
                if (playerController) {
                    playerController.hideScoreEffects();
                }
            }
        }
        else if (this.currentMapType === 1 && this.hexChessBoardController && this.hexChessBoardController.boardNode) {
            // 六边形地图
            var children = this.hexChessBoardController.boardNode.children;
            for (var i = 0; i < children.length; i++) {
                var child = children[i];
                var playerController = child.getComponent(PlayerGameController_1.default);
                if (playerController) {
                    playerController.hideScoreEffects();
                }
            }
        }
    };
    // 更新炸弹数显示
    GamePageController.prototype.updateMineCountDisplay = function (mineCount) {
        if (this.mineCountLabel) {
            this.mineCountLabel.string = "" + mineCount;
        }
    };
    // 根据地图类型切换地图显示
    GamePageController.prototype.switchMapDisplay = function (mapType) {
        // 先隐藏所有地图
        this.hideAllMaps();
        // 根据地图类型显示对应的地图
        if (mapType === 0) {
            this.showSquareMap();
        }
        else if (mapType === 1) {
            this.showHexMap();
        }
        else {
            console.warn("\u672A\u77E5\u7684\u5730\u56FE\u7C7B\u578B: " + mapType + "\uFF0C\u9ED8\u8BA4\u663E\u793A\u65B9\u5F62\u5730\u56FE");
            this.showSquareMap();
        }
    };
    // 显示方形地图
    GamePageController.prototype.showSquareMap = function () {
        if (this.squareMapNode) {
            this.squareMapNode.active = true;
        }
        else {
            console.warn('方形地图节点未挂载');
        }
    };
    // 显示六边形地图
    GamePageController.prototype.showHexMap = function () {
        if (this.hexMapNode) {
            this.hexMapNode.active = true;
        }
        else {
            console.warn('六边形地图节点未挂载');
        }
    };
    // 隐藏所有地图
    GamePageController.prototype.hideAllMaps = function () {
        if (this.squareMapNode) {
            this.squareMapNode.active = false;
        }
        if (this.hexMapNode) {
            this.hexMapNode.active = false;
        }
    };
    // 清除倒计时定时器
    GamePageController.prototype.clearCountdownTimer = function () {
        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
            this.countdownInterval = null;
        }
    };
    /**
     * 显示所有玩家的分数动画和更新总分（参考先手加分逻辑）
     * @param playerActions 玩家操作列表
     * @param playerTotalScores 玩家总分数据
     */
    GamePageController.prototype.displayPlayerScoreAnimationsAndUpdateTotalScores = function (playerActions, playerTotalScores) {
        var _this = this;
        var _a, _b;
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 查找先手玩家
        var firstChoicePlayer = playerActions.find(function (action) { return action.isFirstChoice; });
        var isCurrentUserFirstChoice = firstChoicePlayer && firstChoicePlayer.userId === currentUserId;
        // 如果我不是先手，先为先手玩家在分数面板显示+1
        if (!isCurrentUserFirstChoice && firstChoicePlayer) {
            var firstChoiceUserIndex_1 = this.findUserIndex(firstChoicePlayer.userId);
            if (firstChoiceUserIndex_1 !== -1) {
                // 0.1秒后显示先手+1
                this.scheduleOnce(function () {
                    _this.showScoreInScorePanel(firstChoiceUserIndex_1, 1);
                }, 0.1);
            }
        }
        // 为每个玩家显示分数动画和更新总分
        playerActions.forEach(function (action, index) {
            var totalScore = playerTotalScores[action.userId] || 0;
            var isFirstChoice = action.isFirstChoice;
            // 延迟显示，让动画错开
            _this.scheduleOnce(function () {
                if (isFirstChoice) {
                    // 先手玩家：特殊处理（先显示+1，再显示本回合分数）
                    _this.showFirstChoicePlayerScoreAnimation(action, currentUserId, totalScore);
                }
                else {
                    // 非先手玩家：直接显示本回合分数
                    _this.showPlayerScoreAnimationAndUpdateTotal(action, currentUserId, totalScore);
                }
            }, index * 0.2);
        });
    };
    /**
     * 显示单个玩家的分数动画和更新总分（参考先手加分逻辑）
     * @param action 玩家操作数据
     * @param currentUserId 当前用户ID
     * @param totalScore 玩家总分
     */
    GamePageController.prototype.showPlayerScoreAnimationAndUpdateTotal = function (action, currentUserId, totalScore) {
        var _this = this;
        var isMyself = action.userId === currentUserId;
        // 1. 在分数面板显示加减分动画（参考先手加分的逻辑）
        if (this.gameScoreController) {
            // 找到用户索引
            var userIndex = this.findUserIndex(action.userId);
            if (userIndex !== -1) {
                // 在分数面板显示加减分效果
                this.showScoreInScorePanel(userIndex, action.score);
            }
        }
        // 2. 更新总分（参考先手加分的updatePlayerScore）
        this.scheduleOnce(function () {
            if (_this.gameScoreController) {
                _this.gameScoreController.updatePlayerScore(action.userId, totalScore);
                // 更新全局数据中的总分
                _this.updatePlayerTotalScoreInGlobalData(action.userId, totalScore);
            }
        }, 1.2);
        // 3. 在所有玩家头像上显示加减分（不仅仅是自己）
        this.scheduleOnce(function () {
            _this.showScoreOnPlayerAvatar(action.userId, action.score);
        }, 0.1);
    };
    /**
     * 更新全局数据中的玩家总分
     * @param userId 用户ID
     * @param totalScore 新的总分
     */
    GamePageController.prototype.updatePlayerTotalScoreInGlobalData = function (userId, totalScore) {
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法更新玩家总分");
            return;
        }
        var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
        var userIndex = users.findIndex(function (user) { return user.userId === userId; });
        if (userIndex !== -1) {
            users[userIndex].score = totalScore;
        }
        else {
            console.warn("\u627E\u4E0D\u5230\u73A9\u5BB6: userId=" + userId);
        }
    };
    /**
     * 查找用户索引
     * @param userId 用户ID
     * @returns 用户索引，找不到返回-1
     */
    GamePageController.prototype.findUserIndex = function (userId) {
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法查找用户索引");
            return -1;
        }
        var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
        return users.findIndex(function (user) { return user.userId === userId; });
    };
    /**
     * 在玩家头像上显示加减分
     * @param userId 用户ID
     * @param score 分数变化
     */
    GamePageController.prototype.showScoreOnPlayerAvatar = function (userId, score) {
        // 根据地图类型调用对应的控制器
        if (this.currentMapType === 0 && this.chessBoardController) {
            // 方形地图
            this.chessBoardController.showPlayerGameScore(userId, score);
        }
        else if (this.currentMapType === 1 && this.hexChessBoardController) {
            // 六边形地图
            this.hexChessBoardController.showHexPlayerGameScore(userId, score);
        }
        else {
            console.warn("没有可用的棋盘控制器，无法显示头像分数");
        }
    };
    /**
     * 在分数面板显示加减分效果
     * @param userIndex 用户索引
     * @param score 分数变化
     */
    GamePageController.prototype.showScoreInScorePanel = function (userIndex, score) {
        if (!this.gameScoreController) {
            console.warn("gameScoreController 不存在，无法在分数面板显示分数");
            return;
        }
        // 获取对应的PlayerScoreController
        var playerScoreController = this.gameScoreController.getPlayerScoreController(userIndex);
        if (playerScoreController) {
            // 显示加减分效果
            if (score > 0) {
                playerScoreController.showAddScore(score);
            }
            else if (score < 0) {
                playerScoreController.showSubScore(Math.abs(score));
            }
        }
        else {
            console.warn("\u627E\u4E0D\u5230\u7528\u6237\u7D22\u5F15 " + userIndex + " \u5BF9\u5E94\u7684PlayerScoreController");
        }
    };
    /**
     * 显示先手玩家的分数动画（在分数面板先显示+1，再显示本回合分数）
     * @param action 玩家操作数据
     * @param currentUserId 当前用户ID
     * @param totalScore 玩家总分
     */
    GamePageController.prototype.showFirstChoicePlayerScoreAnimation = function (action, currentUserId, totalScore) {
        var _this = this;
        var userIndex = this.findUserIndex(action.userId);
        // 第一步：在分数面板显示+1先手奖励（1.2秒，与非先手玩家同步）
        this.scheduleOnce(function () {
            // 分数面板显示本回合分数（+1已经在前面显示过了）
            if (userIndex !== -1) {
                _this.showScoreInScorePanel(userIndex, action.score);
            }
        }, 1.2);
        // 第二步：更新总分（2.4秒）
        this.scheduleOnce(function () {
            if (_this.gameScoreController) {
                _this.gameScoreController.updatePlayerScore(action.userId, totalScore);
                _this.updatePlayerTotalScoreInGlobalData(action.userId, totalScore);
            }
        }, 2.4);
        // 第三步：在player_game_pfb中显示本回合的加减分（与非先手玩家同步）
        this.scheduleOnce(function () {
            _this.showScoreOnPlayerAvatar(action.userId, action.score);
        }, 0.1);
    };
    /**
     * 初始化动画节点
     */
    GamePageController.prototype.initializeAnimationNodes = function () {
        // 如果节点没有在编辑器中设置，尝试通过路径查找或创建
        if (!this.gameStartNode) {
            this.gameStartNode = cc.find('Canvas/game_start_node');
            if (!this.gameStartNode) {
                // 创建游戏开始节点
                this.gameStartNode = this.createGameStartNode();
            }
        }
        if (!this.roundStartNode) {
            this.roundStartNode = cc.find('Canvas/round_start_node');
            if (!this.roundStartNode) {
                // 创建回合开始节点
                this.roundStartNode = this.createRoundStartNode();
            }
        }
        // 初始状态设为隐藏
        if (this.gameStartNode) {
            this.gameStartNode.active = false;
        }
        if (this.roundStartNode) {
            this.roundStartNode.active = false;
        }
    };
    /**
     * 创建游戏开始节点
     */
    GamePageController.prototype.createGameStartNode = function () {
        var node = new cc.Node('game_start_node');
        var canvas = cc.find('Canvas');
        if (canvas) {
            canvas.addChild(node);
        }
        // 设置节点位置和层级
        node.setPosition(0, 0);
        node.zIndex = 1000;
        // 添加Sprite组件并加载图片
        var sprite = node.addComponent(cc.Sprite);
        cc.resources.load('开始游戏@2x-2', cc.SpriteFrame, function (err, spriteFrame) {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
            }
            else {
                console.warn("无法加载游戏开始图片资源");
            }
        });
        return node;
    };
    /**
     * 创建回合开始节点
     */
    GamePageController.prototype.createRoundStartNode = function () {
        var node = new cc.Node('round_start_node');
        var canvas = cc.find('Canvas');
        if (canvas) {
            canvas.addChild(node);
        }
        else {
            console.warn("找不到Canvas节点");
        }
        // 设置节点位置和层级
        node.setPosition(-750, -1);
        node.zIndex = 1000;
        // 添加Sprite组件并加载图片
        var sprite = node.addComponent(cc.Sprite);
        cc.resources.load('huihe@2x-2', cc.SpriteFrame, function (err, spriteFrame) {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
            }
            else {
                console.warn("无法加载回合开始图片资源:", err);
            }
        });
        return node;
    };
    /**
     * 显示游戏开始节点动画（放大展示）
     */
    GamePageController.prototype.showGameStartAnimation = function () {
        if (!this.gameStartNode) {
            console.warn("游戏开始节点不存在");
            return;
        }
        // 停止之前可能正在进行的动画
        this.gameStartNode.stopAllActions();
        // 初始化节点状态
        this.gameStartNode.active = true;
        this.gameStartNode.scale = 0;
        this.gameStartNode.opacity = 255;
        // 放大展示动画
        cc.tween(this.gameStartNode)
            .to(0.3, { scale: 1.2 }, { easing: 'backOut' })
            .to(0.2, { scale: 1.0 }, { easing: 'backOut' })
            .start();
    };
    /**
     * 隐藏游戏开始节点动画（缩小隐藏）
     */
    GamePageController.prototype.hideGameStartAnimation = function () {
        var _this = this;
        if (!this.gameStartNode || !this.gameStartNode.active) {
            return;
        }
        // 停止之前可能正在进行的动画
        this.gameStartNode.stopAllActions();
        // 缩小隐藏动画
        cc.tween(this.gameStartNode)
            .to(0.3, { scale: 0, opacity: 0 }, { easing: 'backIn' })
            .call(function () {
            _this.gameStartNode.active = false;
        })
            .start();
    };
    /**
     * 显示回合开始节点动画（从左边移入到中间）
     */
    GamePageController.prototype.showRoundStartAnimation = function () {
        var _this = this;
        if (!this.roundStartNode) {
            console.warn("回合开始节点不存在");
            return;
        }
        // 停止之前可能正在进行的动画
        this.roundStartNode.stopAllActions();
        // 初始化节点状态：在(-750, -1)位置
        this.roundStartNode.active = true;
        this.roundStartNode.setPosition(-750, -1);
        this.roundStartNode.opacity = 255;
        this.roundStartNode.scale = 1;
        // 0.5秒移动到中间(0, -1)，1秒展示，0.5秒移动到(750, -1)，然后恢复到(-750, -1)
        cc.tween(this.roundStartNode)
            .to(0.5, { x: 0 }, { easing: 'quartOut' })
            .delay(1.0)
            .to(0.5, { x: 750 }, { easing: 'quartIn' })
            .call(function () {
            // 恢复到初始位置(-750, -1)为下一次回合开始做准备
            _this.roundStartNode.setPosition(-750, -1);
            _this.roundStartNode.active = false;
        })
            .start();
    };
    /**
     * 停止所有动画并重置动画节点状态
     */
    GamePageController.prototype.stopAllAnimations = function () {
        // 停止回合开始动画
        if (this.roundStartNode) {
            this.roundStartNode.stopAllActions();
            // 重置回合开始节点到初始状态
            this.roundStartNode.active = false;
            this.roundStartNode.setPosition(-750, -1);
            this.roundStartNode.opacity = 255;
            this.roundStartNode.scale = 1;
        }
        // 停止游戏开始动画
        if (this.gameStartNode) {
            this.gameStartNode.stopAllActions();
            // 重置游戏开始节点到初始状态
            this.gameStartNode.active = false;
            this.gameStartNode.scale = 0;
            this.gameStartNode.opacity = 255;
        }
        // 停止AI托管页面动画
        if (this.aiManagedDialogController) {
            this.aiManagedDialogController.hide();
        }
    };
    __decorate([
        property(cc.Node)
    ], GamePageController.prototype, "boardBtnBack", void 0);
    __decorate([
        property(cc.Label)
    ], GamePageController.prototype, "timeLabel", void 0);
    __decorate([
        property(cc.Label)
    ], GamePageController.prototype, "mineCountLabel", void 0);
    __decorate([
        property(cc.Node)
    ], GamePageController.prototype, "squareMapNode", void 0);
    __decorate([
        property(cc.Node)
    ], GamePageController.prototype, "hexMapNode", void 0);
    __decorate([
        property(LeaveDialogController_1.default)
    ], GamePageController.prototype, "leaveDialogController", void 0);
    __decorate([
        property(CongratsDialogController_1.default)
    ], GamePageController.prototype, "congratsDialogController", void 0);
    __decorate([
        property(AIManagedDialogController_1.default)
    ], GamePageController.prototype, "aiManagedDialogController", void 0);
    __decorate([
        property(GameScoreController_1.default)
    ], GamePageController.prototype, "gameScoreController", void 0);
    __decorate([
        property(ChessBoardController_1.default)
    ], GamePageController.prototype, "chessBoardController", void 0);
    __decorate([
        property(HexChessBoardController_1.default)
    ], GamePageController.prototype, "hexChessBoardController", void 0);
    __decorate([
        property(cc.Node)
    ], GamePageController.prototype, "gameStartNode", void 0);
    __decorate([
        property(cc.Node)
    ], GamePageController.prototype, "roundStartNode", void 0);
    GamePageController = __decorate([
        ccclass
    ], GamePageController);
    return GamePageController;
}(cc.Component));
exports.default = GamePageController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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