
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/hall/HallPageController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '63546mbf2xAv6Hj/p5rHgOb', 'HallPageController');
// scripts/hall/HallPageController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HallOrMatch = void 0;
var GlobalBean_1 = require("../bean/GlobalBean");
var GameMgr_1 = require("../common/GameMgr");
var MessageId_1 = require("../net/MessageId");
var WebSocketManager_1 = require("../net/WebSocketManager");
var WebSocketTool_1 = require("../net/WebSocketTool");
var ToastController_1 = require("../ToastController");
var AudioManager_1 = require("../util/AudioManager");
var HallParentController_1 = require("./HallParentController");
var InfoDialogController_1 = require("./InfoDialogController");
var KickOutDialogController_1 = require("./KickOutDialogController");
var LeaveDialogController_1 = require("./LeaveDialogController");
var LevelSelectPageController_1 = require("./Level/LevelSelectPageController");
var MatchParentController_1 = require("./MatchParentController");
var SettingDialogController_1 = require("./SettingDialogController");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var HallOrMatch;
(function (HallOrMatch) {
    HallOrMatch[HallOrMatch["HALL_PARENT"] = 0] = "HALL_PARENT";
    HallOrMatch[HallOrMatch["MATCH_PARENT"] = 1] = "MATCH_PARENT";
})(HallOrMatch = exports.HallOrMatch || (exports.HallOrMatch = {}));
var HallPageController = /** @class */ (function (_super) {
    __extends(HallPageController, _super);
    function HallPageController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.hallParentNode = null;
        _this.matchParentNode = null;
        _this.infoDialogController = null; //道具简介弹窗
        _this.leaveDialogController = null; // 退出游戏弹窗
        _this.settingDialogController = null; //设置弹窗
        _this.kickOutDialogController = null; //踢出用户的 dialog
        _this.toastController = null; //toast 的布局
        _this.levelSelectPageController = null; //关卡选择页面控制器
        _this.hallOrMatch = null;
        return _this;
        // update (dt) {}
    }
    HallPageController.prototype.onLoad = function () {
        this.hallParentController = this.hallParentNode.getComponent(HallParentController_1.default);
        this.matchParentController = this.matchParentNode.getComponent(MatchParentController_1.default);
    };
    HallPageController.prototype.onEnable = function () {
        AudioManager_1.AudioManager.playBgm();
        this.setHallOrMatch(HallOrMatch.HALL_PARENT);
    };
    HallPageController.prototype.start = function () {
        var _this = this;
        this.hallParentController.setClick(function () {
            //返回键的回调
            _this.leaveDialogController.show(0, function () { });
        }, function () {
            //info 的回调
            _this.infoDialogController.show(function () { });
        }, function () {
            //设置键的回调
            _this.settingDialogController.show(function () { });
        }, function () {
            //start 按钮点击
            _this.startOrCreate(MessageId_1.MessageId.MsgTypePairRequest);
        }, function () {
            //create 点击创建房间
            _this.startOrCreate(MessageId_1.MessageId.MsgTypeCreateInvite);
        }, function (userId, nickname) {
            //点击创建房间内的 点击玩家头像 弹出的踢出房间弹窗
            _this.kickOutDialogController.show(userId, nickname);
        });
        this.matchParentController.setClick(function () {
            //匹配页面的返回键的回调
            WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeCancelPair, {});
        });
    };
    HallPageController.prototype.updateGold = function () {
        this.hallParentController.updateGold();
    };
    //开始匹配 或者创建房间
    HallPageController.prototype.startOrCreate = function (msgId) {
        //判断是否链接成功，并且还得有登录成功的数据返回 ，不成功的话就不允许执行下面的操作
        if (WebSocketManager_1.WebSocketManager.GetInstance().webState != WebSocketTool_1.WebSocketToolState.Connected || GlobalBean_1.GlobalBean.GetInstance().loginData == null) {
            return;
        }
        //点击 快速开始游戏 start 的回调
        var pairRequest = {
            playerNum: GlobalBean_1.GlobalBean.GetInstance().players,
            fee: GlobalBean_1.GlobalBean.GetInstance().ticketsNum,
        };
        //发送请求开始游戏的消息
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(msgId, pairRequest);
    };
    //设置是大厅 还是匹配页面
    HallPageController.prototype.setHallOrMatch = function (hallOrMatch) {
        if (this.hallOrMatch === hallOrMatch) {
            return;
        }
        this.hallOrMatch = hallOrMatch;
        this.hallParentNode.active = false;
        this.matchParentNode.active = false;
        switch (hallOrMatch) {
            case HallOrMatch.HALL_PARENT:
                this.hallParentNode.active = true;
                break;
            case HallOrMatch.MATCH_PARENT:
                this.matchParentNode.active = true;
                break;
        }
    };
    HallPageController.prototype.LoginSuccess = function () {
        //登录成功后 执行的操作
        GameMgr_1.GameMgr.Console.Log("登录成功");
        var loginData = GlobalBean_1.GlobalBean.GetInstance().loginData;
        if (loginData) {
            if (loginData.roomId > 0) { //正在游戏中，需要断线重连
                // 先发送EnterRoom消息，服务端会返回isOnlineMode字段
                WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeEnterRoom, {}); //重连进来的 玩家请求进入房间
            }
            if (loginData.inviteCode > 0) { //正在私人房间
                WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeAcceptInvite, { 'inviteCode': Number(loginData.inviteCode) });
            }
            else {
                //房间已经解散了  但是我还留在私人房间
                this.hallParentController.exitTheRoom();
            }
            //重连的时候 被遗留在匹配页面的话 就回到大厅
            if (loginData.roomId == 0 && loginData.inviteCode == 0 && this.hallOrMatch == HallOrMatch.MATCH_PARENT) {
                this.setHallOrMatch(HallOrMatch.HALL_PARENT);
            }
        }
        this.setFees();
        // 登录成功后请求关卡进度
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeExtendLevelProgress, {});
    };
    //设置接受邀请成功
    HallPageController.prototype.setAcceptInvite = function (acceptInvite) {
        this.hallParentController.setAcceptInvite(acceptInvite);
    };
    //离开房间
    HallPageController.prototype.leaveRoom = function (noticeLeaveInvite) {
        this.hallParentController.leaveRoom(noticeLeaveInvite);
    };
    //设置门票
    HallPageController.prototype.setFees = function () {
        this.hallParentController.setFees();
    };
    //初始化 match 页面
    HallPageController.prototype.createMatchView = function () {
        this.matchParentController.createMatchView();
    };
    //设置匹配数据
    HallPageController.prototype.setGameData = function () {
        this.matchParentController.setGameData();
    };
    //进入私人房间
    HallPageController.prototype.joinCreateRoom = function () {
        this.hallParentController.joinCreateRoom();
    };
    //房间号无效
    HallPageController.prototype.joinError = function () {
        this.hallParentController.joinError();
    };
    //准备 取消准备
    HallPageController.prototype.setReadyState = function (noticeUserInviteStatus) {
        if (this.hallParentController) {
            this.hallParentController.setReadyState(noticeUserInviteStatus);
        }
    };
    /**
     * 设置关卡进度（从后端获取数据后调用）
     * @param levelProgressData 关卡进度数据，包含clearedLevels, currentLevel, totalLevels
     */
    HallPageController.prototype.setLevelProgress = function (levelProgressData) {
        if (this.levelSelectPageController) {
            this.levelSelectPageController.setLevelProgress(levelProgressData);
        }
    };
    __decorate([
        property(cc.Node)
    ], HallPageController.prototype, "hallParentNode", void 0);
    __decorate([
        property(cc.Node)
    ], HallPageController.prototype, "matchParentNode", void 0);
    __decorate([
        property(InfoDialogController_1.default)
    ], HallPageController.prototype, "infoDialogController", void 0);
    __decorate([
        property(LeaveDialogController_1.default)
    ], HallPageController.prototype, "leaveDialogController", void 0);
    __decorate([
        property(SettingDialogController_1.default)
    ], HallPageController.prototype, "settingDialogController", void 0);
    __decorate([
        property(KickOutDialogController_1.default)
    ], HallPageController.prototype, "kickOutDialogController", void 0);
    __decorate([
        property(ToastController_1.default)
    ], HallPageController.prototype, "toastController", void 0);
    __decorate([
        property(LevelSelectPageController_1.default)
    ], HallPageController.prototype, "levelSelectPageController", void 0);
    HallPageController = __decorate([
        ccclass
    ], HallPageController);
    return HallPageController;
}(cc.Component));
exports.default = HallPageController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0cy9zY3JpcHRzL2hhbGwvSGFsbFBhZ2VDb250cm9sbGVyLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxvQkFBb0I7QUFDcEIsNEVBQTRFO0FBQzVFLG1CQUFtQjtBQUNuQixzRkFBc0Y7QUFDdEYsOEJBQThCO0FBQzlCLHNGQUFzRjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUd0RixpREFBZ0Q7QUFDaEQsNkNBQTRDO0FBQzVDLDhDQUE2QztBQUM3Qyw0REFBMkQ7QUFDM0Qsc0RBQTBEO0FBQzFELHNEQUFpRDtBQUNqRCxxREFBb0Q7QUFDcEQsK0RBQTBEO0FBQzFELCtEQUEwRDtBQUMxRCxxRUFBZ0U7QUFDaEUsaUVBQTREO0FBQzVELCtFQUEwRTtBQUMxRSxpRUFBNEQ7QUFDNUQscUVBQWdFO0FBRTFELElBQUEsS0FBd0IsRUFBRSxDQUFDLFVBQVUsRUFBbkMsT0FBTyxhQUFBLEVBQUUsUUFBUSxjQUFrQixDQUFDO0FBRzVDLElBQVksV0FHWDtBQUhELFdBQVksV0FBVztJQUNuQiwyREFBVyxDQUFBO0lBQ1gsNkRBQVksQ0FBQTtBQUNoQixDQUFDLEVBSFcsV0FBVyxHQUFYLG1CQUFXLEtBQVgsbUJBQVcsUUFHdEI7QUFHRDtJQUFnRCxzQ0FBWTtJQUE1RDtRQUFBLHFFQXNMQztRQW5MRyxvQkFBYyxHQUFZLElBQUksQ0FBQztRQUUvQixxQkFBZSxHQUFZLElBQUksQ0FBQztRQUVoQywwQkFBb0IsR0FBeUIsSUFBSSxDQUFBLENBQUMsUUFBUTtRQUUxRCwyQkFBcUIsR0FBMEIsSUFBSSxDQUFBLENBQUMsU0FBUztRQUU3RCw2QkFBdUIsR0FBNEIsSUFBSSxDQUFBLENBQUMsTUFBTTtRQUU5RCw2QkFBdUIsR0FBNEIsSUFBSSxDQUFBLENBQUMsY0FBYztRQUV0RSxxQkFBZSxHQUFvQixJQUFJLENBQUEsQ0FBRSxXQUFXO1FBRXBELCtCQUF5QixHQUE4QixJQUFJLENBQUEsQ0FBQyxXQUFXO1FBRXZFLGlCQUFXLEdBQWdCLElBQUksQ0FBQTs7UUFrSy9CLGlCQUFpQjtJQUNyQixDQUFDO0lBOUpHLG1DQUFNLEdBQU47UUFDSSxJQUFJLENBQUMsb0JBQW9CLEdBQUcsSUFBSSxDQUFDLGNBQWMsQ0FBQyxZQUFZLENBQUMsOEJBQW9CLENBQUMsQ0FBQTtRQUNsRixJQUFJLENBQUMscUJBQXFCLEdBQUcsSUFBSSxDQUFDLGVBQWUsQ0FBQyxZQUFZLENBQUMsK0JBQXFCLENBQUMsQ0FBQTtJQUV6RixDQUFDO0lBRVMscUNBQVEsR0FBbEI7UUFDSSwyQkFBWSxDQUFDLE9BQU8sRUFBRSxDQUFBO1FBQ3RCLElBQUksQ0FBQyxjQUFjLENBQUMsV0FBVyxDQUFDLFdBQVcsQ0FBQyxDQUFBO0lBRWhELENBQUM7SUFFRCxrQ0FBSyxHQUFMO1FBQUEsaUJBOEJDO1FBNUJHLElBQUksQ0FBQyxvQkFBb0IsQ0FBQyxRQUFRLENBQUM7WUFDL0IsUUFBUTtZQUNSLEtBQUksQ0FBQyxxQkFBcUIsQ0FBQyxJQUFJLENBQUMsQ0FBQyxFQUFDLGNBQUssQ0FBQyxDQUFDLENBQUE7UUFDN0MsQ0FBQyxFQUFFO1lBQ0MsVUFBVTtZQUNWLEtBQUksQ0FBQyxvQkFBb0IsQ0FBQyxJQUFJLENBQUMsY0FBSyxDQUFDLENBQUMsQ0FBQTtRQUMxQyxDQUFDLEVBQUU7WUFDQyxRQUFRO1lBQ1IsS0FBSSxDQUFDLHVCQUF1QixDQUFDLElBQUksQ0FBQyxjQUFLLENBQUMsQ0FBQyxDQUFBO1FBQzdDLENBQUMsRUFBRTtZQUNDLFlBQVk7WUFDWixLQUFJLENBQUMsYUFBYSxDQUFDLHFCQUFTLENBQUMsa0JBQWtCLENBQUMsQ0FBQTtRQUVwRCxDQUFDLEVBQUU7WUFDQyxlQUFlO1lBQ2YsS0FBSSxDQUFDLGFBQWEsQ0FBQyxxQkFBUyxDQUFDLG1CQUFtQixDQUFDLENBQUE7UUFFckQsQ0FBQyxFQUFFLFVBQUMsTUFBYyxFQUFFLFFBQWdCO1lBQ2hDLDJCQUEyQjtZQUMzQixLQUFJLENBQUMsdUJBQXVCLENBQUMsSUFBSSxDQUFDLE1BQU0sRUFBRSxRQUFRLENBQUMsQ0FBQTtRQUN2RCxDQUFDLENBQUMsQ0FBQTtRQUVGLElBQUksQ0FBQyxxQkFBcUIsQ0FBQyxRQUFRLENBQUM7WUFDaEMsYUFBYTtZQUNiLG1DQUFnQixDQUFDLFdBQVcsRUFBRSxDQUFDLE9BQU8sQ0FBQyxxQkFBUyxDQUFDLGlCQUFpQixFQUFFLEVBQUUsQ0FBQyxDQUFDO1FBRTVFLENBQUMsQ0FBQyxDQUFBO0lBRU4sQ0FBQztJQUNELHVDQUFVLEdBQVY7UUFDSSxJQUFJLENBQUMsb0JBQW9CLENBQUMsVUFBVSxFQUFFLENBQUE7SUFDMUMsQ0FBQztJQUVELGFBQWE7SUFDYiwwQ0FBYSxHQUFiLFVBQWMsS0FBZ0I7UUFDMUIsMkNBQTJDO1FBQzNDLElBQUksbUNBQWdCLENBQUMsV0FBVyxFQUFFLENBQUMsUUFBUSxJQUFJLGtDQUFrQixDQUFDLFNBQVMsSUFBSSx1QkFBVSxDQUFDLFdBQVcsRUFBRSxDQUFDLFNBQVMsSUFBSSxJQUFJLEVBQUU7WUFDdkgsT0FBTztTQUNWO1FBRUQscUJBQXFCO1FBQ3JCLElBQUksV0FBVyxHQUFnQjtZQUMzQixTQUFTLEVBQUUsdUJBQVUsQ0FBQyxXQUFXLEVBQUUsQ0FBQyxPQUFPO1lBQzNDLEdBQUcsRUFBRSx1QkFBVSxDQUFDLFdBQVcsRUFBRSxDQUFDLFVBQVU7U0FDM0MsQ0FBQTtRQUNELGFBQWE7UUFDYixtQ0FBZ0IsQ0FBQyxXQUFXLEVBQUUsQ0FBQyxPQUFPLENBQUMsS0FBSyxFQUFFLFdBQVcsQ0FBQyxDQUFDO0lBQy9ELENBQUM7SUFFRCxjQUFjO0lBQ2QsMkNBQWMsR0FBZCxVQUFlLFdBQXdCO1FBQ25DLElBQUksSUFBSSxDQUFDLFdBQVcsS0FBSyxXQUFXLEVBQUU7WUFDbEMsT0FBTTtTQUNUO1FBQ0QsSUFBSSxDQUFDLFdBQVcsR0FBRyxXQUFXLENBQUE7UUFDOUIsSUFBSSxDQUFDLGNBQWMsQ0FBQyxNQUFNLEdBQUcsS0FBSyxDQUFBO1FBQ2xDLElBQUksQ0FBQyxlQUFlLENBQUMsTUFBTSxHQUFHLEtBQUssQ0FBQTtRQUNuQyxRQUFRLFdBQVcsRUFBRTtZQUNqQixLQUFLLFdBQVcsQ0FBQyxXQUFXO2dCQUN4QixJQUFJLENBQUMsY0FBYyxDQUFDLE1BQU0sR0FBRyxJQUFJLENBQUE7Z0JBQ2pDLE1BQU07WUFDVixLQUFLLFdBQVcsQ0FBQyxZQUFZO2dCQUN6QixJQUFJLENBQUMsZUFBZSxDQUFDLE1BQU0sR0FBRyxJQUFJLENBQUE7Z0JBQ2xDLE1BQU07U0FDYjtJQUNMLENBQUM7SUFHRCx5Q0FBWSxHQUFaO1FBQ0ksYUFBYTtRQUNiLGlCQUFPLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxNQUFNLENBQUMsQ0FBQTtRQUMzQixJQUFJLFNBQVMsR0FBRyx1QkFBVSxDQUFDLFdBQVcsRUFBRSxDQUFDLFNBQVMsQ0FBQTtRQUNsRCxJQUFJLFNBQVMsRUFBRTtZQUNYLElBQUksU0FBUyxDQUFDLE1BQU0sR0FBRyxDQUFDLEVBQUUsRUFBRSxjQUFjO2dCQUN0QyxzQ0FBc0M7Z0JBQ3RDLG1DQUFnQixDQUFDLFdBQVcsRUFBRSxDQUFDLE9BQU8sQ0FBQyxxQkFBUyxDQUFDLGdCQUFnQixFQUFFLEVBQUUsQ0FBQyxDQUFBLENBQUEsZ0JBQWdCO2FBQ3pGO1lBQ0QsSUFBSSxTQUFTLENBQUMsVUFBVSxHQUFHLENBQUMsRUFBRSxFQUFDLFFBQVE7Z0JBQ25DLG1DQUFnQixDQUFDLFdBQVcsRUFBRSxDQUFDLE9BQU8sQ0FBQyxxQkFBUyxDQUFDLG1CQUFtQixFQUFFLEVBQUUsWUFBWSxFQUFFLE1BQU0sQ0FBQyxTQUFTLENBQUMsVUFBVSxDQUFDLEVBQUUsQ0FBQyxDQUFBO2FBQ3hIO2lCQUFNO2dCQUNILHFCQUFxQjtnQkFDckIsSUFBSSxDQUFDLG9CQUFvQixDQUFDLFdBQVcsRUFBRSxDQUFBO2FBQzFDO1lBQ0Qsd0JBQXdCO1lBQ3hCLElBQUksU0FBUyxDQUFDLE1BQU0sSUFBSSxDQUFDLElBQUksU0FBUyxDQUFDLFVBQVUsSUFBSSxDQUFDLElBQUksSUFBSSxDQUFDLFdBQVcsSUFBSSxXQUFXLENBQUMsWUFBWSxFQUFFO2dCQUNwRyxJQUFJLENBQUMsY0FBYyxDQUFDLFdBQVcsQ0FBQyxXQUFXLENBQUMsQ0FBQTthQUMvQztTQUNKO1FBQ0QsSUFBSSxDQUFDLE9BQU8sRUFBRSxDQUFBO1FBQ2QsY0FBYztRQUNkLG1DQUFnQixDQUFDLFdBQVcsRUFBRSxDQUFDLE9BQU8sQ0FBQyxxQkFBUyxDQUFDLDBCQUEwQixFQUFFLEVBQUUsQ0FBQyxDQUFDO0lBQ3JGLENBQUM7SUFFRCxVQUFVO0lBQ1YsNENBQWUsR0FBZixVQUFnQixZQUEwQjtRQUN0QyxJQUFJLENBQUMsb0JBQW9CLENBQUMsZUFBZSxDQUFDLFlBQVksQ0FBQyxDQUFBO0lBQzNELENBQUM7SUFDRCxNQUFNO0lBQ04sc0NBQVMsR0FBVCxVQUFVLGlCQUFvQztRQUMxQyxJQUFJLENBQUMsb0JBQW9CLENBQUMsU0FBUyxDQUFDLGlCQUFpQixDQUFDLENBQUE7SUFDMUQsQ0FBQztJQUdELE1BQU07SUFDTixvQ0FBTyxHQUFQO1FBQ0ksSUFBSSxDQUFDLG9CQUFvQixDQUFDLE9BQU8sRUFBRSxDQUFBO0lBQ3ZDLENBQUM7SUFFRCxjQUFjO0lBQ2QsNENBQWUsR0FBZjtRQUNJLElBQUksQ0FBQyxxQkFBcUIsQ0FBQyxlQUFlLEVBQUUsQ0FBQTtJQUNoRCxDQUFDO0lBQ0QsUUFBUTtJQUNSLHdDQUFXLEdBQVg7UUFDSSxJQUFJLENBQUMscUJBQXFCLENBQUMsV0FBVyxFQUFFLENBQUE7SUFDNUMsQ0FBQztJQUVELFFBQVE7SUFDUiwyQ0FBYyxHQUFkO1FBQ0ksSUFBSSxDQUFDLG9CQUFvQixDQUFDLGNBQWMsRUFBRSxDQUFBO0lBQzlDLENBQUM7SUFDRCxPQUFPO0lBQ1Asc0NBQVMsR0FBVDtRQUNJLElBQUksQ0FBQyxvQkFBb0IsQ0FBQyxTQUFTLEVBQUUsQ0FBQTtJQUN6QyxDQUFDO0lBQ0QsU0FBUztJQUNULDBDQUFhLEdBQWIsVUFBYyxzQkFBOEM7UUFDeEQsSUFBSSxJQUFJLENBQUMsb0JBQW9CLEVBQUU7WUFDM0IsSUFBSSxDQUFDLG9CQUFvQixDQUFDLGFBQWEsQ0FBQyxzQkFBc0IsQ0FBQyxDQUFBO1NBQ2xFO0lBRUwsQ0FBQztJQUVEOzs7T0FHRztJQUNILDZDQUFnQixHQUFoQixVQUFpQixpQkFBc0I7UUFDbkMsSUFBSSxJQUFJLENBQUMseUJBQXlCLEVBQUU7WUFDaEMsSUFBSSxDQUFDLHlCQUF5QixDQUFDLGdCQUFnQixDQUFDLGlCQUFpQixDQUFDLENBQUM7U0FDdEU7SUFDTCxDQUFDO0lBaExEO1FBREMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUM7OERBQ2E7SUFFL0I7UUFEQyxRQUFRLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQzsrREFDYztJQUVoQztRQURDLFFBQVEsQ0FBQyw4QkFBb0IsQ0FBQztvRUFDa0I7SUFFakQ7UUFEQyxRQUFRLENBQUMsK0JBQXFCLENBQUM7cUVBQ21CO0lBRW5EO1FBREMsUUFBUSxDQUFDLGlDQUF1QixDQUFDO3VFQUNxQjtJQUV2RDtRQURDLFFBQVEsQ0FBQyxpQ0FBdUIsQ0FBQzt1RUFDcUI7SUFFdkQ7UUFEQyxRQUFRLENBQUMseUJBQWUsQ0FBQzsrREFDYTtJQUV2QztRQURDLFFBQVEsQ0FBQyxtQ0FBeUIsQ0FBQzt5RUFDdUI7SUFqQjFDLGtCQUFrQjtRQUR0QyxPQUFPO09BQ2Esa0JBQWtCLENBc0x0QztJQUFELHlCQUFDO0NBdExELEFBc0xDLENBdEwrQyxFQUFFLENBQUMsU0FBUyxHQXNMM0Q7a0JBdExvQixrQkFBa0IiLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyIvLyBMZWFybiBUeXBlU2NyaXB0OlxuLy8gIC0gaHR0cHM6Ly9kb2NzLmNvY29zLmNvbS9jcmVhdG9yLzIuNC9tYW51YWwvZW4vc2NyaXB0aW5nL3R5cGVzY3JpcHQuaHRtbFxuLy8gTGVhcm4gQXR0cmlidXRlOlxuLy8gIC0gaHR0cHM6Ly9kb2NzLmNvY29zLmNvbS9jcmVhdG9yLzIuNC9tYW51YWwvZW4vc2NyaXB0aW5nL3JlZmVyZW5jZS9hdHRyaWJ1dGVzLmh0bWxcbi8vIExlYXJuIGxpZmUtY3ljbGUgY2FsbGJhY2tzOlxuLy8gIC0gaHR0cHM6Ly9kb2NzLmNvY29zLmNvbS9jcmVhdG9yLzIuNC9tYW51YWwvZW4vc2NyaXB0aW5nL2xpZmUtY3ljbGUtY2FsbGJhY2tzLmh0bWxcblxuaW1wb3J0IHsgQWNjZXB0SW52aXRlLCBOb3RpY2VMZWF2ZUludml0ZSwgTm90aWNlVXNlckludml0ZVN0YXR1cywgUGFpclJlcXVlc3QgfSBmcm9tIFwiLi4vYmVhbi9HYW1lQmVhblwiO1xuaW1wb3J0IHsgR2xvYmFsQmVhbiB9IGZyb20gXCIuLi9iZWFuL0dsb2JhbEJlYW5cIjtcbmltcG9ydCB7IEdhbWVNZ3IgfSBmcm9tIFwiLi4vY29tbW9uL0dhbWVNZ3JcIjtcbmltcG9ydCB7IE1lc3NhZ2VJZCB9IGZyb20gXCIuLi9uZXQvTWVzc2FnZUlkXCI7XG5pbXBvcnQgeyBXZWJTb2NrZXRNYW5hZ2VyIH0gZnJvbSBcIi4uL25ldC9XZWJTb2NrZXRNYW5hZ2VyXCI7XG5pbXBvcnQgeyBXZWJTb2NrZXRUb29sU3RhdGUgfSBmcm9tIFwiLi4vbmV0L1dlYlNvY2tldFRvb2xcIjtcbmltcG9ydCBUb2FzdENvbnRyb2xsZXIgZnJvbSBcIi4uL1RvYXN0Q29udHJvbGxlclwiO1xuaW1wb3J0IHsgQXVkaW9NYW5hZ2VyIH0gZnJvbSBcIi4uL3V0aWwvQXVkaW9NYW5hZ2VyXCI7XG5pbXBvcnQgSGFsbFBhcmVudENvbnRyb2xsZXIgZnJvbSBcIi4vSGFsbFBhcmVudENvbnRyb2xsZXJcIjtcbmltcG9ydCBJbmZvRGlhbG9nQ29udHJvbGxlciBmcm9tIFwiLi9JbmZvRGlhbG9nQ29udHJvbGxlclwiO1xuaW1wb3J0IEtpY2tPdXREaWFsb2dDb250cm9sbGVyIGZyb20gXCIuL0tpY2tPdXREaWFsb2dDb250cm9sbGVyXCI7XG5pbXBvcnQgTGVhdmVEaWFsb2dDb250cm9sbGVyIGZyb20gXCIuL0xlYXZlRGlhbG9nQ29udHJvbGxlclwiO1xuaW1wb3J0IExldmVsU2VsZWN0UGFnZUNvbnRyb2xsZXIgZnJvbSBcIi4vTGV2ZWwvTGV2ZWxTZWxlY3RQYWdlQ29udHJvbGxlclwiO1xuaW1wb3J0IE1hdGNoUGFyZW50Q29udHJvbGxlciBmcm9tIFwiLi9NYXRjaFBhcmVudENvbnRyb2xsZXJcIjtcbmltcG9ydCBTZXR0aW5nRGlhbG9nQ29udHJvbGxlciBmcm9tIFwiLi9TZXR0aW5nRGlhbG9nQ29udHJvbGxlclwiO1xuXG5jb25zdCB7IGNjY2xhc3MsIHByb3BlcnR5IH0gPSBjYy5fZGVjb3JhdG9yO1xuXG5cbmV4cG9ydCBlbnVtIEhhbGxPck1hdGNoIHtcbiAgICBIQUxMX1BBUkVOVCwgICAgLy/lpKfljoXpobXpnaJcbiAgICBNQVRDSF9QQVJFTlQsICAgLy/ljLnphY3pobXpnaJcbn1cblxuQGNjY2xhc3NcbmV4cG9ydCBkZWZhdWx0IGNsYXNzIEhhbGxQYWdlQ29udHJvbGxlciBleHRlbmRzIGNjLkNvbXBvbmVudCB7XG5cbiAgICBAcHJvcGVydHkoY2MuTm9kZSlcbiAgICBoYWxsUGFyZW50Tm9kZTogY2MuTm9kZSA9IG51bGw7XG4gICAgQHByb3BlcnR5KGNjLk5vZGUpXG4gICAgbWF0Y2hQYXJlbnROb2RlOiBjYy5Ob2RlID0gbnVsbDtcbiAgICBAcHJvcGVydHkoSW5mb0RpYWxvZ0NvbnRyb2xsZXIpXG4gICAgaW5mb0RpYWxvZ0NvbnRyb2xsZXI6IEluZm9EaWFsb2dDb250cm9sbGVyID0gbnVsbCAvL+mBk+WFt+eugOS7i+W8ueeql1xuICAgIEBwcm9wZXJ0eShMZWF2ZURpYWxvZ0NvbnRyb2xsZXIpXG4gICAgbGVhdmVEaWFsb2dDb250cm9sbGVyOiBMZWF2ZURpYWxvZ0NvbnRyb2xsZXIgPSBudWxsIC8vIOmAgOWHuua4uOaIj+W8ueeql1xuICAgIEBwcm9wZXJ0eShTZXR0aW5nRGlhbG9nQ29udHJvbGxlcilcbiAgICBzZXR0aW5nRGlhbG9nQ29udHJvbGxlcjogU2V0dGluZ0RpYWxvZ0NvbnRyb2xsZXIgPSBudWxsIC8v6K6+572u5by556qXXG4gICAgQHByb3BlcnR5KEtpY2tPdXREaWFsb2dDb250cm9sbGVyKVxuICAgIGtpY2tPdXREaWFsb2dDb250cm9sbGVyOiBLaWNrT3V0RGlhbG9nQ29udHJvbGxlciA9IG51bGwgLy/ouKLlh7rnlKjmiLfnmoQgZGlhbG9nXG4gICAgQHByb3BlcnR5KFRvYXN0Q29udHJvbGxlcilcbiAgICB0b2FzdENvbnRyb2xsZXI6IFRvYXN0Q29udHJvbGxlciA9IG51bGwgIC8vdG9hc3Qg55qE5biD5bGAXG4gICAgQHByb3BlcnR5KExldmVsU2VsZWN0UGFnZUNvbnRyb2xsZXIpXG4gICAgbGV2ZWxTZWxlY3RQYWdlQ29udHJvbGxlcjogTGV2ZWxTZWxlY3RQYWdlQ29udHJvbGxlciA9IG51bGwgLy/lhbPljaHpgInmi6npobXpnaLmjqfliLblmahcblxuICAgIGhhbGxPck1hdGNoOiBIYWxsT3JNYXRjaCA9IG51bGxcblxuICAgIGhhbGxQYXJlbnRDb250cm9sbGVyOiBIYWxsUGFyZW50Q29udHJvbGxlclxuICAgIG1hdGNoUGFyZW50Q29udHJvbGxlcjogTWF0Y2hQYXJlbnRDb250cm9sbGVyXG5cbiAgICBvbkxvYWQoKSB7XG4gICAgICAgIHRoaXMuaGFsbFBhcmVudENvbnRyb2xsZXIgPSB0aGlzLmhhbGxQYXJlbnROb2RlLmdldENvbXBvbmVudChIYWxsUGFyZW50Q29udHJvbGxlcilcbiAgICAgICAgdGhpcy5tYXRjaFBhcmVudENvbnRyb2xsZXIgPSB0aGlzLm1hdGNoUGFyZW50Tm9kZS5nZXRDb21wb25lbnQoTWF0Y2hQYXJlbnRDb250cm9sbGVyKVxuXG4gICAgfVxuXG4gICAgcHJvdGVjdGVkIG9uRW5hYmxlKCk6IHZvaWQge1xuICAgICAgICBBdWRpb01hbmFnZXIucGxheUJnbSgpXG4gICAgICAgIHRoaXMuc2V0SGFsbE9yTWF0Y2goSGFsbE9yTWF0Y2guSEFMTF9QQVJFTlQpXG5cbiAgICB9XG5cbiAgICBzdGFydCgpIHtcblxuICAgICAgICB0aGlzLmhhbGxQYXJlbnRDb250cm9sbGVyLnNldENsaWNrKCgpID0+IHtcbiAgICAgICAgICAgIC8v6L+U5Zue6ZSu55qE5Zue6LCDXG4gICAgICAgICAgICB0aGlzLmxlYXZlRGlhbG9nQ29udHJvbGxlci5zaG93KDAsKCk9Pnt9KVxuICAgICAgICB9LCAoKSA9PiB7XG4gICAgICAgICAgICAvL2luZm8g55qE5Zue6LCDXG4gICAgICAgICAgICB0aGlzLmluZm9EaWFsb2dDb250cm9sbGVyLnNob3coKCk9Pnt9KVxuICAgICAgICB9LCAoKSA9PiB7XG4gICAgICAgICAgICAvL+iuvue9rumUrueahOWbnuiwg1xuICAgICAgICAgICAgdGhpcy5zZXR0aW5nRGlhbG9nQ29udHJvbGxlci5zaG93KCgpPT57fSlcbiAgICAgICAgfSwgKCkgPT4ge1xuICAgICAgICAgICAgLy9zdGFydCDmjInpkq7ngrnlh7tcbiAgICAgICAgICAgIHRoaXMuc3RhcnRPckNyZWF0ZShNZXNzYWdlSWQuTXNnVHlwZVBhaXJSZXF1ZXN0KVxuXG4gICAgICAgIH0sICgpID0+IHtcbiAgICAgICAgICAgIC8vY3JlYXRlIOeCueWHu+WIm+W7uuaIv+mXtFxuICAgICAgICAgICAgdGhpcy5zdGFydE9yQ3JlYXRlKE1lc3NhZ2VJZC5Nc2dUeXBlQ3JlYXRlSW52aXRlKVxuXG4gICAgICAgIH0sICh1c2VySWQ6IHN0cmluZywgbmlja25hbWU6IHN0cmluZykgPT4ge1xuICAgICAgICAgICAgLy/ngrnlh7vliJvlu7rmiL/pl7TlhoXnmoQg54K55Ye7546p5a625aS05YOPIOW8ueWHuueahOi4ouWHuuaIv+mXtOW8ueeql1xuICAgICAgICAgICAgdGhpcy5raWNrT3V0RGlhbG9nQ29udHJvbGxlci5zaG93KHVzZXJJZCwgbmlja25hbWUpXG4gICAgICAgIH0pXG5cbiAgICAgICAgdGhpcy5tYXRjaFBhcmVudENvbnRyb2xsZXIuc2V0Q2xpY2soKCkgPT4ge1xuICAgICAgICAgICAgLy/ljLnphY3pobXpnaLnmoTov5Tlm57plK7nmoTlm57osINcbiAgICAgICAgICAgIFdlYlNvY2tldE1hbmFnZXIuR2V0SW5zdGFuY2UoKS5zZW5kTXNnKE1lc3NhZ2VJZC5Nc2dUeXBlQ2FuY2VsUGFpciwge30pO1xuXG4gICAgICAgIH0pXG5cbiAgICB9XG4gICAgdXBkYXRlR29sZCgpIHtcbiAgICAgICAgdGhpcy5oYWxsUGFyZW50Q29udHJvbGxlci51cGRhdGVHb2xkKClcbiAgICB9XG5cbiAgICAvL+W8gOWni+WMuemFjSDmiJbogIXliJvlu7rmiL/pl7RcbiAgICBzdGFydE9yQ3JlYXRlKG1zZ0lkOiBNZXNzYWdlSWQpIHtcbiAgICAgICAgLy/liKTmlq3mmK/lkKbpk77mjqXmiJDlip/vvIzlubbkuJTov5jlvpfmnInnmbvlvZXmiJDlip/nmoTmlbDmja7ov5Tlm54g77yM5LiN5oiQ5Yqf55qE6K+d5bCx5LiN5YWB6K645omn6KGM5LiL6Z2i55qE5pON5L2cXG4gICAgICAgIGlmIChXZWJTb2NrZXRNYW5hZ2VyLkdldEluc3RhbmNlKCkud2ViU3RhdGUgIT0gV2ViU29ja2V0VG9vbFN0YXRlLkNvbm5lY3RlZCB8fCBHbG9iYWxCZWFuLkdldEluc3RhbmNlKCkubG9naW5EYXRhID09IG51bGwpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuXG4gICAgICAgIC8v54K55Ye7IOW/q+mAn+W8gOWni+a4uOaIjyBzdGFydCDnmoTlm57osINcbiAgICAgICAgbGV0IHBhaXJSZXF1ZXN0OiBQYWlyUmVxdWVzdCA9IHtcbiAgICAgICAgICAgIHBsYXllck51bTogR2xvYmFsQmVhbi5HZXRJbnN0YW5jZSgpLnBsYXllcnMsXG4gICAgICAgICAgICBmZWU6IEdsb2JhbEJlYW4uR2V0SW5zdGFuY2UoKS50aWNrZXRzTnVtLFxuICAgICAgICB9XG4gICAgICAgIC8v5Y+R6YCB6K+35rGC5byA5aeL5ri45oiP55qE5raI5oGvXG4gICAgICAgIFdlYlNvY2tldE1hbmFnZXIuR2V0SW5zdGFuY2UoKS5zZW5kTXNnKG1zZ0lkLCBwYWlyUmVxdWVzdCk7XG4gICAgfVxuXG4gICAgLy/orr7nva7mmK/lpKfljoUg6L+Y5piv5Yy56YWN6aG16Z2iXG4gICAgc2V0SGFsbE9yTWF0Y2goaGFsbE9yTWF0Y2g6IEhhbGxPck1hdGNoKSB7XG4gICAgICAgIGlmICh0aGlzLmhhbGxPck1hdGNoID09PSBoYWxsT3JNYXRjaCkge1xuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5oYWxsT3JNYXRjaCA9IGhhbGxPck1hdGNoXG4gICAgICAgIHRoaXMuaGFsbFBhcmVudE5vZGUuYWN0aXZlID0gZmFsc2VcbiAgICAgICAgdGhpcy5tYXRjaFBhcmVudE5vZGUuYWN0aXZlID0gZmFsc2VcbiAgICAgICAgc3dpdGNoIChoYWxsT3JNYXRjaCkge1xuICAgICAgICAgICAgY2FzZSBIYWxsT3JNYXRjaC5IQUxMX1BBUkVOVDpcbiAgICAgICAgICAgICAgICB0aGlzLmhhbGxQYXJlbnROb2RlLmFjdGl2ZSA9IHRydWVcbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIGNhc2UgSGFsbE9yTWF0Y2guTUFUQ0hfUEFSRU5UOlxuICAgICAgICAgICAgICAgIHRoaXMubWF0Y2hQYXJlbnROb2RlLmFjdGl2ZSA9IHRydWVcbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgfVxuICAgIH1cblxuXG4gICAgTG9naW5TdWNjZXNzKCkge1xuICAgICAgICAvL+eZu+W9leaIkOWKn+WQjiDmiafooYznmoTmk43kvZxcbiAgICAgICAgR2FtZU1nci5Db25zb2xlLkxvZyhcIueZu+W9leaIkOWKn1wiKVxuICAgICAgICBsZXQgbG9naW5EYXRhID0gR2xvYmFsQmVhbi5HZXRJbnN0YW5jZSgpLmxvZ2luRGF0YVxuICAgICAgICBpZiAobG9naW5EYXRhKSB7XG4gICAgICAgICAgICBpZiAobG9naW5EYXRhLnJvb21JZCA+IDApIHsgLy/mraPlnKjmuLjmiI/kuK3vvIzpnIDopoHmlq3nur/ph43ov55cbiAgICAgICAgICAgICAgICAvLyDlhYjlj5HpgIFFbnRlclJvb23mtojmga/vvIzmnI3liqHnq6/kvJrov5Tlm55pc09ubGluZU1vZGXlrZfmrrVcbiAgICAgICAgICAgICAgICBXZWJTb2NrZXRNYW5hZ2VyLkdldEluc3RhbmNlKCkuc2VuZE1zZyhNZXNzYWdlSWQuTXNnVHlwZUVudGVyUm9vbSwge30pLy/ph43ov57ov5vmnaXnmoQg546p5a626K+35rGC6L+b5YWl5oi/6Ze0XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAobG9naW5EYXRhLmludml0ZUNvZGUgPiAwKSB7Ly/mraPlnKjnp4HkurrmiL/pl7RcbiAgICAgICAgICAgICAgICBXZWJTb2NrZXRNYW5hZ2VyLkdldEluc3RhbmNlKCkuc2VuZE1zZyhNZXNzYWdlSWQuTXNnVHlwZUFjY2VwdEludml0ZSwgeyAnaW52aXRlQ29kZSc6IE51bWJlcihsb2dpbkRhdGEuaW52aXRlQ29kZSkgfSlcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgLy/miL/pl7Tlt7Lnu4/op6PmlaPkuoYgIOS9huaYr+aIkei/mOeVmeWcqOengeS6uuaIv+mXtFxuICAgICAgICAgICAgICAgIHRoaXMuaGFsbFBhcmVudENvbnRyb2xsZXIuZXhpdFRoZVJvb20oKVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy/ph43ov57nmoTml7blgJkg6KKr6YGX55WZ5Zyo5Yy56YWN6aG16Z2i55qE6K+dIOWwseWbnuWIsOWkp+WOhVxuICAgICAgICAgICAgaWYgKGxvZ2luRGF0YS5yb29tSWQgPT0gMCAmJiBsb2dpbkRhdGEuaW52aXRlQ29kZSA9PSAwICYmIHRoaXMuaGFsbE9yTWF0Y2ggPT0gSGFsbE9yTWF0Y2guTUFUQ0hfUEFSRU5UKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5zZXRIYWxsT3JNYXRjaChIYWxsT3JNYXRjaC5IQUxMX1BBUkVOVClcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICB0aGlzLnNldEZlZXMoKVxuICAgICAgICAvLyDnmbvlvZXmiJDlip/lkI7or7fmsYLlhbPljaHov5vluqZcbiAgICAgICAgV2ViU29ja2V0TWFuYWdlci5HZXRJbnN0YW5jZSgpLnNlbmRNc2coTWVzc2FnZUlkLk1zZ1R5cGVFeHRlbmRMZXZlbFByb2dyZXNzLCB7fSk7XG4gICAgfVxuXG4gICAgLy/orr7nva7mjqXlj5fpgoDor7fmiJDlip9cbiAgICBzZXRBY2NlcHRJbnZpdGUoYWNjZXB0SW52aXRlOiBBY2NlcHRJbnZpdGUpIHtcbiAgICAgICAgdGhpcy5oYWxsUGFyZW50Q29udHJvbGxlci5zZXRBY2NlcHRJbnZpdGUoYWNjZXB0SW52aXRlKVxuICAgIH1cbiAgICAvL+emu+W8gOaIv+mXtFxuICAgIGxlYXZlUm9vbShub3RpY2VMZWF2ZUludml0ZTogTm90aWNlTGVhdmVJbnZpdGUpIHtcbiAgICAgICAgdGhpcy5oYWxsUGFyZW50Q29udHJvbGxlci5sZWF2ZVJvb20obm90aWNlTGVhdmVJbnZpdGUpXG4gICAgfVxuXG5cbiAgICAvL+iuvue9rumXqOelqFxuICAgIHNldEZlZXMoKSB7XG4gICAgICAgIHRoaXMuaGFsbFBhcmVudENvbnRyb2xsZXIuc2V0RmVlcygpXG4gICAgfVxuXG4gICAgLy/liJ3lp4vljJYgbWF0Y2gg6aG16Z2iXG4gICAgY3JlYXRlTWF0Y2hWaWV3KCkge1xuICAgICAgICB0aGlzLm1hdGNoUGFyZW50Q29udHJvbGxlci5jcmVhdGVNYXRjaFZpZXcoKVxuICAgIH1cbiAgICAvL+iuvue9ruWMuemFjeaVsOaNrlxuICAgIHNldEdhbWVEYXRhKCkge1xuICAgICAgICB0aGlzLm1hdGNoUGFyZW50Q29udHJvbGxlci5zZXRHYW1lRGF0YSgpXG4gICAgfVxuXG4gICAgLy/ov5vlhaXnp4HkurrmiL/pl7RcbiAgICBqb2luQ3JlYXRlUm9vbSgpe1xuICAgICAgICB0aGlzLmhhbGxQYXJlbnRDb250cm9sbGVyLmpvaW5DcmVhdGVSb29tKClcbiAgICB9XG4gICAgLy/miL/pl7Tlj7fml6DmlYhcbiAgICBqb2luRXJyb3IoKSB7XG4gICAgICAgIHRoaXMuaGFsbFBhcmVudENvbnRyb2xsZXIuam9pbkVycm9yKClcbiAgICB9XG4gICAgLy/lh4blpIcg5Y+W5raI5YeG5aSHXG4gICAgc2V0UmVhZHlTdGF0ZShub3RpY2VVc2VySW52aXRlU3RhdHVzOiBOb3RpY2VVc2VySW52aXRlU3RhdHVzKSB7XG4gICAgICAgIGlmICh0aGlzLmhhbGxQYXJlbnRDb250cm9sbGVyKSB7XG4gICAgICAgICAgICB0aGlzLmhhbGxQYXJlbnRDb250cm9sbGVyLnNldFJlYWR5U3RhdGUobm90aWNlVXNlckludml0ZVN0YXR1cylcbiAgICAgICAgfVxuXG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog6K6+572u5YWz5Y2h6L+b5bqm77yI5LuO5ZCO56uv6I635Y+W5pWw5o2u5ZCO6LCD55So77yJXG4gICAgICogQHBhcmFtIGxldmVsUHJvZ3Jlc3NEYXRhIOWFs+WNoei/m+W6puaVsOaNru+8jOWMheWQq2NsZWFyZWRMZXZlbHMsIGN1cnJlbnRMZXZlbCwgdG90YWxMZXZlbHNcbiAgICAgKi9cbiAgICBzZXRMZXZlbFByb2dyZXNzKGxldmVsUHJvZ3Jlc3NEYXRhOiBhbnkpIHtcbiAgICAgICAgaWYgKHRoaXMubGV2ZWxTZWxlY3RQYWdlQ29udHJvbGxlcikge1xuICAgICAgICAgICAgdGhpcy5sZXZlbFNlbGVjdFBhZ2VDb250cm9sbGVyLnNldExldmVsUHJvZ3Jlc3MobGV2ZWxQcm9ncmVzc0RhdGEpO1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgLy8gdXBkYXRlIChkdCkge31cbn1cbiJdfQ==