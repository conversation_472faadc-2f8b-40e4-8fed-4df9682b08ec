
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/level/LevelPageController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'c8e13uMxpxGELez69T2SfJx', 'LevelPageController');
// scripts/level/LevelPageController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var LeaveDialogController_1 = require("../hall/LeaveDialogController");
var Tools_1 = require("../util/Tools");
var Config_1 = require("../util/Config");
var GlobalManagerController_1 = require("../GlobalManagerController");
var SingleChessBoardController_1 = require("../game/Chess/SingleChessBoardController");
var HexSingleChessBoardController_1 = require("../game/Chess/HexSingleChessBoardController");
var WebSocketManager_1 = require("../net/WebSocketManager");
var MessageId_1 = require("../net/MessageId");
var GameMgr_1 = require("../common/GameMgr");
var EventCenter_1 = require("../common/EventCenter");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var LevelPageController = /** @class */ (function (_super) {
    __extends(LevelPageController, _super);
    function LevelPageController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        // 返回按钮
        _this.backButton = null;
        // 开始游戏按钮
        _this.startGameButton = null;
        // 地雷数UI标签
        _this.mineCountLabel = null;
        // 当前关卡数UI标签
        _this.currentLevelLabel = null;
        // 退出游戏弹窗
        _this.leaveDialogController = null;
        // level_page节点
        _this.levelPageNode = null;
        // game_map_1节点
        _this.gameMap1Node = null;
        // game_map_2节点
        _this.gameMap2Node = null;
        // 方形地图节点引用
        _this.qipan8x8Node = null; // level_page/game_map_1/chess_bg/qipan8*8
        _this.qipan8x9Node = null; // level_page/game_map_1/chess_bg/qipan8*9
        _this.qipan9x9Node = null; // level_page/game_map_1/chess_bg/qipan9*9
        _this.qipan9x10Node = null; // level_page/game_map_1/chess_bg/qipan9*10
        _this.qipan10x10Node = null; // level_page/game_map_1/chess_bg/qipan10*10
        // 特殊关卡节点引用
        _this.levelS001Node = null; // level_page/game_map_2/game_bg/Level_S001
        _this.levelS002Node = null; // level_page/game_map_2/game_bg/Level_S002
        _this.levelS003Node = null; // level_page/game_map_2/game_bg/Level_S003
        _this.levelS004Node = null; // level_page/game_map_2/game_bg/Level_S004
        _this.levelS005Node = null; // level_page/game_map_2/game_bg/Level_S005
        _this.levelS006Node = null; // level_page/game_map_2/game_bg/Level_S006
        // 单机模式棋盘控制器
        _this.singleChessBoardController = null;
        // 六边形单机模式棋盘控制器
        _this.hexSingleChessBoardController = null;
        // 测试按钮（用于调试显示地雷位置）
        _this.debugShowMinesButton = null;
        // 测试预制体（用于显示地雷位置）
        _this.debugMinePrefab = null;
        // 存储创建的测试预制体节点，用于清理
        _this.debugMineNodes = [];
        // 结算页面相关节点
        _this.levelSettlementNode = null; // level_settlement节点
        _this.boardBgNode = null; // level_settlement/board_bg节点
        _this.loseBgNode = null; // level_settlement/board_bg/lose_bg节点
        _this.winBgNode = null; // level_settlement/board_bg/win_bg节点
        _this.retryButton = null; // 再来一次按钮
        _this.nextLevelButton = null; // 下一关按钮
        _this.exitButton = null; // 退出按钮
        // 当前关卡数据
        _this.currentLevel = 1;
        _this.currentLevelInfo = null;
        _this.currentRoomId = 0; // 当前关卡游戏的房间ID
        _this.currentSingleChessBoard = null; // 当前激活的单机棋盘
        // 记录最后一次点击是否是炸弹
        _this.lastClickWasBomb = false;
        // 性能优化相关
        _this.lastShownMapNode = null; // 记录上次显示的地图节点
        _this.isUpdating = false; // 防止重复更新
        return _this;
    }
    LevelPageController.prototype.onLoad = function () {
        var _this = this;
        // 设置返回按钮点击事件 - 使用与GamePageController相同的样式
        if (this.backButton) {
            Tools_1.Tools.imageButtonClick(this.backButton, Config_1.Config.buttonRes + 'side_btn_back_normal', Config_1.Config.buttonRes + 'side_btn_back_pressed', function () {
                _this.onBackButtonClick();
            });
        }
        // 设置开始游戏按钮点击事件
        if (this.startGameButton) {
            this.startGameButton.node.on('click', this.onStartGameButtonClick, this);
        }
        // 注册单机模式消息监听
        this.registerSingleModeMessageHandlers();
        // 设置结算页面按钮事件
        this.setupSettlementButtons();
        // 注意：不在onLoad中初始化关卡数UI，等待外部调用setCurrentLevel时再更新
    };
    LevelPageController.prototype.start = function () {
        // 初始化时隐藏所有地图节点
        this.hideAllMapNodes();
        // 设置测试按钮点击事件
        this.setupDebugButton();
    };
    /**
     * 返回按钮点击事件
     */
    LevelPageController.prototype.onBackButtonClick = function () {
        // 如果没有有效的房间ID，说明还没有进入游戏，直接返回关卡选择页面
        if (this.currentRoomId <= 0) {
            this.returnToLevelSelect();
            return;
        }
        // 弹出确认退出对话框，type=1表示退出本局游戏，传递当前房间ID
        if (this.leaveDialogController) {
            this.leaveDialogController.show(1, function () {
            }, this.currentRoomId);
        }
        else {
            cc.warn("LeaveDialogController 未配置");
        }
    };
    /**
     * 返回到关卡选择页面
     */
    LevelPageController.prototype.returnToLevelSelect = function () {
        // 查找GlobalManagerController并切换到大厅页面
        var globalManagerNode = cc.find("Canvas/global_node") || cc.find("global_node");
        if (globalManagerNode) {
            var globalManager = globalManagerNode.getComponent(GlobalManagerController_1.default);
            if (globalManager) {
                globalManager.setCurrentPage(GlobalManagerController_1.PageType.HALL_PAGE);
            }
        }
    };
    /**
     * 禁用返回按钮
     */
    LevelPageController.prototype.disableBackButton = function () {
        if (this.backButton) {
            // 移除所有触摸事件监听器
            this.backButton.off(cc.Node.EventType.TOUCH_START);
            this.backButton.off(cc.Node.EventType.TOUCH_END);
            this.backButton.off(cc.Node.EventType.TOUCH_CANCEL);
            // 设置按钮为半透明状态，表示禁用
            this.backButton.opacity = 128;
        }
    };
    /**
     * 启用返回按钮
     */
    LevelPageController.prototype.enableBackButton = function () {
        var _this = this;
        if (this.backButton) {
            // 恢复按钮透明度
            this.backButton.opacity = 255;
            // 重新设置返回按钮点击事件
            Tools_1.Tools.imageButtonClick(this.backButton, Config_1.Config.buttonRes + 'side_btn_back_normal', Config_1.Config.buttonRes + 'side_btn_back_pressed', function () {
                _this.onBackButtonClick();
            });
        }
    };
    /**
     * 开始游戏按钮点击事件
     */
    LevelPageController.prototype.onStartGameButtonClick = function () {
        // ExtendLevelInfo消息现在由LevelSelectPageController发送
        // 这里直接进入游戏，等待后端响应
    };
    /**
     * 处理ExtendLevelInfo响应
     * @param levelInfo 关卡信息响应数据
     */
    LevelPageController.prototype.onExtendLevelInfo = function (levelInfo) {
        this.currentLevelInfo = levelInfo;
        // 保存房间ID，用于退出时使用
        if (levelInfo.roomId) {
            this.currentRoomId = levelInfo.roomId;
        }
        // 重置关卡状态（包括清除测试预制体）
        this.resetLevelState();
        // 重置炸弹点击标记
        this.lastClickWasBomb = false;
        // 开始新游戏时，先重置当前棋盘（清理上一局的痕迹）
        if (this.currentSingleChessBoard) {
            this.currentSingleChessBoard.resetBoard();
        }
        // 更新地雷数UI
        this.updateMineCountUI(levelInfo.mineCount);
        // 使用当前设置的关卡编号，而不是后端返回的levelId
        // 因为后端的levelId可能与前端的关卡编号不一致
        this.enterLevel(this.currentLevel);
        // 通知当前激活的单机棋盘控制器处理ExtendLevelInfo
        if (this.currentSingleChessBoard) {
            this.currentSingleChessBoard.onExtendLevelInfo();
        }
    };
    /**
     * 更新地雷数UI
     * @param mineCount 地雷数量
     */
    LevelPageController.prototype.updateMineCountUI = function (mineCount) {
        if (this.mineCountLabel) {
            this.mineCountLabel.string = mineCount.toString();
        }
    };
    /**
     * 更新当前关卡数UI
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.updateCurrentLevelUI = function (levelNumber) {
        if (this.currentLevelLabel) {
            this.currentLevelLabel.string = "\u7B2C" + levelNumber + "\u5173";
        }
    };
    /**
     * 根据关卡数进入相应的关卡（优化版本）
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.enterLevel = function (levelNumber) {
        // 防止重复更新
        if (this.isUpdating) {
            return;
        }
        this.isUpdating = true;
        // 更新关卡数UI显示
        this.updateCurrentLevelUI(levelNumber);
        // 获取目标地图节点和容器
        var targetMapInfo = this.getMapNodeByLevel(levelNumber);
        if (!targetMapInfo) {
            cc.warn("\u672A\u77E5\u7684\u5173\u5361\u7F16\u53F7: " + levelNumber);
            this.isUpdating = false;
            return;
        }
        // 只有当目标节点与当前显示的节点不同时才进行切换
        if (this.lastShownMapNode !== targetMapInfo.mapNode) {
            // 隐藏上一个显示的地图节点
            if (this.lastShownMapNode) {
                this.lastShownMapNode.active = false;
            }
            // 显示目标容器和地图节点
            this.showMapContainer(targetMapInfo.containerType);
            this.showMapNodeOptimized(targetMapInfo.mapNode, targetMapInfo.mapName);
            // 记录当前显示的节点
            this.lastShownMapNode = targetMapInfo.mapNode;
        }
        // 设置当前激活的单机棋盘控制器
        if (this.isHexLevel(levelNumber)) {
            // 六边形关卡使用六边形控制器
            if (this.hexSingleChessBoardController) {
                var hexBoardType = this.getHexBoardTypeByLevel(levelNumber);
                this.hexSingleChessBoardController.initBoard(hexBoardType);
                this.currentSingleChessBoard = this.hexSingleChessBoardController;
            }
            else {
                console.error("❌ 六边形单机控制器未配置！");
                this.currentSingleChessBoard = null;
            }
        }
        else {
            // 四边形关卡使用四边形控制器
            if (this.singleChessBoardController) {
                var boardType = this.getBoardTypeByLevel(levelNumber);
                this.singleChessBoardController.initBoard(boardType);
                this.currentSingleChessBoard = this.singleChessBoardController;
            }
            else {
                console.error("❌ 四边形单机控制器未配置！");
                this.currentSingleChessBoard = null;
            }
        }
        this.isUpdating = false;
    };
    /**
     * 根据关卡数获取对应的地图节点信息
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.getMapNodeByLevel = function (levelNumber) {
        if (levelNumber >= 1 && levelNumber <= 4) {
            return { mapNode: this.qipan8x8Node, mapName: "qipan8*8", containerType: 'map1' };
        }
        else if (levelNumber === 5) {
            return { mapNode: this.levelS001Node, mapName: "Level_S001", containerType: 'map2' };
        }
        else if (levelNumber >= 6 && levelNumber <= 9) {
            return { mapNode: this.qipan8x9Node, mapName: "qipan8*9", containerType: 'map1' };
        }
        else if (levelNumber === 10) {
            return { mapNode: this.levelS002Node, mapName: "Level_S002", containerType: 'map2' };
        }
        else if (levelNumber >= 11 && levelNumber <= 14) {
            return { mapNode: this.qipan9x9Node, mapName: "qipan9*9", containerType: 'map1' };
        }
        else if (levelNumber === 15) {
            return { mapNode: this.levelS003Node, mapName: "Level_S003", containerType: 'map2' };
        }
        else if (levelNumber >= 16 && levelNumber <= 19) {
            return { mapNode: this.qipan9x10Node, mapName: "qipan9*10", containerType: 'map1' };
        }
        else if (levelNumber === 20) {
            return { mapNode: this.levelS004Node, mapName: "Level_S004", containerType: 'map2' };
        }
        else if (levelNumber >= 21 && levelNumber <= 24) {
            return { mapNode: this.qipan10x10Node, mapName: "qipan10*10", containerType: 'map1' };
        }
        else if (levelNumber === 25) {
            return { mapNode: this.levelS005Node, mapName: "Level_S005", containerType: 'map2' };
        }
        else if (levelNumber >= 26 && levelNumber <= 29) {
            return { mapNode: this.qipan10x10Node, mapName: "qipan10*10", containerType: 'map1' };
        }
        else if (levelNumber === 30) {
            return { mapNode: this.levelS006Node, mapName: "Level_S006", containerType: 'map2' };
        }
        return null;
    };
    /**
     * 根据关卡编号获取棋盘类型
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.getBoardTypeByLevel = function (levelNumber) {
        if (levelNumber >= 1 && levelNumber <= 4) {
            return "8x8";
        }
        else if (levelNumber >= 6 && levelNumber <= 9) {
            return "8x9";
        }
        else if (levelNumber >= 11 && levelNumber <= 14) {
            return "9x9";
        }
        else if (levelNumber >= 16 && levelNumber <= 19) {
            return "9x10";
        }
        else if (levelNumber >= 21 && levelNumber <= 24 || levelNumber >= 26 && levelNumber <= 29) {
            return "10x10";
        }
        return "8x8"; // 默认
    };
    /**
     * 判断是否为六边形关卡
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.isHexLevel = function (levelNumber) {
        // 特殊关卡使用六边形棋盘
        return levelNumber === 5 || levelNumber === 10 || levelNumber === 15 ||
            levelNumber === 20 || levelNumber === 25 || levelNumber === 30;
    };
    /**
     * 根据关卡编号获取六边形棋盘类型
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.getHexBoardTypeByLevel = function (levelNumber) {
        // 六关对应六个六边形棋盘
        if (levelNumber === 5) {
            return "hexBoard1"; // 第1关六边形
        }
        else if (levelNumber === 10) {
            return "hexBoard2"; // 第2关六边形
        }
        else if (levelNumber === 15) {
            return "hexBoard3"; // 第3关六边形
        }
        else if (levelNumber === 20) {
            return "hexBoard4"; // 第4关六边形
        }
        else if (levelNumber === 25) {
            return "hexBoard5"; // 第5关六边形
        }
        else if (levelNumber === 30) {
            return "hexBoard6"; // 第6关六边形
        }
        return "hexBoard1"; // 默认
    };
    /**
     * 显示指定的地图节点（优化版本）
     * @param mapNode 要显示的地图节点
     * @param mapName 地图名称（用于日志）
     */
    LevelPageController.prototype.showMapNodeOptimized = function (mapNode, mapName) {
        if (mapNode) {
            mapNode.active = true;
        }
        else {
            cc.warn("\u274C \u5730\u56FE\u8282\u70B9\u672A\u627E\u5230: " + mapName);
        }
    };
    /**
     * 显示指定的地图容器
     * @param containerType 容器类型
     */
    LevelPageController.prototype.showMapContainer = function (containerType) {
        // 确保 level_page 节点是激活的
        if (this.levelPageNode) {
            this.levelPageNode.active = true;
        }
        // 根据容器类型显示对应容器，隐藏另一个
        if (containerType === 'map1') {
            if (this.gameMap1Node && !this.gameMap1Node.active) {
                this.gameMap1Node.active = true;
            }
            if (this.gameMap2Node && this.gameMap2Node.active) {
                this.gameMap2Node.active = false;
            }
        }
        else {
            if (this.gameMap2Node && !this.gameMap2Node.active) {
                this.gameMap2Node.active = true;
            }
            if (this.gameMap1Node && this.gameMap1Node.active) {
                this.gameMap1Node.active = false;
            }
        }
    };
    /**
     * 隐藏所有地图节点
     */
    LevelPageController.prototype.hideAllMapNodes = function () {
        var allMapNodes = [
            this.qipan8x8Node,
            this.qipan8x9Node,
            this.qipan9x9Node,
            this.qipan9x10Node,
            this.qipan10x10Node,
            this.levelS001Node,
            this.levelS002Node,
            this.levelS003Node,
            this.levelS004Node,
            this.levelS005Node,
            this.levelS006Node
        ];
        allMapNodes.forEach(function (node) {
            if (node) {
                node.active = false;
            }
        });
    };
    /**
     * 设置当前关卡（从外部调用）
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.setCurrentLevel = function (levelNumber) {
        // 重置关卡状态（包括清除测试预制体）
        this.resetLevelState();
        this.currentLevel = levelNumber;
        // 立即根据关卡数切换地图显示
        this.enterLevel(levelNumber);
    };
    /**
     * 获取当前关卡编号
     */
    LevelPageController.prototype.getCurrentLevel = function () {
        return this.currentLevel;
    };
    /**
     * 获取当前关卡信息
     */
    LevelPageController.prototype.getCurrentLevelInfo = function () {
        return this.currentLevelInfo;
    };
    /**
     * 获取当前房间ID
     */
    LevelPageController.prototype.getCurrentRoomId = function () {
        return this.currentRoomId;
    };
    /**
     * 隐藏所有地图容器
     */
    LevelPageController.prototype.hideAllMapContainers = function () {
        // 确保 level_page 节点是激活的
        if (this.levelPageNode) {
            this.levelPageNode.active = true;
        }
        // 隐藏两个主要的地图容器
        if (this.gameMap1Node) {
            this.gameMap1Node.active = false;
        }
        if (this.gameMap2Node) {
            this.gameMap2Node.active = false;
        }
        // 同时隐藏所有具体的地图节点
        this.hideAllMapNodes();
    };
    /**
     * 显示 game_map_1 容器（方形地图）
     */
    LevelPageController.prototype.showGameMap1 = function () {
        if (this.gameMap1Node) {
            this.gameMap1Node.active = true;
        }
        else {
            cc.warn("❌ game_map_1 节点未找到");
        }
    };
    /**
     * 显示 game_map_2 容器（特殊关卡）
     */
    LevelPageController.prototype.showGameMap2 = function () {
        if (this.gameMap2Node) {
            this.gameMap2Node.active = true;
        }
        else {
            cc.warn("❌ game_map_2 节点未找到");
        }
    };
    /**
     * 获取当前激活的单机棋盘控制器
     */
    LevelPageController.prototype.getCurrentSingleChessBoard = function () {
        return this.currentSingleChessBoard;
    };
    /**
     * 处理单机模式的点击响应
     * @param response 点击响应数据
     */
    LevelPageController.prototype.handleSingleModeClickResponse = function (response) {
        if (this.currentSingleChessBoard) {
            var x = response.x, y = response.y, result = response.result, chainReaction = response.chainReaction;
            // 处理点击结果
            if (x !== undefined && y !== undefined && result !== undefined) {
                this.currentSingleChessBoard.handleClickResponse(x, y, result);
            }
            // 处理连锁反应
            if (chainReaction && Array.isArray(chainReaction)) {
                this.currentSingleChessBoard.handleChainReaction(chainReaction);
            }
        }
    };
    /**
     * 处理单机模式游戏结束
     * @param gameEndData 游戏结束数据
     */
    LevelPageController.prototype.handleSingleModeGameEnd = function (gameEndData) {
        if (this.currentSingleChessBoard) {
            // 禁用棋盘触摸
            this.currentSingleChessBoard.disableAllGridTouch();
            // 处理游戏结束
            this.currentSingleChessBoard.onLevelGameEnd();
        }
    };
    /**
     * 重置当前单机棋盘（仅在开始新游戏时调用）
     */
    LevelPageController.prototype.resetCurrentSingleChessBoard = function () {
        if (this.currentSingleChessBoard) {
            // 重置棋盘状态（清理所有预制体和格子状态）
            this.currentSingleChessBoard.resetBoard();
            // 重新启用触摸事件
            this.currentSingleChessBoard.enableAllGridTouch();
        }
    };
    /**
     * 注册单机模式消息处理器
     */
    LevelPageController.prototype.registerSingleModeMessageHandlers = function () {
        // 监听WebSocket消息
        GameMgr_1.GameMgr.Event.AddEventListener(EventCenter_1.EventType.ReceiveMessage, this.onReceiveMessage, this);
    };
    /**
     * 取消单机模式消息监听
     */
    LevelPageController.prototype.unregisterSingleModeMessageHandlers = function () {
        GameMgr_1.GameMgr.Event.RemoveEventListener(EventCenter_1.EventType.ReceiveMessage, this.onReceiveMessage, this);
    };
    /**
     * 处理接收到的WebSocket消息
     * @param messageBean 消息数据
     */
    LevelPageController.prototype.onReceiveMessage = function (messageBean) {
        switch (messageBean.msgId) {
            case MessageId_1.MessageId.MsgTypeLevelClickBlock:
                this.onLevelClickBlockResponse(messageBean.data);
                break;
            case MessageId_1.MessageId.MsgTypeLevelGameEnd:
                this.onLevelGameEnd(messageBean.data);
                break;
        }
    };
    /**
     * 处理LevelClickBlock响应
     * @param response 点击响应数据
     */
    LevelPageController.prototype.onLevelClickBlockResponse = function (response) {
        if (this.currentSingleChessBoard) {
            // 解构响应数据，支持多种可能的字段名
            var x = response.x, y = response.y, q = response.q, r = response.r, result = response.result, action = response.action, chainReaction = response.chainReaction, revealedGrids = response.revealedGrids, floodFill = response.floodFill, revealedBlocks = response.revealedBlocks, floodFillResults = response.floodFillResults // 单机模式使用这个字段
            ;
            // 判断当前使用的是哪种控制器
            var isUsingHexController = (this.currentSingleChessBoard === this.hexSingleChessBoardController);
            // 根据控制器类型决定坐标处理方式
            var coordX = void 0, coordY = void 0, coordQ = void 0, coordR = void 0;
            var hasValidCoord = false;
            if (isUsingHexController) {
                // 六边形控制器：优先使用六边形坐标，如果没有则将x,y映射为q,r
                if (q !== undefined && r !== undefined) {
                    coordQ = q;
                    coordR = r;
                    hasValidCoord = true;
                }
                else if (x !== undefined && y !== undefined) {
                    // 服务器返回x,y字段，但实际是六边形坐标：x=q, y=r
                    coordQ = x; // x 就是 q
                    coordR = y; // y 就是 r
                    hasValidCoord = true;
                }
            }
            else {
                // 四边形控制器：使用四边形坐标
                if (x !== undefined && y !== undefined) {
                    coordX = x;
                    coordY = y;
                    hasValidCoord = true;
                }
                else if (q !== undefined && r !== undefined) {
                    console.warn("\u26A0\uFE0F \u56DB\u8FB9\u5F62\u63A7\u5236\u5668\u6536\u5230\u516D\u8FB9\u5F62\u5750\u6807 (" + q + ", " + r + ")\uFF0C\u8FD9\u53EF\u80FD\u4E0D\u6B63\u786E");
                    hasValidCoord = false;
                }
            }
            if (hasValidCoord && result !== undefined) {
                if (action === 2) {
                    // 标记/取消标记操作，不调用handleClickResponse，避免格子消失
                    // 不调用 handleClickResponse，因为标记操作不应该隐藏格子
                }
                else if (action === 1) {
                    // 挖掘操作
                    // 检查是否点到炸弹
                    if (result === "boom" || result === "mine") {
                        this.lastClickWasBomb = true;
                    }
                    // 根据控制器类型调用对应的方法
                    if (isUsingHexController) {
                        // 六边形控制器
                        this.currentSingleChessBoard.handleClickResponse(coordQ, coordR, result);
                    }
                    else {
                        // 四边形控制器
                        this.currentSingleChessBoard.handleClickResponse(coordX, coordY, result);
                    }
                    // 处理连锁展开数据
                    if (floodFillResults && Array.isArray(floodFillResults) && floodFillResults.length > 0) {
                        if (isUsingHexController) {
                            // 六边形控制器使用handleChainReaction方法
                            this.currentSingleChessBoard.handleChainReaction(floodFillResults);
                        }
                        else {
                            // 四边形控制器使用handleFloodFillResults方法
                            this.currentSingleChessBoard.handleFloodFillResults(floodFillResults);
                        }
                    }
                }
                else {
                    // 其他操作，默认按挖掘处理
                    if (isUsingHexController) {
                        this.currentSingleChessBoard.handleClickResponse(coordQ, coordR, result);
                    }
                    else {
                        this.currentSingleChessBoard.handleClickResponse(coordX, coordY, result);
                    }
                }
            }
            else {
                console.warn("⚠️ 响应数据缺少有效坐标或结果信息");
                console.warn("   \u63A7\u5236\u5668\u7C7B\u578B: " + (isUsingHexController ? '六边形' : '四边形'));
                console.warn("   \u5750\u6807\u6570\u636E: x=" + x + ", y=" + y + ", q=" + q + ", r=" + r);
                console.warn("   \u7ED3\u679C: " + result);
            }
        }
    };
    /**
     * 处理LevelGameEnd通知
     * @param gameEndData 游戏结束数据
     */
    LevelPageController.prototype.onLevelGameEnd = function (gameEndData) {
        var _this = this;
        if (this.currentSingleChessBoard) {
            // 禁用棋盘触摸
            this.currentSingleChessBoard.disableAllGridTouch();
            // 处理游戏结束（不清理数据）
            this.currentSingleChessBoard.onLevelGameEnd();
        }
        // 禁用返回按钮，防止游戏结束时玩家误点击
        this.disableBackButton();
        // 检查是否点到了炸弹，如果是游戏失败且点到炸弹，则延迟显示结算页面
        var isGameFailed = !gameEndData.success;
        var hasBombExploded = this.lastClickWasBomb ||
            (this.currentSingleChessBoard && this.currentSingleChessBoard.hasBombExplodedInThisGame());
        if (isGameFailed && hasBombExploded) {
            // 点到炸弹导致的游戏失败，延迟1.5秒显示结算页面
            this.scheduleOnce(function () {
                _this.showLevelSettlement(gameEndData);
                // 重置标记
                _this.lastClickWasBomb = false;
            }, 1.5);
        }
        else {
            // 其他情况，立即显示结算页面
            this.showLevelSettlement(gameEndData);
            // 重置标记
            this.lastClickWasBomb = false;
        }
    };
    /**
     * 设置结算页面按钮事件
     */
    LevelPageController.prototype.setupSettlementButtons = function () {
        var _this = this;
        // 再来一次按钮
        if (this.retryButton) {
            this.retryButton.node.on('click', this.onRetryButtonClick, this);
        }
        // 下一关按钮
        if (this.nextLevelButton) {
            this.nextLevelButton.node.on('click', this.onNextLevelButtonClick, this);
        }
        // 退出按钮 - 使用按压效果，模仿其他返回按钮
        if (this.exitButton) {
            Tools_1.Tools.imageButtonClick(this.exitButton.node, Config_1.Config.buttonRes + 'board_btn_back_normal', Config_1.Config.buttonRes + 'board_btn_back_pressed', function () {
                _this.onExitButtonClick();
            });
        }
    };
    /**
     * 显示结算页面
     * @param gameEndData 游戏结束数据
     */
    LevelPageController.prototype.showLevelSettlement = function (gameEndData) {
        if (!this.levelSettlementNode) {
            console.error("levelSettlementNode 未配置");
            return;
        }
        // 显示结算页面
        this.levelSettlementNode.active = true;
        // 根据游戏结果显示对应的背景 - 修复成功判断逻辑
        var isSuccess = gameEndData.isSuccess || gameEndData.success || gameEndData.isWin || gameEndData.gameStatus === 1;
        if (isSuccess) {
            // 成功 - 显示胜利背景
            if (this.winBgNode) {
                this.winBgNode.active = true;
            }
            if (this.loseBgNode) {
                this.loseBgNode.active = false;
            }
        }
        else {
            // 失败 - 显示失败背景
            if (this.loseBgNode) {
                this.loseBgNode.active = true;
            }
            if (this.winBgNode) {
                this.winBgNode.active = false;
            }
        }
    };
    /**
     * 再来一次按钮点击事件
     */
    LevelPageController.prototype.onRetryButtonClick = function () {
        // 关闭结算页面
        this.hideLevelSettlement();
        // 重新启用返回按钮
        this.enableBackButton();
        // 注意：不在这里重置棋盘，等收到ExtendLevelInfo响应时再重置
        // 这样可以避免过早清理，让玩家在点击按钮后还能短暂看到游玩痕迹
        // 发送当前关卡的ExtendLevelInfo
        this.sendExtendLevelInfo(this.currentLevel);
    };
    /**
     * 下一关按钮点击事件
     */
    LevelPageController.prototype.onNextLevelButtonClick = function () {
        // 关闭结算页面
        this.hideLevelSettlement();
        // 重新启用返回按钮
        this.enableBackButton();
        // 进入下一关
        var nextLevel = this.currentLevel + 1;
        this.setCurrentLevel(nextLevel);
        // 注意：不在这里重置棋盘，等收到ExtendLevelInfo响应时再重置
        // 这样可以避免过早清理，让玩家在点击按钮后还能短暂看到游玩痕迹
        // 发送下一关的ExtendLevelInfo
        this.sendExtendLevelInfo(nextLevel);
    };
    /**
     * 退出按钮点击事件
     */
    LevelPageController.prototype.onExitButtonClick = function () {
        // 关闭结算页面
        this.hideLevelSettlement();
        // 重新启用返回按钮（虽然要退出了，但保持一致性）
        this.enableBackButton();
        // 返回到关卡选择页面（匹配界面）
        this.returnToLevelSelect();
    };
    /**
     * 隐藏结算页面
     */
    LevelPageController.prototype.hideLevelSettlement = function () {
        if (this.levelSettlementNode) {
            this.levelSettlementNode.active = false;
        }
    };
    /**
     * 发送ExtendLevelInfo消息
     * @param levelId 关卡ID
     */
    LevelPageController.prototype.sendExtendLevelInfo = function (levelId) {
        var request = {
            levelId: levelId
        };
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeExtendLevelInfo, request);
    };
    /**
     * 设置测试按钮
     */
    LevelPageController.prototype.setupDebugButton = function () {
        if (this.debugShowMinesButton) {
            this.debugShowMinesButton.node.on('click', this.onDebugShowMinesClick, this);
        }
    };
    /**
     * 测试按钮点击事件 - 发送DebugShowMines消息
     */
    LevelPageController.prototype.onDebugShowMinesClick = function () {
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeDebugShowMines, {});
    };
    /**
     * 判断是否在单机模式
     */
    LevelPageController.prototype.isInSingleMode = function () {
        // 单机模式的判断逻辑：当前页面是关卡页面且有有效的房间ID
        return this.currentRoomId > 0;
    };
    /**
     * 处理DebugShowMines响应，在炸弹位置生成测试预制体
     * @param minePositions 炸弹位置数组 [{x: number, y: number}]
     */
    LevelPageController.prototype.handleDebugShowMines = function (minePositions) {
        var _this = this;
        if (!this.debugMinePrefab) {
            cc.warn("debugMinePrefab 预制体未设置，无法显示测试标记");
            return;
        }
        if (!this.currentSingleChessBoard) {
            cc.warn("当前没有激活的单机棋盘");
            return;
        }
        if (!minePositions || !Array.isArray(minePositions) || minePositions.length === 0) {
            cc.warn("地雷位置数据无效:", minePositions);
            return;
        }
        // 先尝试直接创建一个测试预制体，不使用延迟
        if (minePositions.length > 0) {
            var firstPosition = minePositions[0];
            // 检查坐标字段
            var coordX = void 0, coordY = void 0;
            var pos = firstPosition; // 使用any类型避免TypeScript报错
            if (pos.x !== undefined && pos.y !== undefined) {
                coordX = pos.x;
                coordY = pos.y;
            }
            else if (pos.q !== undefined && pos.r !== undefined) {
                coordX = pos.q;
                coordY = pos.r;
            }
            else {
                console.error("\u274C \u65E0\u6CD5\u8BC6\u522B\u5750\u6807\u5B57\u6BB5:", firstPosition);
                return;
            }
            // 直接调用，不使用延迟
            this.createDebugMinePrefab(coordX, coordY);
        }
        // 在每个炸弹位置生成测试预制体
        minePositions.forEach(function (position, index) {
            var pos = position; // 使用any类型避免TypeScript报错
            // 获取坐标
            var coordX, coordY;
            if (pos.x !== undefined && pos.y !== undefined) {
                coordX = pos.x;
                coordY = pos.y;
            }
            else if (pos.q !== undefined && pos.r !== undefined) {
                coordX = pos.q;
                coordY = pos.r;
            }
            else {
                console.error("\u274C \u5730\u96F7\u4F4D\u7F6E" + index + "\u5750\u6807\u5B57\u6BB5\u65E0\u6548:", position);
                return;
            }
            if (index === 0) {
                // 第一个不延迟，立即执行（已经在上面处理过了，跳过）
                return;
            }
            else {
                // 其他的使用延迟 - 修复闭包问题
                var capturedX_1 = coordX; // 捕获当前值
                var capturedY_1 = coordY; // 捕获当前值
                _this.scheduleOnce(function () {
                    _this.createDebugMinePrefab(capturedX_1, capturedY_1);
                }, index * 0.1);
            }
        });
    };
    /**
     * 在指定位置创建测试预制体
     * @param x 格子x坐标（四边形）或q坐标（六边形）
     * @param y 格子y坐标（四边形）或r坐标（六边形）
     */
    LevelPageController.prototype.createDebugMinePrefab = function (x, y) {
        // 检查坐标是否有效
        if (x === undefined || y === undefined || x === null || y === null) {
            console.error("\u274C \u65E0\u6548\u7684\u5750\u6807\u53C2\u6570: x=" + x + ", y=" + y);
            console.error("   x\u7C7B\u578B: " + typeof x + ", y\u7C7B\u578B: " + typeof y);
            console.error("   \u8C03\u7528\u5806\u6808:", new Error().stack);
            return;
        }
        if (!this.debugMinePrefab) {
            cc.error("debugMinePrefab 为空，无法创建测试预制体");
            return;
        }
        if (!this.currentSingleChessBoard) {
            cc.error("currentSingleChessBoard 为空，无法创建测试预制体");
            return;
        }
        try {
            // 判断当前使用的是哪种控制器
            var isUsingHexController = (this.currentSingleChessBoard === this.hexSingleChessBoardController);
            var debugNode = null;
            if (isUsingHexController) {
                // 六边形控制器：x实际是q，y实际是r
                debugNode = this.currentSingleChessBoard.createCustomPrefab(x, y, // 对于六边形，x就是q，y就是r
                this.debugMinePrefab, "DebugMine_" + x + "_" + y);
            }
            else {
                // 四边形控制器
                debugNode = this.currentSingleChessBoard.createCustomPrefab(x, y, this.debugMinePrefab, "DebugMine_" + x + "_" + y);
            }
            if (debugNode) {
                // 将创建的节点存储起来，用于后续清理
                this.debugMineNodes.push(debugNode);
            }
            else {
                cc.error("\u274C \u5728\u4F4D\u7F6E (" + x + ", " + y + ") \u521B\u5EFA\u6D4B\u8BD5\u9884\u5236\u4F53\u5931\u8D25\uFF0C\u8FD4\u56DE\u503C\u4E3A\u7A7A");
            }
        }
        catch (error) {
            cc.error("\u274C \u521B\u5EFA\u6D4B\u8BD5\u9884\u5236\u4F53\u65F6\u53D1\u751F\u9519\u8BEF:", error);
        }
    };
    /**
     * 清除所有测试预制体
     */
    LevelPageController.prototype.clearDebugMines = function () {
        this.debugMineNodes.forEach(function (node, index) {
            if (node && cc.isValid(node)) {
                node.destroy();
            }
        });
        // 清空数组
        this.debugMineNodes = [];
    };
    /**
     * 重置关卡状态（包括清除测试预制体）
     */
    LevelPageController.prototype.resetLevelState = function () {
        this.clearDebugMines();
        // 这里可以添加其他需要重置的状态
    };
    LevelPageController.prototype.onDestroy = function () {
        // 取消消息监听
        this.unregisterSingleModeMessageHandlers();
        // 清理测试预制体
        this.clearDebugMines();
        // 清理按钮事件
        if (this.retryButton) {
            this.retryButton.node.off('click', this.onRetryButtonClick, this);
        }
        if (this.nextLevelButton) {
            this.nextLevelButton.node.off('click', this.onNextLevelButtonClick, this);
        }
        // 退出按钮使用 Tools.imageButtonClick，会自动管理事件，无需手动清理
        if (this.debugShowMinesButton) {
            this.debugShowMinesButton.node.off('click', this.onDebugShowMinesClick, this);
        }
    };
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "backButton", void 0);
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "startGameButton", void 0);
    __decorate([
        property(cc.Label)
    ], LevelPageController.prototype, "mineCountLabel", void 0);
    __decorate([
        property(cc.Label)
    ], LevelPageController.prototype, "currentLevelLabel", void 0);
    __decorate([
        property(LeaveDialogController_1.default)
    ], LevelPageController.prototype, "leaveDialogController", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelPageNode", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "gameMap1Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "gameMap2Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan8x8Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan8x9Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan9x9Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan9x10Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan10x10Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS001Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS002Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS003Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS004Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS005Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS006Node", void 0);
    __decorate([
        property(SingleChessBoardController_1.default)
    ], LevelPageController.prototype, "singleChessBoardController", void 0);
    __decorate([
        property(HexSingleChessBoardController_1.default)
    ], LevelPageController.prototype, "hexSingleChessBoardController", void 0);
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "debugShowMinesButton", void 0);
    __decorate([
        property(cc.Prefab)
    ], LevelPageController.prototype, "debugMinePrefab", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelSettlementNode", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "boardBgNode", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "loseBgNode", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "winBgNode", void 0);
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "retryButton", void 0);
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "nextLevelButton", void 0);
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "exitButton", void 0);
    LevelPageController = __decorate([
        ccclass
    ], LevelPageController);
    return LevelPageController;
}(cc.Component));
exports.default = LevelPageController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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