
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/level/LevelPageController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'c8e13uMxpxGELez69T2SfJx', 'LevelPageController');
// scripts/level/LevelPageController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var LeaveDialogController_1 = require("../hall/LeaveDialogController");
var Tools_1 = require("../util/Tools");
var Config_1 = require("../util/Config");
var GlobalManagerController_1 = require("../GlobalManagerController");
var SingleChessBoardController_1 = require("../game/Chess/SingleChessBoardController");
var HexSingleChessBoardController_1 = require("../game/Chess/HexSingleChessBoardController");
var WebSocketManager_1 = require("../net/WebSocketManager");
var MessageId_1 = require("../net/MessageId");
var GameMgr_1 = require("../common/GameMgr");
var EventCenter_1 = require("../common/EventCenter");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var LevelPageController = /** @class */ (function (_super) {
    __extends(LevelPageController, _super);
    function LevelPageController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        // 返回按钮
        _this.backButton = null;
        // 开始游戏按钮
        _this.startGameButton = null;
        // 地雷数UI标签
        _this.mineCountLabel = null;
        // 当前关卡数UI标签
        _this.currentLevelLabel = null;
        // 退出游戏弹窗
        _this.leaveDialogController = null;
        // level_page节点
        _this.levelPageNode = null;
        // game_map_1节点
        _this.gameMap1Node = null;
        // game_map_2节点
        _this.gameMap2Node = null;
        // 方形地图节点引用
        _this.qipan8x8Node = null; // level_page/game_map_1/chess_bg/qipan8*8
        _this.qipan8x9Node = null; // level_page/game_map_1/chess_bg/qipan8*9
        _this.qipan9x9Node = null; // level_page/game_map_1/chess_bg/qipan9*9
        _this.qipan9x10Node = null; // level_page/game_map_1/chess_bg/qipan9*10
        _this.qipan10x10Node = null; // level_page/game_map_1/chess_bg/qipan10*10
        // 特殊关卡节点引用
        _this.levelS001Node = null; // level_page/game_map_2/game_bg/Level_S001
        _this.levelS002Node = null; // level_page/game_map_2/game_bg/Level_S002
        _this.levelS003Node = null; // level_page/game_map_2/game_bg/Level_S003
        _this.levelS004Node = null; // level_page/game_map_2/game_bg/Level_S004
        _this.levelS005Node = null; // level_page/game_map_2/game_bg/Level_S005
        _this.levelS006Node = null; // level_page/game_map_2/game_bg/Level_S006
        // 单机模式棋盘控制器
        _this.singleChessBoardController = null;
        // 六边形单机模式棋盘控制器
        _this.hexSingleChessBoardController = null;
        // 测试按钮（用于调试显示地雷位置）
        _this.debugShowMinesButton = null;
        // 测试预制体（用于显示地雷位置）
        _this.debugMinePrefab = null;
        // 存储创建的测试预制体节点，用于清理
        _this.debugMineNodes = [];
        // 结算页面相关节点
        _this.levelSettlementNode = null; // level_settlement节点
        _this.boardBgNode = null; // level_settlement/board_bg节点
        _this.loseBgNode = null; // level_settlement/board_bg/lose_bg节点
        _this.winBgNode = null; // level_settlement/board_bg/win_bg节点
        _this.retryButton = null; // 再来一次按钮
        _this.nextLevelButton = null; // 下一关按钮
        _this.exitButton = null; // 退出按钮
        // 当前关卡数据
        _this.currentLevel = 1;
        _this.currentLevelInfo = null;
        _this.currentRoomId = 0; // 当前关卡游戏的房间ID
        _this.currentSingleChessBoard = null; // 当前激活的单机棋盘
        // 记录最后一次点击是否是炸弹
        _this.lastClickWasBomb = false;
        // 性能优化相关
        _this.lastShownMapNode = null; // 记录上次显示的地图节点
        _this.isUpdating = false; // 防止重复更新
        return _this;
    }
    LevelPageController.prototype.onLoad = function () {
        var _this = this;
        // 设置返回按钮点击事件 - 使用与GamePageController相同的样式
        if (this.backButton) {
            Tools_1.Tools.imageButtonClick(this.backButton, Config_1.Config.buttonRes + 'side_btn_back_normal', Config_1.Config.buttonRes + 'side_btn_back_pressed', function () {
                _this.onBackButtonClick();
            });
        }
        // 设置开始游戏按钮点击事件
        if (this.startGameButton) {
            this.startGameButton.node.on('click', this.onStartGameButtonClick, this);
        }
        // 注册单机模式消息监听
        this.registerSingleModeMessageHandlers();
        // 设置结算页面按钮事件
        this.setupSettlementButtons();
        // 注意：不在onLoad中初始化关卡数UI，等待外部调用setCurrentLevel时再更新
    };
    LevelPageController.prototype.start = function () {
        // 初始化时隐藏所有地图节点
        this.hideAllMapNodes();
        // 设置测试按钮点击事件
        this.setupDebugButton();
    };
    /**
     * 返回按钮点击事件
     */
    LevelPageController.prototype.onBackButtonClick = function () {
        // 如果没有有效的房间ID，说明还没有进入游戏，直接返回关卡选择页面
        if (this.currentRoomId <= 0) {
            this.returnToLevelSelect();
            return;
        }
        // 弹出确认退出对话框，type=1表示退出本局游戏，传递当前房间ID
        if (this.leaveDialogController) {
            this.leaveDialogController.show(1, function () {
            }, this.currentRoomId);
        }
        else {
            cc.warn("LeaveDialogController 未配置");
        }
    };
    /**
     * 返回到关卡选择页面
     */
    LevelPageController.prototype.returnToLevelSelect = function () {
        // 查找GlobalManagerController并切换到大厅页面
        var globalManagerNode = cc.find("Canvas/global_node") || cc.find("global_node");
        if (globalManagerNode) {
            var globalManager = globalManagerNode.getComponent(GlobalManagerController_1.default);
            if (globalManager) {
                globalManager.setCurrentPage(GlobalManagerController_1.PageType.HALL_PAGE);
            }
        }
    };
    /**
     * 禁用返回按钮
     */
    LevelPageController.prototype.disableBackButton = function () {
        if (this.backButton) {
            // 移除所有触摸事件监听器
            this.backButton.off(cc.Node.EventType.TOUCH_START);
            this.backButton.off(cc.Node.EventType.TOUCH_END);
            this.backButton.off(cc.Node.EventType.TOUCH_CANCEL);
            // 设置按钮为半透明状态，表示禁用
            this.backButton.opacity = 128;
        }
    };
    /**
     * 启用返回按钮
     */
    LevelPageController.prototype.enableBackButton = function () {
        var _this = this;
        if (this.backButton) {
            // 恢复按钮透明度
            this.backButton.opacity = 255;
            // 重新设置返回按钮点击事件
            Tools_1.Tools.imageButtonClick(this.backButton, Config_1.Config.buttonRes + 'side_btn_back_normal', Config_1.Config.buttonRes + 'side_btn_back_pressed', function () {
                _this.onBackButtonClick();
            });
        }
    };
    /**
     * 开始游戏按钮点击事件
     */
    LevelPageController.prototype.onStartGameButtonClick = function () {
        // ExtendLevelInfo消息现在由LevelSelectPageController发送
        // 这里直接进入游戏，等待后端响应
    };
    /**
     * 处理ExtendLevelInfo响应
     * @param levelInfo 关卡信息响应数据
     */
    LevelPageController.prototype.onExtendLevelInfo = function (levelInfo) {
        var _this = this;
        this.currentLevelInfo = levelInfo;
        // 保存房间ID，用于退出时使用
        if (levelInfo.roomId) {
            this.currentRoomId = levelInfo.roomId;
        }
        // 检查是否为断线重连
        if (levelInfo.reconnected === true) {
            GameMgr_1.GameMgr.Console.Log("关卡模式断线重连，恢复游戏状态");
            GameMgr_1.GameMgr.Console.Log("断线重连数据:", levelInfo);
            // 断线重连时，使用后端返回的levelId设置当前关卡
            if (levelInfo.levelId) {
                this.currentLevel = levelInfo.levelId;
                GameMgr_1.GameMgr.Console.Log("设置当前关卡为:", this.currentLevel);
            }
            // 恢复地雷数UI
            if (levelInfo.remainingMines !== undefined) {
                GameMgr_1.GameMgr.Console.Log("恢复剩余地雷数:", levelInfo.remainingMines);
                this.updateMineCountUI(levelInfo.remainingMines);
            }
            else {
                GameMgr_1.GameMgr.Console.Log("使用总地雷数:", levelInfo.mineCount);
                this.updateMineCountUI(levelInfo.mineCount);
            }
            // 进入关卡（这会初始化棋盘）
            this.enterLevel(this.currentLevel);
            // 延迟处理断线重连，确保棋盘初始化完成
            this.scheduleOnce(function () {
                // 通知当前激活的单机棋盘控制器处理断线重连
                if (_this.currentSingleChessBoard) {
                    // 检查控制器类型并调用相应的方法
                    if ('onExtendLevelInfoReconnect' in _this.currentSingleChessBoard) {
                        GameMgr_1.GameMgr.Console.Log("调用棋盘控制器的断线重连方法");
                        _this.currentSingleChessBoard.onExtendLevelInfoReconnect(levelInfo);
                    }
                    else {
                        console.warn("当前棋盘控制器不支持断线重连方法");
                    }
                }
                else {
                    console.warn("当前没有激活的棋盘控制器");
                }
                // 处理游戏状态
                if (levelInfo.gameStatus !== undefined) {
                    GameMgr_1.GameMgr.Console.Log("恢复游戏状态:", levelInfo.gameStatus);
                    // 根据游戏状态进行相应处理
                    _this.handleGameStatusRestore(levelInfo.gameStatus);
                }
            }, 0.2);
        }
        else {
            // 正常开始新游戏
            GameMgr_1.GameMgr.Console.Log("关卡模式开始新游戏");
            // 重置关卡状态（包括清除测试预制体）
            this.resetLevelState();
            // 重置炸弹点击标记
            this.lastClickWasBomb = false;
            // 开始新游戏时，先重置当前棋盘（清理上一局的痕迹）
            if (this.currentSingleChessBoard) {
                this.currentSingleChessBoard.resetBoard();
            }
            // 更新地雷数UI
            this.updateMineCountUI(levelInfo.mineCount);
            // 使用当前设置的关卡编号，而不是后端返回的levelId
            // 因为后端的levelId可能与前端的关卡编号不一致
            this.enterLevel(this.currentLevel);
            // 通知当前激活的单机棋盘控制器处理ExtendLevelInfo
            if (this.currentSingleChessBoard) {
                this.currentSingleChessBoard.onExtendLevelInfo();
            }
        }
    };
    /**
     * 更新地雷数UI
     * @param mineCount 地雷数量
     */
    LevelPageController.prototype.updateMineCountUI = function (mineCount) {
        if (this.mineCountLabel) {
            this.mineCountLabel.string = mineCount.toString();
        }
    };
    /**
     * 更新当前关卡数UI
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.updateCurrentLevelUI = function (levelNumber) {
        if (this.currentLevelLabel) {
            this.currentLevelLabel.string = "\u7B2C" + levelNumber + "\u5173";
        }
    };
    /**
     * 根据关卡数进入相应的关卡（优化版本）
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.enterLevel = function (levelNumber) {
        // 防止重复更新
        if (this.isUpdating) {
            return;
        }
        this.isUpdating = true;
        // 更新关卡数UI显示
        this.updateCurrentLevelUI(levelNumber);
        // 获取目标地图节点和容器
        var targetMapInfo = this.getMapNodeByLevel(levelNumber);
        if (!targetMapInfo) {
            cc.warn("\u672A\u77E5\u7684\u5173\u5361\u7F16\u53F7: " + levelNumber);
            this.isUpdating = false;
            return;
        }
        // 只有当目标节点与当前显示的节点不同时才进行切换
        if (this.lastShownMapNode !== targetMapInfo.mapNode) {
            // 隐藏上一个显示的地图节点
            if (this.lastShownMapNode) {
                this.lastShownMapNode.active = false;
            }
            // 显示目标容器和地图节点
            this.showMapContainer(targetMapInfo.containerType);
            this.showMapNodeOptimized(targetMapInfo.mapNode, targetMapInfo.mapName);
            // 记录当前显示的节点
            this.lastShownMapNode = targetMapInfo.mapNode;
        }
        // 设置当前激活的单机棋盘控制器
        if (this.isHexLevel(levelNumber)) {
            // 六边形关卡使用六边形控制器
            if (this.hexSingleChessBoardController) {
                var hexBoardType = this.getHexBoardTypeByLevel(levelNumber);
                this.hexSingleChessBoardController.initBoard(hexBoardType);
                this.currentSingleChessBoard = this.hexSingleChessBoardController;
            }
            else {
                console.error("❌ 六边形单机控制器未配置！");
                this.currentSingleChessBoard = null;
            }
        }
        else {
            // 四边形关卡使用四边形控制器
            if (this.singleChessBoardController) {
                var boardType = this.getBoardTypeByLevel(levelNumber);
                this.singleChessBoardController.initBoard(boardType);
                this.currentSingleChessBoard = this.singleChessBoardController;
            }
            else {
                console.error("❌ 四边形单机控制器未配置！");
                this.currentSingleChessBoard = null;
            }
        }
        this.isUpdating = false;
    };
    /**
     * 根据关卡数获取对应的地图节点信息
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.getMapNodeByLevel = function (levelNumber) {
        if (levelNumber >= 1 && levelNumber <= 4) {
            return { mapNode: this.qipan8x8Node, mapName: "qipan8*8", containerType: 'map1' };
        }
        else if (levelNumber === 5) {
            return { mapNode: this.levelS001Node, mapName: "Level_S001", containerType: 'map2' };
        }
        else if (levelNumber >= 6 && levelNumber <= 9) {
            return { mapNode: this.qipan8x9Node, mapName: "qipan8*9", containerType: 'map1' };
        }
        else if (levelNumber === 10) {
            return { mapNode: this.levelS002Node, mapName: "Level_S002", containerType: 'map2' };
        }
        else if (levelNumber >= 11 && levelNumber <= 14) {
            return { mapNode: this.qipan9x9Node, mapName: "qipan9*9", containerType: 'map1' };
        }
        else if (levelNumber === 15) {
            return { mapNode: this.levelS003Node, mapName: "Level_S003", containerType: 'map2' };
        }
        else if (levelNumber >= 16 && levelNumber <= 19) {
            return { mapNode: this.qipan9x10Node, mapName: "qipan9*10", containerType: 'map1' };
        }
        else if (levelNumber === 20) {
            return { mapNode: this.levelS004Node, mapName: "Level_S004", containerType: 'map2' };
        }
        else if (levelNumber >= 21 && levelNumber <= 24) {
            return { mapNode: this.qipan10x10Node, mapName: "qipan10*10", containerType: 'map1' };
        }
        else if (levelNumber === 25) {
            return { mapNode: this.levelS005Node, mapName: "Level_S005", containerType: 'map2' };
        }
        else if (levelNumber >= 26 && levelNumber <= 29) {
            return { mapNode: this.qipan10x10Node, mapName: "qipan10*10", containerType: 'map1' };
        }
        else if (levelNumber === 30) {
            return { mapNode: this.levelS006Node, mapName: "Level_S006", containerType: 'map2' };
        }
        return null;
    };
    /**
     * 根据关卡编号获取棋盘类型
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.getBoardTypeByLevel = function (levelNumber) {
        if (levelNumber >= 1 && levelNumber <= 4) {
            return "8x8";
        }
        else if (levelNumber >= 6 && levelNumber <= 9) {
            return "8x9";
        }
        else if (levelNumber >= 11 && levelNumber <= 14) {
            return "9x9";
        }
        else if (levelNumber >= 16 && levelNumber <= 19) {
            return "9x10";
        }
        else if (levelNumber >= 21 && levelNumber <= 24 || levelNumber >= 26 && levelNumber <= 29) {
            return "10x10";
        }
        return "8x8"; // 默认
    };
    /**
     * 判断是否为六边形关卡
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.isHexLevel = function (levelNumber) {
        // 特殊关卡使用六边形棋盘
        return levelNumber === 5 || levelNumber === 10 || levelNumber === 15 ||
            levelNumber === 20 || levelNumber === 25 || levelNumber === 30;
    };
    /**
     * 根据关卡编号获取六边形棋盘类型
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.getHexBoardTypeByLevel = function (levelNumber) {
        // 六关对应六个六边形棋盘
        if (levelNumber === 5) {
            return "hexBoard1"; // 第1关六边形
        }
        else if (levelNumber === 10) {
            return "hexBoard2"; // 第2关六边形
        }
        else if (levelNumber === 15) {
            return "hexBoard3"; // 第3关六边形
        }
        else if (levelNumber === 20) {
            return "hexBoard4"; // 第4关六边形
        }
        else if (levelNumber === 25) {
            return "hexBoard5"; // 第5关六边形
        }
        else if (levelNumber === 30) {
            return "hexBoard6"; // 第6关六边形
        }
        return "hexBoard1"; // 默认
    };
    /**
     * 显示指定的地图节点（优化版本）
     * @param mapNode 要显示的地图节点
     * @param mapName 地图名称（用于日志）
     */
    LevelPageController.prototype.showMapNodeOptimized = function (mapNode, mapName) {
        if (mapNode) {
            mapNode.active = true;
        }
        else {
            cc.warn("\u274C \u5730\u56FE\u8282\u70B9\u672A\u627E\u5230: " + mapName);
        }
    };
    /**
     * 显示指定的地图容器
     * @param containerType 容器类型
     */
    LevelPageController.prototype.showMapContainer = function (containerType) {
        // 确保 level_page 节点是激活的
        if (this.levelPageNode) {
            this.levelPageNode.active = true;
        }
        // 根据容器类型显示对应容器，隐藏另一个
        if (containerType === 'map1') {
            if (this.gameMap1Node && !this.gameMap1Node.active) {
                this.gameMap1Node.active = true;
            }
            if (this.gameMap2Node && this.gameMap2Node.active) {
                this.gameMap2Node.active = false;
            }
        }
        else {
            if (this.gameMap2Node && !this.gameMap2Node.active) {
                this.gameMap2Node.active = true;
            }
            if (this.gameMap1Node && this.gameMap1Node.active) {
                this.gameMap1Node.active = false;
            }
        }
    };
    /**
     * 隐藏所有地图节点
     */
    LevelPageController.prototype.hideAllMapNodes = function () {
        var allMapNodes = [
            this.qipan8x8Node,
            this.qipan8x9Node,
            this.qipan9x9Node,
            this.qipan9x10Node,
            this.qipan10x10Node,
            this.levelS001Node,
            this.levelS002Node,
            this.levelS003Node,
            this.levelS004Node,
            this.levelS005Node,
            this.levelS006Node
        ];
        allMapNodes.forEach(function (node) {
            if (node) {
                node.active = false;
            }
        });
    };
    /**
     * 设置当前关卡（从外部调用）
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.setCurrentLevel = function (levelNumber) {
        // 重置关卡状态（包括清除测试预制体）
        this.resetLevelState();
        this.currentLevel = levelNumber;
        // 立即根据关卡数切换地图显示
        this.enterLevel(levelNumber);
    };
    /**
     * 获取当前关卡编号
     */
    LevelPageController.prototype.getCurrentLevel = function () {
        return this.currentLevel;
    };
    /**
     * 获取当前关卡信息
     */
    LevelPageController.prototype.getCurrentLevelInfo = function () {
        return this.currentLevelInfo;
    };
    /**
     * 获取当前房间ID
     */
    LevelPageController.prototype.getCurrentRoomId = function () {
        return this.currentRoomId;
    };
    /**
     * 处理游戏状态恢复（断线重连时使用）
     * @param gameStatus 游戏状态
     */
    LevelPageController.prototype.handleGameStatusRestore = function (gameStatus) {
        GameMgr_1.GameMgr.Console.Log("处理游戏状态恢复:", gameStatus);
        // 根据API文档中的游戏状态值进行处理
        switch (gameStatus) {
            case 0: // 等待开始
                GameMgr_1.GameMgr.Console.Log("游戏状态：等待开始");
                // 游戏还未开始，保持当前状态
                break;
            case 1: // 扫雷进行中
                GameMgr_1.GameMgr.Console.Log("游戏状态：扫雷进行中");
                // 游戏正在进行中，确保棋盘可以交互
                if (this.currentSingleChessBoard) {
                    // 启用棋盘触摸
                    if ('enableAllGridTouch' in this.currentSingleChessBoard) {
                        this.currentSingleChessBoard.enableAllGridTouch();
                    }
                }
                break;
            case 4: // 关卡胜利
                GameMgr_1.GameMgr.Console.Log("游戏状态：关卡胜利");
                // 游戏已胜利，显示胜利状态但不显示结算页面（等待服务端的LevelGameEnd消息）
                break;
            case 5: // 关卡失败
                GameMgr_1.GameMgr.Console.Log("游戏状态：关卡失败");
                // 游戏已失败，显示失败状态但不显示结算页面（等待服务端的LevelGameEnd消息）
                break;
            default:
                GameMgr_1.GameMgr.Console.Log("未知游戏状态:", gameStatus);
                break;
        }
    };
    /**
     * 隐藏所有地图容器
     */
    LevelPageController.prototype.hideAllMapContainers = function () {
        // 确保 level_page 节点是激活的
        if (this.levelPageNode) {
            this.levelPageNode.active = true;
        }
        // 隐藏两个主要的地图容器
        if (this.gameMap1Node) {
            this.gameMap1Node.active = false;
        }
        if (this.gameMap2Node) {
            this.gameMap2Node.active = false;
        }
        // 同时隐藏所有具体的地图节点
        this.hideAllMapNodes();
    };
    /**
     * 显示 game_map_1 容器（方形地图）
     */
    LevelPageController.prototype.showGameMap1 = function () {
        if (this.gameMap1Node) {
            this.gameMap1Node.active = true;
        }
        else {
            cc.warn("❌ game_map_1 节点未找到");
        }
    };
    /**
     * 显示 game_map_2 容器（特殊关卡）
     */
    LevelPageController.prototype.showGameMap2 = function () {
        if (this.gameMap2Node) {
            this.gameMap2Node.active = true;
        }
        else {
            cc.warn("❌ game_map_2 节点未找到");
        }
    };
    /**
     * 获取当前激活的单机棋盘控制器
     */
    LevelPageController.prototype.getCurrentSingleChessBoard = function () {
        return this.currentSingleChessBoard;
    };
    /**
     * 处理单机模式的点击响应
     * @param response 点击响应数据
     */
    LevelPageController.prototype.handleSingleModeClickResponse = function (response) {
        if (this.currentSingleChessBoard) {
            var x = response.x, y = response.y, result = response.result, chainReaction = response.chainReaction;
            // 处理点击结果
            if (x !== undefined && y !== undefined && result !== undefined) {
                this.currentSingleChessBoard.handleClickResponse(x, y, result);
            }
            // 处理连锁反应
            if (chainReaction && Array.isArray(chainReaction)) {
                this.currentSingleChessBoard.handleChainReaction(chainReaction);
            }
        }
    };
    /**
     * 处理单机模式游戏结束
     * @param gameEndData 游戏结束数据
     */
    LevelPageController.prototype.handleSingleModeGameEnd = function (gameEndData) {
        if (this.currentSingleChessBoard) {
            // 禁用棋盘触摸
            this.currentSingleChessBoard.disableAllGridTouch();
            // 处理游戏结束
            this.currentSingleChessBoard.onLevelGameEnd();
        }
    };
    /**
     * 重置当前单机棋盘（仅在开始新游戏时调用）
     */
    LevelPageController.prototype.resetCurrentSingleChessBoard = function () {
        if (this.currentSingleChessBoard) {
            // 重置棋盘状态（清理所有预制体和格子状态）
            this.currentSingleChessBoard.resetBoard();
            // 重新启用触摸事件
            this.currentSingleChessBoard.enableAllGridTouch();
        }
    };
    /**
     * 注册单机模式消息处理器
     */
    LevelPageController.prototype.registerSingleModeMessageHandlers = function () {
        // 监听WebSocket消息
        GameMgr_1.GameMgr.Event.AddEventListener(EventCenter_1.EventType.ReceiveMessage, this.onReceiveMessage, this);
    };
    /**
     * 取消单机模式消息监听
     */
    LevelPageController.prototype.unregisterSingleModeMessageHandlers = function () {
        GameMgr_1.GameMgr.Event.RemoveEventListener(EventCenter_1.EventType.ReceiveMessage, this.onReceiveMessage, this);
    };
    /**
     * 处理接收到的WebSocket消息
     * @param messageBean 消息数据
     */
    LevelPageController.prototype.onReceiveMessage = function (messageBean) {
        switch (messageBean.msgId) {
            case MessageId_1.MessageId.MsgTypeLevelClickBlock:
                this.onLevelClickBlockResponse(messageBean.data);
                break;
            case MessageId_1.MessageId.MsgTypeLevelGameEnd:
                this.onLevelGameEnd(messageBean.data);
                break;
        }
    };
    /**
     * 处理LevelClickBlock响应
     * @param response 点击响应数据
     */
    LevelPageController.prototype.onLevelClickBlockResponse = function (response) {
        if (this.currentSingleChessBoard) {
            // 解构响应数据，支持多种可能的字段名
            var x = response.x, y = response.y, q = response.q, r = response.r, result = response.result, action = response.action, chainReaction = response.chainReaction, revealedGrids = response.revealedGrids, floodFill = response.floodFill, revealedBlocks = response.revealedBlocks, floodFillResults = response.floodFillResults // 单机模式使用这个字段
            ;
            // 判断当前使用的是哪种控制器
            var isUsingHexController = (this.currentSingleChessBoard === this.hexSingleChessBoardController);
            // 根据控制器类型决定坐标处理方式
            var coordX = void 0, coordY = void 0, coordQ = void 0, coordR = void 0;
            var hasValidCoord = false;
            if (isUsingHexController) {
                // 六边形控制器：优先使用六边形坐标，如果没有则将x,y映射为q,r
                if (q !== undefined && r !== undefined) {
                    coordQ = q;
                    coordR = r;
                    hasValidCoord = true;
                }
                else if (x !== undefined && y !== undefined) {
                    // 服务器返回x,y字段，但实际是六边形坐标：x=q, y=r
                    coordQ = x; // x 就是 q
                    coordR = y; // y 就是 r
                    hasValidCoord = true;
                }
            }
            else {
                // 四边形控制器：使用四边形坐标
                if (x !== undefined && y !== undefined) {
                    coordX = x;
                    coordY = y;
                    hasValidCoord = true;
                }
                else if (q !== undefined && r !== undefined) {
                    console.warn("\u26A0\uFE0F \u56DB\u8FB9\u5F62\u63A7\u5236\u5668\u6536\u5230\u516D\u8FB9\u5F62\u5750\u6807 (" + q + ", " + r + ")\uFF0C\u8FD9\u53EF\u80FD\u4E0D\u6B63\u786E");
                    hasValidCoord = false;
                }
            }
            if (hasValidCoord && result !== undefined) {
                if (action === 2) {
                    // 标记/取消标记操作，不调用handleClickResponse，避免格子消失
                    // 不调用 handleClickResponse，因为标记操作不应该隐藏格子
                }
                else if (action === 1) {
                    // 挖掘操作
                    // 检查是否点到炸弹
                    if (result === "boom" || result === "mine") {
                        this.lastClickWasBomb = true;
                    }
                    // 根据控制器类型调用对应的方法
                    if (isUsingHexController) {
                        // 六边形控制器
                        this.currentSingleChessBoard.handleClickResponse(coordQ, coordR, result);
                    }
                    else {
                        // 四边形控制器
                        this.currentSingleChessBoard.handleClickResponse(coordX, coordY, result);
                    }
                    // 处理连锁展开数据
                    if (floodFillResults && Array.isArray(floodFillResults) && floodFillResults.length > 0) {
                        if (isUsingHexController) {
                            // 六边形控制器使用handleChainReaction方法
                            this.currentSingleChessBoard.handleChainReaction(floodFillResults);
                        }
                        else {
                            // 四边形控制器使用handleFloodFillResults方法
                            this.currentSingleChessBoard.handleFloodFillResults(floodFillResults);
                        }
                    }
                }
                else {
                    // 其他操作，默认按挖掘处理
                    if (isUsingHexController) {
                        this.currentSingleChessBoard.handleClickResponse(coordQ, coordR, result);
                    }
                    else {
                        this.currentSingleChessBoard.handleClickResponse(coordX, coordY, result);
                    }
                }
            }
            else {
                console.warn("⚠️ 响应数据缺少有效坐标或结果信息");
                console.warn("   \u63A7\u5236\u5668\u7C7B\u578B: " + (isUsingHexController ? '六边形' : '四边形'));
                console.warn("   \u5750\u6807\u6570\u636E: x=" + x + ", y=" + y + ", q=" + q + ", r=" + r);
                console.warn("   \u7ED3\u679C: " + result);
            }
        }
    };
    /**
     * 处理LevelGameEnd通知
     * @param gameEndData 游戏结束数据
     */
    LevelPageController.prototype.onLevelGameEnd = function (gameEndData) {
        var _this = this;
        if (this.currentSingleChessBoard) {
            // 禁用棋盘触摸
            this.currentSingleChessBoard.disableAllGridTouch();
            // 处理游戏结束（不清理数据）
            this.currentSingleChessBoard.onLevelGameEnd();
        }
        // 禁用返回按钮，防止游戏结束时玩家误点击
        this.disableBackButton();
        // 检查是否点到了炸弹，如果是游戏失败且点到炸弹，则延迟显示结算页面
        var isGameFailed = !gameEndData.success;
        var hasBombExploded = this.lastClickWasBomb ||
            (this.currentSingleChessBoard && this.currentSingleChessBoard.hasBombExplodedInThisGame());
        if (isGameFailed && hasBombExploded) {
            // 点到炸弹导致的游戏失败，延迟1.5秒显示结算页面
            this.scheduleOnce(function () {
                _this.showLevelSettlement(gameEndData);
                // 重置标记
                _this.lastClickWasBomb = false;
            }, 1.5);
        }
        else {
            // 其他情况，立即显示结算页面
            this.showLevelSettlement(gameEndData);
            // 重置标记
            this.lastClickWasBomb = false;
        }
    };
    /**
     * 设置结算页面按钮事件
     */
    LevelPageController.prototype.setupSettlementButtons = function () {
        var _this = this;
        // 再来一次按钮
        if (this.retryButton) {
            this.retryButton.node.on('click', this.onRetryButtonClick, this);
        }
        // 下一关按钮
        if (this.nextLevelButton) {
            this.nextLevelButton.node.on('click', this.onNextLevelButtonClick, this);
        }
        // 退出按钮 - 使用按压效果，模仿其他返回按钮
        if (this.exitButton) {
            Tools_1.Tools.imageButtonClick(this.exitButton.node, Config_1.Config.buttonRes + 'board_btn_back_normal', Config_1.Config.buttonRes + 'board_btn_back_pressed', function () {
                _this.onExitButtonClick();
            });
        }
    };
    /**
     * 显示结算页面
     * @param gameEndData 游戏结束数据
     */
    LevelPageController.prototype.showLevelSettlement = function (gameEndData) {
        if (!this.levelSettlementNode) {
            console.error("levelSettlementNode 未配置");
            return;
        }
        // 显示结算页面
        this.levelSettlementNode.active = true;
        // 根据游戏结果显示对应的背景 - 修复成功判断逻辑
        var isSuccess = gameEndData.isSuccess || gameEndData.success || gameEndData.isWin || gameEndData.gameStatus === 1;
        if (isSuccess) {
            // 成功 - 显示胜利背景
            if (this.winBgNode) {
                this.winBgNode.active = true;
            }
            if (this.loseBgNode) {
                this.loseBgNode.active = false;
            }
        }
        else {
            // 失败 - 显示失败背景
            if (this.loseBgNode) {
                this.loseBgNode.active = true;
            }
            if (this.winBgNode) {
                this.winBgNode.active = false;
            }
        }
    };
    /**
     * 再来一次按钮点击事件
     */
    LevelPageController.prototype.onRetryButtonClick = function () {
        // 关闭结算页面
        this.hideLevelSettlement();
        // 重新启用返回按钮
        this.enableBackButton();
        // 注意：不在这里重置棋盘，等收到ExtendLevelInfo响应时再重置
        // 这样可以避免过早清理，让玩家在点击按钮后还能短暂看到游玩痕迹
        // 发送当前关卡的ExtendLevelInfo
        this.sendExtendLevelInfo(this.currentLevel);
    };
    /**
     * 下一关按钮点击事件
     */
    LevelPageController.prototype.onNextLevelButtonClick = function () {
        // 关闭结算页面
        this.hideLevelSettlement();
        // 重新启用返回按钮
        this.enableBackButton();
        // 进入下一关
        var nextLevel = this.currentLevel + 1;
        this.setCurrentLevel(nextLevel);
        // 注意：不在这里重置棋盘，等收到ExtendLevelInfo响应时再重置
        // 这样可以避免过早清理，让玩家在点击按钮后还能短暂看到游玩痕迹
        // 发送下一关的ExtendLevelInfo
        this.sendExtendLevelInfo(nextLevel);
    };
    /**
     * 退出按钮点击事件
     */
    LevelPageController.prototype.onExitButtonClick = function () {
        // 关闭结算页面
        this.hideLevelSettlement();
        // 重新启用返回按钮（虽然要退出了，但保持一致性）
        this.enableBackButton();
        // 返回到关卡选择页面（匹配界面）
        this.returnToLevelSelect();
    };
    /**
     * 隐藏结算页面
     */
    LevelPageController.prototype.hideLevelSettlement = function () {
        if (this.levelSettlementNode) {
            this.levelSettlementNode.active = false;
        }
    };
    /**
     * 发送ExtendLevelInfo消息
     * @param levelId 关卡ID
     */
    LevelPageController.prototype.sendExtendLevelInfo = function (levelId) {
        var request = {
            levelId: levelId
        };
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeExtendLevelInfo, request);
    };
    /**
     * 设置测试按钮
     */
    LevelPageController.prototype.setupDebugButton = function () {
        if (this.debugShowMinesButton) {
            this.debugShowMinesButton.node.on('click', this.onDebugShowMinesClick, this);
        }
    };
    /**
     * 测试按钮点击事件 - 发送DebugShowMines消息
     */
    LevelPageController.prototype.onDebugShowMinesClick = function () {
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeDebugShowMines, {});
    };
    /**
     * 判断是否在单机模式
     */
    LevelPageController.prototype.isInSingleMode = function () {
        // 单机模式的判断逻辑：当前页面是关卡页面且有有效的房间ID
        return this.currentRoomId > 0;
    };
    /**
     * 处理DebugShowMines响应，在炸弹位置生成测试预制体
     * @param minePositions 炸弹位置数组 [{x: number, y: number}]
     */
    LevelPageController.prototype.handleDebugShowMines = function (minePositions) {
        var _this = this;
        if (!this.debugMinePrefab) {
            cc.warn("debugMinePrefab 预制体未设置，无法显示测试标记");
            return;
        }
        if (!this.currentSingleChessBoard) {
            cc.warn("当前没有激活的单机棋盘");
            return;
        }
        if (!minePositions || !Array.isArray(minePositions) || minePositions.length === 0) {
            cc.warn("地雷位置数据无效:", minePositions);
            return;
        }
        // 先尝试直接创建一个测试预制体，不使用延迟
        if (minePositions.length > 0) {
            var firstPosition = minePositions[0];
            // 检查坐标字段
            var coordX = void 0, coordY = void 0;
            var pos = firstPosition; // 使用any类型避免TypeScript报错
            if (pos.x !== undefined && pos.y !== undefined) {
                coordX = pos.x;
                coordY = pos.y;
            }
            else if (pos.q !== undefined && pos.r !== undefined) {
                coordX = pos.q;
                coordY = pos.r;
            }
            else {
                console.error("\u274C \u65E0\u6CD5\u8BC6\u522B\u5750\u6807\u5B57\u6BB5:", firstPosition);
                return;
            }
            // 直接调用，不使用延迟
            this.createDebugMinePrefab(coordX, coordY);
        }
        // 在每个炸弹位置生成测试预制体
        minePositions.forEach(function (position, index) {
            var pos = position; // 使用any类型避免TypeScript报错
            // 获取坐标
            var coordX, coordY;
            if (pos.x !== undefined && pos.y !== undefined) {
                coordX = pos.x;
                coordY = pos.y;
            }
            else if (pos.q !== undefined && pos.r !== undefined) {
                coordX = pos.q;
                coordY = pos.r;
            }
            else {
                console.error("\u274C \u5730\u96F7\u4F4D\u7F6E" + index + "\u5750\u6807\u5B57\u6BB5\u65E0\u6548:", position);
                return;
            }
            if (index === 0) {
                // 第一个不延迟，立即执行（已经在上面处理过了，跳过）
                return;
            }
            else {
                // 其他的使用延迟 - 修复闭包问题
                var capturedX_1 = coordX; // 捕获当前值
                var capturedY_1 = coordY; // 捕获当前值
                _this.scheduleOnce(function () {
                    _this.createDebugMinePrefab(capturedX_1, capturedY_1);
                }, index * 0.1);
            }
        });
    };
    /**
     * 在指定位置创建测试预制体
     * @param x 格子x坐标（四边形）或q坐标（六边形）
     * @param y 格子y坐标（四边形）或r坐标（六边形）
     */
    LevelPageController.prototype.createDebugMinePrefab = function (x, y) {
        // 检查坐标是否有效
        if (x === undefined || y === undefined || x === null || y === null) {
            console.error("\u274C \u65E0\u6548\u7684\u5750\u6807\u53C2\u6570: x=" + x + ", y=" + y);
            console.error("   x\u7C7B\u578B: " + typeof x + ", y\u7C7B\u578B: " + typeof y);
            console.error("   \u8C03\u7528\u5806\u6808:", new Error().stack);
            return;
        }
        if (!this.debugMinePrefab) {
            cc.error("debugMinePrefab 为空，无法创建测试预制体");
            return;
        }
        if (!this.currentSingleChessBoard) {
            cc.error("currentSingleChessBoard 为空，无法创建测试预制体");
            return;
        }
        try {
            // 判断当前使用的是哪种控制器
            var isUsingHexController = (this.currentSingleChessBoard === this.hexSingleChessBoardController);
            var debugNode = null;
            if (isUsingHexController) {
                // 六边形控制器：x实际是q，y实际是r
                debugNode = this.currentSingleChessBoard.createCustomPrefab(x, y, // 对于六边形，x就是q，y就是r
                this.debugMinePrefab, "DebugMine_" + x + "_" + y);
            }
            else {
                // 四边形控制器
                debugNode = this.currentSingleChessBoard.createCustomPrefab(x, y, this.debugMinePrefab, "DebugMine_" + x + "_" + y);
            }
            if (debugNode) {
                // 将创建的节点存储起来，用于后续清理
                this.debugMineNodes.push(debugNode);
            }
            else {
                cc.error("\u274C \u5728\u4F4D\u7F6E (" + x + ", " + y + ") \u521B\u5EFA\u6D4B\u8BD5\u9884\u5236\u4F53\u5931\u8D25\uFF0C\u8FD4\u56DE\u503C\u4E3A\u7A7A");
            }
        }
        catch (error) {
            cc.error("\u274C \u521B\u5EFA\u6D4B\u8BD5\u9884\u5236\u4F53\u65F6\u53D1\u751F\u9519\u8BEF:", error);
        }
    };
    /**
     * 清除所有测试预制体
     */
    LevelPageController.prototype.clearDebugMines = function () {
        this.debugMineNodes.forEach(function (node, index) {
            if (node && cc.isValid(node)) {
                node.destroy();
            }
        });
        // 清空数组
        this.debugMineNodes = [];
    };
    /**
     * 重置关卡状态（包括清除测试预制体）
     */
    LevelPageController.prototype.resetLevelState = function () {
        this.clearDebugMines();
        // 这里可以添加其他需要重置的状态
    };
    LevelPageController.prototype.onDestroy = function () {
        // 取消消息监听
        this.unregisterSingleModeMessageHandlers();
        // 清理测试预制体
        this.clearDebugMines();
        // 清理按钮事件
        if (this.retryButton) {
            this.retryButton.node.off('click', this.onRetryButtonClick, this);
        }
        if (this.nextLevelButton) {
            this.nextLevelButton.node.off('click', this.onNextLevelButtonClick, this);
        }
        // 退出按钮使用 Tools.imageButtonClick，会自动管理事件，无需手动清理
        if (this.debugShowMinesButton) {
            this.debugShowMinesButton.node.off('click', this.onDebugShowMinesClick, this);
        }
    };
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "backButton", void 0);
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "startGameButton", void 0);
    __decorate([
        property(cc.Label)
    ], LevelPageController.prototype, "mineCountLabel", void 0);
    __decorate([
        property(cc.Label)
    ], LevelPageController.prototype, "currentLevelLabel", void 0);
    __decorate([
        property(LeaveDialogController_1.default)
    ], LevelPageController.prototype, "leaveDialogController", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelPageNode", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "gameMap1Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "gameMap2Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan8x8Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan8x9Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan9x9Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan9x10Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan10x10Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS001Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS002Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS003Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS004Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS005Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS006Node", void 0);
    __decorate([
        property(SingleChessBoardController_1.default)
    ], LevelPageController.prototype, "singleChessBoardController", void 0);
    __decorate([
        property(HexSingleChessBoardController_1.default)
    ], LevelPageController.prototype, "hexSingleChessBoardController", void 0);
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "debugShowMinesButton", void 0);
    __decorate([
        property(cc.Prefab)
    ], LevelPageController.prototype, "debugMinePrefab", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelSettlementNode", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "boardBgNode", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "loseBgNode", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "winBgNode", void 0);
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "retryButton", void 0);
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "nextLevelButton", void 0);
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "exitButton", void 0);
    LevelPageController = __decorate([
        ccclass
    ], LevelPageController);
    return LevelPageController;
}(cc.Component));
exports.default = LevelPageController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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