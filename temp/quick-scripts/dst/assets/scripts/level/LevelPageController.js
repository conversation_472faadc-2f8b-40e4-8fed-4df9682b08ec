
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/level/LevelPageController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'c8e13uMxpxGELez69T2SfJx', 'LevelPageController');
// scripts/level/LevelPageController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var LeaveDialogController_1 = require("../hall/LeaveDialogController");
var Tools_1 = require("../util/Tools");
var Config_1 = require("../util/Config");
var GlobalManagerController_1 = require("../GlobalManagerController");
var SingleChessBoardController_1 = require("../game/Chess/SingleChessBoardController");
var HexSingleChessBoardController_1 = require("../game/Chess/HexSingleChessBoardController");
var WebSocketManager_1 = require("../net/WebSocketManager");
var MessageId_1 = require("../net/MessageId");
var GameMgr_1 = require("../common/GameMgr");
var EventCenter_1 = require("../common/EventCenter");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var LevelPageController = /** @class */ (function (_super) {
    __extends(LevelPageController, _super);
    function LevelPageController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        // 返回按钮
        _this.backButton = null;
        // 开始游戏按钮
        _this.startGameButton = null;
        // 地雷数UI标签
        _this.mineCountLabel = null;
        // 当前关卡数UI标签
        _this.currentLevelLabel = null;
        // 退出游戏弹窗
        _this.leaveDialogController = null;
        // level_page节点
        _this.levelPageNode = null;
        // game_map_1节点
        _this.gameMap1Node = null;
        // game_map_2节点
        _this.gameMap2Node = null;
        // 方形地图节点引用
        _this.qipan8x8Node = null; // level_page/game_map_1/chess_bg/qipan8*8
        _this.qipan8x9Node = null; // level_page/game_map_1/chess_bg/qipan8*9
        _this.qipan9x9Node = null; // level_page/game_map_1/chess_bg/qipan9*9
        _this.qipan9x10Node = null; // level_page/game_map_1/chess_bg/qipan9*10
        _this.qipan10x10Node = null; // level_page/game_map_1/chess_bg/qipan10*10
        // 特殊关卡节点引用
        _this.levelS001Node = null; // level_page/game_map_2/game_bg/Level_S001
        _this.levelS002Node = null; // level_page/game_map_2/game_bg/Level_S002
        _this.levelS003Node = null; // level_page/game_map_2/game_bg/Level_S003
        _this.levelS004Node = null; // level_page/game_map_2/game_bg/Level_S004
        _this.levelS005Node = null; // level_page/game_map_2/game_bg/Level_S005
        _this.levelS006Node = null; // level_page/game_map_2/game_bg/Level_S006
        // 单机模式棋盘控制器
        _this.singleChessBoardController = null;
        // 六边形单机模式棋盘控制器
        _this.hexSingleChessBoardController = null;
        // 测试按钮（用于调试显示地雷位置）
        _this.debugShowMinesButton = null;
        // 测试预制体（用于显示地雷位置）
        _this.debugMinePrefab = null;
        // 存储创建的测试预制体节点，用于清理
        _this.debugMineNodes = [];
        // 结算页面相关节点
        _this.levelSettlementNode = null; // level_settlement节点
        _this.boardBgNode = null; // level_settlement/board_bg节点
        _this.loseBgNode = null; // level_settlement/board_bg/lose_bg节点
        _this.winBgNode = null; // level_settlement/board_bg/win_bg节点
        _this.retryButton = null; // 再来一次按钮
        _this.nextLevelButton = null; // 下一关按钮
        _this.exitButton = null; // 退出按钮
        // 当前关卡数据
        _this.currentLevel = 1;
        _this.currentLevelInfo = null;
        _this.currentRoomId = 0; // 当前关卡游戏的房间ID
        _this.currentSingleChessBoard = null; // 当前激活的单机棋盘
        // 记录最后一次点击是否是炸弹
        _this.lastClickWasBomb = false;
        // 性能优化相关
        _this.lastShownMapNode = null; // 记录上次显示的地图节点
        _this.isUpdating = false; // 防止重复更新
        return _this;
    }
    LevelPageController.prototype.onLoad = function () {
        var _this = this;
        // 设置返回按钮点击事件 - 使用与GamePageController相同的样式
        if (this.backButton) {
            Tools_1.Tools.imageButtonClick(this.backButton, Config_1.Config.buttonRes + 'side_btn_back_normal', Config_1.Config.buttonRes + 'side_btn_back_pressed', function () {
                _this.onBackButtonClick();
            });
        }
        // 设置开始游戏按钮点击事件
        if (this.startGameButton) {
            this.startGameButton.node.on('click', this.onStartGameButtonClick, this);
        }
        // 注册单机模式消息监听
        this.registerSingleModeMessageHandlers();
        // 设置结算页面按钮事件
        this.setupSettlementButtons();
        // 注意：不在onLoad中初始化关卡数UI，等待外部调用setCurrentLevel时再更新
    };
    LevelPageController.prototype.start = function () {
        // 初始化时隐藏所有地图节点
        this.hideAllMapNodes();
        // 设置测试按钮点击事件
        this.setupDebugButton();
    };
    /**
     * 返回按钮点击事件
     */
    LevelPageController.prototype.onBackButtonClick = function () {
        // 如果没有有效的房间ID，说明还没有进入游戏，直接返回关卡选择页面
        if (this.currentRoomId <= 0) {
            this.returnToLevelSelect();
            return;
        }
        // 弹出确认退出对话框，type=1表示退出本局游戏，传递当前房间ID
        if (this.leaveDialogController) {
            this.leaveDialogController.show(1, function () {
            }, this.currentRoomId);
        }
        else {
            cc.warn("LeaveDialogController 未配置");
        }
    };
    /**
     * 返回到关卡选择页面
     */
    LevelPageController.prototype.returnToLevelSelect = function () {
        // 查找GlobalManagerController并切换到大厅页面
        var globalManagerNode = cc.find("Canvas/global_node") || cc.find("global_node");
        if (globalManagerNode) {
            var globalManager = globalManagerNode.getComponent(GlobalManagerController_1.default);
            if (globalManager) {
                globalManager.setCurrentPage(GlobalManagerController_1.PageType.HALL_PAGE);
            }
        }
    };
    /**
     * 禁用返回按钮
     */
    LevelPageController.prototype.disableBackButton = function () {
        if (this.backButton) {
            // 移除所有触摸事件监听器
            this.backButton.off(cc.Node.EventType.TOUCH_START);
            this.backButton.off(cc.Node.EventType.TOUCH_END);
            this.backButton.off(cc.Node.EventType.TOUCH_CANCEL);
            // 设置按钮为半透明状态，表示禁用
            this.backButton.opacity = 128;
        }
    };
    /**
     * 启用返回按钮
     */
    LevelPageController.prototype.enableBackButton = function () {
        var _this = this;
        if (this.backButton) {
            // 恢复按钮透明度
            this.backButton.opacity = 255;
            // 重新设置返回按钮点击事件
            Tools_1.Tools.imageButtonClick(this.backButton, Config_1.Config.buttonRes + 'side_btn_back_normal', Config_1.Config.buttonRes + 'side_btn_back_pressed', function () {
                _this.onBackButtonClick();
            });
        }
    };
    /**
     * 开始游戏按钮点击事件
     */
    LevelPageController.prototype.onStartGameButtonClick = function () {
        // ExtendLevelInfo消息现在由LevelSelectPageController发送
        // 这里直接进入游戏，等待后端响应
    };
    /**
     * 处理ExtendLevelInfo响应
     * @param levelInfo 关卡信息响应数据
     */
    LevelPageController.prototype.onExtendLevelInfo = function (levelInfo) {
        this.currentLevelInfo = levelInfo;
        // 保存房间ID，用于退出时使用
        if (levelInfo.roomId) {
            this.currentRoomId = levelInfo.roomId;
        }
        // 检查是否为断线重连
        if (levelInfo.reconnected === true) {
            GameMgr_1.GameMgr.Console.Log("关卡模式断线重连，恢复游戏状态");
            // 断线重连时，使用后端返回的levelId设置当前关卡
            if (levelInfo.levelId) {
                this.currentLevel = levelInfo.levelId;
            }
            // 恢复地雷数UI
            if (levelInfo.remainingMines !== undefined) {
                this.updateMineCountUI(levelInfo.remainingMines);
            }
            else {
                this.updateMineCountUI(levelInfo.mineCount);
            }
            // 进入关卡
            this.enterLevel(this.currentLevel);
            // 通知当前激活的单机棋盘控制器处理断线重连
            if (this.currentSingleChessBoard) {
                // 检查控制器类型并调用相应的方法
                if ('onExtendLevelInfoReconnect' in this.currentSingleChessBoard) {
                    this.currentSingleChessBoard.onExtendLevelInfoReconnect(levelInfo);
                }
                else {
                    console.warn("当前棋盘控制器不支持断线重连方法");
                }
            }
        }
        else {
            // 正常开始新游戏
            GameMgr_1.GameMgr.Console.Log("关卡模式开始新游戏");
            // 重置关卡状态（包括清除测试预制体）
            this.resetLevelState();
            // 重置炸弹点击标记
            this.lastClickWasBomb = false;
            // 开始新游戏时，先重置当前棋盘（清理上一局的痕迹）
            if (this.currentSingleChessBoard) {
                this.currentSingleChessBoard.resetBoard();
            }
            // 更新地雷数UI
            this.updateMineCountUI(levelInfo.mineCount);
            // 使用当前设置的关卡编号，而不是后端返回的levelId
            // 因为后端的levelId可能与前端的关卡编号不一致
            this.enterLevel(this.currentLevel);
            // 通知当前激活的单机棋盘控制器处理ExtendLevelInfo
            if (this.currentSingleChessBoard) {
                this.currentSingleChessBoard.onExtendLevelInfo();
            }
        }
    };
    /**
     * 更新地雷数UI
     * @param mineCount 地雷数量
     */
    LevelPageController.prototype.updateMineCountUI = function (mineCount) {
        if (this.mineCountLabel) {
            this.mineCountLabel.string = mineCount.toString();
        }
    };
    /**
     * 更新当前关卡数UI
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.updateCurrentLevelUI = function (levelNumber) {
        if (this.currentLevelLabel) {
            this.currentLevelLabel.string = "\u7B2C" + levelNumber + "\u5173";
        }
    };
    /**
     * 根据关卡数进入相应的关卡（优化版本）
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.enterLevel = function (levelNumber) {
        // 防止重复更新
        if (this.isUpdating) {
            return;
        }
        this.isUpdating = true;
        // 更新关卡数UI显示
        this.updateCurrentLevelUI(levelNumber);
        // 获取目标地图节点和容器
        var targetMapInfo = this.getMapNodeByLevel(levelNumber);
        if (!targetMapInfo) {
            cc.warn("\u672A\u77E5\u7684\u5173\u5361\u7F16\u53F7: " + levelNumber);
            this.isUpdating = false;
            return;
        }
        // 只有当目标节点与当前显示的节点不同时才进行切换
        if (this.lastShownMapNode !== targetMapInfo.mapNode) {
            // 隐藏上一个显示的地图节点
            if (this.lastShownMapNode) {
                this.lastShownMapNode.active = false;
            }
            // 显示目标容器和地图节点
            this.showMapContainer(targetMapInfo.containerType);
            this.showMapNodeOptimized(targetMapInfo.mapNode, targetMapInfo.mapName);
            // 记录当前显示的节点
            this.lastShownMapNode = targetMapInfo.mapNode;
        }
        // 设置当前激活的单机棋盘控制器
        if (this.isHexLevel(levelNumber)) {
            // 六边形关卡使用六边形控制器
            if (this.hexSingleChessBoardController) {
                var hexBoardType = this.getHexBoardTypeByLevel(levelNumber);
                this.hexSingleChessBoardController.initBoard(hexBoardType);
                this.currentSingleChessBoard = this.hexSingleChessBoardController;
            }
            else {
                console.error("❌ 六边形单机控制器未配置！");
                this.currentSingleChessBoard = null;
            }
        }
        else {
            // 四边形关卡使用四边形控制器
            if (this.singleChessBoardController) {
                var boardType = this.getBoardTypeByLevel(levelNumber);
                this.singleChessBoardController.initBoard(boardType);
                this.currentSingleChessBoard = this.singleChessBoardController;
            }
            else {
                console.error("❌ 四边形单机控制器未配置！");
                this.currentSingleChessBoard = null;
            }
        }
        this.isUpdating = false;
    };
    /**
     * 根据关卡数获取对应的地图节点信息
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.getMapNodeByLevel = function (levelNumber) {
        if (levelNumber >= 1 && levelNumber <= 4) {
            return { mapNode: this.qipan8x8Node, mapName: "qipan8*8", containerType: 'map1' };
        }
        else if (levelNumber === 5) {
            return { mapNode: this.levelS001Node, mapName: "Level_S001", containerType: 'map2' };
        }
        else if (levelNumber >= 6 && levelNumber <= 9) {
            return { mapNode: this.qipan8x9Node, mapName: "qipan8*9", containerType: 'map1' };
        }
        else if (levelNumber === 10) {
            return { mapNode: this.levelS002Node, mapName: "Level_S002", containerType: 'map2' };
        }
        else if (levelNumber >= 11 && levelNumber <= 14) {
            return { mapNode: this.qipan9x9Node, mapName: "qipan9*9", containerType: 'map1' };
        }
        else if (levelNumber === 15) {
            return { mapNode: this.levelS003Node, mapName: "Level_S003", containerType: 'map2' };
        }
        else if (levelNumber >= 16 && levelNumber <= 19) {
            return { mapNode: this.qipan9x10Node, mapName: "qipan9*10", containerType: 'map1' };
        }
        else if (levelNumber === 20) {
            return { mapNode: this.levelS004Node, mapName: "Level_S004", containerType: 'map2' };
        }
        else if (levelNumber >= 21 && levelNumber <= 24) {
            return { mapNode: this.qipan10x10Node, mapName: "qipan10*10", containerType: 'map1' };
        }
        else if (levelNumber === 25) {
            return { mapNode: this.levelS005Node, mapName: "Level_S005", containerType: 'map2' };
        }
        else if (levelNumber >= 26 && levelNumber <= 29) {
            return { mapNode: this.qipan10x10Node, mapName: "qipan10*10", containerType: 'map1' };
        }
        else if (levelNumber === 30) {
            return { mapNode: this.levelS006Node, mapName: "Level_S006", containerType: 'map2' };
        }
        return null;
    };
    /**
     * 根据关卡编号获取棋盘类型
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.getBoardTypeByLevel = function (levelNumber) {
        if (levelNumber >= 1 && levelNumber <= 4) {
            return "8x8";
        }
        else if (levelNumber >= 6 && levelNumber <= 9) {
            return "8x9";
        }
        else if (levelNumber >= 11 && levelNumber <= 14) {
            return "9x9";
        }
        else if (levelNumber >= 16 && levelNumber <= 19) {
            return "9x10";
        }
        else if (levelNumber >= 21 && levelNumber <= 24 || levelNumber >= 26 && levelNumber <= 29) {
            return "10x10";
        }
        return "8x8"; // 默认
    };
    /**
     * 判断是否为六边形关卡
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.isHexLevel = function (levelNumber) {
        // 特殊关卡使用六边形棋盘
        return levelNumber === 5 || levelNumber === 10 || levelNumber === 15 ||
            levelNumber === 20 || levelNumber === 25 || levelNumber === 30;
    };
    /**
     * 根据关卡编号获取六边形棋盘类型
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.getHexBoardTypeByLevel = function (levelNumber) {
        // 六关对应六个六边形棋盘
        if (levelNumber === 5) {
            return "hexBoard1"; // 第1关六边形
        }
        else if (levelNumber === 10) {
            return "hexBoard2"; // 第2关六边形
        }
        else if (levelNumber === 15) {
            return "hexBoard3"; // 第3关六边形
        }
        else if (levelNumber === 20) {
            return "hexBoard4"; // 第4关六边形
        }
        else if (levelNumber === 25) {
            return "hexBoard5"; // 第5关六边形
        }
        else if (levelNumber === 30) {
            return "hexBoard6"; // 第6关六边形
        }
        return "hexBoard1"; // 默认
    };
    /**
     * 显示指定的地图节点（优化版本）
     * @param mapNode 要显示的地图节点
     * @param mapName 地图名称（用于日志）
     */
    LevelPageController.prototype.showMapNodeOptimized = function (mapNode, mapName) {
        if (mapNode) {
            mapNode.active = true;
        }
        else {
            cc.warn("\u274C \u5730\u56FE\u8282\u70B9\u672A\u627E\u5230: " + mapName);
        }
    };
    /**
     * 显示指定的地图容器
     * @param containerType 容器类型
     */
    LevelPageController.prototype.showMapContainer = function (containerType) {
        // 确保 level_page 节点是激活的
        if (this.levelPageNode) {
            this.levelPageNode.active = true;
        }
        // 根据容器类型显示对应容器，隐藏另一个
        if (containerType === 'map1') {
            if (this.gameMap1Node && !this.gameMap1Node.active) {
                this.gameMap1Node.active = true;
            }
            if (this.gameMap2Node && this.gameMap2Node.active) {
                this.gameMap2Node.active = false;
            }
        }
        else {
            if (this.gameMap2Node && !this.gameMap2Node.active) {
                this.gameMap2Node.active = true;
            }
            if (this.gameMap1Node && this.gameMap1Node.active) {
                this.gameMap1Node.active = false;
            }
        }
    };
    /**
     * 隐藏所有地图节点
     */
    LevelPageController.prototype.hideAllMapNodes = function () {
        var allMapNodes = [
            this.qipan8x8Node,
            this.qipan8x9Node,
            this.qipan9x9Node,
            this.qipan9x10Node,
            this.qipan10x10Node,
            this.levelS001Node,
            this.levelS002Node,
            this.levelS003Node,
            this.levelS004Node,
            this.levelS005Node,
            this.levelS006Node
        ];
        allMapNodes.forEach(function (node) {
            if (node) {
                node.active = false;
            }
        });
    };
    /**
     * 设置当前关卡（从外部调用）
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.setCurrentLevel = function (levelNumber) {
        // 重置关卡状态（包括清除测试预制体）
        this.resetLevelState();
        this.currentLevel = levelNumber;
        // 立即根据关卡数切换地图显示
        this.enterLevel(levelNumber);
    };
    /**
     * 获取当前关卡编号
     */
    LevelPageController.prototype.getCurrentLevel = function () {
        return this.currentLevel;
    };
    /**
     * 获取当前关卡信息
     */
    LevelPageController.prototype.getCurrentLevelInfo = function () {
        return this.currentLevelInfo;
    };
    /**
     * 获取当前房间ID
     */
    LevelPageController.prototype.getCurrentRoomId = function () {
        return this.currentRoomId;
    };
    /**
     * 隐藏所有地图容器
     */
    LevelPageController.prototype.hideAllMapContainers = function () {
        // 确保 level_page 节点是激活的
        if (this.levelPageNode) {
            this.levelPageNode.active = true;
        }
        // 隐藏两个主要的地图容器
        if (this.gameMap1Node) {
            this.gameMap1Node.active = false;
        }
        if (this.gameMap2Node) {
            this.gameMap2Node.active = false;
        }
        // 同时隐藏所有具体的地图节点
        this.hideAllMapNodes();
    };
    /**
     * 显示 game_map_1 容器（方形地图）
     */
    LevelPageController.prototype.showGameMap1 = function () {
        if (this.gameMap1Node) {
            this.gameMap1Node.active = true;
        }
        else {
            cc.warn("❌ game_map_1 节点未找到");
        }
    };
    /**
     * 显示 game_map_2 容器（特殊关卡）
     */
    LevelPageController.prototype.showGameMap2 = function () {
        if (this.gameMap2Node) {
            this.gameMap2Node.active = true;
        }
        else {
            cc.warn("❌ game_map_2 节点未找到");
        }
    };
    /**
     * 获取当前激活的单机棋盘控制器
     */
    LevelPageController.prototype.getCurrentSingleChessBoard = function () {
        return this.currentSingleChessBoard;
    };
    /**
     * 处理单机模式的点击响应
     * @param response 点击响应数据
     */
    LevelPageController.prototype.handleSingleModeClickResponse = function (response) {
        if (this.currentSingleChessBoard) {
            var x = response.x, y = response.y, result = response.result, chainReaction = response.chainReaction;
            // 处理点击结果
            if (x !== undefined && y !== undefined && result !== undefined) {
                this.currentSingleChessBoard.handleClickResponse(x, y, result);
            }
            // 处理连锁反应
            if (chainReaction && Array.isArray(chainReaction)) {
                this.currentSingleChessBoard.handleChainReaction(chainReaction);
            }
        }
    };
    /**
     * 处理单机模式游戏结束
     * @param gameEndData 游戏结束数据
     */
    LevelPageController.prototype.handleSingleModeGameEnd = function (gameEndData) {
        if (this.currentSingleChessBoard) {
            // 禁用棋盘触摸
            this.currentSingleChessBoard.disableAllGridTouch();
            // 处理游戏结束
            this.currentSingleChessBoard.onLevelGameEnd();
        }
    };
    /**
     * 重置当前单机棋盘（仅在开始新游戏时调用）
     */
    LevelPageController.prototype.resetCurrentSingleChessBoard = function () {
        if (this.currentSingleChessBoard) {
            // 重置棋盘状态（清理所有预制体和格子状态）
            this.currentSingleChessBoard.resetBoard();
            // 重新启用触摸事件
            this.currentSingleChessBoard.enableAllGridTouch();
        }
    };
    /**
     * 注册单机模式消息处理器
     */
    LevelPageController.prototype.registerSingleModeMessageHandlers = function () {
        // 监听WebSocket消息
        GameMgr_1.GameMgr.Event.AddEventListener(EventCenter_1.EventType.ReceiveMessage, this.onReceiveMessage, this);
    };
    /**
     * 取消单机模式消息监听
     */
    LevelPageController.prototype.unregisterSingleModeMessageHandlers = function () {
        GameMgr_1.GameMgr.Event.RemoveEventListener(EventCenter_1.EventType.ReceiveMessage, this.onReceiveMessage, this);
    };
    /**
     * 处理接收到的WebSocket消息
     * @param messageBean 消息数据
     */
    LevelPageController.prototype.onReceiveMessage = function (messageBean) {
        switch (messageBean.msgId) {
            case MessageId_1.MessageId.MsgTypeLevelClickBlock:
                this.onLevelClickBlockResponse(messageBean.data);
                break;
            case MessageId_1.MessageId.MsgTypeLevelGameEnd:
                this.onLevelGameEnd(messageBean.data);
                break;
        }
    };
    /**
     * 处理LevelClickBlock响应
     * @param response 点击响应数据
     */
    LevelPageController.prototype.onLevelClickBlockResponse = function (response) {
        if (this.currentSingleChessBoard) {
            // 解构响应数据，支持多种可能的字段名
            var x = response.x, y = response.y, q = response.q, r = response.r, result = response.result, action = response.action, chainReaction = response.chainReaction, revealedGrids = response.revealedGrids, floodFill = response.floodFill, revealedBlocks = response.revealedBlocks, floodFillResults = response.floodFillResults // 单机模式使用这个字段
            ;
            // 判断当前使用的是哪种控制器
            var isUsingHexController = (this.currentSingleChessBoard === this.hexSingleChessBoardController);
            // 根据控制器类型决定坐标处理方式
            var coordX = void 0, coordY = void 0, coordQ = void 0, coordR = void 0;
            var hasValidCoord = false;
            if (isUsingHexController) {
                // 六边形控制器：优先使用六边形坐标，如果没有则将x,y映射为q,r
                if (q !== undefined && r !== undefined) {
                    coordQ = q;
                    coordR = r;
                    hasValidCoord = true;
                }
                else if (x !== undefined && y !== undefined) {
                    // 服务器返回x,y字段，但实际是六边形坐标：x=q, y=r
                    coordQ = x; // x 就是 q
                    coordR = y; // y 就是 r
                    hasValidCoord = true;
                }
            }
            else {
                // 四边形控制器：使用四边形坐标
                if (x !== undefined && y !== undefined) {
                    coordX = x;
                    coordY = y;
                    hasValidCoord = true;
                }
                else if (q !== undefined && r !== undefined) {
                    console.warn("\u26A0\uFE0F \u56DB\u8FB9\u5F62\u63A7\u5236\u5668\u6536\u5230\u516D\u8FB9\u5F62\u5750\u6807 (" + q + ", " + r + ")\uFF0C\u8FD9\u53EF\u80FD\u4E0D\u6B63\u786E");
                    hasValidCoord = false;
                }
            }
            if (hasValidCoord && result !== undefined) {
                if (action === 2) {
                    // 标记/取消标记操作，不调用handleClickResponse，避免格子消失
                    // 不调用 handleClickResponse，因为标记操作不应该隐藏格子
                }
                else if (action === 1) {
                    // 挖掘操作
                    // 检查是否点到炸弹
                    if (result === "boom" || result === "mine") {
                        this.lastClickWasBomb = true;
                    }
                    // 根据控制器类型调用对应的方法
                    if (isUsingHexController) {
                        // 六边形控制器
                        this.currentSingleChessBoard.handleClickResponse(coordQ, coordR, result);
                    }
                    else {
                        // 四边形控制器
                        this.currentSingleChessBoard.handleClickResponse(coordX, coordY, result);
                    }
                    // 处理连锁展开数据
                    if (floodFillResults && Array.isArray(floodFillResults) && floodFillResults.length > 0) {
                        if (isUsingHexController) {
                            // 六边形控制器使用handleChainReaction方法
                            this.currentSingleChessBoard.handleChainReaction(floodFillResults);
                        }
                        else {
                            // 四边形控制器使用handleFloodFillResults方法
                            this.currentSingleChessBoard.handleFloodFillResults(floodFillResults);
                        }
                    }
                }
                else {
                    // 其他操作，默认按挖掘处理
                    if (isUsingHexController) {
                        this.currentSingleChessBoard.handleClickResponse(coordQ, coordR, result);
                    }
                    else {
                        this.currentSingleChessBoard.handleClickResponse(coordX, coordY, result);
                    }
                }
            }
            else {
                console.warn("⚠️ 响应数据缺少有效坐标或结果信息");
                console.warn("   \u63A7\u5236\u5668\u7C7B\u578B: " + (isUsingHexController ? '六边形' : '四边形'));
                console.warn("   \u5750\u6807\u6570\u636E: x=" + x + ", y=" + y + ", q=" + q + ", r=" + r);
                console.warn("   \u7ED3\u679C: " + result);
            }
        }
    };
    /**
     * 处理LevelGameEnd通知
     * @param gameEndData 游戏结束数据
     */
    LevelPageController.prototype.onLevelGameEnd = function (gameEndData) {
        var _this = this;
        if (this.currentSingleChessBoard) {
            // 禁用棋盘触摸
            this.currentSingleChessBoard.disableAllGridTouch();
            // 处理游戏结束（不清理数据）
            this.currentSingleChessBoard.onLevelGameEnd();
        }
        // 禁用返回按钮，防止游戏结束时玩家误点击
        this.disableBackButton();
        // 检查是否点到了炸弹，如果是游戏失败且点到炸弹，则延迟显示结算页面
        var isGameFailed = !gameEndData.success;
        var hasBombExploded = this.lastClickWasBomb ||
            (this.currentSingleChessBoard && this.currentSingleChessBoard.hasBombExplodedInThisGame());
        if (isGameFailed && hasBombExploded) {
            // 点到炸弹导致的游戏失败，延迟1.5秒显示结算页面
            this.scheduleOnce(function () {
                _this.showLevelSettlement(gameEndData);
                // 重置标记
                _this.lastClickWasBomb = false;
            }, 1.5);
        }
        else {
            // 其他情况，立即显示结算页面
            this.showLevelSettlement(gameEndData);
            // 重置标记
            this.lastClickWasBomb = false;
        }
    };
    /**
     * 设置结算页面按钮事件
     */
    LevelPageController.prototype.setupSettlementButtons = function () {
        var _this = this;
        // 再来一次按钮
        if (this.retryButton) {
            this.retryButton.node.on('click', this.onRetryButtonClick, this);
        }
        // 下一关按钮
        if (this.nextLevelButton) {
            this.nextLevelButton.node.on('click', this.onNextLevelButtonClick, this);
        }
        // 退出按钮 - 使用按压效果，模仿其他返回按钮
        if (this.exitButton) {
            Tools_1.Tools.imageButtonClick(this.exitButton.node, Config_1.Config.buttonRes + 'board_btn_back_normal', Config_1.Config.buttonRes + 'board_btn_back_pressed', function () {
                _this.onExitButtonClick();
            });
        }
    };
    /**
     * 显示结算页面
     * @param gameEndData 游戏结束数据
     */
    LevelPageController.prototype.showLevelSettlement = function (gameEndData) {
        if (!this.levelSettlementNode) {
            console.error("levelSettlementNode 未配置");
            return;
        }
        // 显示结算页面
        this.levelSettlementNode.active = true;
        // 根据游戏结果显示对应的背景 - 修复成功判断逻辑
        var isSuccess = gameEndData.isSuccess || gameEndData.success || gameEndData.isWin || gameEndData.gameStatus === 1;
        if (isSuccess) {
            // 成功 - 显示胜利背景
            if (this.winBgNode) {
                this.winBgNode.active = true;
            }
            if (this.loseBgNode) {
                this.loseBgNode.active = false;
            }
        }
        else {
            // 失败 - 显示失败背景
            if (this.loseBgNode) {
                this.loseBgNode.active = true;
            }
            if (this.winBgNode) {
                this.winBgNode.active = false;
            }
        }
    };
    /**
     * 再来一次按钮点击事件
     */
    LevelPageController.prototype.onRetryButtonClick = function () {
        // 关闭结算页面
        this.hideLevelSettlement();
        // 重新启用返回按钮
        this.enableBackButton();
        // 注意：不在这里重置棋盘，等收到ExtendLevelInfo响应时再重置
        // 这样可以避免过早清理，让玩家在点击按钮后还能短暂看到游玩痕迹
        // 发送当前关卡的ExtendLevelInfo
        this.sendExtendLevelInfo(this.currentLevel);
    };
    /**
     * 下一关按钮点击事件
     */
    LevelPageController.prototype.onNextLevelButtonClick = function () {
        // 关闭结算页面
        this.hideLevelSettlement();
        // 重新启用返回按钮
        this.enableBackButton();
        // 进入下一关
        var nextLevel = this.currentLevel + 1;
        this.setCurrentLevel(nextLevel);
        // 注意：不在这里重置棋盘，等收到ExtendLevelInfo响应时再重置
        // 这样可以避免过早清理，让玩家在点击按钮后还能短暂看到游玩痕迹
        // 发送下一关的ExtendLevelInfo
        this.sendExtendLevelInfo(nextLevel);
    };
    /**
     * 退出按钮点击事件
     */
    LevelPageController.prototype.onExitButtonClick = function () {
        // 关闭结算页面
        this.hideLevelSettlement();
        // 重新启用返回按钮（虽然要退出了，但保持一致性）
        this.enableBackButton();
        // 返回到关卡选择页面（匹配界面）
        this.returnToLevelSelect();
    };
    /**
     * 隐藏结算页面
     */
    LevelPageController.prototype.hideLevelSettlement = function () {
        if (this.levelSettlementNode) {
            this.levelSettlementNode.active = false;
        }
    };
    /**
     * 发送ExtendLevelInfo消息
     * @param levelId 关卡ID
     */
    LevelPageController.prototype.sendExtendLevelInfo = function (levelId) {
        var request = {
            levelId: levelId
        };
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeExtendLevelInfo, request);
    };
    /**
     * 设置测试按钮
     */
    LevelPageController.prototype.setupDebugButton = function () {
        if (this.debugShowMinesButton) {
            this.debugShowMinesButton.node.on('click', this.onDebugShowMinesClick, this);
        }
    };
    /**
     * 测试按钮点击事件 - 发送DebugShowMines消息
     */
    LevelPageController.prototype.onDebugShowMinesClick = function () {
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeDebugShowMines, {});
    };
    /**
     * 判断是否在单机模式
     */
    LevelPageController.prototype.isInSingleMode = function () {
        // 单机模式的判断逻辑：当前页面是关卡页面且有有效的房间ID
        return this.currentRoomId > 0;
    };
    /**
     * 处理DebugShowMines响应，在炸弹位置生成测试预制体
     * @param minePositions 炸弹位置数组 [{x: number, y: number}]
     */
    LevelPageController.prototype.handleDebugShowMines = function (minePositions) {
        var _this = this;
        if (!this.debugMinePrefab) {
            cc.warn("debugMinePrefab 预制体未设置，无法显示测试标记");
            return;
        }
        if (!this.currentSingleChessBoard) {
            cc.warn("当前没有激活的单机棋盘");
            return;
        }
        if (!minePositions || !Array.isArray(minePositions) || minePositions.length === 0) {
            cc.warn("地雷位置数据无效:", minePositions);
            return;
        }
        // 先尝试直接创建一个测试预制体，不使用延迟
        if (minePositions.length > 0) {
            var firstPosition = minePositions[0];
            // 检查坐标字段
            var coordX = void 0, coordY = void 0;
            var pos = firstPosition; // 使用any类型避免TypeScript报错
            if (pos.x !== undefined && pos.y !== undefined) {
                coordX = pos.x;
                coordY = pos.y;
            }
            else if (pos.q !== undefined && pos.r !== undefined) {
                coordX = pos.q;
                coordY = pos.r;
            }
            else {
                console.error("\u274C \u65E0\u6CD5\u8BC6\u522B\u5750\u6807\u5B57\u6BB5:", firstPosition);
                return;
            }
            // 直接调用，不使用延迟
            this.createDebugMinePrefab(coordX, coordY);
        }
        // 在每个炸弹位置生成测试预制体
        minePositions.forEach(function (position, index) {
            var pos = position; // 使用any类型避免TypeScript报错
            // 获取坐标
            var coordX, coordY;
            if (pos.x !== undefined && pos.y !== undefined) {
                coordX = pos.x;
                coordY = pos.y;
            }
            else if (pos.q !== undefined && pos.r !== undefined) {
                coordX = pos.q;
                coordY = pos.r;
            }
            else {
                console.error("\u274C \u5730\u96F7\u4F4D\u7F6E" + index + "\u5750\u6807\u5B57\u6BB5\u65E0\u6548:", position);
                return;
            }
            if (index === 0) {
                // 第一个不延迟，立即执行（已经在上面处理过了，跳过）
                return;
            }
            else {
                // 其他的使用延迟 - 修复闭包问题
                var capturedX_1 = coordX; // 捕获当前值
                var capturedY_1 = coordY; // 捕获当前值
                _this.scheduleOnce(function () {
                    _this.createDebugMinePrefab(capturedX_1, capturedY_1);
                }, index * 0.1);
            }
        });
    };
    /**
     * 在指定位置创建测试预制体
     * @param x 格子x坐标（四边形）或q坐标（六边形）
     * @param y 格子y坐标（四边形）或r坐标（六边形）
     */
    LevelPageController.prototype.createDebugMinePrefab = function (x, y) {
        // 检查坐标是否有效
        if (x === undefined || y === undefined || x === null || y === null) {
            console.error("\u274C \u65E0\u6548\u7684\u5750\u6807\u53C2\u6570: x=" + x + ", y=" + y);
            console.error("   x\u7C7B\u578B: " + typeof x + ", y\u7C7B\u578B: " + typeof y);
            console.error("   \u8C03\u7528\u5806\u6808:", new Error().stack);
            return;
        }
        if (!this.debugMinePrefab) {
            cc.error("debugMinePrefab 为空，无法创建测试预制体");
            return;
        }
        if (!this.currentSingleChessBoard) {
            cc.error("currentSingleChessBoard 为空，无法创建测试预制体");
            return;
        }
        try {
            // 判断当前使用的是哪种控制器
            var isUsingHexController = (this.currentSingleChessBoard === this.hexSingleChessBoardController);
            var debugNode = null;
            if (isUsingHexController) {
                // 六边形控制器：x实际是q，y实际是r
                debugNode = this.currentSingleChessBoard.createCustomPrefab(x, y, // 对于六边形，x就是q，y就是r
                this.debugMinePrefab, "DebugMine_" + x + "_" + y);
            }
            else {
                // 四边形控制器
                debugNode = this.currentSingleChessBoard.createCustomPrefab(x, y, this.debugMinePrefab, "DebugMine_" + x + "_" + y);
            }
            if (debugNode) {
                // 将创建的节点存储起来，用于后续清理
                this.debugMineNodes.push(debugNode);
            }
            else {
                cc.error("\u274C \u5728\u4F4D\u7F6E (" + x + ", " + y + ") \u521B\u5EFA\u6D4B\u8BD5\u9884\u5236\u4F53\u5931\u8D25\uFF0C\u8FD4\u56DE\u503C\u4E3A\u7A7A");
            }
        }
        catch (error) {
            cc.error("\u274C \u521B\u5EFA\u6D4B\u8BD5\u9884\u5236\u4F53\u65F6\u53D1\u751F\u9519\u8BEF:", error);
        }
    };
    /**
     * 清除所有测试预制体
     */
    LevelPageController.prototype.clearDebugMines = function () {
        this.debugMineNodes.forEach(function (node, index) {
            if (node && cc.isValid(node)) {
                node.destroy();
            }
        });
        // 清空数组
        this.debugMineNodes = [];
    };
    /**
     * 重置关卡状态（包括清除测试预制体）
     */
    LevelPageController.prototype.resetLevelState = function () {
        this.clearDebugMines();
        // 这里可以添加其他需要重置的状态
    };
    LevelPageController.prototype.onDestroy = function () {
        // 取消消息监听
        this.unregisterSingleModeMessageHandlers();
        // 清理测试预制体
        this.clearDebugMines();
        // 清理按钮事件
        if (this.retryButton) {
            this.retryButton.node.off('click', this.onRetryButtonClick, this);
        }
        if (this.nextLevelButton) {
            this.nextLevelButton.node.off('click', this.onNextLevelButtonClick, this);
        }
        // 退出按钮使用 Tools.imageButtonClick，会自动管理事件，无需手动清理
        if (this.debugShowMinesButton) {
            this.debugShowMinesButton.node.off('click', this.onDebugShowMinesClick, this);
        }
    };
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "backButton", void 0);
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "startGameButton", void 0);
    __decorate([
        property(cc.Label)
    ], LevelPageController.prototype, "mineCountLabel", void 0);
    __decorate([
        property(cc.Label)
    ], LevelPageController.prototype, "currentLevelLabel", void 0);
    __decorate([
        property(LeaveDialogController_1.default)
    ], LevelPageController.prototype, "leaveDialogController", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelPageNode", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "gameMap1Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "gameMap2Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan8x8Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan8x9Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan9x9Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan9x10Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan10x10Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS001Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS002Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS003Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS004Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS005Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS006Node", void 0);
    __decorate([
        property(SingleChessBoardController_1.default)
    ], LevelPageController.prototype, "singleChessBoardController", void 0);
    __decorate([
        property(HexSingleChessBoardController_1.default)
    ], LevelPageController.prototype, "hexSingleChessBoardController", void 0);
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "debugShowMinesButton", void 0);
    __decorate([
        property(cc.Prefab)
    ], LevelPageController.prototype, "debugMinePrefab", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelSettlementNode", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "boardBgNode", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "loseBgNode", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "winBgNode", void 0);
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "retryButton", void 0);
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "nextLevelButton", void 0);
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "exitButton", void 0);
    LevelPageController = __decorate([
        ccclass
    ], LevelPageController);
    return LevelPageController;
}(cc.Component));
exports.default = LevelPageController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0cy9zY3JpcHRzL2xldmVsL0xldmVsUGFnZUNvbnRyb2xsZXIudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLG9CQUFvQjtBQUNwQiw0RUFBNEU7QUFDNUUsbUJBQW1CO0FBQ25CLHNGQUFzRjtBQUN0Riw4QkFBOEI7QUFDOUIsc0ZBQXNGOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFHdEYsdUVBQWtFO0FBQ2xFLHVDQUFzQztBQUN0Qyx5Q0FBd0M7QUFDeEMsc0VBQStFO0FBQy9FLHVGQUFrRjtBQUNsRiw2RkFBd0Y7QUFDeEYsNERBQTJEO0FBQzNELDhDQUE2QztBQUM3Qyw2Q0FBNEM7QUFDNUMscURBQWtEO0FBRzVDLElBQUEsS0FBd0IsRUFBRSxDQUFDLFVBQVUsRUFBbkMsT0FBTyxhQUFBLEVBQUUsUUFBUSxjQUFrQixDQUFDO0FBRzVDO0lBQWlELHVDQUFZO0lBQTdEO1FBQUEscUVBc3NDQztRQXBzQ0csT0FBTztRQUVQLGdCQUFVLEdBQVksSUFBSSxDQUFDO1FBRTNCLFNBQVM7UUFFVCxxQkFBZSxHQUFjLElBQUksQ0FBQztRQUVsQyxVQUFVO1FBRVYsb0JBQWMsR0FBYSxJQUFJLENBQUM7UUFFaEMsWUFBWTtRQUVaLHVCQUFpQixHQUFhLElBQUksQ0FBQztRQUVuQyxTQUFTO1FBRVQsMkJBQXFCLEdBQTBCLElBQUksQ0FBQztRQUVwRCxlQUFlO1FBRWYsbUJBQWEsR0FBWSxJQUFJLENBQUM7UUFFOUIsZUFBZTtRQUVmLGtCQUFZLEdBQVksSUFBSSxDQUFDO1FBRTdCLGVBQWU7UUFFZixrQkFBWSxHQUFZLElBQUksQ0FBQztRQUU3QixXQUFXO1FBRVgsa0JBQVksR0FBWSxJQUFJLENBQUMsQ0FBQywwQ0FBMEM7UUFHeEUsa0JBQVksR0FBWSxJQUFJLENBQUMsQ0FBQywwQ0FBMEM7UUFHeEUsa0JBQVksR0FBWSxJQUFJLENBQUMsQ0FBQywwQ0FBMEM7UUFHeEUsbUJBQWEsR0FBWSxJQUFJLENBQUMsQ0FBQywyQ0FBMkM7UUFHMUUsb0JBQWMsR0FBWSxJQUFJLENBQUMsQ0FBQyw0Q0FBNEM7UUFFNUUsV0FBVztRQUVYLG1CQUFhLEdBQVksSUFBSSxDQUFDLENBQUMsMkNBQTJDO1FBRzFFLG1CQUFhLEdBQVksSUFBSSxDQUFDLENBQUMsMkNBQTJDO1FBRzFFLG1CQUFhLEdBQVksSUFBSSxDQUFDLENBQUMsMkNBQTJDO1FBRzFFLG1CQUFhLEdBQVksSUFBSSxDQUFDLENBQUMsMkNBQTJDO1FBRzFFLG1CQUFhLEdBQVksSUFBSSxDQUFDLENBQUMsMkNBQTJDO1FBRzFFLG1CQUFhLEdBQVksSUFBSSxDQUFDLENBQUMsMkNBQTJDO1FBRTFFLFlBQVk7UUFFWixnQ0FBMEIsR0FBK0IsSUFBSSxDQUFDO1FBRTlELGVBQWU7UUFFZixtQ0FBNkIsR0FBa0MsSUFBSSxDQUFDO1FBRXBFLG1CQUFtQjtRQUVuQiwwQkFBb0IsR0FBYyxJQUFJLENBQUM7UUFFdkMsa0JBQWtCO1FBRWxCLHFCQUFlLEdBQWMsSUFBSSxDQUFDO1FBRWxDLG9CQUFvQjtRQUNaLG9CQUFjLEdBQWMsRUFBRSxDQUFDO1FBRXZDLFdBQVc7UUFFWCx5QkFBbUIsR0FBWSxJQUFJLENBQUMsQ0FBQyxxQkFBcUI7UUFHMUQsaUJBQVcsR0FBWSxJQUFJLENBQUMsQ0FBQyw4QkFBOEI7UUFHM0QsZ0JBQVUsR0FBWSxJQUFJLENBQUMsQ0FBQyxzQ0FBc0M7UUFHbEUsZUFBUyxHQUFZLElBQUksQ0FBQyxDQUFDLHFDQUFxQztRQUdoRSxpQkFBVyxHQUFjLElBQUksQ0FBQyxDQUFDLFNBQVM7UUFHeEMscUJBQWUsR0FBYyxJQUFJLENBQUMsQ0FBQyxRQUFRO1FBRzNDLGdCQUFVLEdBQWMsSUFBSSxDQUFDLENBQUMsT0FBTztRQUVyQyxTQUFTO1FBQ0Qsa0JBQVksR0FBVyxDQUFDLENBQUM7UUFDekIsc0JBQWdCLEdBQTRCLElBQUksQ0FBQztRQUNqRCxtQkFBYSxHQUFXLENBQUMsQ0FBQyxDQUFDLGNBQWM7UUFDekMsNkJBQXVCLEdBQStELElBQUksQ0FBQyxDQUFDLFlBQVk7UUFFaEgsZ0JBQWdCO1FBQ1Isc0JBQWdCLEdBQVksS0FBSyxDQUFDO1FBRTFDLFNBQVM7UUFDRCxzQkFBZ0IsR0FBWSxJQUFJLENBQUMsQ0FBQyxjQUFjO1FBQ2hELGdCQUFVLEdBQVksS0FBSyxDQUFDLENBQUMsU0FBUzs7SUE2a0NsRCxDQUFDO0lBM2tDRyxvQ0FBTSxHQUFOO1FBQUEsaUJBb0JDO1FBbkJHLDBDQUEwQztRQUMxQyxJQUFJLElBQUksQ0FBQyxVQUFVLEVBQUU7WUFDakIsYUFBSyxDQUFDLGdCQUFnQixDQUFDLElBQUksQ0FBQyxVQUFVLEVBQUUsZUFBTSxDQUFDLFNBQVMsR0FBRyxzQkFBc0IsRUFBRSxlQUFNLENBQUMsU0FBUyxHQUFHLHVCQUF1QixFQUFFO2dCQUMzSCxLQUFJLENBQUMsaUJBQWlCLEVBQUUsQ0FBQztZQUM3QixDQUFDLENBQUMsQ0FBQztTQUNOO1FBRUQsZUFBZTtRQUNmLElBQUksSUFBSSxDQUFDLGVBQWUsRUFBRTtZQUN0QixJQUFJLENBQUMsZUFBZSxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUMsT0FBTyxFQUFFLElBQUksQ0FBQyxzQkFBc0IsRUFBRSxJQUFJLENBQUMsQ0FBQztTQUM1RTtRQUVELGFBQWE7UUFDYixJQUFJLENBQUMsaUNBQWlDLEVBQUUsQ0FBQztRQUV6QyxhQUFhO1FBQ2IsSUFBSSxDQUFDLHNCQUFzQixFQUFFLENBQUM7UUFFOUIsaURBQWlEO0lBQ3JELENBQUM7SUFFRCxtQ0FBSyxHQUFMO1FBQ0ksZUFBZTtRQUNmLElBQUksQ0FBQyxlQUFlLEVBQUUsQ0FBQztRQUV2QixhQUFhO1FBQ2IsSUFBSSxDQUFDLGdCQUFnQixFQUFFLENBQUM7SUFDNUIsQ0FBQztJQUVEOztPQUVHO0lBQ0ssK0NBQWlCLEdBQXpCO1FBQ0ksbUNBQW1DO1FBQ25DLElBQUksSUFBSSxDQUFDLGFBQWEsSUFBSSxDQUFDLEVBQUU7WUFFekIsSUFBSSxDQUFDLG1CQUFtQixFQUFFLENBQUM7WUFDM0IsT0FBTztTQUNWO1FBRUQsb0NBQW9DO1FBQ3BDLElBQUksSUFBSSxDQUFDLHFCQUFxQixFQUFFO1lBRTVCLElBQUksQ0FBQyxxQkFBcUIsQ0FBQyxJQUFJLENBQUMsQ0FBQyxFQUFFO1lBRW5DLENBQUMsRUFBRSxJQUFJLENBQUMsYUFBYSxDQUFDLENBQUM7U0FDMUI7YUFBTTtZQUNILEVBQUUsQ0FBQyxJQUFJLENBQUMsMkJBQTJCLENBQUMsQ0FBQztTQUN4QztJQUNMLENBQUM7SUFFRDs7T0FFRztJQUNLLGlEQUFtQixHQUEzQjtRQUNJLG9DQUFvQztRQUNwQyxJQUFNLGlCQUFpQixHQUFHLEVBQUUsQ0FBQyxJQUFJLENBQUMsb0JBQW9CLENBQUMsSUFBSSxFQUFFLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDO1FBQ2xGLElBQUksaUJBQWlCLEVBQUU7WUFDbkIsSUFBTSxhQUFhLEdBQUcsaUJBQWlCLENBQUMsWUFBWSxDQUFDLGlDQUF1QixDQUFDLENBQUM7WUFDOUUsSUFBSSxhQUFhLEVBQUU7Z0JBQ2YsYUFBYSxDQUFDLGNBQWMsQ0FBQyxrQ0FBUSxDQUFDLFNBQVMsQ0FBQyxDQUFDO2FBQ3BEO1NBQ0o7SUFDTCxDQUFDO0lBRUQ7O09BRUc7SUFDSywrQ0FBaUIsR0FBekI7UUFDSSxJQUFJLElBQUksQ0FBQyxVQUFVLEVBQUU7WUFDakIsY0FBYztZQUNkLElBQUksQ0FBQyxVQUFVLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLFdBQVcsQ0FBQyxDQUFDO1lBQ25ELElBQUksQ0FBQyxVQUFVLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLFNBQVMsQ0FBQyxDQUFDO1lBQ2pELElBQUksQ0FBQyxVQUFVLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLFlBQVksQ0FBQyxDQUFDO1lBRXBELGtCQUFrQjtZQUNsQixJQUFJLENBQUMsVUFBVSxDQUFDLE9BQU8sR0FBRyxHQUFHLENBQUM7U0FHakM7SUFDTCxDQUFDO0lBRUQ7O09BRUc7SUFDSyw4Q0FBZ0IsR0FBeEI7UUFBQSxpQkFZQztRQVhHLElBQUksSUFBSSxDQUFDLFVBQVUsRUFBRTtZQUNqQixVQUFVO1lBQ1YsSUFBSSxDQUFDLFVBQVUsQ0FBQyxPQUFPLEdBQUcsR0FBRyxDQUFDO1lBRTlCLGVBQWU7WUFDZixhQUFLLENBQUMsZ0JBQWdCLENBQUMsSUFBSSxDQUFDLFVBQVUsRUFBRSxlQUFNLENBQUMsU0FBUyxHQUFHLHNCQUFzQixFQUFFLGVBQU0sQ0FBQyxTQUFTLEdBQUcsdUJBQXVCLEVBQUU7Z0JBQzNILEtBQUksQ0FBQyxpQkFBaUIsRUFBRSxDQUFDO1lBQzdCLENBQUMsQ0FBQyxDQUFDO1NBR047SUFDTCxDQUFDO0lBRUQ7O09BRUc7SUFDSyxvREFBc0IsR0FBOUI7UUFDSSxrREFBa0Q7UUFDbEQsa0JBQWtCO0lBRXRCLENBQUM7SUFFRDs7O09BR0c7SUFDSSwrQ0FBaUIsR0FBeEIsVUFBeUIsU0FBa0M7UUFFdkQsSUFBSSxDQUFDLGdCQUFnQixHQUFHLFNBQVMsQ0FBQztRQUVsQyxpQkFBaUI7UUFDakIsSUFBSSxTQUFTLENBQUMsTUFBTSxFQUFFO1lBQ2xCLElBQUksQ0FBQyxhQUFhLEdBQUcsU0FBUyxDQUFDLE1BQU0sQ0FBQztTQUN6QztRQUVELFlBQVk7UUFDWixJQUFJLFNBQVMsQ0FBQyxXQUFXLEtBQUssSUFBSSxFQUFFO1lBQ2hDLGlCQUFPLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDO1lBRXZDLDZCQUE2QjtZQUM3QixJQUFJLFNBQVMsQ0FBQyxPQUFPLEVBQUU7Z0JBQ25CLElBQUksQ0FBQyxZQUFZLEdBQUcsU0FBUyxDQUFDLE9BQU8sQ0FBQzthQUN6QztZQUVELFVBQVU7WUFDVixJQUFJLFNBQVMsQ0FBQyxjQUFjLEtBQUssU0FBUyxFQUFFO2dCQUN4QyxJQUFJLENBQUMsaUJBQWlCLENBQUMsU0FBUyxDQUFDLGNBQWMsQ0FBQyxDQUFDO2FBQ3BEO2lCQUFNO2dCQUNILElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxTQUFTLENBQUMsU0FBUyxDQUFDLENBQUM7YUFDL0M7WUFFRCxPQUFPO1lBQ1AsSUFBSSxDQUFDLFVBQVUsQ0FBQyxJQUFJLENBQUMsWUFBWSxDQUFDLENBQUM7WUFFbkMsdUJBQXVCO1lBQ3ZCLElBQUksSUFBSSxDQUFDLHVCQUF1QixFQUFFO2dCQUM5QixrQkFBa0I7Z0JBQ2xCLElBQUksNEJBQTRCLElBQUksSUFBSSxDQUFDLHVCQUF1QixFQUFFO29CQUM3RCxJQUFJLENBQUMsdUJBQStCLENBQUMsMEJBQTBCLENBQUMsU0FBUyxDQUFDLENBQUM7aUJBQy9FO3FCQUFNO29CQUNILE9BQU8sQ0FBQyxJQUFJLENBQUMsa0JBQWtCLENBQUMsQ0FBQztpQkFDcEM7YUFDSjtTQUNKO2FBQU07WUFDSCxVQUFVO1lBQ1YsaUJBQU8sQ0FBQyxPQUFPLENBQUMsR0FBRyxDQUFDLFdBQVcsQ0FBQyxDQUFDO1lBRWpDLG9CQUFvQjtZQUNwQixJQUFJLENBQUMsZUFBZSxFQUFFLENBQUM7WUFFdkIsV0FBVztZQUNYLElBQUksQ0FBQyxnQkFBZ0IsR0FBRyxLQUFLLENBQUM7WUFFOUIsMkJBQTJCO1lBQzNCLElBQUksSUFBSSxDQUFDLHVCQUF1QixFQUFFO2dCQUM5QixJQUFJLENBQUMsdUJBQXVCLENBQUMsVUFBVSxFQUFFLENBQUM7YUFDN0M7WUFFRCxVQUFVO1lBQ1YsSUFBSSxDQUFDLGlCQUFpQixDQUFDLFNBQVMsQ0FBQyxTQUFTLENBQUMsQ0FBQztZQUU1Qyw4QkFBOEI7WUFDOUIsNEJBQTRCO1lBQzVCLElBQUksQ0FBQyxVQUFVLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFDO1lBRW5DLGtDQUFrQztZQUNsQyxJQUFJLElBQUksQ0FBQyx1QkFBdUIsRUFBRTtnQkFDOUIsSUFBSSxDQUFDLHVCQUF1QixDQUFDLGlCQUFpQixFQUFFLENBQUM7YUFDcEQ7U0FDSjtJQUNMLENBQUM7SUFFRDs7O09BR0c7SUFDSywrQ0FBaUIsR0FBekIsVUFBMEIsU0FBaUI7UUFDdkMsSUFBSSxJQUFJLENBQUMsY0FBYyxFQUFFO1lBQ3JCLElBQUksQ0FBQyxjQUFjLENBQUMsTUFBTSxHQUFHLFNBQVMsQ0FBQyxRQUFRLEVBQUUsQ0FBQztTQUVyRDtJQUNMLENBQUM7SUFFRDs7O09BR0c7SUFDSyxrREFBb0IsR0FBNUIsVUFBNkIsV0FBbUI7UUFDNUMsSUFBSSxJQUFJLENBQUMsaUJBQWlCLEVBQUU7WUFDeEIsSUFBSSxDQUFDLGlCQUFpQixDQUFDLE1BQU0sR0FBRyxXQUFJLFdBQVcsV0FBRyxDQUFDO1NBRXREO0lBQ0wsQ0FBQztJQUVEOzs7T0FHRztJQUNLLHdDQUFVLEdBQWxCLFVBQW1CLFdBQW1CO1FBQ2xDLFNBQVM7UUFDVCxJQUFJLElBQUksQ0FBQyxVQUFVLEVBQUU7WUFDakIsT0FBTztTQUNWO1FBQ0QsSUFBSSxDQUFDLFVBQVUsR0FBRyxJQUFJLENBQUM7UUFFdkIsWUFBWTtRQUNaLElBQUksQ0FBQyxvQkFBb0IsQ0FBQyxXQUFXLENBQUMsQ0FBQztRQUV2QyxjQUFjO1FBQ2QsSUFBTSxhQUFhLEdBQUcsSUFBSSxDQUFDLGlCQUFpQixDQUFDLFdBQVcsQ0FBQyxDQUFDO1FBQzFELElBQUksQ0FBQyxhQUFhLEVBQUU7WUFDaEIsRUFBRSxDQUFDLElBQUksQ0FBQyxpREFBWSxXQUFhLENBQUMsQ0FBQztZQUNuQyxJQUFJLENBQUMsVUFBVSxHQUFHLEtBQUssQ0FBQztZQUN4QixPQUFPO1NBQ1Y7UUFFRCwwQkFBMEI7UUFDMUIsSUFBSSxJQUFJLENBQUMsZ0JBQWdCLEtBQUssYUFBYSxDQUFDLE9BQU8sRUFBRTtZQUNqRCxlQUFlO1lBQ2YsSUFBSSxJQUFJLENBQUMsZ0JBQWdCLEVBQUU7Z0JBQ3ZCLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxNQUFNLEdBQUcsS0FBSyxDQUFDO2FBQ3hDO1lBRUQsY0FBYztZQUNkLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxhQUFhLENBQUMsYUFBYSxDQUFDLENBQUM7WUFDbkQsSUFBSSxDQUFDLG9CQUFvQixDQUFDLGFBQWEsQ0FBQyxPQUFPLEVBQUUsYUFBYSxDQUFDLE9BQU8sQ0FBQyxDQUFDO1lBRXhFLFlBQVk7WUFDWixJQUFJLENBQUMsZ0JBQWdCLEdBQUcsYUFBYSxDQUFDLE9BQU8sQ0FBQztTQUNqRDtRQUVELGlCQUFpQjtRQUNqQixJQUFJLElBQUksQ0FBQyxVQUFVLENBQUMsV0FBVyxDQUFDLEVBQUU7WUFDOUIsZ0JBQWdCO1lBQ2hCLElBQUksSUFBSSxDQUFDLDZCQUE2QixFQUFFO2dCQUNwQyxJQUFNLFlBQVksR0FBRyxJQUFJLENBQUMsc0JBQXNCLENBQUMsV0FBVyxDQUFDLENBQUM7Z0JBRTlELElBQUksQ0FBQyw2QkFBNkIsQ0FBQyxTQUFTLENBQUMsWUFBWSxDQUFDLENBQUM7Z0JBQzNELElBQUksQ0FBQyx1QkFBdUIsR0FBRyxJQUFJLENBQUMsNkJBQW9DLENBQUM7YUFDNUU7aUJBQU07Z0JBQ0gsT0FBTyxDQUFDLEtBQUssQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDO2dCQUNoQyxJQUFJLENBQUMsdUJBQXVCLEdBQUcsSUFBSSxDQUFDO2FBQ3ZDO1NBQ0o7YUFBTTtZQUNILGdCQUFnQjtZQUNoQixJQUFJLElBQUksQ0FBQywwQkFBMEIsRUFBRTtnQkFDakMsSUFBTSxTQUFTLEdBQUcsSUFBSSxDQUFDLG1CQUFtQixDQUFDLFdBQVcsQ0FBQyxDQUFDO2dCQUV4RCxJQUFJLENBQUMsMEJBQTBCLENBQUMsU0FBUyxDQUFDLFNBQVMsQ0FBQyxDQUFDO2dCQUNyRCxJQUFJLENBQUMsdUJBQXVCLEdBQUcsSUFBSSxDQUFDLDBCQUEwQixDQUFDO2FBQ2xFO2lCQUFNO2dCQUNILE9BQU8sQ0FBQyxLQUFLLENBQUMsZ0JBQWdCLENBQUMsQ0FBQztnQkFDaEMsSUFBSSxDQUFDLHVCQUF1QixHQUFHLElBQUksQ0FBQzthQUN2QztTQUNKO1FBRUQsSUFBSSxDQUFDLFVBQVUsR0FBRyxLQUFLLENBQUM7SUFDNUIsQ0FBQztJQUVEOzs7T0FHRztJQUNLLCtDQUFpQixHQUF6QixVQUEwQixXQUFtQjtRQUN6QyxJQUFJLFdBQVcsSUFBSSxDQUFDLElBQUksV0FBVyxJQUFJLENBQUMsRUFBRTtZQUN0QyxPQUFPLEVBQUMsT0FBTyxFQUFFLElBQUksQ0FBQyxZQUFZLEVBQUUsT0FBTyxFQUFFLFVBQVUsRUFBRSxhQUFhLEVBQUUsTUFBTSxFQUFDLENBQUM7U0FDbkY7YUFBTSxJQUFJLFdBQVcsS0FBSyxDQUFDLEVBQUU7WUFDMUIsT0FBTyxFQUFDLE9BQU8sRUFBRSxJQUFJLENBQUMsYUFBYSxFQUFFLE9BQU8sRUFBRSxZQUFZLEVBQUUsYUFBYSxFQUFFLE1BQU0sRUFBQyxDQUFDO1NBQ3RGO2FBQU0sSUFBSSxXQUFXLElBQUksQ0FBQyxJQUFJLFdBQVcsSUFBSSxDQUFDLEVBQUU7WUFDN0MsT0FBTyxFQUFDLE9BQU8sRUFBRSxJQUFJLENBQUMsWUFBWSxFQUFFLE9BQU8sRUFBRSxVQUFVLEVBQUUsYUFBYSxFQUFFLE1BQU0sRUFBQyxDQUFDO1NBQ25GO2FBQU0sSUFBSSxXQUFXLEtBQUssRUFBRSxFQUFFO1lBQzNCLE9BQU8sRUFBQyxPQUFPLEVBQUUsSUFBSSxDQUFDLGFBQWEsRUFBRSxPQUFPLEVBQUUsWUFBWSxFQUFFLGFBQWEsRUFBRSxNQUFNLEVBQUMsQ0FBQztTQUN0RjthQUFNLElBQUksV0FBVyxJQUFJLEVBQUUsSUFBSSxXQUFXLElBQUksRUFBRSxFQUFFO1lBQy9DLE9BQU8sRUFBQyxPQUFPLEVBQUUsSUFBSSxDQUFDLFlBQVksRUFBRSxPQUFPLEVBQUUsVUFBVSxFQUFFLGFBQWEsRUFBRSxNQUFNLEVBQUMsQ0FBQztTQUNuRjthQUFNLElBQUksV0FBVyxLQUFLLEVBQUUsRUFBRTtZQUMzQixPQUFPLEVBQUMsT0FBTyxFQUFFLElBQUksQ0FBQyxhQUFhLEVBQUUsT0FBTyxFQUFFLFlBQVksRUFBRSxhQUFhLEVBQUUsTUFBTSxFQUFDLENBQUM7U0FDdEY7YUFBTSxJQUFJLFdBQVcsSUFBSSxFQUFFLElBQUksV0FBVyxJQUFJLEVBQUUsRUFBRTtZQUMvQyxPQUFPLEVBQUMsT0FBTyxFQUFFLElBQUksQ0FBQyxhQUFhLEVBQUUsT0FBTyxFQUFFLFdBQVcsRUFBRSxhQUFhLEVBQUUsTUFBTSxFQUFDLENBQUM7U0FDckY7YUFBTSxJQUFJLFdBQVcsS0FBSyxFQUFFLEVBQUU7WUFDM0IsT0FBTyxFQUFDLE9BQU8sRUFBRSxJQUFJLENBQUMsYUFBYSxFQUFFLE9BQU8sRUFBRSxZQUFZLEVBQUUsYUFBYSxFQUFFLE1BQU0sRUFBQyxDQUFDO1NBQ3RGO2FBQU0sSUFBSSxXQUFXLElBQUksRUFBRSxJQUFJLFdBQVcsSUFBSSxFQUFFLEVBQUU7WUFDL0MsT0FBTyxFQUFDLE9BQU8sRUFBRSxJQUFJLENBQUMsY0FBYyxFQUFFLE9BQU8sRUFBRSxZQUFZLEVBQUUsYUFBYSxFQUFFLE1BQU0sRUFBQyxDQUFDO1NBQ3ZGO2FBQU0sSUFBSSxXQUFXLEtBQUssRUFBRSxFQUFFO1lBQzNCLE9BQU8sRUFBQyxPQUFPLEVBQUUsSUFBSSxDQUFDLGFBQWEsRUFBRSxPQUFPLEVBQUUsWUFBWSxFQUFFLGFBQWEsRUFBRSxNQUFNLEVBQUMsQ0FBQztTQUN0RjthQUFNLElBQUksV0FBVyxJQUFJLEVBQUUsSUFBSSxXQUFXLElBQUksRUFBRSxFQUFFO1lBQy9DLE9BQU8sRUFBQyxPQUFPLEVBQUUsSUFBSSxDQUFDLGNBQWMsRUFBRSxPQUFPLEVBQUUsWUFBWSxFQUFFLGFBQWEsRUFBRSxNQUFNLEVBQUMsQ0FBQztTQUN2RjthQUFNLElBQUksV0FBVyxLQUFLLEVBQUUsRUFBRTtZQUMzQixPQUFPLEVBQUMsT0FBTyxFQUFFLElBQUksQ0FBQyxhQUFhLEVBQUUsT0FBTyxFQUFFLFlBQVksRUFBRSxhQUFhLEVBQUUsTUFBTSxFQUFDLENBQUM7U0FDdEY7UUFDRCxPQUFPLElBQUksQ0FBQztJQUNoQixDQUFDO0lBSUQ7OztPQUdHO0lBQ0ssaURBQW1CLEdBQTNCLFVBQTRCLFdBQW1CO1FBQzNDLElBQUksV0FBVyxJQUFJLENBQUMsSUFBSSxXQUFXLElBQUksQ0FBQyxFQUFFO1lBQ3RDLE9BQU8sS0FBSyxDQUFDO1NBQ2hCO2FBQU0sSUFBSSxXQUFXLElBQUksQ0FBQyxJQUFJLFdBQVcsSUFBSSxDQUFDLEVBQUU7WUFDN0MsT0FBTyxLQUFLLENBQUM7U0FDaEI7YUFBTSxJQUFJLFdBQVcsSUFBSSxFQUFFLElBQUksV0FBVyxJQUFJLEVBQUUsRUFBRTtZQUMvQyxPQUFPLEtBQUssQ0FBQztTQUNoQjthQUFNLElBQUksV0FBVyxJQUFJLEVBQUUsSUFBSSxXQUFXLElBQUksRUFBRSxFQUFFO1lBQy9DLE9BQU8sTUFBTSxDQUFDO1NBQ2pCO2FBQU0sSUFBSSxXQUFXLElBQUksRUFBRSxJQUFJLFdBQVcsSUFBSSxFQUFFLElBQUksV0FBVyxJQUFJLEVBQUUsSUFBSSxXQUFXLElBQUksRUFBRSxFQUFFO1lBQ3pGLE9BQU8sT0FBTyxDQUFDO1NBQ2xCO1FBQ0QsT0FBTyxLQUFLLENBQUMsQ0FBQyxLQUFLO0lBQ3ZCLENBQUM7SUFFRDs7O09BR0c7SUFDSyx3Q0FBVSxHQUFsQixVQUFtQixXQUFtQjtRQUNsQyxjQUFjO1FBQ2QsT0FBTyxXQUFXLEtBQUssQ0FBQyxJQUFJLFdBQVcsS0FBSyxFQUFFLElBQUksV0FBVyxLQUFLLEVBQUU7WUFDN0QsV0FBVyxLQUFLLEVBQUUsSUFBSSxXQUFXLEtBQUssRUFBRSxJQUFJLFdBQVcsS0FBSyxFQUFFLENBQUM7SUFDMUUsQ0FBQztJQUVEOzs7T0FHRztJQUNLLG9EQUFzQixHQUE5QixVQUErQixXQUFtQjtRQUM5QyxjQUFjO1FBQ2QsSUFBSSxXQUFXLEtBQUssQ0FBQyxFQUFFO1lBQ25CLE9BQU8sV0FBVyxDQUFDLENBQUUsU0FBUztTQUNqQzthQUFNLElBQUksV0FBVyxLQUFLLEVBQUUsRUFBRTtZQUMzQixPQUFPLFdBQVcsQ0FBQyxDQUFFLFNBQVM7U0FDakM7YUFBTSxJQUFJLFdBQVcsS0FBSyxFQUFFLEVBQUU7WUFDM0IsT0FBTyxXQUFXLENBQUMsQ0FBRSxTQUFTO1NBQ2pDO2FBQU0sSUFBSSxXQUFXLEtBQUssRUFBRSxFQUFFO1lBQzNCLE9BQU8sV0FBVyxDQUFDLENBQUUsU0FBUztTQUNqQzthQUFNLElBQUksV0FBVyxLQUFLLEVBQUUsRUFBRTtZQUMzQixPQUFPLFdBQVcsQ0FBQyxDQUFFLFNBQVM7U0FDakM7YUFBTSxJQUFJLFdBQVcsS0FBSyxFQUFFLEVBQUU7WUFDM0IsT0FBTyxXQUFXLENBQUMsQ0FBRSxTQUFTO1NBQ2pDO1FBQ0QsT0FBTyxXQUFXLENBQUMsQ0FBQyxLQUFLO0lBQzdCLENBQUM7SUFFRDs7OztPQUlHO0lBQ0ssa0RBQW9CLEdBQTVCLFVBQTZCLE9BQWdCLEVBQUUsT0FBZTtRQUMxRCxJQUFJLE9BQU8sRUFBRTtZQUNULE9BQU8sQ0FBQyxNQUFNLEdBQUcsSUFBSSxDQUFDO1NBQ3pCO2FBQU07WUFDSCxFQUFFLENBQUMsSUFBSSxDQUFDLHdEQUFjLE9BQVMsQ0FBQyxDQUFDO1NBQ3BDO0lBQ0wsQ0FBQztJQUVEOzs7T0FHRztJQUNLLDhDQUFnQixHQUF4QixVQUF5QixhQUE4QjtRQUNuRCx1QkFBdUI7UUFDdkIsSUFBSSxJQUFJLENBQUMsYUFBYSxFQUFFO1lBQ3BCLElBQUksQ0FBQyxhQUFhLENBQUMsTUFBTSxHQUFHLElBQUksQ0FBQztTQUNwQztRQUVELHFCQUFxQjtRQUNyQixJQUFJLGFBQWEsS0FBSyxNQUFNLEVBQUU7WUFDMUIsSUFBSSxJQUFJLENBQUMsWUFBWSxJQUFJLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxNQUFNLEVBQUU7Z0JBQ2hELElBQUksQ0FBQyxZQUFZLENBQUMsTUFBTSxHQUFHLElBQUksQ0FBQzthQUNuQztZQUNELElBQUksSUFBSSxDQUFDLFlBQVksSUFBSSxJQUFJLENBQUMsWUFBWSxDQUFDLE1BQU0sRUFBRTtnQkFDL0MsSUFBSSxDQUFDLFlBQVksQ0FBQyxNQUFNLEdBQUcsS0FBSyxDQUFDO2FBQ3BDO1NBQ0o7YUFBTTtZQUNILElBQUksSUFBSSxDQUFDLFlBQVksSUFBSSxDQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsTUFBTSxFQUFFO2dCQUNoRCxJQUFJLENBQUMsWUFBWSxDQUFDLE1BQU0sR0FBRyxJQUFJLENBQUM7YUFDbkM7WUFDRCxJQUFJLElBQUksQ0FBQyxZQUFZLElBQUksSUFBSSxDQUFDLFlBQVksQ0FBQyxNQUFNLEVBQUU7Z0JBQy9DLElBQUksQ0FBQyxZQUFZLENBQUMsTUFBTSxHQUFHLEtBQUssQ0FBQzthQUNwQztTQUNKO0lBQ0wsQ0FBQztJQUVEOztPQUVHO0lBQ0ssNkNBQWUsR0FBdkI7UUFDSSxJQUFNLFdBQVcsR0FBRztZQUNoQixJQUFJLENBQUMsWUFBWTtZQUNqQixJQUFJLENBQUMsWUFBWTtZQUNqQixJQUFJLENBQUMsWUFBWTtZQUNqQixJQUFJLENBQUMsYUFBYTtZQUNsQixJQUFJLENBQUMsY0FBYztZQUNuQixJQUFJLENBQUMsYUFBYTtZQUNsQixJQUFJLENBQUMsYUFBYTtZQUNsQixJQUFJLENBQUMsYUFBYTtZQUNsQixJQUFJLENBQUMsYUFBYTtZQUNsQixJQUFJLENBQUMsYUFBYTtZQUNsQixJQUFJLENBQUMsYUFBYTtTQUNyQixDQUFDO1FBRUYsV0FBVyxDQUFDLE9BQU8sQ0FBQyxVQUFBLElBQUk7WUFDcEIsSUFBSSxJQUFJLEVBQUU7Z0JBQ04sSUFBSSxDQUFDLE1BQU0sR0FBRyxLQUFLLENBQUM7YUFDdkI7UUFDTCxDQUFDLENBQUMsQ0FBQztJQUNQLENBQUM7SUFFRDs7O09BR0c7SUFDSSw2Q0FBZSxHQUF0QixVQUF1QixXQUFtQjtRQUd0QyxvQkFBb0I7UUFDcEIsSUFBSSxDQUFDLGVBQWUsRUFBRSxDQUFDO1FBRXZCLElBQUksQ0FBQyxZQUFZLEdBQUcsV0FBVyxDQUFDO1FBRWhDLGdCQUFnQjtRQUNoQixJQUFJLENBQUMsVUFBVSxDQUFDLFdBQVcsQ0FBQyxDQUFDO0lBQ2pDLENBQUM7SUFFRDs7T0FFRztJQUNJLDZDQUFlLEdBQXRCO1FBQ0ksT0FBTyxJQUFJLENBQUMsWUFBWSxDQUFDO0lBQzdCLENBQUM7SUFFRDs7T0FFRztJQUNJLGlEQUFtQixHQUExQjtRQUNJLE9BQU8sSUFBSSxDQUFDLGdCQUFnQixDQUFDO0lBQ2pDLENBQUM7SUFFRDs7T0FFRztJQUNJLDhDQUFnQixHQUF2QjtRQUNJLE9BQU8sSUFBSSxDQUFDLGFBQWEsQ0FBQztJQUM5QixDQUFDO0lBRUQ7O09BRUc7SUFDSyxrREFBb0IsR0FBNUI7UUFDSSx1QkFBdUI7UUFDdkIsSUFBSSxJQUFJLENBQUMsYUFBYSxFQUFFO1lBQ3BCLElBQUksQ0FBQyxhQUFhLENBQUMsTUFBTSxHQUFHLElBQUksQ0FBQztTQUVwQztRQUVELGNBQWM7UUFDZCxJQUFJLElBQUksQ0FBQyxZQUFZLEVBQUU7WUFDbkIsSUFBSSxDQUFDLFlBQVksQ0FBQyxNQUFNLEdBQUcsS0FBSyxDQUFDO1NBRXBDO1FBQ0QsSUFBSSxJQUFJLENBQUMsWUFBWSxFQUFFO1lBQ25CLElBQUksQ0FBQyxZQUFZLENBQUMsTUFBTSxHQUFHLEtBQUssQ0FBQztTQUVwQztRQUVELGdCQUFnQjtRQUNoQixJQUFJLENBQUMsZUFBZSxFQUFFLENBQUM7SUFDM0IsQ0FBQztJQUVEOztPQUVHO0lBQ0ssMENBQVksR0FBcEI7UUFDSSxJQUFJLElBQUksQ0FBQyxZQUFZLEVBQUU7WUFDbkIsSUFBSSxDQUFDLFlBQVksQ0FBQyxNQUFNLEdBQUcsSUFBSSxDQUFDO1NBRW5DO2FBQU07WUFDSCxFQUFFLENBQUMsSUFBSSxDQUFDLG9CQUFvQixDQUFDLENBQUM7U0FDakM7SUFDTCxDQUFDO0lBRUQ7O09BRUc7SUFDSywwQ0FBWSxHQUFwQjtRQUNJLElBQUksSUFBSSxDQUFDLFlBQVksRUFBRTtZQUNuQixJQUFJLENBQUMsWUFBWSxDQUFDLE1BQU0sR0FBRyxJQUFJLENBQUM7U0FFbkM7YUFBTTtZQUNILEVBQUUsQ0FBQyxJQUFJLENBQUMsb0JBQW9CLENBQUMsQ0FBQztTQUNqQztJQUNMLENBQUM7SUFFRDs7T0FFRztJQUNJLHdEQUEwQixHQUFqQztRQUNJLE9BQU8sSUFBSSxDQUFDLHVCQUF1QixDQUFDO0lBQ3hDLENBQUM7SUFFRDs7O09BR0c7SUFDSSwyREFBNkIsR0FBcEMsVUFBcUMsUUFBYTtRQUM5QyxJQUFJLElBQUksQ0FBQyx1QkFBdUIsRUFBRTtZQUN0QixJQUFBLENBQUMsR0FBK0IsUUFBUSxFQUF2QyxFQUFFLENBQUMsR0FBNEIsUUFBUSxFQUFwQyxFQUFFLE1BQU0sR0FBb0IsUUFBUSxPQUE1QixFQUFFLGFBQWEsR0FBSyxRQUFRLGNBQWIsQ0FBYztZQUVqRCxTQUFTO1lBQ1QsSUFBSSxDQUFDLEtBQUssU0FBUyxJQUFJLENBQUMsS0FBSyxTQUFTLElBQUksTUFBTSxLQUFLLFNBQVMsRUFBRTtnQkFDNUQsSUFBSSxDQUFDLHVCQUF1QixDQUFDLG1CQUFtQixDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsTUFBTSxDQUFDLENBQUM7YUFDbEU7WUFFRCxTQUFTO1lBQ1QsSUFBSSxhQUFhLElBQUksS0FBSyxDQUFDLE9BQU8sQ0FBQyxhQUFhLENBQUMsRUFBRTtnQkFDL0MsSUFBSSxDQUFDLHVCQUF1QixDQUFDLG1CQUFtQixDQUFDLGFBQWEsQ0FBQyxDQUFDO2FBQ25FO1NBQ0o7SUFDTCxDQUFDO0lBRUQ7OztPQUdHO0lBQ0kscURBQXVCLEdBQTlCLFVBQStCLFdBQWdCO1FBQzNDLElBQUksSUFBSSxDQUFDLHVCQUF1QixFQUFFO1lBQzlCLFNBQVM7WUFDVCxJQUFJLENBQUMsdUJBQXVCLENBQUMsbUJBQW1CLEVBQUUsQ0FBQztZQUVuRCxTQUFTO1lBQ1QsSUFBSSxDQUFDLHVCQUF1QixDQUFDLGNBQWMsRUFBRSxDQUFDO1NBQ2pEO0lBQ0wsQ0FBQztJQUVEOztPQUVHO0lBQ0ksMERBQTRCLEdBQW5DO1FBRUksSUFBSSxJQUFJLENBQUMsdUJBQXVCLEVBQUU7WUFDOUIsdUJBQXVCO1lBQ3ZCLElBQUksQ0FBQyx1QkFBdUIsQ0FBQyxVQUFVLEVBQUUsQ0FBQztZQUUxQyxXQUFXO1lBQ1gsSUFBSSxDQUFDLHVCQUF1QixDQUFDLGtCQUFrQixFQUFFLENBQUM7U0FDckQ7SUFDTCxDQUFDO0lBRUQ7O09BRUc7SUFDSywrREFBaUMsR0FBekM7UUFDSSxnQkFBZ0I7UUFDaEIsaUJBQU8sQ0FBQyxLQUFLLENBQUMsZ0JBQWdCLENBQUMsdUJBQVMsQ0FBQyxjQUFjLEVBQUUsSUFBSSxDQUFDLGdCQUFnQixFQUFFLElBQUksQ0FBQyxDQUFDO0lBQzFGLENBQUM7SUFFRDs7T0FFRztJQUNLLGlFQUFtQyxHQUEzQztRQUNJLGlCQUFPLENBQUMsS0FBSyxDQUFDLG1CQUFtQixDQUFDLHVCQUFTLENBQUMsY0FBYyxFQUFFLElBQUksQ0FBQyxnQkFBZ0IsRUFBRSxJQUFJLENBQUMsQ0FBQztJQUM3RixDQUFDO0lBRUQ7OztPQUdHO0lBQ0ssOENBQWdCLEdBQXhCLFVBQXlCLFdBQWdDO1FBQ3JELFFBQVEsV0FBVyxDQUFDLEtBQUssRUFBRTtZQUN2QixLQUFLLHFCQUFTLENBQUMsc0JBQXNCO2dCQUNqQyxJQUFJLENBQUMseUJBQXlCLENBQUMsV0FBVyxDQUFDLElBQUksQ0FBQyxDQUFDO2dCQUNqRCxNQUFNO1lBQ1YsS0FBSyxxQkFBUyxDQUFDLG1CQUFtQjtnQkFDOUIsSUFBSSxDQUFDLGNBQWMsQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLENBQUM7Z0JBQ3RDLE1BQU07U0FDYjtJQUNMLENBQUM7SUFFRDs7O09BR0c7SUFDSSx1REFBeUIsR0FBaEMsVUFBaUMsUUFBYTtRQUUxQyxJQUFJLElBQUksQ0FBQyx1QkFBdUIsRUFBRTtZQUM5QixvQkFBb0I7WUFFaEIsSUFBQSxDQUFDLEdBR0QsUUFBUSxFQUhQLEVBQUUsQ0FBQyxHQUdKLFFBQVEsRUFISixFQUFFLENBQUMsR0FHUCxRQUFRLEVBSEQsRUFBRSxDQUFDLEdBR1YsUUFBUSxFQUhFLEVBQUUsTUFBTSxHQUdsQixRQUFRLE9BSFUsRUFBRSxNQUFNLEdBRzFCLFFBQVEsT0FIa0IsRUFDMUIsYUFBYSxHQUViLFFBQVEsY0FGSyxFQUFFLGFBQWEsR0FFNUIsUUFBUSxjQUZvQixFQUFFLFNBQVMsR0FFdkMsUUFBUSxVQUYrQixFQUFFLGNBQWMsR0FFdkQsUUFBUSxlQUYrQyxFQUN2RCxnQkFBZ0IsR0FDaEIsUUFBUSxpQkFEUSxDQUFFLGFBQWE7WUFBZixDQUNQO1lBRWIsZ0JBQWdCO1lBQ2hCLElBQU0sb0JBQW9CLEdBQUcsQ0FBQyxJQUFJLENBQUMsdUJBQXVCLEtBQUssSUFBSSxDQUFDLDZCQUFvQyxDQUFDLENBQUM7WUFFMUcsa0JBQWtCO1lBQ2xCLElBQUksTUFBTSxTQUFRLEVBQUUsTUFBTSxTQUFRLEVBQUUsTUFBTSxTQUFRLEVBQUUsTUFBTSxTQUFRLENBQUM7WUFDbkUsSUFBSSxhQUFhLEdBQUcsS0FBSyxDQUFDO1lBRTFCLElBQUksb0JBQW9CLEVBQUU7Z0JBQ3RCLG1DQUFtQztnQkFDbkMsSUFBSSxDQUFDLEtBQUssU0FBUyxJQUFJLENBQUMsS0FBSyxTQUFTLEVBQUU7b0JBQ3BDLE1BQU0sR0FBRyxDQUFDLENBQUM7b0JBQ1gsTUFBTSxHQUFHLENBQUMsQ0FBQztvQkFDWCxhQUFhLEdBQUcsSUFBSSxDQUFDO2lCQUV4QjtxQkFBTSxJQUFJLENBQUMsS0FBSyxTQUFTLElBQUksQ0FBQyxLQUFLLFNBQVMsRUFBRTtvQkFDM0MsZ0NBQWdDO29CQUNoQyxNQUFNLEdBQUcsQ0FBQyxDQUFDLENBQUUsU0FBUztvQkFDdEIsTUFBTSxHQUFHLENBQUMsQ0FBQyxDQUFFLFNBQVM7b0JBQ3RCLGFBQWEsR0FBRyxJQUFJLENBQUM7aUJBRXhCO2FBQ0o7aUJBQU07Z0JBQ0gsaUJBQWlCO2dCQUNqQixJQUFJLENBQUMsS0FBSyxTQUFTLElBQUksQ0FBQyxLQUFLLFNBQVMsRUFBRTtvQkFDcEMsTUFBTSxHQUFHLENBQUMsQ0FBQztvQkFDWCxNQUFNLEdBQUcsQ0FBQyxDQUFDO29CQUNYLGFBQWEsR0FBRyxJQUFJLENBQUM7aUJBRXhCO3FCQUFNLElBQUksQ0FBQyxLQUFLLFNBQVMsSUFBSSxDQUFDLEtBQUssU0FBUyxFQUFFO29CQUMzQyxPQUFPLENBQUMsSUFBSSxDQUFDLGtHQUFxQixDQUFDLFVBQUssQ0FBQyxnREFBVSxDQUFDLENBQUM7b0JBQ3JELGFBQWEsR0FBRyxLQUFLLENBQUM7aUJBQ3pCO2FBQ0o7WUFFRCxJQUFJLGFBQWEsSUFBSSxNQUFNLEtBQUssU0FBUyxFQUFFO2dCQUd2QyxJQUFJLE1BQU0sS0FBSyxDQUFDLEVBQUU7b0JBQ2QsMENBQTBDO29CQUUxQyx3Q0FBd0M7aUJBQzNDO3FCQUFNLElBQUksTUFBTSxLQUFLLENBQUMsRUFBRTtvQkFDckIsT0FBTztvQkFHUCxXQUFXO29CQUNYLElBQUksTUFBTSxLQUFLLE1BQU0sSUFBSSxNQUFNLEtBQUssTUFBTSxFQUFFO3dCQUV4QyxJQUFJLENBQUMsZ0JBQWdCLEdBQUcsSUFBSSxDQUFDO3FCQUNoQztvQkFFRCxpQkFBaUI7b0JBQ2pCLElBQUksb0JBQW9CLEVBQUU7d0JBQ3RCLFNBQVM7d0JBQ1IsSUFBSSxDQUFDLHVCQUErQixDQUFDLG1CQUFtQixDQUFDLE1BQU0sRUFBRSxNQUFNLEVBQUUsTUFBTSxDQUFDLENBQUM7cUJBQ3JGO3lCQUFNO3dCQUNILFNBQVM7d0JBQ1QsSUFBSSxDQUFDLHVCQUF1QixDQUFDLG1CQUFtQixDQUFDLE1BQU0sRUFBRSxNQUFNLEVBQUUsTUFBTSxDQUFDLENBQUM7cUJBQzVFO29CQUVELFdBQVc7b0JBQ1gsSUFBSSxnQkFBZ0IsSUFBSSxLQUFLLENBQUMsT0FBTyxDQUFDLGdCQUFnQixDQUFDLElBQUksZ0JBQWdCLENBQUMsTUFBTSxHQUFHLENBQUMsRUFBRTt3QkFHcEYsSUFBSSxvQkFBb0IsRUFBRTs0QkFDdEIsZ0NBQWdDOzRCQUUvQixJQUFJLENBQUMsdUJBQStCLENBQUMsbUJBQW1CLENBQUMsZ0JBQWdCLENBQUMsQ0FBQzt5QkFDL0U7NkJBQU07NEJBQ0gsbUNBQW1DOzRCQUVsQyxJQUFJLENBQUMsdUJBQXNELENBQUMsc0JBQXNCLENBQUMsZ0JBQWdCLENBQUMsQ0FBQzt5QkFDekc7cUJBQ0o7aUJBQ0o7cUJBQU07b0JBQ0gsZUFBZTtvQkFFZixJQUFJLG9CQUFvQixFQUFFO3dCQUNyQixJQUFJLENBQUMsdUJBQStCLENBQUMsbUJBQW1CLENBQUMsTUFBTSxFQUFFLE1BQU0sRUFBRSxNQUFNLENBQUMsQ0FBQztxQkFDckY7eUJBQU07d0JBQ0gsSUFBSSxDQUFDLHVCQUF1QixDQUFDLG1CQUFtQixDQUFDLE1BQU0sRUFBRSxNQUFNLEVBQUUsTUFBTSxDQUFDLENBQUM7cUJBQzVFO2lCQUNKO2FBQ0o7aUJBQU07Z0JBQ0gsT0FBTyxDQUFDLElBQUksQ0FBQyxvQkFBb0IsQ0FBQyxDQUFDO2dCQUNuQyxPQUFPLENBQUMsSUFBSSxDQUFDLHlDQUFhLG9CQUFvQixDQUFDLENBQUMsQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBRSxDQUFDLENBQUM7Z0JBQ2xFLE9BQU8sQ0FBQyxJQUFJLENBQUMsb0NBQWMsQ0FBQyxZQUFPLENBQUMsWUFBTyxDQUFDLFlBQU8sQ0FBRyxDQUFDLENBQUM7Z0JBQ3hELE9BQU8sQ0FBQyxJQUFJLENBQUMsc0JBQVUsTUFBUSxDQUFDLENBQUM7YUFDcEM7U0FPSjtJQUNMLENBQUM7SUFFRDs7O09BR0c7SUFDSSw0Q0FBYyxHQUFyQixVQUFzQixXQUFnQjtRQUF0QyxpQkFxQ0M7UUFsQ0csSUFBSSxJQUFJLENBQUMsdUJBQXVCLEVBQUU7WUFDOUIsU0FBUztZQUNULElBQUksQ0FBQyx1QkFBdUIsQ0FBQyxtQkFBbUIsRUFBRSxDQUFDO1lBRW5ELGdCQUFnQjtZQUNoQixJQUFJLENBQUMsdUJBQXVCLENBQUMsY0FBYyxFQUFFLENBQUM7U0FDakQ7UUFFRCxzQkFBc0I7UUFDdEIsSUFBSSxDQUFDLGlCQUFpQixFQUFFLENBQUM7UUFFekIsbUNBQW1DO1FBQ25DLElBQU0sWUFBWSxHQUFHLENBQUMsV0FBVyxDQUFDLE9BQU8sQ0FBQztRQUMxQyxJQUFNLGVBQWUsR0FBRyxJQUFJLENBQUMsZ0JBQWdCO1lBQ3RCLENBQUMsSUFBSSxDQUFDLHVCQUF1QixJQUFJLElBQUksQ0FBQyx1QkFBdUIsQ0FBQyx5QkFBeUIsRUFBRSxDQUFDLENBQUM7UUFJbEgsSUFBSSxZQUFZLElBQUksZUFBZSxFQUFFO1lBQ2pDLDJCQUEyQjtZQUUzQixJQUFJLENBQUMsWUFBWSxDQUFDO2dCQUVkLEtBQUksQ0FBQyxtQkFBbUIsQ0FBQyxXQUFXLENBQUMsQ0FBQztnQkFDdEMsT0FBTztnQkFDUCxLQUFJLENBQUMsZ0JBQWdCLEdBQUcsS0FBSyxDQUFDO1lBQ2xDLENBQUMsRUFBRSxHQUFHLENBQUMsQ0FBQztTQUNYO2FBQU07WUFDSCxnQkFBZ0I7WUFFaEIsSUFBSSxDQUFDLG1CQUFtQixDQUFDLFdBQVcsQ0FBQyxDQUFDO1lBQ3RDLE9BQU87WUFDUCxJQUFJLENBQUMsZ0JBQWdCLEdBQUcsS0FBSyxDQUFDO1NBQ2pDO0lBQ0wsQ0FBQztJQUVEOztPQUVHO0lBQ0ssb0RBQXNCLEdBQTlCO1FBQUEsaUJBaUJDO1FBaEJHLFNBQVM7UUFDVCxJQUFJLElBQUksQ0FBQyxXQUFXLEVBQUU7WUFDbEIsSUFBSSxDQUFDLFdBQVcsQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLE9BQU8sRUFBRSxJQUFJLENBQUMsa0JBQWtCLEVBQUUsSUFBSSxDQUFDLENBQUM7U0FDcEU7UUFFRCxRQUFRO1FBQ1IsSUFBSSxJQUFJLENBQUMsZUFBZSxFQUFFO1lBQ3RCLElBQUksQ0FBQyxlQUFlLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQyxPQUFPLEVBQUUsSUFBSSxDQUFDLHNCQUFzQixFQUFFLElBQUksQ0FBQyxDQUFDO1NBQzVFO1FBRUQseUJBQXlCO1FBQ3pCLElBQUksSUFBSSxDQUFDLFVBQVUsRUFBRTtZQUNqQixhQUFLLENBQUMsZ0JBQWdCLENBQUMsSUFBSSxDQUFDLFVBQVUsQ0FBQyxJQUFJLEVBQUUsZUFBTSxDQUFDLFNBQVMsR0FBRyx1QkFBdUIsRUFBRSxlQUFNLENBQUMsU0FBUyxHQUFHLHdCQUF3QixFQUFFO2dCQUNsSSxLQUFJLENBQUMsaUJBQWlCLEVBQUUsQ0FBQztZQUM3QixDQUFDLENBQUMsQ0FBQztTQUNOO0lBQ0wsQ0FBQztJQUVEOzs7T0FHRztJQUNLLGlEQUFtQixHQUEzQixVQUE0QixXQUFnQjtRQUV4QyxJQUFJLENBQUMsSUFBSSxDQUFDLG1CQUFtQixFQUFFO1lBQzNCLE9BQU8sQ0FBQyxLQUFLLENBQUMseUJBQXlCLENBQUMsQ0FBQztZQUN6QyxPQUFPO1NBQ1Y7UUFFRCxTQUFTO1FBRVQsSUFBSSxDQUFDLG1CQUFtQixDQUFDLE1BQU0sR0FBRyxJQUFJLENBQUM7UUFFdkMsMkJBQTJCO1FBQzNCLElBQU0sU0FBUyxHQUFHLFdBQVcsQ0FBQyxTQUFTLElBQUksV0FBVyxDQUFDLE9BQU8sSUFBSSxXQUFXLENBQUMsS0FBSyxJQUFJLFdBQVcsQ0FBQyxVQUFVLEtBQUssQ0FBQyxDQUFDO1FBSXBILElBQUksU0FBUyxFQUFFO1lBQ1gsY0FBYztZQUNkLElBQUksSUFBSSxDQUFDLFNBQVMsRUFBRTtnQkFDaEIsSUFBSSxDQUFDLFNBQVMsQ0FBQyxNQUFNLEdBQUcsSUFBSSxDQUFDO2FBQ2hDO1lBQ0QsSUFBSSxJQUFJLENBQUMsVUFBVSxFQUFFO2dCQUNqQixJQUFJLENBQUMsVUFBVSxDQUFDLE1BQU0sR0FBRyxLQUFLLENBQUM7YUFDbEM7U0FFSjthQUFNO1lBQ0gsY0FBYztZQUNkLElBQUksSUFBSSxDQUFDLFVBQVUsRUFBRTtnQkFDakIsSUFBSSxDQUFDLFVBQVUsQ0FBQyxNQUFNLEdBQUcsSUFBSSxDQUFDO2FBQ2pDO1lBQ0QsSUFBSSxJQUFJLENBQUMsU0FBUyxFQUFFO2dCQUNoQixJQUFJLENBQUMsU0FBUyxDQUFDLE1BQU0sR0FBRyxLQUFLLENBQUM7YUFDakM7U0FFSjtJQUNMLENBQUM7SUFFRDs7T0FFRztJQUNLLGdEQUFrQixHQUExQjtRQUdJLFNBQVM7UUFDVCxJQUFJLENBQUMsbUJBQW1CLEVBQUUsQ0FBQztRQUUzQixXQUFXO1FBQ1gsSUFBSSxDQUFDLGdCQUFnQixFQUFFLENBQUM7UUFFeEIsdUNBQXVDO1FBQ3ZDLGlDQUFpQztRQUVqQyx5QkFBeUI7UUFDekIsSUFBSSxDQUFDLG1CQUFtQixDQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsQ0FBQztJQUNoRCxDQUFDO0lBRUQ7O09BRUc7SUFDSyxvREFBc0IsR0FBOUI7UUFHSSxTQUFTO1FBQ1QsSUFBSSxDQUFDLG1CQUFtQixFQUFFLENBQUM7UUFFM0IsV0FBVztRQUNYLElBQUksQ0FBQyxnQkFBZ0IsRUFBRSxDQUFDO1FBRXhCLFFBQVE7UUFDUixJQUFNLFNBQVMsR0FBRyxJQUFJLENBQUMsWUFBWSxHQUFHLENBQUMsQ0FBQztRQUN4QyxJQUFJLENBQUMsZUFBZSxDQUFDLFNBQVMsQ0FBQyxDQUFDO1FBRWhDLHVDQUF1QztRQUN2QyxpQ0FBaUM7UUFFakMsd0JBQXdCO1FBQ3hCLElBQUksQ0FBQyxtQkFBbUIsQ0FBQyxTQUFTLENBQUMsQ0FBQztJQUN4QyxDQUFDO0lBRUQ7O09BRUc7SUFDSywrQ0FBaUIsR0FBekI7UUFHSSxTQUFTO1FBQ1QsSUFBSSxDQUFDLG1CQUFtQixFQUFFLENBQUM7UUFFM0IsMEJBQTBCO1FBQzFCLElBQUksQ0FBQyxnQkFBZ0IsRUFBRSxDQUFDO1FBRXhCLGtCQUFrQjtRQUNsQixJQUFJLENBQUMsbUJBQW1CLEVBQUUsQ0FBQztJQUMvQixDQUFDO0lBRUQ7O09BRUc7SUFDSyxpREFBbUIsR0FBM0I7UUFDSSxJQUFJLElBQUksQ0FBQyxtQkFBbUIsRUFBRTtZQUMxQixJQUFJLENBQUMsbUJBQW1CLENBQUMsTUFBTSxHQUFHLEtBQUssQ0FBQztTQUMzQztJQUNMLENBQUM7SUFFRDs7O09BR0c7SUFDSyxpREFBbUIsR0FBM0IsVUFBNEIsT0FBZTtRQUd2QyxJQUFNLE9BQU8sR0FBRztZQUNaLE9BQU8sRUFBRSxPQUFPO1NBQ25CLENBQUM7UUFFRixtQ0FBZ0IsQ0FBQyxXQUFXLEVBQUUsQ0FBQyxPQUFPLENBQUMscUJBQVMsQ0FBQyxzQkFBc0IsRUFBRSxPQUFPLENBQUMsQ0FBQztJQUN0RixDQUFDO0lBRUQ7O09BRUc7SUFDSyw4Q0FBZ0IsR0FBeEI7UUFDSSxJQUFJLElBQUksQ0FBQyxvQkFBb0IsRUFBRTtZQUMzQixJQUFJLENBQUMsb0JBQW9CLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQyxPQUFPLEVBQUUsSUFBSSxDQUFDLHFCQUFxQixFQUFFLElBQUksQ0FBQyxDQUFDO1NBQ2hGO0lBQ0wsQ0FBQztJQUVEOztPQUVHO0lBQ0ssbURBQXFCLEdBQTdCO1FBRUksbUNBQWdCLENBQUMsV0FBVyxFQUFFLENBQUMsT0FBTyxDQUFDLHFCQUFTLENBQUMscUJBQXFCLEVBQUUsRUFBRSxDQUFDLENBQUM7SUFDaEYsQ0FBQztJQUVEOztPQUVHO0lBQ0ssNENBQWMsR0FBdEI7UUFDSSwrQkFBK0I7UUFDL0IsT0FBTyxJQUFJLENBQUMsYUFBYSxHQUFHLENBQUMsQ0FBQztJQUNsQyxDQUFDO0lBRUQ7OztPQUdHO0lBQ0ksa0RBQW9CLEdBQTNCLFVBQTRCLGFBQTRDO1FBQXhFLGlCQTBFQztRQXhFRyxJQUFJLENBQUMsSUFBSSxDQUFDLGVBQWUsRUFBRTtZQUN2QixFQUFFLENBQUMsSUFBSSxDQUFDLGlDQUFpQyxDQUFDLENBQUM7WUFDM0MsT0FBTztTQUNWO1FBRUQsSUFBSSxDQUFDLElBQUksQ0FBQyx1QkFBdUIsRUFBRTtZQUMvQixFQUFFLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDO1lBQ3ZCLE9BQU87U0FDVjtRQUVELElBQUksQ0FBQyxhQUFhLElBQUksQ0FBQyxLQUFLLENBQUMsT0FBTyxDQUFDLGFBQWEsQ0FBQyxJQUFJLGFBQWEsQ0FBQyxNQUFNLEtBQUssQ0FBQyxFQUFFO1lBQy9FLEVBQUUsQ0FBQyxJQUFJLENBQUMsV0FBVyxFQUFFLGFBQWEsQ0FBQyxDQUFDO1lBQ3BDLE9BQU87U0FDVjtRQUlELHVCQUF1QjtRQUN2QixJQUFJLGFBQWEsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFO1lBQzFCLElBQU0sYUFBYSxHQUFHLGFBQWEsQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUV2QyxTQUFTO1lBQ1QsSUFBSSxNQUFNLFNBQVEsRUFBRSxNQUFNLFNBQVEsQ0FBQztZQUNuQyxJQUFNLEdBQUcsR0FBRyxhQUFvQixDQUFDLENBQUUsd0JBQXdCO1lBRTNELElBQUksR0FBRyxDQUFDLENBQUMsS0FBSyxTQUFTLElBQUksR0FBRyxDQUFDLENBQUMsS0FBSyxTQUFTLEVBQUU7Z0JBQzVDLE1BQU0sR0FBRyxHQUFHLENBQUMsQ0FBQyxDQUFDO2dCQUNmLE1BQU0sR0FBRyxHQUFHLENBQUMsQ0FBQyxDQUFDO2FBRWxCO2lCQUFNLElBQUksR0FBRyxDQUFDLENBQUMsS0FBSyxTQUFTLElBQUksR0FBRyxDQUFDLENBQUMsS0FBSyxTQUFTLEVBQUU7Z0JBQ25ELE1BQU0sR0FBRyxHQUFHLENBQUMsQ0FBQyxDQUFDO2dCQUNmLE1BQU0sR0FBRyxHQUFHLENBQUMsQ0FBQyxDQUFDO2FBRWxCO2lCQUFNO2dCQUNILE9BQU8sQ0FBQyxLQUFLLENBQUMsMERBQWEsRUFBRSxhQUFhLENBQUMsQ0FBQztnQkFDNUMsT0FBTzthQUNWO1lBRUQsYUFBYTtZQUNiLElBQUksQ0FBQyxxQkFBcUIsQ0FBQyxNQUFNLEVBQUUsTUFBTSxDQUFDLENBQUM7U0FDOUM7UUFFRCxpQkFBaUI7UUFDakIsYUFBYSxDQUFDLE9BQU8sQ0FBQyxVQUFDLFFBQVEsRUFBRSxLQUFLO1lBQ2xDLElBQU0sR0FBRyxHQUFHLFFBQWUsQ0FBQyxDQUFFLHdCQUF3QjtZQUV0RCxPQUFPO1lBQ1AsSUFBSSxNQUFjLEVBQUUsTUFBYyxDQUFDO1lBQ25DLElBQUksR0FBRyxDQUFDLENBQUMsS0FBSyxTQUFTLElBQUksR0FBRyxDQUFDLENBQUMsS0FBSyxTQUFTLEVBQUU7Z0JBQzVDLE1BQU0sR0FBRyxHQUFHLENBQUMsQ0FBQyxDQUFDO2dCQUNmLE1BQU0sR0FBRyxHQUFHLENBQUMsQ0FBQyxDQUFDO2FBQ2xCO2lCQUFNLElBQUksR0FBRyxDQUFDLENBQUMsS0FBSyxTQUFTLElBQUksR0FBRyxDQUFDLENBQUMsS0FBSyxTQUFTLEVBQUU7Z0JBQ25ELE1BQU0sR0FBRyxHQUFHLENBQUMsQ0FBQyxDQUFDO2dCQUNmLE1BQU0sR0FBRyxHQUFHLENBQUMsQ0FBQyxDQUFDO2FBQ2xCO2lCQUFNO2dCQUNILE9BQU8sQ0FBQyxLQUFLLENBQUMsb0NBQVMsS0FBSywwQ0FBUyxFQUFFLFFBQVEsQ0FBQyxDQUFDO2dCQUNqRCxPQUFPO2FBQ1Y7WUFFRCxJQUFJLEtBQUssS0FBSyxDQUFDLEVBQUU7Z0JBQ2IsNEJBQTRCO2dCQUM1QixPQUFPO2FBQ1Y7aUJBQU07Z0JBQ0gsbUJBQW1CO2dCQUNuQixJQUFNLFdBQVMsR0FBRyxNQUFNLENBQUMsQ0FBRSxRQUFRO2dCQUNuQyxJQUFNLFdBQVMsR0FBRyxNQUFNLENBQUMsQ0FBRSxRQUFRO2dCQUNuQyxLQUFJLENBQUMsWUFBWSxDQUFDO29CQUVkLEtBQUksQ0FBQyxxQkFBcUIsQ0FBQyxXQUFTLEVBQUUsV0FBUyxDQUFDLENBQUM7Z0JBQ3JELENBQUMsRUFBRSxLQUFLLEdBQUcsR0FBRyxDQUFDLENBQUM7YUFDbkI7UUFDTCxDQUFDLENBQUMsQ0FBQztJQUNQLENBQUM7SUFFRDs7OztPQUlHO0lBQ0ssbURBQXFCLEdBQTdCLFVBQThCLENBQVMsRUFBRSxDQUFTO1FBRTlDLFdBQVc7UUFDWCxJQUFJLENBQUMsS0FBSyxTQUFTLElBQUksQ0FBQyxLQUFLLFNBQVMsSUFBSSxDQUFDLEtBQUssSUFBSSxJQUFJLENBQUMsS0FBSyxJQUFJLEVBQUU7WUFDaEUsT0FBTyxDQUFDLEtBQUssQ0FBQywwREFBZ0IsQ0FBQyxZQUFPLENBQUcsQ0FBQyxDQUFDO1lBQzNDLE9BQU8sQ0FBQyxLQUFLLENBQUMsdUJBQVcsT0FBTyxDQUFDLHlCQUFVLE9BQU8sQ0FBRyxDQUFDLENBQUM7WUFDdkQsT0FBTyxDQUFDLEtBQUssQ0FBQyw4QkFBVSxFQUFFLElBQUksS0FBSyxFQUFFLENBQUMsS0FBSyxDQUFDLENBQUM7WUFDN0MsT0FBTztTQUNWO1FBRUQsSUFBSSxDQUFDLElBQUksQ0FBQyxlQUFlLEVBQUU7WUFDdkIsRUFBRSxDQUFDLEtBQUssQ0FBQyw4QkFBOEIsQ0FBQyxDQUFDO1lBQ3pDLE9BQU87U0FDVjtRQUVELElBQUksQ0FBQyxJQUFJLENBQUMsdUJBQXVCLEVBQUU7WUFDL0IsRUFBRSxDQUFDLEtBQUssQ0FBQyxzQ0FBc0MsQ0FBQyxDQUFDO1lBQ2pELE9BQU87U0FDVjtRQUVELElBQUk7WUFDQSxnQkFBZ0I7WUFDaEIsSUFBTSxvQkFBb0IsR0FBRyxDQUFDLElBQUksQ0FBQyx1QkFBdUIsS0FBSyxJQUFJLENBQUMsNkJBQW9DLENBQUMsQ0FBQztZQUUxRyxJQUFJLFNBQVMsR0FBWSxJQUFJLENBQUM7WUFFOUIsSUFBSSxvQkFBb0IsRUFBRTtnQkFDdEIscUJBQXFCO2dCQUVyQixTQUFTLEdBQUksSUFBSSxDQUFDLHVCQUErQixDQUFDLGtCQUFrQixDQUNoRSxDQUFDLEVBQUUsQ0FBQyxFQUFHLGtCQUFrQjtnQkFDekIsSUFBSSxDQUFDLGVBQWUsRUFDcEIsZUFBYSxDQUFDLFNBQUksQ0FBRyxDQUN4QixDQUFDO2FBQ0w7aUJBQU07Z0JBQ0gsU0FBUztnQkFFVCxTQUFTLEdBQUcsSUFBSSxDQUFDLHVCQUF1QixDQUFDLGtCQUFrQixDQUN2RCxDQUFDLEVBQUUsQ0FBQyxFQUNKLElBQUksQ0FBQyxlQUFlLEVBQ3BCLGVBQWEsQ0FBQyxTQUFJLENBQUcsQ0FDeEIsQ0FBQzthQUNMO1lBRUQsSUFBSSxTQUFTLEVBQUU7Z0JBQ1gsb0JBQW9CO2dCQUNwQixJQUFJLENBQUMsY0FBYyxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsQ0FBQzthQUV2QztpQkFBTTtnQkFDSCxFQUFFLENBQUMsS0FBSyxDQUFDLGdDQUFVLENBQUMsVUFBSyxDQUFDLGlHQUFtQixDQUFDLENBQUM7YUFDbEQ7U0FDSjtRQUFDLE9BQU8sS0FBSyxFQUFFO1lBQ1osRUFBRSxDQUFDLEtBQUssQ0FBQyxrRkFBaUIsRUFBRSxLQUFLLENBQUMsQ0FBQztTQUN0QztJQUNMLENBQUM7SUFFRDs7T0FFRztJQUNJLDZDQUFlLEdBQXRCO1FBR0ksSUFBSSxDQUFDLGNBQWMsQ0FBQyxPQUFPLENBQUMsVUFBQyxJQUFJLEVBQUUsS0FBSztZQUNwQyxJQUFJLElBQUksSUFBSSxFQUFFLENBQUMsT0FBTyxDQUFDLElBQUksQ0FBQyxFQUFFO2dCQUUxQixJQUFJLENBQUMsT0FBTyxFQUFFLENBQUM7YUFDbEI7UUFDTCxDQUFDLENBQUMsQ0FBQztRQUVILE9BQU87UUFDUCxJQUFJLENBQUMsY0FBYyxHQUFHLEVBQUUsQ0FBQztJQUU3QixDQUFDO0lBRUQ7O09BRUc7SUFDSSw2Q0FBZSxHQUF0QjtRQUVJLElBQUksQ0FBQyxlQUFlLEVBQUUsQ0FBQztRQUN2QixrQkFBa0I7SUFDdEIsQ0FBQztJQUVELHVDQUFTLEdBQVQ7UUFDSSxTQUFTO1FBQ1QsSUFBSSxDQUFDLG1DQUFtQyxFQUFFLENBQUM7UUFFM0MsVUFBVTtRQUNWLElBQUksQ0FBQyxlQUFlLEVBQUUsQ0FBQztRQUV2QixTQUFTO1FBQ1QsSUFBSSxJQUFJLENBQUMsV0FBVyxFQUFFO1lBQ2xCLElBQUksQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxPQUFPLEVBQUUsSUFBSSxDQUFDLGtCQUFrQixFQUFFLElBQUksQ0FBQyxDQUFDO1NBQ3JFO1FBQ0QsSUFBSSxJQUFJLENBQUMsZUFBZSxFQUFFO1lBQ3RCLElBQUksQ0FBQyxlQUFlLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxPQUFPLEVBQUUsSUFBSSxDQUFDLHNCQUFzQixFQUFFLElBQUksQ0FBQyxDQUFDO1NBQzdFO1FBQ0QsK0NBQStDO1FBQy9DLElBQUksSUFBSSxDQUFDLG9CQUFvQixFQUFFO1lBQzNCLElBQUksQ0FBQyxvQkFBb0IsQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLE9BQU8sRUFBRSxJQUFJLENBQUMscUJBQXFCLEVBQUUsSUFBSSxDQUFDLENBQUM7U0FDakY7SUFDTCxDQUFDO0lBanNDRDtRQURDLFFBQVEsQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDOzJEQUNTO0lBSTNCO1FBREMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxNQUFNLENBQUM7Z0VBQ2M7SUFJbEM7UUFEQyxRQUFRLENBQUMsRUFBRSxDQUFDLEtBQUssQ0FBQzsrREFDYTtJQUloQztRQURDLFFBQVEsQ0FBQyxFQUFFLENBQUMsS0FBSyxDQUFDO2tFQUNnQjtJQUluQztRQURDLFFBQVEsQ0FBQywrQkFBcUIsQ0FBQztzRUFDb0I7SUFJcEQ7UUFEQyxRQUFRLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQzs4REFDWTtJQUk5QjtRQURDLFFBQVEsQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDOzZEQUNXO0lBSTdCO1FBREMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUM7NkRBQ1c7SUFJN0I7UUFEQyxRQUFRLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQzs2REFDVztJQUc3QjtRQURDLFFBQVEsQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDOzZEQUNXO0lBRzdCO1FBREMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUM7NkRBQ1c7SUFHN0I7UUFEQyxRQUFRLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQzs4REFDWTtJQUc5QjtRQURDLFFBQVEsQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDOytEQUNhO0lBSS9CO1FBREMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUM7OERBQ1k7SUFHOUI7UUFEQyxRQUFRLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQzs4REFDWTtJQUc5QjtRQURDLFFBQVEsQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDOzhEQUNZO0lBRzlCO1FBREMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUM7OERBQ1k7SUFHOUI7UUFEQyxRQUFRLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQzs4REFDWTtJQUc5QjtRQURDLFFBQVEsQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDOzhEQUNZO0lBSTlCO1FBREMsUUFBUSxDQUFDLG9DQUEwQixDQUFDOzJFQUN5QjtJQUk5RDtRQURDLFFBQVEsQ0FBQyx1Q0FBNkIsQ0FBQzs4RUFDNEI7SUFJcEU7UUFEQyxRQUFRLENBQUMsRUFBRSxDQUFDLE1BQU0sQ0FBQztxRUFDbUI7SUFJdkM7UUFEQyxRQUFRLENBQUMsRUFBRSxDQUFDLE1BQU0sQ0FBQztnRUFDYztJQU9sQztRQURDLFFBQVEsQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDO29FQUNrQjtJQUdwQztRQURDLFFBQVEsQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDOzREQUNVO0lBRzVCO1FBREMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUM7MkRBQ1M7SUFHM0I7UUFEQyxRQUFRLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQzswREFDUTtJQUcxQjtRQURDLFFBQVEsQ0FBQyxFQUFFLENBQUMsTUFBTSxDQUFDOzREQUNVO0lBRzlCO1FBREMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxNQUFNLENBQUM7Z0VBQ2M7SUFHbEM7UUFEQyxRQUFRLENBQUMsRUFBRSxDQUFDLE1BQU0sQ0FBQzsyREFDUztJQTVHWixtQkFBbUI7UUFEdkMsT0FBTztPQUNhLG1CQUFtQixDQXNzQ3ZDO0lBQUQsMEJBQUM7Q0F0c0NELEFBc3NDQyxDQXRzQ2dELEVBQUUsQ0FBQyxTQUFTLEdBc3NDNUQ7a0JBdHNDb0IsbUJBQW1CIiwiZmlsZSI6IiIsInNvdXJjZVJvb3QiOiIvIiwic291cmNlc0NvbnRlbnQiOlsiLy8gTGVhcm4gVHlwZVNjcmlwdDpcbi8vICAtIGh0dHBzOi8vZG9jcy5jb2Nvcy5jb20vY3JlYXRvci8yLjQvbWFudWFsL2VuL3NjcmlwdGluZy90eXBlc2NyaXB0Lmh0bWxcbi8vIExlYXJuIEF0dHJpYnV0ZTpcbi8vICAtIGh0dHBzOi8vZG9jcy5jb2Nvcy5jb20vY3JlYXRvci8yLjQvbWFudWFsL2VuL3NjcmlwdGluZy9yZWZlcmVuY2UvYXR0cmlidXRlcy5odG1sXG4vLyBMZWFybiBsaWZlLWN5Y2xlIGNhbGxiYWNrczpcbi8vICAtIGh0dHBzOi8vZG9jcy5jb2Nvcy5jb20vY3JlYXRvci8yLjQvbWFudWFsL2VuL3NjcmlwdGluZy9saWZlLWN5Y2xlLWNhbGxiYWNrcy5odG1sXG5cbmltcG9ydCB7IEV4dGVuZExldmVsSW5mb1Jlc3BvbnNlIH0gZnJvbSBcIi4uL2JlYW4vR2FtZUJlYW5cIjtcbmltcG9ydCBMZWF2ZURpYWxvZ0NvbnRyb2xsZXIgZnJvbSBcIi4uL2hhbGwvTGVhdmVEaWFsb2dDb250cm9sbGVyXCI7XG5pbXBvcnQgeyBUb29scyB9IGZyb20gXCIuLi91dGlsL1Rvb2xzXCI7XG5pbXBvcnQgeyBDb25maWcgfSBmcm9tIFwiLi4vdXRpbC9Db25maWdcIjtcbmltcG9ydCBHbG9iYWxNYW5hZ2VyQ29udHJvbGxlciwgeyBQYWdlVHlwZSB9IGZyb20gXCIuLi9HbG9iYWxNYW5hZ2VyQ29udHJvbGxlclwiO1xuaW1wb3J0IFNpbmdsZUNoZXNzQm9hcmRDb250cm9sbGVyIGZyb20gXCIuLi9nYW1lL0NoZXNzL1NpbmdsZUNoZXNzQm9hcmRDb250cm9sbGVyXCI7XG5pbXBvcnQgSGV4U2luZ2xlQ2hlc3NCb2FyZENvbnRyb2xsZXIgZnJvbSBcIi4uL2dhbWUvQ2hlc3MvSGV4U2luZ2xlQ2hlc3NCb2FyZENvbnRyb2xsZXJcIjtcbmltcG9ydCB7IFdlYlNvY2tldE1hbmFnZXIgfSBmcm9tIFwiLi4vbmV0L1dlYlNvY2tldE1hbmFnZXJcIjtcbmltcG9ydCB7IE1lc3NhZ2VJZCB9IGZyb20gXCIuLi9uZXQvTWVzc2FnZUlkXCI7XG5pbXBvcnQgeyBHYW1lTWdyIH0gZnJvbSBcIi4uL2NvbW1vbi9HYW1lTWdyXCI7XG5pbXBvcnQgeyBFdmVudFR5cGUgfSBmcm9tIFwiLi4vY29tbW9uL0V2ZW50Q2VudGVyXCI7XG5pbXBvcnQgeyBSZWNlaXZlZE1lc3NhZ2VCZWFuIH0gZnJvbSBcIi4uL25ldC9NZXNzYWdlQmFzZUJlYW5cIjtcblxuY29uc3QgeyBjY2NsYXNzLCBwcm9wZXJ0eSB9ID0gY2MuX2RlY29yYXRvcjtcblxuQGNjY2xhc3NcbmV4cG9ydCBkZWZhdWx0IGNsYXNzIExldmVsUGFnZUNvbnRyb2xsZXIgZXh0ZW5kcyBjYy5Db21wb25lbnQge1xuXG4gICAgLy8g6L+U5Zue5oyJ6ZKuXG4gICAgQHByb3BlcnR5KGNjLk5vZGUpXG4gICAgYmFja0J1dHRvbjogY2MuTm9kZSA9IG51bGw7XG5cbiAgICAvLyDlvIDlp4vmuLjmiI/mjInpkq5cbiAgICBAcHJvcGVydHkoY2MuQnV0dG9uKVxuICAgIHN0YXJ0R2FtZUJ1dHRvbjogY2MuQnV0dG9uID0gbnVsbDtcblxuICAgIC8vIOWcsOmbt+aVsFVJ5qCH562+XG4gICAgQHByb3BlcnR5KGNjLkxhYmVsKVxuICAgIG1pbmVDb3VudExhYmVsOiBjYy5MYWJlbCA9IG51bGw7XG5cbiAgICAvLyDlvZPliY3lhbPljaHmlbBVSeagh+etvlxuICAgIEBwcm9wZXJ0eShjYy5MYWJlbClcbiAgICBjdXJyZW50TGV2ZWxMYWJlbDogY2MuTGFiZWwgPSBudWxsO1xuXG4gICAgLy8g6YCA5Ye65ri45oiP5by556qXXG4gICAgQHByb3BlcnR5KExlYXZlRGlhbG9nQ29udHJvbGxlcilcbiAgICBsZWF2ZURpYWxvZ0NvbnRyb2xsZXI6IExlYXZlRGlhbG9nQ29udHJvbGxlciA9IG51bGw7XG5cbiAgICAvLyBsZXZlbF9wYWdl6IqC54K5XG4gICAgQHByb3BlcnR5KGNjLk5vZGUpXG4gICAgbGV2ZWxQYWdlTm9kZTogY2MuTm9kZSA9IG51bGw7XG5cbiAgICAvLyBnYW1lX21hcF8x6IqC54K5XG4gICAgQHByb3BlcnR5KGNjLk5vZGUpXG4gICAgZ2FtZU1hcDFOb2RlOiBjYy5Ob2RlID0gbnVsbDtcblxuICAgIC8vIGdhbWVfbWFwXzLoioLngrlcbiAgICBAcHJvcGVydHkoY2MuTm9kZSlcbiAgICBnYW1lTWFwMk5vZGU6IGNjLk5vZGUgPSBudWxsO1xuXG4gICAgLy8g5pa55b2i5Zyw5Zu+6IqC54K55byV55SoXG4gICAgQHByb3BlcnR5KGNjLk5vZGUpXG4gICAgcWlwYW44eDhOb2RlOiBjYy5Ob2RlID0gbnVsbDsgLy8gbGV2ZWxfcGFnZS9nYW1lX21hcF8xL2NoZXNzX2JnL3FpcGFuOCo4XG5cbiAgICBAcHJvcGVydHkoY2MuTm9kZSlcbiAgICBxaXBhbjh4OU5vZGU6IGNjLk5vZGUgPSBudWxsOyAvLyBsZXZlbF9wYWdlL2dhbWVfbWFwXzEvY2hlc3NfYmcvcWlwYW44KjlcblxuICAgIEBwcm9wZXJ0eShjYy5Ob2RlKVxuICAgIHFpcGFuOXg5Tm9kZTogY2MuTm9kZSA9IG51bGw7IC8vIGxldmVsX3BhZ2UvZ2FtZV9tYXBfMS9jaGVzc19iZy9xaXBhbjkqOVxuXG4gICAgQHByb3BlcnR5KGNjLk5vZGUpXG4gICAgcWlwYW45eDEwTm9kZTogY2MuTm9kZSA9IG51bGw7IC8vIGxldmVsX3BhZ2UvZ2FtZV9tYXBfMS9jaGVzc19iZy9xaXBhbjkqMTBcblxuICAgIEBwcm9wZXJ0eShjYy5Ob2RlKVxuICAgIHFpcGFuMTB4MTBOb2RlOiBjYy5Ob2RlID0gbnVsbDsgLy8gbGV2ZWxfcGFnZS9nYW1lX21hcF8xL2NoZXNzX2JnL3FpcGFuMTAqMTBcblxuICAgIC8vIOeJueauiuWFs+WNoeiKgueCueW8leeUqFxuICAgIEBwcm9wZXJ0eShjYy5Ob2RlKVxuICAgIGxldmVsUzAwMU5vZGU6IGNjLk5vZGUgPSBudWxsOyAvLyBsZXZlbF9wYWdlL2dhbWVfbWFwXzIvZ2FtZV9iZy9MZXZlbF9TMDAxXG5cbiAgICBAcHJvcGVydHkoY2MuTm9kZSlcbiAgICBsZXZlbFMwMDJOb2RlOiBjYy5Ob2RlID0gbnVsbDsgLy8gbGV2ZWxfcGFnZS9nYW1lX21hcF8yL2dhbWVfYmcvTGV2ZWxfUzAwMlxuXG4gICAgQHByb3BlcnR5KGNjLk5vZGUpXG4gICAgbGV2ZWxTMDAzTm9kZTogY2MuTm9kZSA9IG51bGw7IC8vIGxldmVsX3BhZ2UvZ2FtZV9tYXBfMi9nYW1lX2JnL0xldmVsX1MwMDNcblxuICAgIEBwcm9wZXJ0eShjYy5Ob2RlKVxuICAgIGxldmVsUzAwNE5vZGU6IGNjLk5vZGUgPSBudWxsOyAvLyBsZXZlbF9wYWdlL2dhbWVfbWFwXzIvZ2FtZV9iZy9MZXZlbF9TMDA0XG5cbiAgICBAcHJvcGVydHkoY2MuTm9kZSlcbiAgICBsZXZlbFMwMDVOb2RlOiBjYy5Ob2RlID0gbnVsbDsgLy8gbGV2ZWxfcGFnZS9nYW1lX21hcF8yL2dhbWVfYmcvTGV2ZWxfUzAwNVxuXG4gICAgQHByb3BlcnR5KGNjLk5vZGUpXG4gICAgbGV2ZWxTMDA2Tm9kZTogY2MuTm9kZSA9IG51bGw7IC8vIGxldmVsX3BhZ2UvZ2FtZV9tYXBfMi9nYW1lX2JnL0xldmVsX1MwMDZcblxuICAgIC8vIOWNleacuuaooeW8j+aji+ebmOaOp+WItuWZqFxuICAgIEBwcm9wZXJ0eShTaW5nbGVDaGVzc0JvYXJkQ29udHJvbGxlcilcbiAgICBzaW5nbGVDaGVzc0JvYXJkQ29udHJvbGxlcjogU2luZ2xlQ2hlc3NCb2FyZENvbnRyb2xsZXIgPSBudWxsO1xuXG4gICAgLy8g5YWt6L655b2i5Y2V5py65qih5byP5qOL55uY5o6n5Yi25ZmoXG4gICAgQHByb3BlcnR5KEhleFNpbmdsZUNoZXNzQm9hcmRDb250cm9sbGVyKVxuICAgIGhleFNpbmdsZUNoZXNzQm9hcmRDb250cm9sbGVyOiBIZXhTaW5nbGVDaGVzc0JvYXJkQ29udHJvbGxlciA9IG51bGw7XG5cbiAgICAvLyDmtYvor5XmjInpkq7vvIjnlKjkuo7osIPor5XmmL7npLrlnLDpm7fkvY3nva7vvIlcbiAgICBAcHJvcGVydHkoY2MuQnV0dG9uKVxuICAgIGRlYnVnU2hvd01pbmVzQnV0dG9uOiBjYy5CdXR0b24gPSBudWxsO1xuXG4gICAgLy8g5rWL6K+V6aKE5Yi25L2T77yI55So5LqO5pi+56S65Zyw6Zu35L2N572u77yJXG4gICAgQHByb3BlcnR5KGNjLlByZWZhYilcbiAgICBkZWJ1Z01pbmVQcmVmYWI6IGNjLlByZWZhYiA9IG51bGw7XG5cbiAgICAvLyDlrZjlgqjliJvlu7rnmoTmtYvor5XpooTliLbkvZPoioLngrnvvIznlKjkuo7muIXnkIZcbiAgICBwcml2YXRlIGRlYnVnTWluZU5vZGVzOiBjYy5Ob2RlW10gPSBbXTtcblxuICAgIC8vIOe7k+eul+mhtemdouebuOWFs+iKgueCuVxuICAgIEBwcm9wZXJ0eShjYy5Ob2RlKVxuICAgIGxldmVsU2V0dGxlbWVudE5vZGU6IGNjLk5vZGUgPSBudWxsOyAvLyBsZXZlbF9zZXR0bGVtZW506IqC54K5XG5cbiAgICBAcHJvcGVydHkoY2MuTm9kZSlcbiAgICBib2FyZEJnTm9kZTogY2MuTm9kZSA9IG51bGw7IC8vIGxldmVsX3NldHRsZW1lbnQvYm9hcmRfYmfoioLngrlcblxuICAgIEBwcm9wZXJ0eShjYy5Ob2RlKVxuICAgIGxvc2VCZ05vZGU6IGNjLk5vZGUgPSBudWxsOyAvLyBsZXZlbF9zZXR0bGVtZW50L2JvYXJkX2JnL2xvc2VfYmfoioLngrlcblxuICAgIEBwcm9wZXJ0eShjYy5Ob2RlKVxuICAgIHdpbkJnTm9kZTogY2MuTm9kZSA9IG51bGw7IC8vIGxldmVsX3NldHRsZW1lbnQvYm9hcmRfYmcvd2luX2Jn6IqC54K5XG5cbiAgICBAcHJvcGVydHkoY2MuQnV0dG9uKVxuICAgIHJldHJ5QnV0dG9uOiBjYy5CdXR0b24gPSBudWxsOyAvLyDlho3mnaXkuIDmrKHmjInpkq5cblxuICAgIEBwcm9wZXJ0eShjYy5CdXR0b24pXG4gICAgbmV4dExldmVsQnV0dG9uOiBjYy5CdXR0b24gPSBudWxsOyAvLyDkuIvkuIDlhbPmjInpkq5cblxuICAgIEBwcm9wZXJ0eShjYy5CdXR0b24pXG4gICAgZXhpdEJ1dHRvbjogY2MuQnV0dG9uID0gbnVsbDsgLy8g6YCA5Ye65oyJ6ZKuXG5cbiAgICAvLyDlvZPliY3lhbPljaHmlbDmja5cbiAgICBwcml2YXRlIGN1cnJlbnRMZXZlbDogbnVtYmVyID0gMTtcbiAgICBwcml2YXRlIGN1cnJlbnRMZXZlbEluZm86IEV4dGVuZExldmVsSW5mb1Jlc3BvbnNlID0gbnVsbDtcbiAgICBwcml2YXRlIGN1cnJlbnRSb29tSWQ6IG51bWJlciA9IDA7IC8vIOW9k+WJjeWFs+WNoea4uOaIj+eahOaIv+mXtElEXG4gICAgcHJpdmF0ZSBjdXJyZW50U2luZ2xlQ2hlc3NCb2FyZDogU2luZ2xlQ2hlc3NCb2FyZENvbnRyb2xsZXIgfCBIZXhTaW5nbGVDaGVzc0JvYXJkQ29udHJvbGxlciA9IG51bGw7IC8vIOW9k+WJjea/gOa0u+eahOWNleacuuaji+ebmFxuXG4gICAgLy8g6K6w5b2V5pyA5ZCO5LiA5qyh54K55Ye75piv5ZCm5piv54K45by5XG4gICAgcHJpdmF0ZSBsYXN0Q2xpY2tXYXNCb21iOiBib29sZWFuID0gZmFsc2U7XG5cbiAgICAvLyDmgKfog73kvJjljJbnm7jlhbNcbiAgICBwcml2YXRlIGxhc3RTaG93bk1hcE5vZGU6IGNjLk5vZGUgPSBudWxsOyAvLyDorrDlvZXkuIrmrKHmmL7npLrnmoTlnLDlm77oioLngrlcbiAgICBwcml2YXRlIGlzVXBkYXRpbmc6IGJvb2xlYW4gPSBmYWxzZTsgLy8g6Ziy5q2i6YeN5aSN5pu05pawXG5cbiAgICBvbkxvYWQoKSB7XG4gICAgICAgIC8vIOiuvue9rui/lOWbnuaMiemSrueCueWHu+S6i+S7tiAtIOS9v+eUqOS4jkdhbWVQYWdlQ29udHJvbGxlcuebuOWQjOeahOagt+W8j1xuICAgICAgICBpZiAodGhpcy5iYWNrQnV0dG9uKSB7XG4gICAgICAgICAgICBUb29scy5pbWFnZUJ1dHRvbkNsaWNrKHRoaXMuYmFja0J1dHRvbiwgQ29uZmlnLmJ1dHRvblJlcyArICdzaWRlX2J0bl9iYWNrX25vcm1hbCcsIENvbmZpZy5idXR0b25SZXMgKyAnc2lkZV9idG5fYmFja19wcmVzc2VkJywgKCkgPT4ge1xuICAgICAgICAgICAgICAgIHRoaXMub25CYWNrQnV0dG9uQ2xpY2soKTtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8g6K6+572u5byA5aeL5ri45oiP5oyJ6ZKu54K55Ye75LqL5Lu2XG4gICAgICAgIGlmICh0aGlzLnN0YXJ0R2FtZUJ1dHRvbikge1xuICAgICAgICAgICAgdGhpcy5zdGFydEdhbWVCdXR0b24ubm9kZS5vbignY2xpY2snLCB0aGlzLm9uU3RhcnRHYW1lQnV0dG9uQ2xpY2ssIHRoaXMpO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8g5rOo5YaM5Y2V5py65qih5byP5raI5oGv55uR5ZCsXG4gICAgICAgIHRoaXMucmVnaXN0ZXJTaW5nbGVNb2RlTWVzc2FnZUhhbmRsZXJzKCk7XG5cbiAgICAgICAgLy8g6K6+572u57uT566X6aG16Z2i5oyJ6ZKu5LqL5Lu2XG4gICAgICAgIHRoaXMuc2V0dXBTZXR0bGVtZW50QnV0dG9ucygpO1xuXG4gICAgICAgIC8vIOazqOaEj++8muS4jeWcqG9uTG9hZOS4reWIneWni+WMluWFs+WNoeaVsFVJ77yM562J5b6F5aSW6YOo6LCD55Soc2V0Q3VycmVudExldmVs5pe25YaN5pu05pawXG4gICAgfVxuXG4gICAgc3RhcnQoKSB7XG4gICAgICAgIC8vIOWIneWni+WMluaXtumakOiXj+aJgOacieWcsOWbvuiKgueCuVxuICAgICAgICB0aGlzLmhpZGVBbGxNYXBOb2RlcygpO1xuXG4gICAgICAgIC8vIOiuvue9rua1i+ivleaMiemSrueCueWHu+S6i+S7tlxuICAgICAgICB0aGlzLnNldHVwRGVidWdCdXR0b24oKTtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDov5Tlm57mjInpkq7ngrnlh7vkuovku7ZcbiAgICAgKi9cbiAgICBwcml2YXRlIG9uQmFja0J1dHRvbkNsaWNrKCkge1xuICAgICAgICAvLyDlpoLmnpzmsqHmnInmnInmlYjnmoTmiL/pl7RJRO+8jOivtOaYjui/mOayoeaciei/m+WFpea4uOaIj++8jOebtOaOpei/lOWbnuWFs+WNoemAieaLqemhtemdolxuICAgICAgICBpZiAodGhpcy5jdXJyZW50Um9vbUlkIDw9IDApIHtcbiAgICAgICAgICAgXG4gICAgICAgICAgICB0aGlzLnJldHVyblRvTGV2ZWxTZWxlY3QoKTtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIOW8ueWHuuehruiupOmAgOWHuuWvueivneahhu+8jHR5cGU9MeihqOekuumAgOWHuuacrOWxgOa4uOaIj++8jOS8oOmAkuW9k+WJjeaIv+mXtElEXG4gICAgICAgIGlmICh0aGlzLmxlYXZlRGlhbG9nQ29udHJvbGxlcikge1xuXG4gICAgICAgICAgICB0aGlzLmxlYXZlRGlhbG9nQ29udHJvbGxlci5zaG93KDEsICgpID0+IHtcblxuICAgICAgICAgICAgfSwgdGhpcy5jdXJyZW50Um9vbUlkKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIGNjLndhcm4oXCJMZWF2ZURpYWxvZ0NvbnRyb2xsZXIg5pyq6YWN572uXCIpO1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog6L+U5Zue5Yiw5YWz5Y2h6YCJ5oup6aG16Z2iXG4gICAgICovXG4gICAgcHJpdmF0ZSByZXR1cm5Ub0xldmVsU2VsZWN0KCkge1xuICAgICAgICAvLyDmn6Xmib5HbG9iYWxNYW5hZ2VyQ29udHJvbGxlcuW5tuWIh+aNouWIsOWkp+WOhemhtemdolxuICAgICAgICBjb25zdCBnbG9iYWxNYW5hZ2VyTm9kZSA9IGNjLmZpbmQoXCJDYW52YXMvZ2xvYmFsX25vZGVcIikgfHwgY2MuZmluZChcImdsb2JhbF9ub2RlXCIpO1xuICAgICAgICBpZiAoZ2xvYmFsTWFuYWdlck5vZGUpIHtcbiAgICAgICAgICAgIGNvbnN0IGdsb2JhbE1hbmFnZXIgPSBnbG9iYWxNYW5hZ2VyTm9kZS5nZXRDb21wb25lbnQoR2xvYmFsTWFuYWdlckNvbnRyb2xsZXIpO1xuICAgICAgICAgICAgaWYgKGdsb2JhbE1hbmFnZXIpIHtcbiAgICAgICAgICAgICAgICBnbG9iYWxNYW5hZ2VyLnNldEN1cnJlbnRQYWdlKFBhZ2VUeXBlLkhBTExfUEFHRSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDnpoHnlKjov5Tlm57mjInpkq5cbiAgICAgKi9cbiAgICBwcml2YXRlIGRpc2FibGVCYWNrQnV0dG9uKCkge1xuICAgICAgICBpZiAodGhpcy5iYWNrQnV0dG9uKSB7XG4gICAgICAgICAgICAvLyDnp7vpmaTmiYDmnInop6bmkbjkuovku7bnm5HlkKzlmahcbiAgICAgICAgICAgIHRoaXMuYmFja0J1dHRvbi5vZmYoY2MuTm9kZS5FdmVudFR5cGUuVE9VQ0hfU1RBUlQpO1xuICAgICAgICAgICAgdGhpcy5iYWNrQnV0dG9uLm9mZihjYy5Ob2RlLkV2ZW50VHlwZS5UT1VDSF9FTkQpO1xuICAgICAgICAgICAgdGhpcy5iYWNrQnV0dG9uLm9mZihjYy5Ob2RlLkV2ZW50VHlwZS5UT1VDSF9DQU5DRUwpO1xuXG4gICAgICAgICAgICAvLyDorr7nva7mjInpkq7kuLrljYrpgI/mmI7nirbmgIHvvIzooajnpLrnpoHnlKhcbiAgICAgICAgICAgIHRoaXMuYmFja0J1dHRvbi5vcGFjaXR5ID0gMTI4O1xuXG4gICAgICAgICAgIFxuICAgICAgICB9XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5ZCv55So6L+U5Zue5oyJ6ZKuXG4gICAgICovXG4gICAgcHJpdmF0ZSBlbmFibGVCYWNrQnV0dG9uKCkge1xuICAgICAgICBpZiAodGhpcy5iYWNrQnV0dG9uKSB7XG4gICAgICAgICAgICAvLyDmgaLlpI3mjInpkq7pgI/mmI7luqZcbiAgICAgICAgICAgIHRoaXMuYmFja0J1dHRvbi5vcGFjaXR5ID0gMjU1O1xuXG4gICAgICAgICAgICAvLyDph43mlrDorr7nva7ov5Tlm57mjInpkq7ngrnlh7vkuovku7ZcbiAgICAgICAgICAgIFRvb2xzLmltYWdlQnV0dG9uQ2xpY2sodGhpcy5iYWNrQnV0dG9uLCBDb25maWcuYnV0dG9uUmVzICsgJ3NpZGVfYnRuX2JhY2tfbm9ybWFsJywgQ29uZmlnLmJ1dHRvblJlcyArICdzaWRlX2J0bl9iYWNrX3ByZXNzZWQnLCAoKSA9PiB7XG4gICAgICAgICAgICAgICAgdGhpcy5vbkJhY2tCdXR0b25DbGljaygpO1xuICAgICAgICAgICAgfSk7XG5cbiAgICAgICAgICBcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOW8gOWni+a4uOaIj+aMiemSrueCueWHu+S6i+S7tlxuICAgICAqL1xuICAgIHByaXZhdGUgb25TdGFydEdhbWVCdXR0b25DbGljaygpIHtcbiAgICAgICAgLy8gRXh0ZW5kTGV2ZWxJbmZv5raI5oGv546w5Zyo55SxTGV2ZWxTZWxlY3RQYWdlQ29udHJvbGxlcuWPkemAgVxuICAgICAgICAvLyDov5nph4znm7TmjqXov5vlhaXmuLjmiI/vvIznrYnlvoXlkI7nq6/lk43lupRcbiAgICAgICAgXG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5aSE55CGRXh0ZW5kTGV2ZWxJbmZv5ZON5bqUXG4gICAgICogQHBhcmFtIGxldmVsSW5mbyDlhbPljaHkv6Hmga/lk43lupTmlbDmja5cbiAgICAgKi9cbiAgICBwdWJsaWMgb25FeHRlbmRMZXZlbEluZm8obGV2ZWxJbmZvOiBFeHRlbmRMZXZlbEluZm9SZXNwb25zZSkge1xuXG4gICAgICAgIHRoaXMuY3VycmVudExldmVsSW5mbyA9IGxldmVsSW5mbztcblxuICAgICAgICAvLyDkv53lrZjmiL/pl7RJRO+8jOeUqOS6jumAgOWHuuaXtuS9v+eUqFxuICAgICAgICBpZiAobGV2ZWxJbmZvLnJvb21JZCkge1xuICAgICAgICAgICAgdGhpcy5jdXJyZW50Um9vbUlkID0gbGV2ZWxJbmZvLnJvb21JZDtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIOajgOafpeaYr+WQpuS4uuaWree6v+mHjei/nlxuICAgICAgICBpZiAobGV2ZWxJbmZvLnJlY29ubmVjdGVkID09PSB0cnVlKSB7XG4gICAgICAgICAgICBHYW1lTWdyLkNvbnNvbGUuTG9nKFwi5YWz5Y2h5qih5byP5pat57q/6YeN6L+e77yM5oGi5aSN5ri45oiP54q25oCBXCIpO1xuXG4gICAgICAgICAgICAvLyDmlq3nur/ph43ov57ml7bvvIzkvb/nlKjlkI7nq6/ov5Tlm57nmoRsZXZlbElk6K6+572u5b2T5YmN5YWz5Y2hXG4gICAgICAgICAgICBpZiAobGV2ZWxJbmZvLmxldmVsSWQpIHtcbiAgICAgICAgICAgICAgICB0aGlzLmN1cnJlbnRMZXZlbCA9IGxldmVsSW5mby5sZXZlbElkO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAvLyDmgaLlpI3lnLDpm7fmlbBVSVxuICAgICAgICAgICAgaWYgKGxldmVsSW5mby5yZW1haW5pbmdNaW5lcyAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICAgICAgdGhpcy51cGRhdGVNaW5lQ291bnRVSShsZXZlbEluZm8ucmVtYWluaW5nTWluZXMpO1xuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICB0aGlzLnVwZGF0ZU1pbmVDb3VudFVJKGxldmVsSW5mby5taW5lQ291bnQpO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAvLyDov5vlhaXlhbPljaFcbiAgICAgICAgICAgIHRoaXMuZW50ZXJMZXZlbCh0aGlzLmN1cnJlbnRMZXZlbCk7XG5cbiAgICAgICAgICAgIC8vIOmAmuefpeW9k+WJjea/gOa0u+eahOWNleacuuaji+ebmOaOp+WItuWZqOWkhOeQhuaWree6v+mHjei/nlxuICAgICAgICAgICAgaWYgKHRoaXMuY3VycmVudFNpbmdsZUNoZXNzQm9hcmQpIHtcbiAgICAgICAgICAgICAgICAvLyDmo4Dmn6XmjqfliLblmajnsbvlnovlubbosIPnlKjnm7jlupTnmoTmlrnms5VcbiAgICAgICAgICAgICAgICBpZiAoJ29uRXh0ZW5kTGV2ZWxJbmZvUmVjb25uZWN0JyBpbiB0aGlzLmN1cnJlbnRTaW5nbGVDaGVzc0JvYXJkKSB7XG4gICAgICAgICAgICAgICAgICAgICh0aGlzLmN1cnJlbnRTaW5nbGVDaGVzc0JvYXJkIGFzIGFueSkub25FeHRlbmRMZXZlbEluZm9SZWNvbm5lY3QobGV2ZWxJbmZvKTtcbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICBjb25zb2xlLndhcm4oXCLlvZPliY3mo4vnm5jmjqfliLblmajkuI3mlK/mjIHmlq3nur/ph43ov57mlrnms5VcIik7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgLy8g5q2j5bi45byA5aeL5paw5ri45oiPXG4gICAgICAgICAgICBHYW1lTWdyLkNvbnNvbGUuTG9nKFwi5YWz5Y2h5qih5byP5byA5aeL5paw5ri45oiPXCIpO1xuXG4gICAgICAgICAgICAvLyDph43nva7lhbPljaHnirbmgIHvvIjljIXmi6zmuIXpmaTmtYvor5XpooTliLbkvZPvvIlcbiAgICAgICAgICAgIHRoaXMucmVzZXRMZXZlbFN0YXRlKCk7XG5cbiAgICAgICAgICAgIC8vIOmHjee9rueCuOW8ueeCueWHu+agh+iusFxuICAgICAgICAgICAgdGhpcy5sYXN0Q2xpY2tXYXNCb21iID0gZmFsc2U7XG5cbiAgICAgICAgICAgIC8vIOW8gOWni+aWsOa4uOaIj+aXtu+8jOWFiOmHjee9ruW9k+WJjeaji+ebmO+8iOa4heeQhuS4iuS4gOWxgOeahOeXlei/ue+8iVxuICAgICAgICAgICAgaWYgKHRoaXMuY3VycmVudFNpbmdsZUNoZXNzQm9hcmQpIHtcbiAgICAgICAgICAgICAgICB0aGlzLmN1cnJlbnRTaW5nbGVDaGVzc0JvYXJkLnJlc2V0Qm9hcmQoKTtcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgLy8g5pu05paw5Zyw6Zu35pWwVUlcbiAgICAgICAgICAgIHRoaXMudXBkYXRlTWluZUNvdW50VUkobGV2ZWxJbmZvLm1pbmVDb3VudCk7XG5cbiAgICAgICAgICAgIC8vIOS9v+eUqOW9k+WJjeiuvue9rueahOWFs+WNoee8luWPt++8jOiAjOS4jeaYr+WQjuerr+i/lOWbnueahGxldmVsSWRcbiAgICAgICAgICAgIC8vIOWboOS4uuWQjuerr+eahGxldmVsSWTlj6/og73kuI7liY3nq6/nmoTlhbPljaHnvJblj7fkuI3kuIDoh7RcbiAgICAgICAgICAgIHRoaXMuZW50ZXJMZXZlbCh0aGlzLmN1cnJlbnRMZXZlbCk7XG5cbiAgICAgICAgICAgIC8vIOmAmuefpeW9k+WJjea/gOa0u+eahOWNleacuuaji+ebmOaOp+WItuWZqOWkhOeQhkV4dGVuZExldmVsSW5mb1xuICAgICAgICAgICAgaWYgKHRoaXMuY3VycmVudFNpbmdsZUNoZXNzQm9hcmQpIHtcbiAgICAgICAgICAgICAgICB0aGlzLmN1cnJlbnRTaW5nbGVDaGVzc0JvYXJkLm9uRXh0ZW5kTGV2ZWxJbmZvKCk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDmm7TmlrDlnLDpm7fmlbBVSVxuICAgICAqIEBwYXJhbSBtaW5lQ291bnQg5Zyw6Zu35pWw6YePXG4gICAgICovXG4gICAgcHJpdmF0ZSB1cGRhdGVNaW5lQ291bnRVSShtaW5lQ291bnQ6IG51bWJlcikge1xuICAgICAgICBpZiAodGhpcy5taW5lQ291bnRMYWJlbCkge1xuICAgICAgICAgICAgdGhpcy5taW5lQ291bnRMYWJlbC5zdHJpbmcgPSBtaW5lQ291bnQudG9TdHJpbmcoKTtcbiAgICAgICAgICAgXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDmm7TmlrDlvZPliY3lhbPljaHmlbBVSVxuICAgICAqIEBwYXJhbSBsZXZlbE51bWJlciDlhbPljaHnvJblj7dcbiAgICAgKi9cbiAgICBwcml2YXRlIHVwZGF0ZUN1cnJlbnRMZXZlbFVJKGxldmVsTnVtYmVyOiBudW1iZXIpIHtcbiAgICAgICAgaWYgKHRoaXMuY3VycmVudExldmVsTGFiZWwpIHtcbiAgICAgICAgICAgIHRoaXMuY3VycmVudExldmVsTGFiZWwuc3RyaW5nID0gYOesrCR7bGV2ZWxOdW1iZXJ95YWzYDtcbiAgICAgICAgICAgXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDmoLnmja7lhbPljaHmlbDov5vlhaXnm7jlupTnmoTlhbPljaHvvIjkvJjljJbniYjmnKzvvIlcbiAgICAgKiBAcGFyYW0gbGV2ZWxOdW1iZXIg5YWz5Y2h57yW5Y+3XG4gICAgICovXG4gICAgcHJpdmF0ZSBlbnRlckxldmVsKGxldmVsTnVtYmVyOiBudW1iZXIpIHtcbiAgICAgICAgLy8g6Ziy5q2i6YeN5aSN5pu05pawXG4gICAgICAgIGlmICh0aGlzLmlzVXBkYXRpbmcpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLmlzVXBkYXRpbmcgPSB0cnVlO1xuXG4gICAgICAgIC8vIOabtOaWsOWFs+WNoeaVsFVJ5pi+56S6XG4gICAgICAgIHRoaXMudXBkYXRlQ3VycmVudExldmVsVUkobGV2ZWxOdW1iZXIpO1xuXG4gICAgICAgIC8vIOiOt+WPluebruagh+WcsOWbvuiKgueCueWSjOWuueWZqFxuICAgICAgICBjb25zdCB0YXJnZXRNYXBJbmZvID0gdGhpcy5nZXRNYXBOb2RlQnlMZXZlbChsZXZlbE51bWJlcik7XG4gICAgICAgIGlmICghdGFyZ2V0TWFwSW5mbykge1xuICAgICAgICAgICAgY2Mud2Fybihg5pyq55+l55qE5YWz5Y2h57yW5Y+3OiAke2xldmVsTnVtYmVyfWApO1xuICAgICAgICAgICAgdGhpcy5pc1VwZGF0aW5nID0gZmFsc2U7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cblxuICAgICAgICAvLyDlj6rmnInlvZPnm67moIfoioLngrnkuI7lvZPliY3mmL7npLrnmoToioLngrnkuI3lkIzml7bmiY3ov5vooYzliIfmjaJcbiAgICAgICAgaWYgKHRoaXMubGFzdFNob3duTWFwTm9kZSAhPT0gdGFyZ2V0TWFwSW5mby5tYXBOb2RlKSB7XG4gICAgICAgICAgICAvLyDpmpDol4/kuIrkuIDkuKrmmL7npLrnmoTlnLDlm77oioLngrlcbiAgICAgICAgICAgIGlmICh0aGlzLmxhc3RTaG93bk1hcE5vZGUpIHtcbiAgICAgICAgICAgICAgICB0aGlzLmxhc3RTaG93bk1hcE5vZGUuYWN0aXZlID0gZmFsc2U7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIC8vIOaYvuekuuebruagh+WuueWZqOWSjOWcsOWbvuiKgueCuVxuICAgICAgICAgICAgdGhpcy5zaG93TWFwQ29udGFpbmVyKHRhcmdldE1hcEluZm8uY29udGFpbmVyVHlwZSk7XG4gICAgICAgICAgICB0aGlzLnNob3dNYXBOb2RlT3B0aW1pemVkKHRhcmdldE1hcEluZm8ubWFwTm9kZSwgdGFyZ2V0TWFwSW5mby5tYXBOYW1lKTtcblxuICAgICAgICAgICAgLy8g6K6w5b2V5b2T5YmN5pi+56S655qE6IqC54K5XG4gICAgICAgICAgICB0aGlzLmxhc3RTaG93bk1hcE5vZGUgPSB0YXJnZXRNYXBJbmZvLm1hcE5vZGU7XG4gICAgICAgIH1cblxuICAgICAgICAvLyDorr7nva7lvZPliY3mv4DmtLvnmoTljZXmnLrmo4vnm5jmjqfliLblmahcbiAgICAgICAgaWYgKHRoaXMuaXNIZXhMZXZlbChsZXZlbE51bWJlcikpIHtcbiAgICAgICAgICAgIC8vIOWFrei+ueW9ouWFs+WNoeS9v+eUqOWFrei+ueW9ouaOp+WItuWZqFxuICAgICAgICAgICAgaWYgKHRoaXMuaGV4U2luZ2xlQ2hlc3NCb2FyZENvbnRyb2xsZXIpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBoZXhCb2FyZFR5cGUgPSB0aGlzLmdldEhleEJvYXJkVHlwZUJ5TGV2ZWwobGV2ZWxOdW1iZXIpO1xuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICB0aGlzLmhleFNpbmdsZUNoZXNzQm9hcmRDb250cm9sbGVyLmluaXRCb2FyZChoZXhCb2FyZFR5cGUpO1xuICAgICAgICAgICAgICAgIHRoaXMuY3VycmVudFNpbmdsZUNoZXNzQm9hcmQgPSB0aGlzLmhleFNpbmdsZUNoZXNzQm9hcmRDb250cm9sbGVyIGFzIGFueTtcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcihcIuKdjCDlha3ovrnlvaLljZXmnLrmjqfliLblmajmnKrphY3nva7vvIFcIik7XG4gICAgICAgICAgICAgICAgdGhpcy5jdXJyZW50U2luZ2xlQ2hlc3NCb2FyZCA9IG51bGw7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAvLyDlm5vovrnlvaLlhbPljaHkvb/nlKjlm5vovrnlvaLmjqfliLblmahcbiAgICAgICAgICAgIGlmICh0aGlzLnNpbmdsZUNoZXNzQm9hcmRDb250cm9sbGVyKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgYm9hcmRUeXBlID0gdGhpcy5nZXRCb2FyZFR5cGVCeUxldmVsKGxldmVsTnVtYmVyKTtcbiAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIHRoaXMuc2luZ2xlQ2hlc3NCb2FyZENvbnRyb2xsZXIuaW5pdEJvYXJkKGJvYXJkVHlwZSk7XG4gICAgICAgICAgICAgICAgdGhpcy5jdXJyZW50U2luZ2xlQ2hlc3NCb2FyZCA9IHRoaXMuc2luZ2xlQ2hlc3NCb2FyZENvbnRyb2xsZXI7XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCLinYwg5Zub6L655b2i5Y2V5py65o6n5Yi25Zmo5pyq6YWN572u77yBXCIpO1xuICAgICAgICAgICAgICAgIHRoaXMuY3VycmVudFNpbmdsZUNoZXNzQm9hcmQgPSBudWxsO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG5cbiAgICAgICAgdGhpcy5pc1VwZGF0aW5nID0gZmFsc2U7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5qC55o2u5YWz5Y2h5pWw6I635Y+W5a+55bqU55qE5Zyw5Zu+6IqC54K55L+h5oGvXG4gICAgICogQHBhcmFtIGxldmVsTnVtYmVyIOWFs+WNoee8luWPt1xuICAgICAqL1xuICAgIHByaXZhdGUgZ2V0TWFwTm9kZUJ5TGV2ZWwobGV2ZWxOdW1iZXI6IG51bWJlcik6IHttYXBOb2RlOiBjYy5Ob2RlLCBtYXBOYW1lOiBzdHJpbmcsIGNvbnRhaW5lclR5cGU6ICdtYXAxJyB8ICdtYXAyJ30gfCBudWxsIHtcbiAgICAgICAgaWYgKGxldmVsTnVtYmVyID49IDEgJiYgbGV2ZWxOdW1iZXIgPD0gNCkge1xuICAgICAgICAgICAgcmV0dXJuIHttYXBOb2RlOiB0aGlzLnFpcGFuOHg4Tm9kZSwgbWFwTmFtZTogXCJxaXBhbjgqOFwiLCBjb250YWluZXJUeXBlOiAnbWFwMSd9O1xuICAgICAgICB9IGVsc2UgaWYgKGxldmVsTnVtYmVyID09PSA1KSB7XG4gICAgICAgICAgICByZXR1cm4ge21hcE5vZGU6IHRoaXMubGV2ZWxTMDAxTm9kZSwgbWFwTmFtZTogXCJMZXZlbF9TMDAxXCIsIGNvbnRhaW5lclR5cGU6ICdtYXAyJ307XG4gICAgICAgIH0gZWxzZSBpZiAobGV2ZWxOdW1iZXIgPj0gNiAmJiBsZXZlbE51bWJlciA8PSA5KSB7XG4gICAgICAgICAgICByZXR1cm4ge21hcE5vZGU6IHRoaXMucWlwYW44eDlOb2RlLCBtYXBOYW1lOiBcInFpcGFuOCo5XCIsIGNvbnRhaW5lclR5cGU6ICdtYXAxJ307XG4gICAgICAgIH0gZWxzZSBpZiAobGV2ZWxOdW1iZXIgPT09IDEwKSB7XG4gICAgICAgICAgICByZXR1cm4ge21hcE5vZGU6IHRoaXMubGV2ZWxTMDAyTm9kZSwgbWFwTmFtZTogXCJMZXZlbF9TMDAyXCIsIGNvbnRhaW5lclR5cGU6ICdtYXAyJ307XG4gICAgICAgIH0gZWxzZSBpZiAobGV2ZWxOdW1iZXIgPj0gMTEgJiYgbGV2ZWxOdW1iZXIgPD0gMTQpIHtcbiAgICAgICAgICAgIHJldHVybiB7bWFwTm9kZTogdGhpcy5xaXBhbjl4OU5vZGUsIG1hcE5hbWU6IFwicWlwYW45KjlcIiwgY29udGFpbmVyVHlwZTogJ21hcDEnfTtcbiAgICAgICAgfSBlbHNlIGlmIChsZXZlbE51bWJlciA9PT0gMTUpIHtcbiAgICAgICAgICAgIHJldHVybiB7bWFwTm9kZTogdGhpcy5sZXZlbFMwMDNOb2RlLCBtYXBOYW1lOiBcIkxldmVsX1MwMDNcIiwgY29udGFpbmVyVHlwZTogJ21hcDInfTtcbiAgICAgICAgfSBlbHNlIGlmIChsZXZlbE51bWJlciA+PSAxNiAmJiBsZXZlbE51bWJlciA8PSAxOSkge1xuICAgICAgICAgICAgcmV0dXJuIHttYXBOb2RlOiB0aGlzLnFpcGFuOXgxME5vZGUsIG1hcE5hbWU6IFwicWlwYW45KjEwXCIsIGNvbnRhaW5lclR5cGU6ICdtYXAxJ307XG4gICAgICAgIH0gZWxzZSBpZiAobGV2ZWxOdW1iZXIgPT09IDIwKSB7XG4gICAgICAgICAgICByZXR1cm4ge21hcE5vZGU6IHRoaXMubGV2ZWxTMDA0Tm9kZSwgbWFwTmFtZTogXCJMZXZlbF9TMDA0XCIsIGNvbnRhaW5lclR5cGU6ICdtYXAyJ307XG4gICAgICAgIH0gZWxzZSBpZiAobGV2ZWxOdW1iZXIgPj0gMjEgJiYgbGV2ZWxOdW1iZXIgPD0gMjQpIHtcbiAgICAgICAgICAgIHJldHVybiB7bWFwTm9kZTogdGhpcy5xaXBhbjEweDEwTm9kZSwgbWFwTmFtZTogXCJxaXBhbjEwKjEwXCIsIGNvbnRhaW5lclR5cGU6ICdtYXAxJ307XG4gICAgICAgIH0gZWxzZSBpZiAobGV2ZWxOdW1iZXIgPT09IDI1KSB7XG4gICAgICAgICAgICByZXR1cm4ge21hcE5vZGU6IHRoaXMubGV2ZWxTMDA1Tm9kZSwgbWFwTmFtZTogXCJMZXZlbF9TMDA1XCIsIGNvbnRhaW5lclR5cGU6ICdtYXAyJ307XG4gICAgICAgIH0gZWxzZSBpZiAobGV2ZWxOdW1iZXIgPj0gMjYgJiYgbGV2ZWxOdW1iZXIgPD0gMjkpIHtcbiAgICAgICAgICAgIHJldHVybiB7bWFwTm9kZTogdGhpcy5xaXBhbjEweDEwTm9kZSwgbWFwTmFtZTogXCJxaXBhbjEwKjEwXCIsIGNvbnRhaW5lclR5cGU6ICdtYXAxJ307XG4gICAgICAgIH0gZWxzZSBpZiAobGV2ZWxOdW1iZXIgPT09IDMwKSB7XG4gICAgICAgICAgICByZXR1cm4ge21hcE5vZGU6IHRoaXMubGV2ZWxTMDA2Tm9kZSwgbWFwTmFtZTogXCJMZXZlbF9TMDA2XCIsIGNvbnRhaW5lclR5cGU6ICdtYXAyJ307XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuXG5cblxuICAgIC8qKlxuICAgICAqIOagueaNruWFs+WNoee8luWPt+iOt+WPluaji+ebmOexu+Wei1xuICAgICAqIEBwYXJhbSBsZXZlbE51bWJlciDlhbPljaHnvJblj7dcbiAgICAgKi9cbiAgICBwcml2YXRlIGdldEJvYXJkVHlwZUJ5TGV2ZWwobGV2ZWxOdW1iZXI6IG51bWJlcik6IHN0cmluZyB7XG4gICAgICAgIGlmIChsZXZlbE51bWJlciA+PSAxICYmIGxldmVsTnVtYmVyIDw9IDQpIHtcbiAgICAgICAgICAgIHJldHVybiBcIjh4OFwiO1xuICAgICAgICB9IGVsc2UgaWYgKGxldmVsTnVtYmVyID49IDYgJiYgbGV2ZWxOdW1iZXIgPD0gOSkge1xuICAgICAgICAgICAgcmV0dXJuIFwiOHg5XCI7XG4gICAgICAgIH0gZWxzZSBpZiAobGV2ZWxOdW1iZXIgPj0gMTEgJiYgbGV2ZWxOdW1iZXIgPD0gMTQpIHtcbiAgICAgICAgICAgIHJldHVybiBcIjl4OVwiO1xuICAgICAgICB9IGVsc2UgaWYgKGxldmVsTnVtYmVyID49IDE2ICYmIGxldmVsTnVtYmVyIDw9IDE5KSB7XG4gICAgICAgICAgICByZXR1cm4gXCI5eDEwXCI7XG4gICAgICAgIH0gZWxzZSBpZiAobGV2ZWxOdW1iZXIgPj0gMjEgJiYgbGV2ZWxOdW1iZXIgPD0gMjQgfHwgbGV2ZWxOdW1iZXIgPj0gMjYgJiYgbGV2ZWxOdW1iZXIgPD0gMjkpIHtcbiAgICAgICAgICAgIHJldHVybiBcIjEweDEwXCI7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIFwiOHg4XCI7IC8vIOm7mOiupFxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOWIpOaWreaYr+WQpuS4uuWFrei+ueW9ouWFs+WNoVxuICAgICAqIEBwYXJhbSBsZXZlbE51bWJlciDlhbPljaHnvJblj7dcbiAgICAgKi9cbiAgICBwcml2YXRlIGlzSGV4TGV2ZWwobGV2ZWxOdW1iZXI6IG51bWJlcik6IGJvb2xlYW4ge1xuICAgICAgICAvLyDnibnmrorlhbPljaHkvb/nlKjlha3ovrnlvaLmo4vnm5hcbiAgICAgICAgcmV0dXJuIGxldmVsTnVtYmVyID09PSA1IHx8IGxldmVsTnVtYmVyID09PSAxMCB8fCBsZXZlbE51bWJlciA9PT0gMTUgfHxcbiAgICAgICAgICAgICAgIGxldmVsTnVtYmVyID09PSAyMCB8fCBsZXZlbE51bWJlciA9PT0gMjUgfHwgbGV2ZWxOdW1iZXIgPT09IDMwO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOagueaNruWFs+WNoee8luWPt+iOt+WPluWFrei+ueW9ouaji+ebmOexu+Wei1xuICAgICAqIEBwYXJhbSBsZXZlbE51bWJlciDlhbPljaHnvJblj7dcbiAgICAgKi9cbiAgICBwcml2YXRlIGdldEhleEJvYXJkVHlwZUJ5TGV2ZWwobGV2ZWxOdW1iZXI6IG51bWJlcik6IHN0cmluZyB7XG4gICAgICAgIC8vIOWFreWFs+WvueW6lOWFreS4quWFrei+ueW9ouaji+ebmFxuICAgICAgICBpZiAobGV2ZWxOdW1iZXIgPT09IDUpIHtcbiAgICAgICAgICAgIHJldHVybiBcImhleEJvYXJkMVwiOyAgLy8g56ysMeWFs+WFrei+ueW9olxuICAgICAgICB9IGVsc2UgaWYgKGxldmVsTnVtYmVyID09PSAxMCkge1xuICAgICAgICAgICAgcmV0dXJuIFwiaGV4Qm9hcmQyXCI7ICAvLyDnrKwy5YWz5YWt6L655b2iXG4gICAgICAgIH0gZWxzZSBpZiAobGV2ZWxOdW1iZXIgPT09IDE1KSB7XG4gICAgICAgICAgICByZXR1cm4gXCJoZXhCb2FyZDNcIjsgIC8vIOesrDPlhbPlha3ovrnlvaJcbiAgICAgICAgfSBlbHNlIGlmIChsZXZlbE51bWJlciA9PT0gMjApIHtcbiAgICAgICAgICAgIHJldHVybiBcImhleEJvYXJkNFwiOyAgLy8g56ysNOWFs+WFrei+ueW9olxuICAgICAgICB9IGVsc2UgaWYgKGxldmVsTnVtYmVyID09PSAyNSkge1xuICAgICAgICAgICAgcmV0dXJuIFwiaGV4Qm9hcmQ1XCI7ICAvLyDnrKw15YWz5YWt6L655b2iXG4gICAgICAgIH0gZWxzZSBpZiAobGV2ZWxOdW1iZXIgPT09IDMwKSB7XG4gICAgICAgICAgICByZXR1cm4gXCJoZXhCb2FyZDZcIjsgIC8vIOesrDblhbPlha3ovrnlvaJcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gXCJoZXhCb2FyZDFcIjsgLy8g6buY6K6kXG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5pi+56S65oyH5a6a55qE5Zyw5Zu+6IqC54K577yI5LyY5YyW54mI5pys77yJXG4gICAgICogQHBhcmFtIG1hcE5vZGUg6KaB5pi+56S655qE5Zyw5Zu+6IqC54K5XG4gICAgICogQHBhcmFtIG1hcE5hbWUg5Zyw5Zu+5ZCN56ew77yI55So5LqO5pel5b+X77yJXG4gICAgICovXG4gICAgcHJpdmF0ZSBzaG93TWFwTm9kZU9wdGltaXplZChtYXBOb2RlOiBjYy5Ob2RlLCBtYXBOYW1lOiBzdHJpbmcpIHtcbiAgICAgICAgaWYgKG1hcE5vZGUpIHtcbiAgICAgICAgICAgIG1hcE5vZGUuYWN0aXZlID0gdHJ1ZTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIGNjLndhcm4oYOKdjCDlnLDlm77oioLngrnmnKrmib7liLA6ICR7bWFwTmFtZX1gKTtcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOaYvuekuuaMh+WumueahOWcsOWbvuWuueWZqFxuICAgICAqIEBwYXJhbSBjb250YWluZXJUeXBlIOWuueWZqOexu+Wei1xuICAgICAqL1xuICAgIHByaXZhdGUgc2hvd01hcENvbnRhaW5lcihjb250YWluZXJUeXBlOiAnbWFwMScgfCAnbWFwMicpIHtcbiAgICAgICAgLy8g56Gu5L+dIGxldmVsX3BhZ2Ug6IqC54K55piv5r+A5rS755qEXG4gICAgICAgIGlmICh0aGlzLmxldmVsUGFnZU5vZGUpIHtcbiAgICAgICAgICAgIHRoaXMubGV2ZWxQYWdlTm9kZS5hY3RpdmUgPSB0cnVlO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8g5qC55o2u5a655Zmo57G75Z6L5pi+56S65a+55bqU5a655Zmo77yM6ZqQ6JeP5Y+m5LiA5LiqXG4gICAgICAgIGlmIChjb250YWluZXJUeXBlID09PSAnbWFwMScpIHtcbiAgICAgICAgICAgIGlmICh0aGlzLmdhbWVNYXAxTm9kZSAmJiAhdGhpcy5nYW1lTWFwMU5vZGUuYWN0aXZlKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5nYW1lTWFwMU5vZGUuYWN0aXZlID0gdHJ1ZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICh0aGlzLmdhbWVNYXAyTm9kZSAmJiB0aGlzLmdhbWVNYXAyTm9kZS5hY3RpdmUpIHtcbiAgICAgICAgICAgICAgICB0aGlzLmdhbWVNYXAyTm9kZS5hY3RpdmUgPSBmYWxzZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIGlmICh0aGlzLmdhbWVNYXAyTm9kZSAmJiAhdGhpcy5nYW1lTWFwMk5vZGUuYWN0aXZlKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5nYW1lTWFwMk5vZGUuYWN0aXZlID0gdHJ1ZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICh0aGlzLmdhbWVNYXAxTm9kZSAmJiB0aGlzLmdhbWVNYXAxTm9kZS5hY3RpdmUpIHtcbiAgICAgICAgICAgICAgICB0aGlzLmdhbWVNYXAxTm9kZS5hY3RpdmUgPSBmYWxzZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOmakOiXj+aJgOacieWcsOWbvuiKgueCuVxuICAgICAqL1xuICAgIHByaXZhdGUgaGlkZUFsbE1hcE5vZGVzKCkge1xuICAgICAgICBjb25zdCBhbGxNYXBOb2RlcyA9IFtcbiAgICAgICAgICAgIHRoaXMucWlwYW44eDhOb2RlLFxuICAgICAgICAgICAgdGhpcy5xaXBhbjh4OU5vZGUsXG4gICAgICAgICAgICB0aGlzLnFpcGFuOXg5Tm9kZSxcbiAgICAgICAgICAgIHRoaXMucWlwYW45eDEwTm9kZSxcbiAgICAgICAgICAgIHRoaXMucWlwYW4xMHgxME5vZGUsXG4gICAgICAgICAgICB0aGlzLmxldmVsUzAwMU5vZGUsXG4gICAgICAgICAgICB0aGlzLmxldmVsUzAwMk5vZGUsXG4gICAgICAgICAgICB0aGlzLmxldmVsUzAwM05vZGUsXG4gICAgICAgICAgICB0aGlzLmxldmVsUzAwNE5vZGUsXG4gICAgICAgICAgICB0aGlzLmxldmVsUzAwNU5vZGUsXG4gICAgICAgICAgICB0aGlzLmxldmVsUzAwNk5vZGVcbiAgICAgICAgXTtcblxuICAgICAgICBhbGxNYXBOb2Rlcy5mb3JFYWNoKG5vZGUgPT4ge1xuICAgICAgICAgICAgaWYgKG5vZGUpIHtcbiAgICAgICAgICAgICAgICBub2RlLmFjdGl2ZSA9IGZhbHNlO1xuICAgICAgICAgICAgfVxuICAgICAgICB9KTtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDorr7nva7lvZPliY3lhbPljaHvvIjku47lpJbpg6josIPnlKjvvIlcbiAgICAgKiBAcGFyYW0gbGV2ZWxOdW1iZXIg5YWz5Y2h57yW5Y+3XG4gICAgICovXG4gICAgcHVibGljIHNldEN1cnJlbnRMZXZlbChsZXZlbE51bWJlcjogbnVtYmVyKSB7XG4gICAgICAgXG5cbiAgICAgICAgLy8g6YeN572u5YWz5Y2h54q25oCB77yI5YyF5ous5riF6Zmk5rWL6K+V6aKE5Yi25L2T77yJXG4gICAgICAgIHRoaXMucmVzZXRMZXZlbFN0YXRlKCk7XG5cbiAgICAgICAgdGhpcy5jdXJyZW50TGV2ZWwgPSBsZXZlbE51bWJlcjtcblxuICAgICAgICAvLyDnq4vljbPmoLnmja7lhbPljaHmlbDliIfmjaLlnLDlm77mmL7npLpcbiAgICAgICAgdGhpcy5lbnRlckxldmVsKGxldmVsTnVtYmVyKTtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDojrflj5blvZPliY3lhbPljaHnvJblj7dcbiAgICAgKi9cbiAgICBwdWJsaWMgZ2V0Q3VycmVudExldmVsKCk6IG51bWJlciB7XG4gICAgICAgIHJldHVybiB0aGlzLmN1cnJlbnRMZXZlbDtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDojrflj5blvZPliY3lhbPljaHkv6Hmga9cbiAgICAgKi9cbiAgICBwdWJsaWMgZ2V0Q3VycmVudExldmVsSW5mbygpOiBFeHRlbmRMZXZlbEluZm9SZXNwb25zZSB7XG4gICAgICAgIHJldHVybiB0aGlzLmN1cnJlbnRMZXZlbEluZm87XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog6I635Y+W5b2T5YmN5oi/6Ze0SURcbiAgICAgKi9cbiAgICBwdWJsaWMgZ2V0Q3VycmVudFJvb21JZCgpOiBudW1iZXIge1xuICAgICAgICByZXR1cm4gdGhpcy5jdXJyZW50Um9vbUlkO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOmakOiXj+aJgOacieWcsOWbvuWuueWZqFxuICAgICAqL1xuICAgIHByaXZhdGUgaGlkZUFsbE1hcENvbnRhaW5lcnMoKSB7XG4gICAgICAgIC8vIOehruS/nSBsZXZlbF9wYWdlIOiKgueCueaYr+a/gOa0u+eahFxuICAgICAgICBpZiAodGhpcy5sZXZlbFBhZ2VOb2RlKSB7XG4gICAgICAgICAgICB0aGlzLmxldmVsUGFnZU5vZGUuYWN0aXZlID0gdHJ1ZTtcbiAgICAgICAgICBcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIOmakOiXj+S4pOS4quS4u+imgeeahOWcsOWbvuWuueWZqFxuICAgICAgICBpZiAodGhpcy5nYW1lTWFwMU5vZGUpIHtcbiAgICAgICAgICAgIHRoaXMuZ2FtZU1hcDFOb2RlLmFjdGl2ZSA9IGZhbHNlO1xuICAgICAgICAgICAgXG4gICAgICAgIH1cbiAgICAgICAgaWYgKHRoaXMuZ2FtZU1hcDJOb2RlKSB7XG4gICAgICAgICAgICB0aGlzLmdhbWVNYXAyTm9kZS5hY3RpdmUgPSBmYWxzZTtcbiAgICAgICAgICAgXG4gICAgICAgIH1cblxuICAgICAgICAvLyDlkIzml7bpmpDol4/miYDmnInlhbfkvZPnmoTlnLDlm77oioLngrlcbiAgICAgICAgdGhpcy5oaWRlQWxsTWFwTm9kZXMoKTtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDmmL7npLogZ2FtZV9tYXBfMSDlrrnlmajvvIjmlrnlvaLlnLDlm77vvIlcbiAgICAgKi9cbiAgICBwcml2YXRlIHNob3dHYW1lTWFwMSgpIHtcbiAgICAgICAgaWYgKHRoaXMuZ2FtZU1hcDFOb2RlKSB7XG4gICAgICAgICAgICB0aGlzLmdhbWVNYXAxTm9kZS5hY3RpdmUgPSB0cnVlO1xuICAgICAgICAgICAgXG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBjYy53YXJuKFwi4p2MIGdhbWVfbWFwXzEg6IqC54K55pyq5om+5YiwXCIpO1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5pi+56S6IGdhbWVfbWFwXzIg5a655Zmo77yI54m55q6K5YWz5Y2h77yJXG4gICAgICovXG4gICAgcHJpdmF0ZSBzaG93R2FtZU1hcDIoKSB7XG4gICAgICAgIGlmICh0aGlzLmdhbWVNYXAyTm9kZSkge1xuICAgICAgICAgICAgdGhpcy5nYW1lTWFwMk5vZGUuYWN0aXZlID0gdHJ1ZTtcblxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgY2Mud2FybihcIuKdjCBnYW1lX21hcF8yIOiKgueCueacquaJvuWIsFwiKTtcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOiOt+WPluW9k+WJjea/gOa0u+eahOWNleacuuaji+ebmOaOp+WItuWZqFxuICAgICAqL1xuICAgIHB1YmxpYyBnZXRDdXJyZW50U2luZ2xlQ2hlc3NCb2FyZCgpOiBTaW5nbGVDaGVzc0JvYXJkQ29udHJvbGxlciB8IEhleFNpbmdsZUNoZXNzQm9hcmRDb250cm9sbGVyIHwgbnVsbCB7XG4gICAgICAgIHJldHVybiB0aGlzLmN1cnJlbnRTaW5nbGVDaGVzc0JvYXJkO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOWkhOeQhuWNleacuuaooeW8j+eahOeCueWHu+WTjeW6lFxuICAgICAqIEBwYXJhbSByZXNwb25zZSDngrnlh7vlk43lupTmlbDmja5cbiAgICAgKi9cbiAgICBwdWJsaWMgaGFuZGxlU2luZ2xlTW9kZUNsaWNrUmVzcG9uc2UocmVzcG9uc2U6IGFueSkge1xuICAgICAgICBpZiAodGhpcy5jdXJyZW50U2luZ2xlQ2hlc3NCb2FyZCkge1xuICAgICAgICAgICAgY29uc3QgeyB4LCB5LCByZXN1bHQsIGNoYWluUmVhY3Rpb24gfSA9IHJlc3BvbnNlO1xuXG4gICAgICAgICAgICAvLyDlpITnkIbngrnlh7vnu5PmnpxcbiAgICAgICAgICAgIGlmICh4ICE9PSB1bmRlZmluZWQgJiYgeSAhPT0gdW5kZWZpbmVkICYmIHJlc3VsdCAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5jdXJyZW50U2luZ2xlQ2hlc3NCb2FyZC5oYW5kbGVDbGlja1Jlc3BvbnNlKHgsIHksIHJlc3VsdCk7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIC8vIOWkhOeQhui/numUgeWPjeW6lFxuICAgICAgICAgICAgaWYgKGNoYWluUmVhY3Rpb24gJiYgQXJyYXkuaXNBcnJheShjaGFpblJlYWN0aW9uKSkge1xuICAgICAgICAgICAgICAgIHRoaXMuY3VycmVudFNpbmdsZUNoZXNzQm9hcmQuaGFuZGxlQ2hhaW5SZWFjdGlvbihjaGFpblJlYWN0aW9uKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOWkhOeQhuWNleacuuaooeW8j+a4uOaIj+e7k+adn1xuICAgICAqIEBwYXJhbSBnYW1lRW5kRGF0YSDmuLjmiI/nu5PmnZ/mlbDmja5cbiAgICAgKi9cbiAgICBwdWJsaWMgaGFuZGxlU2luZ2xlTW9kZUdhbWVFbmQoZ2FtZUVuZERhdGE6IGFueSkge1xuICAgICAgICBpZiAodGhpcy5jdXJyZW50U2luZ2xlQ2hlc3NCb2FyZCkge1xuICAgICAgICAgICAgLy8g56aB55So5qOL55uY6Kem5pG4XG4gICAgICAgICAgICB0aGlzLmN1cnJlbnRTaW5nbGVDaGVzc0JvYXJkLmRpc2FibGVBbGxHcmlkVG91Y2goKTtcblxuICAgICAgICAgICAgLy8g5aSE55CG5ri45oiP57uT5p2fXG4gICAgICAgICAgICB0aGlzLmN1cnJlbnRTaW5nbGVDaGVzc0JvYXJkLm9uTGV2ZWxHYW1lRW5kKCk7XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDph43nva7lvZPliY3ljZXmnLrmo4vnm5jvvIjku4XlnKjlvIDlp4vmlrDmuLjmiI/ml7bosIPnlKjvvIlcbiAgICAgKi9cbiAgICBwdWJsaWMgcmVzZXRDdXJyZW50U2luZ2xlQ2hlc3NCb2FyZCgpIHtcbiAgICAgIFxuICAgICAgICBpZiAodGhpcy5jdXJyZW50U2luZ2xlQ2hlc3NCb2FyZCkge1xuICAgICAgICAgICAgLy8g6YeN572u5qOL55uY54q25oCB77yI5riF55CG5omA5pyJ6aKE5Yi25L2T5ZKM5qC85a2Q54q25oCB77yJXG4gICAgICAgICAgICB0aGlzLmN1cnJlbnRTaW5nbGVDaGVzc0JvYXJkLnJlc2V0Qm9hcmQoKTtcblxuICAgICAgICAgICAgLy8g6YeN5paw5ZCv55So6Kem5pG45LqL5Lu2XG4gICAgICAgICAgICB0aGlzLmN1cnJlbnRTaW5nbGVDaGVzc0JvYXJkLmVuYWJsZUFsbEdyaWRUb3VjaCgpO1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5rOo5YaM5Y2V5py65qih5byP5raI5oGv5aSE55CG5ZmoXG4gICAgICovXG4gICAgcHJpdmF0ZSByZWdpc3RlclNpbmdsZU1vZGVNZXNzYWdlSGFuZGxlcnMoKSB7XG4gICAgICAgIC8vIOebkeWQrFdlYlNvY2tldOa2iOaBr1xuICAgICAgICBHYW1lTWdyLkV2ZW50LkFkZEV2ZW50TGlzdGVuZXIoRXZlbnRUeXBlLlJlY2VpdmVNZXNzYWdlLCB0aGlzLm9uUmVjZWl2ZU1lc3NhZ2UsIHRoaXMpO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOWPlua2iOWNleacuuaooeW8j+a2iOaBr+ebkeWQrFxuICAgICAqL1xuICAgIHByaXZhdGUgdW5yZWdpc3RlclNpbmdsZU1vZGVNZXNzYWdlSGFuZGxlcnMoKSB7XG4gICAgICAgIEdhbWVNZ3IuRXZlbnQuUmVtb3ZlRXZlbnRMaXN0ZW5lcihFdmVudFR5cGUuUmVjZWl2ZU1lc3NhZ2UsIHRoaXMub25SZWNlaXZlTWVzc2FnZSwgdGhpcyk7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5aSE55CG5o6l5pS25Yiw55qEV2ViU29ja2V05raI5oGvXG4gICAgICogQHBhcmFtIG1lc3NhZ2VCZWFuIOa2iOaBr+aVsOaNrlxuICAgICAqL1xuICAgIHByaXZhdGUgb25SZWNlaXZlTWVzc2FnZShtZXNzYWdlQmVhbjogUmVjZWl2ZWRNZXNzYWdlQmVhbikge1xuICAgICAgICBzd2l0Y2ggKG1lc3NhZ2VCZWFuLm1zZ0lkKSB7XG4gICAgICAgICAgICBjYXNlIE1lc3NhZ2VJZC5Nc2dUeXBlTGV2ZWxDbGlja0Jsb2NrOlxuICAgICAgICAgICAgICAgIHRoaXMub25MZXZlbENsaWNrQmxvY2tSZXNwb25zZShtZXNzYWdlQmVhbi5kYXRhKTtcbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIGNhc2UgTWVzc2FnZUlkLk1zZ1R5cGVMZXZlbEdhbWVFbmQ6XG4gICAgICAgICAgICAgICAgdGhpcy5vbkxldmVsR2FtZUVuZChtZXNzYWdlQmVhbi5kYXRhKTtcbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOWkhOeQhkxldmVsQ2xpY2tCbG9ja+WTjeW6lFxuICAgICAqIEBwYXJhbSByZXNwb25zZSDngrnlh7vlk43lupTmlbDmja5cbiAgICAgKi9cbiAgICBwdWJsaWMgb25MZXZlbENsaWNrQmxvY2tSZXNwb25zZShyZXNwb25zZTogYW55KSB7XG4gICAgICBcbiAgICAgICAgaWYgKHRoaXMuY3VycmVudFNpbmdsZUNoZXNzQm9hcmQpIHtcbiAgICAgICAgICAgIC8vIOino+aehOWTjeW6lOaVsOaNru+8jOaUr+aMgeWkmuenjeWPr+iDveeahOWtl+auteWQjVxuICAgICAgICAgICAgY29uc3Qge1xuICAgICAgICAgICAgICAgIHgsIHksIHEsIHIsIHJlc3VsdCwgYWN0aW9uLFxuICAgICAgICAgICAgICAgIGNoYWluUmVhY3Rpb24sIHJldmVhbGVkR3JpZHMsIGZsb29kRmlsbCwgcmV2ZWFsZWRCbG9ja3MsXG4gICAgICAgICAgICAgICAgZmxvb2RGaWxsUmVzdWx0cyAgLy8g5Y2V5py65qih5byP5L2/55So6L+Z5Liq5a2X5q61XG4gICAgICAgICAgICB9ID0gcmVzcG9uc2U7XG5cbiAgICAgICAgICAgIC8vIOWIpOaWreW9k+WJjeS9v+eUqOeahOaYr+WTquenjeaOp+WItuWZqFxuICAgICAgICAgICAgY29uc3QgaXNVc2luZ0hleENvbnRyb2xsZXIgPSAodGhpcy5jdXJyZW50U2luZ2xlQ2hlc3NCb2FyZCA9PT0gdGhpcy5oZXhTaW5nbGVDaGVzc0JvYXJkQ29udHJvbGxlciBhcyBhbnkpO1xuXG4gICAgICAgICAgICAvLyDmoLnmja7mjqfliLblmajnsbvlnovlhrPlrprlnZDmoIflpITnkIbmlrnlvI9cbiAgICAgICAgICAgIGxldCBjb29yZFg6IG51bWJlciwgY29vcmRZOiBudW1iZXIsIGNvb3JkUTogbnVtYmVyLCBjb29yZFI6IG51bWJlcjtcbiAgICAgICAgICAgIGxldCBoYXNWYWxpZENvb3JkID0gZmFsc2U7XG5cbiAgICAgICAgICAgIGlmIChpc1VzaW5nSGV4Q29udHJvbGxlcikge1xuICAgICAgICAgICAgICAgIC8vIOWFrei+ueW9ouaOp+WItuWZqO+8muS8mOWFiOS9v+eUqOWFrei+ueW9ouWdkOagh++8jOWmguaenOayoeacieWImeWwhngseeaYoOWwhOS4unEsclxuICAgICAgICAgICAgICAgIGlmIChxICE9PSB1bmRlZmluZWQgJiYgciAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvb3JkUSA9IHE7XG4gICAgICAgICAgICAgICAgICAgIGNvb3JkUiA9IHI7XG4gICAgICAgICAgICAgICAgICAgIGhhc1ZhbGlkQ29vcmQgPSB0cnVlO1xuICAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAoeCAhPT0gdW5kZWZpbmVkICYmIHkgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgICAgICAgICAvLyDmnI3liqHlmajov5Tlm554LHnlrZfmrrXvvIzkvYblrp7pmYXmmK/lha3ovrnlvaLlnZDmoIfvvJp4PXEsIHk9clxuICAgICAgICAgICAgICAgICAgICBjb29yZFEgPSB4OyAgLy8geCDlsLHmmK8gcVxuICAgICAgICAgICAgICAgICAgICBjb29yZFIgPSB5OyAgLy8geSDlsLHmmK8gclxuICAgICAgICAgICAgICAgICAgICBoYXNWYWxpZENvb3JkID0gdHJ1ZTtcbiAgICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICAvLyDlm5vovrnlvaLmjqfliLblmajvvJrkvb/nlKjlm5vovrnlvaLlnZDmoIdcbiAgICAgICAgICAgICAgICBpZiAoeCAhPT0gdW5kZWZpbmVkICYmIHkgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgICAgICAgICBjb29yZFggPSB4O1xuICAgICAgICAgICAgICAgICAgICBjb29yZFkgPSB5O1xuICAgICAgICAgICAgICAgICAgICBoYXNWYWxpZENvb3JkID0gdHJ1ZTtcbiAgICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKHEgIT09IHVuZGVmaW5lZCAmJiByICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc29sZS53YXJuKGDimqDvuI8g5Zub6L655b2i5o6n5Yi25Zmo5pS25Yiw5YWt6L655b2i5Z2Q5qCHICgke3F9LCAke3J9Ke+8jOi/meWPr+iDveS4jeato+ehrmApO1xuICAgICAgICAgICAgICAgICAgICBoYXNWYWxpZENvb3JkID0gZmFsc2U7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICBpZiAoaGFzVmFsaWRDb29yZCAmJiByZXN1bHQgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgICAgXG5cbiAgICAgICAgICAgICAgICBpZiAoYWN0aW9uID09PSAyKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIOagh+iusC/lj5bmtojmoIforrDmk43kvZzvvIzkuI3osIPnlKhoYW5kbGVDbGlja1Jlc3BvbnNl77yM6YG/5YWN5qC85a2Q5raI5aSxXG4gICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgICAgLy8g5LiN6LCD55SoIGhhbmRsZUNsaWNrUmVzcG9uc2XvvIzlm6DkuLrmoIforrDmk43kvZzkuI3lupTor6XpmpDol4/moLzlrZBcbiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKGFjdGlvbiA9PT0gMSkge1xuICAgICAgICAgICAgICAgICAgICAvLyDmjJbmjpjmk43kvZxcbiAgICAgICAgICAgICAgICAgICBcblxuICAgICAgICAgICAgICAgICAgICAvLyDmo4Dmn6XmmK/lkKbngrnliLDngrjlvLlcbiAgICAgICAgICAgICAgICAgICAgaWYgKHJlc3VsdCA9PT0gXCJib29tXCIgfHwgcmVzdWx0ID09PSBcIm1pbmVcIikge1xuICAgICAgICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMubGFzdENsaWNrV2FzQm9tYiA9IHRydWU7XG4gICAgICAgICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAgICAgICAvLyDmoLnmja7mjqfliLblmajnsbvlnovosIPnlKjlr7nlupTnmoTmlrnms5VcbiAgICAgICAgICAgICAgICAgICAgaWYgKGlzVXNpbmdIZXhDb250cm9sbGVyKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAvLyDlha3ovrnlvaLmjqfliLblmahcbiAgICAgICAgICAgICAgICAgICAgICAgICh0aGlzLmN1cnJlbnRTaW5nbGVDaGVzc0JvYXJkIGFzIGFueSkuaGFuZGxlQ2xpY2tSZXNwb25zZShjb29yZFEsIGNvb3JkUiwgcmVzdWx0KTtcbiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIOWbm+i+ueW9ouaOp+WItuWZqFxuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5jdXJyZW50U2luZ2xlQ2hlc3NCb2FyZC5oYW5kbGVDbGlja1Jlc3BvbnNlKGNvb3JkWCwgY29vcmRZLCByZXN1bHQpO1xuICAgICAgICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgICAgICAgLy8g5aSE55CG6L+e6ZSB5bGV5byA5pWw5o2uXG4gICAgICAgICAgICAgICAgICAgIGlmIChmbG9vZEZpbGxSZXN1bHRzICYmIEFycmF5LmlzQXJyYXkoZmxvb2RGaWxsUmVzdWx0cykgJiYgZmxvb2RGaWxsUmVzdWx0cy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBcblxuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGlzVXNpbmdIZXhDb250cm9sbGVyKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8g5YWt6L655b2i5o6n5Yi25Zmo5L2/55SoaGFuZGxlQ2hhaW5SZWFjdGlvbuaWueazlVxuICAgICAgICAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICh0aGlzLmN1cnJlbnRTaW5nbGVDaGVzc0JvYXJkIGFzIGFueSkuaGFuZGxlQ2hhaW5SZWFjdGlvbihmbG9vZEZpbGxSZXN1bHRzKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8g5Zub6L655b2i5o6n5Yi25Zmo5L2/55SoaGFuZGxlRmxvb2RGaWxsUmVzdWx0c+aWueazlVxuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKHRoaXMuY3VycmVudFNpbmdsZUNoZXNzQm9hcmQgYXMgU2luZ2xlQ2hlc3NCb2FyZENvbnRyb2xsZXIpLmhhbmRsZUZsb29kRmlsbFJlc3VsdHMoZmxvb2RGaWxsUmVzdWx0cyk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAvLyDlhbbku5bmk43kvZzvvIzpu5jorqTmjInmjJbmjpjlpITnkIZcbiAgICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgICAgaWYgKGlzVXNpbmdIZXhDb250cm9sbGVyKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAodGhpcy5jdXJyZW50U2luZ2xlQ2hlc3NCb2FyZCBhcyBhbnkpLmhhbmRsZUNsaWNrUmVzcG9uc2UoY29vcmRRLCBjb29yZFIsIHJlc3VsdCk7XG4gICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmN1cnJlbnRTaW5nbGVDaGVzc0JvYXJkLmhhbmRsZUNsaWNrUmVzcG9uc2UoY29vcmRYLCBjb29yZFksIHJlc3VsdCk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIGNvbnNvbGUud2FybihcIuKaoO+4jyDlk43lupTmlbDmja7nvLrlsJHmnInmlYjlnZDmoIfmiJbnu5Pmnpzkv6Hmga9cIik7XG4gICAgICAgICAgICAgICAgY29uc29sZS53YXJuKGAgICDmjqfliLblmajnsbvlnos6ICR7aXNVc2luZ0hleENvbnRyb2xsZXIgPyAn5YWt6L655b2iJyA6ICflm5vovrnlvaInfWApO1xuICAgICAgICAgICAgICAgIGNvbnNvbGUud2FybihgICAg5Z2Q5qCH5pWw5o2uOiB4PSR7eH0sIHk9JHt5fSwgcT0ke3F9LCByPSR7cn1gKTtcbiAgICAgICAgICAgICAgICBjb25zb2xlLndhcm4oYCAgIOe7k+aenDogJHtyZXN1bHR9YCk7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgIFxuXG4gICAgICAgICAgICBcblxuICAgICAgICAgICAgXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDlpITnkIZMZXZlbEdhbWVFbmTpgJrnn6VcbiAgICAgKiBAcGFyYW0gZ2FtZUVuZERhdGEg5ri45oiP57uT5p2f5pWw5o2uXG4gICAgICovXG4gICAgcHVibGljIG9uTGV2ZWxHYW1lRW5kKGdhbWVFbmREYXRhOiBhbnkpIHtcblxuXG4gICAgICAgIGlmICh0aGlzLmN1cnJlbnRTaW5nbGVDaGVzc0JvYXJkKSB7XG4gICAgICAgICAgICAvLyDnpoHnlKjmo4vnm5jop6bmkbhcbiAgICAgICAgICAgIHRoaXMuY3VycmVudFNpbmdsZUNoZXNzQm9hcmQuZGlzYWJsZUFsbEdyaWRUb3VjaCgpO1xuXG4gICAgICAgICAgICAvLyDlpITnkIbmuLjmiI/nu5PmnZ/vvIjkuI3muIXnkIbmlbDmja7vvIlcbiAgICAgICAgICAgIHRoaXMuY3VycmVudFNpbmdsZUNoZXNzQm9hcmQub25MZXZlbEdhbWVFbmQoKTtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIOemgeeUqOi/lOWbnuaMiemSru+8jOmYsuatoua4uOaIj+e7k+adn+aXtueOqeWutuivr+eCueWHu1xuICAgICAgICB0aGlzLmRpc2FibGVCYWNrQnV0dG9uKCk7XG5cbiAgICAgICAgLy8g5qOA5p+l5piv5ZCm54K55Yiw5LqG54K45by577yM5aaC5p6c5piv5ri45oiP5aSx6LSl5LiU54K55Yiw54K45by577yM5YiZ5bu26L+f5pi+56S657uT566X6aG16Z2iXG4gICAgICAgIGNvbnN0IGlzR2FtZUZhaWxlZCA9ICFnYW1lRW5kRGF0YS5zdWNjZXNzO1xuICAgICAgICBjb25zdCBoYXNCb21iRXhwbG9kZWQgPSB0aGlzLmxhc3RDbGlja1dhc0JvbWIgfHxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAodGhpcy5jdXJyZW50U2luZ2xlQ2hlc3NCb2FyZCAmJiB0aGlzLmN1cnJlbnRTaW5nbGVDaGVzc0JvYXJkLmhhc0JvbWJFeHBsb2RlZEluVGhpc0dhbWUoKSk7XG5cblxuXG4gICAgICAgIGlmIChpc0dhbWVGYWlsZWQgJiYgaGFzQm9tYkV4cGxvZGVkKSB7XG4gICAgICAgICAgICAvLyDngrnliLDngrjlvLnlr7zoh7TnmoTmuLjmiI/lpLHotKXvvIzlu7bov58xLjXnp5LmmL7npLrnu5PnrpfpobXpnaJcblxuICAgICAgICAgICAgdGhpcy5zY2hlZHVsZU9uY2UoKCkgPT4ge1xuXG4gICAgICAgICAgICAgICAgdGhpcy5zaG93TGV2ZWxTZXR0bGVtZW50KGdhbWVFbmREYXRhKTtcbiAgICAgICAgICAgICAgICAvLyDph43nva7moIforrBcbiAgICAgICAgICAgICAgICB0aGlzLmxhc3RDbGlja1dhc0JvbWIgPSBmYWxzZTtcbiAgICAgICAgICAgIH0sIDEuNSk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAvLyDlhbbku5bmg4XlhrXvvIznq4vljbPmmL7npLrnu5PnrpfpobXpnaJcblxuICAgICAgICAgICAgdGhpcy5zaG93TGV2ZWxTZXR0bGVtZW50KGdhbWVFbmREYXRhKTtcbiAgICAgICAgICAgIC8vIOmHjee9ruagh+iusFxuICAgICAgICAgICAgdGhpcy5sYXN0Q2xpY2tXYXNCb21iID0gZmFsc2U7XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDorr7nva7nu5PnrpfpobXpnaLmjInpkq7kuovku7ZcbiAgICAgKi9cbiAgICBwcml2YXRlIHNldHVwU2V0dGxlbWVudEJ1dHRvbnMoKSB7XG4gICAgICAgIC8vIOWGjeadpeS4gOasoeaMiemSrlxuICAgICAgICBpZiAodGhpcy5yZXRyeUJ1dHRvbikge1xuICAgICAgICAgICAgdGhpcy5yZXRyeUJ1dHRvbi5ub2RlLm9uKCdjbGljaycsIHRoaXMub25SZXRyeUJ1dHRvbkNsaWNrLCB0aGlzKTtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIOS4i+S4gOWFs+aMiemSrlxuICAgICAgICBpZiAodGhpcy5uZXh0TGV2ZWxCdXR0b24pIHtcbiAgICAgICAgICAgIHRoaXMubmV4dExldmVsQnV0dG9uLm5vZGUub24oJ2NsaWNrJywgdGhpcy5vbk5leHRMZXZlbEJ1dHRvbkNsaWNrLCB0aGlzKTtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIOmAgOWHuuaMiemSriAtIOS9v+eUqOaMieWOi+aViOaenO+8jOaooeS7v+WFtuS7lui/lOWbnuaMiemSrlxuICAgICAgICBpZiAodGhpcy5leGl0QnV0dG9uKSB7XG4gICAgICAgICAgICBUb29scy5pbWFnZUJ1dHRvbkNsaWNrKHRoaXMuZXhpdEJ1dHRvbi5ub2RlLCBDb25maWcuYnV0dG9uUmVzICsgJ2JvYXJkX2J0bl9iYWNrX25vcm1hbCcsIENvbmZpZy5idXR0b25SZXMgKyAnYm9hcmRfYnRuX2JhY2tfcHJlc3NlZCcsICgpID0+IHtcbiAgICAgICAgICAgICAgICB0aGlzLm9uRXhpdEJ1dHRvbkNsaWNrKCk7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOaYvuekuue7k+eul+mhtemdolxuICAgICAqIEBwYXJhbSBnYW1lRW5kRGF0YSDmuLjmiI/nu5PmnZ/mlbDmja5cbiAgICAgKi9cbiAgICBwcml2YXRlIHNob3dMZXZlbFNldHRsZW1lbnQoZ2FtZUVuZERhdGE6IGFueSkge1xuICAgICAgIFxuICAgICAgICBpZiAoIXRoaXMubGV2ZWxTZXR0bGVtZW50Tm9kZSkge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcihcImxldmVsU2V0dGxlbWVudE5vZGUg5pyq6YWN572uXCIpO1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8g5pi+56S657uT566X6aG16Z2iXG4gICAgICAgIFxuICAgICAgICB0aGlzLmxldmVsU2V0dGxlbWVudE5vZGUuYWN0aXZlID0gdHJ1ZTtcblxuICAgICAgICAvLyDmoLnmja7muLjmiI/nu5PmnpzmmL7npLrlr7nlupTnmoTog4zmma8gLSDkv67lpI3miJDlip/liKTmlq3pgLvovpFcbiAgICAgICAgY29uc3QgaXNTdWNjZXNzID0gZ2FtZUVuZERhdGEuaXNTdWNjZXNzIHx8IGdhbWVFbmREYXRhLnN1Y2Nlc3MgfHwgZ2FtZUVuZERhdGEuaXNXaW4gfHwgZ2FtZUVuZERhdGEuZ2FtZVN0YXR1cyA9PT0gMTtcblxuICAgICAgIFxuXG4gICAgICAgIGlmIChpc1N1Y2Nlc3MpIHtcbiAgICAgICAgICAgIC8vIOaIkOWKnyAtIOaYvuekuuiDnOWIqeiDjOaZr1xuICAgICAgICAgICAgaWYgKHRoaXMud2luQmdOb2RlKSB7XG4gICAgICAgICAgICAgICAgdGhpcy53aW5CZ05vZGUuYWN0aXZlID0gdHJ1ZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICh0aGlzLmxvc2VCZ05vZGUpIHtcbiAgICAgICAgICAgICAgICB0aGlzLmxvc2VCZ05vZGUuYWN0aXZlID0gZmFsc2U7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIC8vIOWksei0pSAtIOaYvuekuuWksei0peiDjOaZr1xuICAgICAgICAgICAgaWYgKHRoaXMubG9zZUJnTm9kZSkge1xuICAgICAgICAgICAgICAgIHRoaXMubG9zZUJnTm9kZS5hY3RpdmUgPSB0cnVlO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKHRoaXMud2luQmdOb2RlKSB7XG4gICAgICAgICAgICAgICAgdGhpcy53aW5CZ05vZGUuYWN0aXZlID0gZmFsc2U7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgIFxuICAgICAgICB9XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5YaN5p2l5LiA5qyh5oyJ6ZKu54K55Ye75LqL5Lu2XG4gICAgICovXG4gICAgcHJpdmF0ZSBvblJldHJ5QnV0dG9uQ2xpY2soKSB7XG5cblxuICAgICAgICAvLyDlhbPpl63nu5PnrpfpobXpnaJcbiAgICAgICAgdGhpcy5oaWRlTGV2ZWxTZXR0bGVtZW50KCk7XG5cbiAgICAgICAgLy8g6YeN5paw5ZCv55So6L+U5Zue5oyJ6ZKuXG4gICAgICAgIHRoaXMuZW5hYmxlQmFja0J1dHRvbigpO1xuXG4gICAgICAgIC8vIOazqOaEj++8muS4jeWcqOi/memHjOmHjee9ruaji+ebmO+8jOetieaUtuWIsEV4dGVuZExldmVsSW5mb+WTjeW6lOaXtuWGjemHjee9rlxuICAgICAgICAvLyDov5nmoLflj6/ku6Xpgb/lhY3ov4fml6nmuIXnkIbvvIzorqnnjqnlrrblnKjngrnlh7vmjInpkq7lkI7ov5jog73nn63mmoLnnIvliLDmuLjnjqnnl5Xov7lcblxuICAgICAgICAvLyDlj5HpgIHlvZPliY3lhbPljaHnmoRFeHRlbmRMZXZlbEluZm9cbiAgICAgICAgdGhpcy5zZW5kRXh0ZW5kTGV2ZWxJbmZvKHRoaXMuY3VycmVudExldmVsKTtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDkuIvkuIDlhbPmjInpkq7ngrnlh7vkuovku7ZcbiAgICAgKi9cbiAgICBwcml2YXRlIG9uTmV4dExldmVsQnV0dG9uQ2xpY2soKSB7XG5cblxuICAgICAgICAvLyDlhbPpl63nu5PnrpfpobXpnaJcbiAgICAgICAgdGhpcy5oaWRlTGV2ZWxTZXR0bGVtZW50KCk7XG5cbiAgICAgICAgLy8g6YeN5paw5ZCv55So6L+U5Zue5oyJ6ZKuXG4gICAgICAgIHRoaXMuZW5hYmxlQmFja0J1dHRvbigpO1xuXG4gICAgICAgIC8vIOi/m+WFpeS4i+S4gOWFs1xuICAgICAgICBjb25zdCBuZXh0TGV2ZWwgPSB0aGlzLmN1cnJlbnRMZXZlbCArIDE7XG4gICAgICAgIHRoaXMuc2V0Q3VycmVudExldmVsKG5leHRMZXZlbCk7XG5cbiAgICAgICAgLy8g5rOo5oSP77ya5LiN5Zyo6L+Z6YeM6YeN572u5qOL55uY77yM562J5pS25YiwRXh0ZW5kTGV2ZWxJbmZv5ZON5bqU5pe25YaN6YeN572uXG4gICAgICAgIC8vIOi/meagt+WPr+S7pemBv+WFjei/h+aXqea4heeQhu+8jOiuqeeOqeWutuWcqOeCueWHu+aMiemSruWQjui/mOiDveefreaagueci+WIsOa4uOeOqeeXlei/uVxuXG4gICAgICAgIC8vIOWPkemAgeS4i+S4gOWFs+eahEV4dGVuZExldmVsSW5mb1xuICAgICAgICB0aGlzLnNlbmRFeHRlbmRMZXZlbEluZm8obmV4dExldmVsKTtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDpgIDlh7rmjInpkq7ngrnlh7vkuovku7ZcbiAgICAgKi9cbiAgICBwcml2YXRlIG9uRXhpdEJ1dHRvbkNsaWNrKCkge1xuXG5cbiAgICAgICAgLy8g5YWz6Zet57uT566X6aG16Z2iXG4gICAgICAgIHRoaXMuaGlkZUxldmVsU2V0dGxlbWVudCgpO1xuXG4gICAgICAgIC8vIOmHjeaWsOWQr+eUqOi/lOWbnuaMiemSru+8iOiZveeEtuimgemAgOWHuuS6hu+8jOS9huS/neaMgeS4gOiHtOaAp++8iVxuICAgICAgICB0aGlzLmVuYWJsZUJhY2tCdXR0b24oKTtcblxuICAgICAgICAvLyDov5Tlm57liLDlhbPljaHpgInmi6npobXpnaLvvIjljLnphY3nlYzpnaLvvIlcbiAgICAgICAgdGhpcy5yZXR1cm5Ub0xldmVsU2VsZWN0KCk7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog6ZqQ6JeP57uT566X6aG16Z2iXG4gICAgICovXG4gICAgcHJpdmF0ZSBoaWRlTGV2ZWxTZXR0bGVtZW50KCkge1xuICAgICAgICBpZiAodGhpcy5sZXZlbFNldHRsZW1lbnROb2RlKSB7XG4gICAgICAgICAgICB0aGlzLmxldmVsU2V0dGxlbWVudE5vZGUuYWN0aXZlID0gZmFsc2U7XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDlj5HpgIFFeHRlbmRMZXZlbEluZm/mtojmga9cbiAgICAgKiBAcGFyYW0gbGV2ZWxJZCDlhbPljaFJRFxuICAgICAqL1xuICAgIHByaXZhdGUgc2VuZEV4dGVuZExldmVsSW5mbyhsZXZlbElkOiBudW1iZXIpIHtcbiAgICAgICBcblxuICAgICAgICBjb25zdCByZXF1ZXN0ID0ge1xuICAgICAgICAgICAgbGV2ZWxJZDogbGV2ZWxJZFxuICAgICAgICB9O1xuXG4gICAgICAgIFdlYlNvY2tldE1hbmFnZXIuR2V0SW5zdGFuY2UoKS5zZW5kTXNnKE1lc3NhZ2VJZC5Nc2dUeXBlRXh0ZW5kTGV2ZWxJbmZvLCByZXF1ZXN0KTtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDorr7nva7mtYvor5XmjInpkq5cbiAgICAgKi9cbiAgICBwcml2YXRlIHNldHVwRGVidWdCdXR0b24oKSB7XG4gICAgICAgIGlmICh0aGlzLmRlYnVnU2hvd01pbmVzQnV0dG9uKSB7XG4gICAgICAgICAgICB0aGlzLmRlYnVnU2hvd01pbmVzQnV0dG9uLm5vZGUub24oJ2NsaWNrJywgdGhpcy5vbkRlYnVnU2hvd01pbmVzQ2xpY2ssIHRoaXMpO1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5rWL6K+V5oyJ6ZKu54K55Ye75LqL5Lu2IC0g5Y+R6YCBRGVidWdTaG93TWluZXPmtojmga9cbiAgICAgKi9cbiAgICBwcml2YXRlIG9uRGVidWdTaG93TWluZXNDbGljaygpIHtcbiAgICAgICBcbiAgICAgICAgV2ViU29ja2V0TWFuYWdlci5HZXRJbnN0YW5jZSgpLnNlbmRNc2coTWVzc2FnZUlkLk1zZ1R5cGVEZWJ1Z1Nob3dNaW5lcywge30pO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOWIpOaWreaYr+WQpuWcqOWNleacuuaooeW8j1xuICAgICAqL1xuICAgIHByaXZhdGUgaXNJblNpbmdsZU1vZGUoKTogYm9vbGVhbiB7XG4gICAgICAgIC8vIOWNleacuuaooeW8j+eahOWIpOaWremAu+i+ke+8muW9k+WJjemhtemdouaYr+WFs+WNoemhtemdouS4lOacieacieaViOeahOaIv+mXtElEXG4gICAgICAgIHJldHVybiB0aGlzLmN1cnJlbnRSb29tSWQgPiAwO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOWkhOeQhkRlYnVnU2hvd01pbmVz5ZON5bqU77yM5Zyo54K45by55L2N572u55Sf5oiQ5rWL6K+V6aKE5Yi25L2TXG4gICAgICogQHBhcmFtIG1pbmVQb3NpdGlvbnMg54K45by55L2N572u5pWw57uEIFt7eDogbnVtYmVyLCB5OiBudW1iZXJ9XVxuICAgICAqL1xuICAgIHB1YmxpYyBoYW5kbGVEZWJ1Z1Nob3dNaW5lcyhtaW5lUG9zaXRpb25zOiBBcnJheTx7eDogbnVtYmVyLCB5OiBudW1iZXJ9Pikge1xuICAgICAgICBcbiAgICAgICAgaWYgKCF0aGlzLmRlYnVnTWluZVByZWZhYikge1xuICAgICAgICAgICAgY2Mud2FybihcImRlYnVnTWluZVByZWZhYiDpooTliLbkvZPmnKrorr7nva7vvIzml6Dms5XmmL7npLrmtYvor5XmoIforrBcIik7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cblxuICAgICAgICBpZiAoIXRoaXMuY3VycmVudFNpbmdsZUNoZXNzQm9hcmQpIHtcbiAgICAgICAgICAgIGNjLndhcm4oXCLlvZPliY3msqHmnInmv4DmtLvnmoTljZXmnLrmo4vnm5hcIik7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cblxuICAgICAgICBpZiAoIW1pbmVQb3NpdGlvbnMgfHwgIUFycmF5LmlzQXJyYXkobWluZVBvc2l0aW9ucykgfHwgbWluZVBvc2l0aW9ucy5sZW5ndGggPT09IDApIHtcbiAgICAgICAgICAgIGNjLndhcm4oXCLlnLDpm7fkvY3nva7mlbDmja7ml6DmlYg6XCIsIG1pbmVQb3NpdGlvbnMpO1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG5cbiAgICAgICBcblxuICAgICAgICAvLyDlhYjlsJ3or5Xnm7TmjqXliJvlu7rkuIDkuKrmtYvor5XpooTliLbkvZPvvIzkuI3kvb/nlKjlu7bov59cbiAgICAgICAgaWYgKG1pbmVQb3NpdGlvbnMubGVuZ3RoID4gMCkge1xuICAgICAgICAgICAgY29uc3QgZmlyc3RQb3NpdGlvbiA9IG1pbmVQb3NpdGlvbnNbMF07XG4gICAgICAgICAgIFxuICAgICAgICAgICAgLy8g5qOA5p+l5Z2Q5qCH5a2X5q61XG4gICAgICAgICAgICBsZXQgY29vcmRYOiBudW1iZXIsIGNvb3JkWTogbnVtYmVyO1xuICAgICAgICAgICAgY29uc3QgcG9zID0gZmlyc3RQb3NpdGlvbiBhcyBhbnk7ICAvLyDkvb/nlKhhbnnnsbvlnovpgb/lhY1UeXBlU2NyaXB05oql6ZSZXG5cbiAgICAgICAgICAgIGlmIChwb3MueCAhPT0gdW5kZWZpbmVkICYmIHBvcy55ICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgICAgICBjb29yZFggPSBwb3MueDtcbiAgICAgICAgICAgICAgICBjb29yZFkgPSBwb3MueTtcbiAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgfSBlbHNlIGlmIChwb3MucSAhPT0gdW5kZWZpbmVkICYmIHBvcy5yICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgICAgICBjb29yZFggPSBwb3MucTtcbiAgICAgICAgICAgICAgICBjb29yZFkgPSBwb3MucjtcbiAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcihg4p2MIOaXoOazleivhuWIq+WdkOagh+Wtl+autTpgLCBmaXJzdFBvc2l0aW9uKTtcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIC8vIOebtOaOpeiwg+eUqO+8jOS4jeS9v+eUqOW7tui/n1xuICAgICAgICAgICAgdGhpcy5jcmVhdGVEZWJ1Z01pbmVQcmVmYWIoY29vcmRYLCBjb29yZFkpO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8g5Zyo5q+P5Liq54K45by55L2N572u55Sf5oiQ5rWL6K+V6aKE5Yi25L2TXG4gICAgICAgIG1pbmVQb3NpdGlvbnMuZm9yRWFjaCgocG9zaXRpb24sIGluZGV4KSA9PiB7XG4gICAgICAgICAgICBjb25zdCBwb3MgPSBwb3NpdGlvbiBhcyBhbnk7ICAvLyDkvb/nlKhhbnnnsbvlnovpgb/lhY1UeXBlU2NyaXB05oql6ZSZXG5cbiAgICAgICAgICAgIC8vIOiOt+WPluWdkOagh1xuICAgICAgICAgICAgbGV0IGNvb3JkWDogbnVtYmVyLCBjb29yZFk6IG51bWJlcjtcbiAgICAgICAgICAgIGlmIChwb3MueCAhPT0gdW5kZWZpbmVkICYmIHBvcy55ICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgICAgICBjb29yZFggPSBwb3MueDtcbiAgICAgICAgICAgICAgICBjb29yZFkgPSBwb3MueTtcbiAgICAgICAgICAgIH0gZWxzZSBpZiAocG9zLnEgIT09IHVuZGVmaW5lZCAmJiBwb3MuciAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICAgICAgY29vcmRYID0gcG9zLnE7XG4gICAgICAgICAgICAgICAgY29vcmRZID0gcG9zLnI7XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoYOKdjCDlnLDpm7fkvY3nva4ke2luZGV4feWdkOagh+Wtl+auteaXoOaViDpgLCBwb3NpdGlvbik7XG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICBpZiAoaW5kZXggPT09IDApIHtcbiAgICAgICAgICAgICAgICAvLyDnrKzkuIDkuKrkuI3lu7bov5/vvIznq4vljbPmiafooYzvvIjlt7Lnu4/lnKjkuIrpnaLlpITnkIbov4fkuobvvIzot7Pov4fvvIlcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIC8vIOWFtuS7lueahOS9v+eUqOW7tui/nyAtIOS/ruWkjemXreWMhemXrumimFxuICAgICAgICAgICAgICAgIGNvbnN0IGNhcHR1cmVkWCA9IGNvb3JkWDsgIC8vIOaNleiOt+W9k+WJjeWAvFxuICAgICAgICAgICAgICAgIGNvbnN0IGNhcHR1cmVkWSA9IGNvb3JkWTsgIC8vIOaNleiOt+W9k+WJjeWAvFxuICAgICAgICAgICAgICAgIHRoaXMuc2NoZWR1bGVPbmNlKCgpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICAgIHRoaXMuY3JlYXRlRGVidWdNaW5lUHJlZmFiKGNhcHR1cmVkWCwgY2FwdHVyZWRZKTtcbiAgICAgICAgICAgICAgICB9LCBpbmRleCAqIDAuMSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0pO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOWcqOaMh+WumuS9jee9ruWIm+W7uua1i+ivlemihOWItuS9k1xuICAgICAqIEBwYXJhbSB4IOagvOWtkHjlnZDmoIfvvIjlm5vovrnlvaLvvInmiJZx5Z2Q5qCH77yI5YWt6L655b2i77yJXG4gICAgICogQHBhcmFtIHkg5qC85a2QeeWdkOagh++8iOWbm+i+ueW9ou+8ieaIlnLlnZDmoIfvvIjlha3ovrnlvaLvvIlcbiAgICAgKi9cbiAgICBwcml2YXRlIGNyZWF0ZURlYnVnTWluZVByZWZhYih4OiBudW1iZXIsIHk6IG51bWJlcikge1xuICAgICAgIFxuICAgICAgICAvLyDmo4Dmn6XlnZDmoIfmmK/lkKbmnInmlYhcbiAgICAgICAgaWYgKHggPT09IHVuZGVmaW5lZCB8fCB5ID09PSB1bmRlZmluZWQgfHwgeCA9PT0gbnVsbCB8fCB5ID09PSBudWxsKSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKGDinYwg5peg5pWI55qE5Z2Q5qCH5Y+C5pWwOiB4PSR7eH0sIHk9JHt5fWApO1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcihgICAgeOexu+WeizogJHt0eXBlb2YgeH0sIHnnsbvlnos6ICR7dHlwZW9mIHl9YCk7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKGAgICDosIPnlKjloIbmoIg6YCwgbmV3IEVycm9yKCkuc3RhY2spO1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG5cbiAgICAgICAgaWYgKCF0aGlzLmRlYnVnTWluZVByZWZhYikge1xuICAgICAgICAgICAgY2MuZXJyb3IoXCJkZWJ1Z01pbmVQcmVmYWIg5Li656m677yM5peg5rOV5Yib5bu65rWL6K+V6aKE5Yi25L2TXCIpO1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG5cbiAgICAgICAgaWYgKCF0aGlzLmN1cnJlbnRTaW5nbGVDaGVzc0JvYXJkKSB7XG4gICAgICAgICAgICBjYy5lcnJvcihcImN1cnJlbnRTaW5nbGVDaGVzc0JvYXJkIOS4uuepuu+8jOaXoOazleWIm+W7uua1i+ivlemihOWItuS9k1wiKTtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuXG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICAvLyDliKTmlq3lvZPliY3kvb/nlKjnmoTmmK/lk6rnp43mjqfliLblmahcbiAgICAgICAgICAgIGNvbnN0IGlzVXNpbmdIZXhDb250cm9sbGVyID0gKHRoaXMuY3VycmVudFNpbmdsZUNoZXNzQm9hcmQgPT09IHRoaXMuaGV4U2luZ2xlQ2hlc3NCb2FyZENvbnRyb2xsZXIgYXMgYW55KTtcblxuICAgICAgICAgICAgbGV0IGRlYnVnTm9kZTogY2MuTm9kZSA9IG51bGw7XG5cbiAgICAgICAgICAgIGlmIChpc1VzaW5nSGV4Q29udHJvbGxlcikge1xuICAgICAgICAgICAgICAgIC8vIOWFrei+ueW9ouaOp+WItuWZqO+8mnjlrp7pmYXmmK9x77yMeeWunumZheaYr3JcbiAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIGRlYnVnTm9kZSA9ICh0aGlzLmN1cnJlbnRTaW5nbGVDaGVzc0JvYXJkIGFzIGFueSkuY3JlYXRlQ3VzdG9tUHJlZmFiKFxuICAgICAgICAgICAgICAgICAgICB4LCB5LCAgLy8g5a+55LqO5YWt6L655b2i77yMeOWwseaYr3HvvIx55bCx5pivclxuICAgICAgICAgICAgICAgICAgICB0aGlzLmRlYnVnTWluZVByZWZhYixcbiAgICAgICAgICAgICAgICAgICAgYERlYnVnTWluZV8ke3h9XyR7eX1gXG4gICAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgLy8g5Zub6L655b2i5o6n5Yi25ZmoXG4gICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICBkZWJ1Z05vZGUgPSB0aGlzLmN1cnJlbnRTaW5nbGVDaGVzc0JvYXJkLmNyZWF0ZUN1c3RvbVByZWZhYihcbiAgICAgICAgICAgICAgICAgICAgeCwgeSxcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5kZWJ1Z01pbmVQcmVmYWIsXG4gICAgICAgICAgICAgICAgICAgIGBEZWJ1Z01pbmVfJHt4fV8ke3l9YFxuICAgICAgICAgICAgICAgICk7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIGlmIChkZWJ1Z05vZGUpIHtcbiAgICAgICAgICAgICAgICAvLyDlsIbliJvlu7rnmoToioLngrnlrZjlgqjotbfmnaXvvIznlKjkuo7lkI7nu63muIXnkIZcbiAgICAgICAgICAgICAgICB0aGlzLmRlYnVnTWluZU5vZGVzLnB1c2goZGVidWdOb2RlKTtcbiAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICBjYy5lcnJvcihg4p2MIOWcqOS9jee9riAoJHt4fSwgJHt5fSkg5Yib5bu65rWL6K+V6aKE5Yi25L2T5aSx6LSl77yM6L+U5Zue5YC85Li656m6YCk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICBjYy5lcnJvcihg4p2MIOWIm+W7uua1i+ivlemihOWItuS9k+aXtuWPkeeUn+mUmeivrzpgLCBlcnJvcik7XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDmuIXpmaTmiYDmnInmtYvor5XpooTliLbkvZNcbiAgICAgKi9cbiAgICBwdWJsaWMgY2xlYXJEZWJ1Z01pbmVzKCkge1xuICAgICAgIFxuXG4gICAgICAgIHRoaXMuZGVidWdNaW5lTm9kZXMuZm9yRWFjaCgobm9kZSwgaW5kZXgpID0+IHtcbiAgICAgICAgICAgIGlmIChub2RlICYmIGNjLmlzVmFsaWQobm9kZSkpIHtcbiAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICBub2RlLmRlc3Ryb3koKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSk7XG5cbiAgICAgICAgLy8g5riF56m65pWw57uEXG4gICAgICAgIHRoaXMuZGVidWdNaW5lTm9kZXMgPSBbXTtcbiAgICAgICBcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDph43nva7lhbPljaHnirbmgIHvvIjljIXmi6zmuIXpmaTmtYvor5XpooTliLbkvZPvvIlcbiAgICAgKi9cbiAgICBwdWJsaWMgcmVzZXRMZXZlbFN0YXRlKCkge1xuICAgICAgIFxuICAgICAgICB0aGlzLmNsZWFyRGVidWdNaW5lcygpO1xuICAgICAgICAvLyDov5nph4zlj6/ku6Xmt7vliqDlhbbku5bpnIDopoHph43nva7nmoTnirbmgIFcbiAgICB9XG5cbiAgICBvbkRlc3Ryb3koKSB7XG4gICAgICAgIC8vIOWPlua2iOa2iOaBr+ebkeWQrFxuICAgICAgICB0aGlzLnVucmVnaXN0ZXJTaW5nbGVNb2RlTWVzc2FnZUhhbmRsZXJzKCk7XG5cbiAgICAgICAgLy8g5riF55CG5rWL6K+V6aKE5Yi25L2TXG4gICAgICAgIHRoaXMuY2xlYXJEZWJ1Z01pbmVzKCk7XG5cbiAgICAgICAgLy8g5riF55CG5oyJ6ZKu5LqL5Lu2XG4gICAgICAgIGlmICh0aGlzLnJldHJ5QnV0dG9uKSB7XG4gICAgICAgICAgICB0aGlzLnJldHJ5QnV0dG9uLm5vZGUub2ZmKCdjbGljaycsIHRoaXMub25SZXRyeUJ1dHRvbkNsaWNrLCB0aGlzKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodGhpcy5uZXh0TGV2ZWxCdXR0b24pIHtcbiAgICAgICAgICAgIHRoaXMubmV4dExldmVsQnV0dG9uLm5vZGUub2ZmKCdjbGljaycsIHRoaXMub25OZXh0TGV2ZWxCdXR0b25DbGljaywgdGhpcyk7XG4gICAgICAgIH1cbiAgICAgICAgLy8g6YCA5Ye65oyJ6ZKu5L2/55SoIFRvb2xzLmltYWdlQnV0dG9uQ2xpY2vvvIzkvJroh6rliqjnrqHnkIbkuovku7bvvIzml6DpnIDmiYvliqjmuIXnkIZcbiAgICAgICAgaWYgKHRoaXMuZGVidWdTaG93TWluZXNCdXR0b24pIHtcbiAgICAgICAgICAgIHRoaXMuZGVidWdTaG93TWluZXNCdXR0b24ubm9kZS5vZmYoJ2NsaWNrJywgdGhpcy5vbkRlYnVnU2hvd01pbmVzQ2xpY2ssIHRoaXMpO1xuICAgICAgICB9XG4gICAgfVxufVxuIl19